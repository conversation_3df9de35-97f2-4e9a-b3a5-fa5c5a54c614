import * as React from 'react';
import { Button } from './components/ui/button';
import { demoPages, getDefaultDemoPage, getDemoPage } from './demo-registry';

function App() {
  // Default to the first demo (newest)
  const [currentPageId, setCurrentPageId] = React.useState<string>(getDefaultDemoPage().id);

  const currentPage = getDemoPage(currentPageId) || getDefaultDemoPage();
  const CurrentComponent = currentPage.component;

  return (
    <div className="dark min-h-screen flex flex-col bg-gray-950">
      {/* Thin Navigation Bar */}
      <nav className="bg-gray-900 border-b border-gray-700 px-4 py-2">
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            {demoPages.map((page) => (
              <Button
                key={page.id}
                variant={currentPageId === page.id ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setCurrentPageId(page.id)}
                className="text-xs px-3 py-1 h-7"
                title={page.description || page.label}
              >
                {page.label}
              </Button>
            ))}
          </div>
        </div>
        {currentPage.description && (
          <div className="mt-2 px-1">
            <span className="text-xs text-gray-400">
              {currentPage.description}
            </span>
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        <CurrentComponent />
      </main>
    </div>
  );
}

export default App;
