import React, { useState, useEffect } from 'react';
import { AuctionChat } from './AuctionChat';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { MessageElement, AuMessageType } from '@/api-client';

// Sample message data with interesting auction scenarios
const createSampleMessage = (
  id: string,
  from: string,
  to: string,
  message: string,
  messageType: AuMessageType,
  timestamp?: number
): MessageElement => ({
  id,
  from,
  to,
  message,
  message_type: messageType,
  message_type_label: messageType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
  timestamp: timestamp || Date.now(),
  timestamp_label: new Date(timestamp || Date.now()).toLocaleTimeString(),
});

const initialMessages: MessageElement[] = [
  createSampleMessage(
    'msg-1',
    'System',
    'All',
    'Auction session started. Welcome to Energy Trading Auction #2024-12-19-001',
    'SYSTEM_BROADCAST',
    Date.now() - 300000
  ),
  createSampleMessage(
    'msg-2',
    'Auctioneer',
    'All',
    'Good morning traders! Today we are auctioning 500 MW of renewable energy capacity. Starting price: $45.50/MWh',
    'AUCTIONEER_BROADCAST',
    Date.now() - 280000
  ),
  createSampleMessage(
    'msg-3',
    'jsmith (GreenPower Corp)',
    'Auctioneer',
    'Ready to participate. What are the minimum bid increments?',
    'TRADER_TO_AUCTIONEER',
    Date.now() - 260000
  ),
  createSampleMessage(
    'msg-4',
    'Auctioneer',
    'All',
    'Minimum bid increment is $0.25/MWh. Good luck to all participants!',
    'AUCTIONEER_BROADCAST',
    Date.now() - 240000
  ),
  createSampleMessage(
    'msg-5',
    'System',
    'All',
    'Round 1 starting in 30 seconds. Current price: $45.50/MWh',
    'SYSTEM_BROADCAST',
    Date.now() - 220000
  ),
  createSampleMessage(
    'msg-6',
    'mwilson (EcoEnergy Ltd)',
    'Auctioneer',
    'Can we get clarification on delivery terms?',
    'TRADER_TO_AUCTIONEER',
    Date.now() - 200000
  ),
  createSampleMessage(
    'msg-7',
    'Auctioneer',
    'All',
    'Delivery: Q2 2025, Grid connection point: Northern Hub. All standard terms apply.',
    'AUCTIONEER_BROADCAST',
    Date.now() - 180000
  ),
  createSampleMessage(
    'msg-8',
    'System',
    'rbrown (PowerTech Inc)',
    'Your bid of $44.75/MWh has been received and is currently leading.',
    'SYSTEM_TO_TRADER',
    Date.now() - 160000
  ),
  createSampleMessage(
    'msg-9',
    'kdavis (CleanEnergy Co)',
    'Auctioneer',
    'Technical issue with our bidding interface. Can we get 2 minutes extension?',
    'TRADER_TO_AUCTIONEER',
    Date.now() - 140000
  ),
  createSampleMessage(
    'msg-10',
    'System',
    'Auctioneer',
    'WARNING: Round timer at 30 seconds. Multiple bids received.',
    'SYSTEM_TO_AUCTIONEER',
    Date.now() - 120000
  ),
  createSampleMessage(
    'msg-11',
    'Auctioneer',
    'All',
    'Extension granted. Round extended by 2 minutes due to technical issues.',
    'AUCTIONEER_BROADCAST',
    Date.now() - 100000
  ),
  createSampleMessage(
    'msg-12',
    'agarcia (RenewablePower)',
    'Auctioneer',
    'Thank you for the extension. Submitting our final bid now.',
    'TRADER_TO_AUCTIONEER',
    Date.now() - 80000
  ),
  createSampleMessage(
    'msg-13',
    'System',
    'All',
    'Round 1 completed. Winning bid: $43.25/MWh by SolarTech Solutions. Round 2 starting...',
    'SYSTEM_BROADCAST',
    Date.now() - 60000
  ),
  createSampleMessage(
    'msg-14',
    'Auctioneer',
    'All',
    'Excellent competition! Round 2: 300 MW available, starting at $43.00/MWh',
    'AUCTIONEER_BROADCAST',
    Date.now() - 40000
  ),
  createSampleMessage(
    'msg-15',
    'tlee (WindPower Inc)',
    'Auctioneer',
    'Is there any volume flexibility on this lot?',
    'TRADER_TO_AUCTIONEER',
    Date.now() - 20000
  ),
];

// Create demo function following the pattern used in the codebase
export function createDemo() {
  return {
    id: 'ChatDemo'
  };
}

export const ChatDemo: React.FC = () => {
  const [messages, setMessages] = useState<MessageElement[]>(initialMessages);
  const [messageCounter, setMessageCounter] = useState(16);

  const addRandomMessage = () => {
    const sampleMessages = [
      'Current market conditions look favorable for renewable energy.',
      'We are seeing strong demand in the northern regions.',
      'Price volatility expected due to weather conditions.',
      'Grid stability requirements updated for this auction.',
      'New environmental regulations may affect pricing.',
      'Transmission capacity constraints in southern corridor.',
      'Peak demand forecast revised upward by 5%.',
      'Backup generation requirements increased.',
    ];

    const traders = [
      'jsmith (GreenPower Corp)',
      'mwilson (EcoEnergy Ltd)',
      'rbrown (PowerTech Inc)',
      'kdavis (CleanEnergy Co)',
      'agarcia (RenewablePower)',
      'tlee (WindPower Inc)',
      'schen (SolarTech Solutions)'
    ];
    const messageTypes: AuMessageType[] = ['AUCTIONEER_BROADCAST', 'TRADER_TO_AUCTIONEER', 'SYSTEM_BROADCAST'];

    const randomMessage = sampleMessages[Math.floor(Math.random() * sampleMessages.length)];
    const randomType = messageTypes[Math.floor(Math.random() * messageTypes.length)];
    const randomTrader = traders[Math.floor(Math.random() * traders.length)];

    let from = 'System';
    let to = 'All';

    if (randomType === 'AUCTIONEER_BROADCAST') {
      from = 'Auctioneer';
      to = 'All';
    } else if (randomType === 'TRADER_TO_AUCTIONEER') {
      from = randomTrader;
      to = 'Auctioneer';
    }

    const newMessage = createSampleMessage(
      `msg-${messageCounter}`,
      from,
      to,
      randomMessage,
      randomType
    );

    setMessages(prev => [...prev, newMessage]);
    setMessageCounter(prev => prev + 1);
  };

  const removeLastMessage = () => {
    setMessages(prev => prev.slice(0, -1));
  };

  const clearMessages = () => {
    setMessages([]);
  };

  const resetMessages = () => {
    setMessages(initialMessages);
    setMessageCounter(16);
  };

  const handleSubmitMessage = (message: string) => {
    const newMessage = createSampleMessage(
      `msg-${messageCounter}`,
      'demouser (Demo Company)',
      'Auctioneer',
      message,
      'TRADER_TO_AUCTIONEER'
    );
    setMessages(prev => [...prev, newMessage]);
    setMessageCounter(prev => prev + 1);
  };

  // Auto-add messages for demo effect
  useEffect(() => {
    const interval = setTimeout(() => {
      if (Math.random() > 0.3) { // 70% chance every 2 seconds
        addRandomMessage();
      }
    }, 2000);

    return () => clearTimeout(interval);
  }, [messages, messageCounter]);

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Auction Chat Demo</h1>
        <p className="text-muted-foreground">
          Interactive chat component for auction communication with real-time messaging
        </p>
      </div>

      <Tabs defaultValue="auctioneer" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="auctioneer">Auctioneer View</TabsTrigger>
          <TabsTrigger value="trader">Trader View</TabsTrigger>
        </TabsList>

        <TabsContent value="auctioneer" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Auctioneer Chat Interface
                <div className="flex gap-2">
                  <Button onClick={addRandomMessage} size="sm" variant="outline">
                    Add Message
                  </Button>
                  <Button onClick={removeLastMessage} size="sm" variant="outline">
                    Remove Last
                  </Button>
                  <Button onClick={clearMessages} size="sm" variant="outline">
                    Clear All
                  </Button>
                  <Button onClick={resetMessages} size="sm" variant="outline">
                    Reset Demo
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AuctionChat
                is_auctioneer={true}
                messages={messages}
                outer_height={400}
                width={400}
                onSubmitMessage={handleSubmitMessage}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trader" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Trader Chat Interface
                <div className="flex gap-2">
                  <Button onClick={addRandomMessage} size="sm" variant="outline">
                    Add Message
                  </Button>
                  <Button onClick={removeLastMessage} size="sm" variant="outline">
                    Remove Last
                  </Button>
                  <Button onClick={clearMessages} size="sm" variant="outline">
                    Clear All
                  </Button>
                  <Button onClick={resetMessages} size="sm" variant="outline">
                    Reset Demo
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AuctionChat
                is_auctioneer={false}
                messages={messages}
                outer_height={400}
                width={400}
                onSubmitMessage={handleSubmitMessage}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Demo Features</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
            <li>Different message types with unique icons and colors</li>
            <li>Animated message appearance with staggered timing</li>
            <li>Incoming/outgoing message alignment based on user role</li>
            <li>Auto-scroll to latest messages</li>
            <li>Trader message highlighting for auctioneers</li>
            <li>Real-time message submission</li>
            <li>Responsive design with proper overflow handling</li>
            <li>Auto-generated demo messages every 5 seconds (30% chance)</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};
