import React, { useEffect, useRef, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Send, User, Megaphone, Settings, AlertCircle, MessageSquare, Broadcast } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { MessageElement, AuMessageType } from '@/api-client';

export interface AuctionChatProps {
  is_auctioneer: boolean;
  messages: MessageElement[];
  outer_height: number;
  width: number;
  onSubmitMessage?: (message: string) => void;
}

const getMessageTypeIcon = (messageType: AuMessageType) => {
  switch (messageType) {
    case 'AUCTIONEER_BROADCAST':
      return <Broadcast className="w-4 h-4" />;
    case 'AUCTIONEER_TO_TRADER':
      return <Megaphone className="w-4 h-4" />;
    case 'TRADER_TO_AUCTIONEER':
      return <User className="w-4 h-4" />;
    case 'SYSTEM_BROADCAST':
      return <Settings className="w-4 h-4" />;
    case 'SYSTEM_TO_TRADER':
      return <AlertCircle className="w-4 h-4" />;
    case 'SYSTEM_TO_AUCTIONEER':
      return <MessageSquare className="w-4 h-4" />;
    default:
      return <MessageSquare className="w-4 h-4" />;
  }
};

const getMessageTypeColor = (messageType: AuMessageType) => {
  switch (messageType) {
    case 'AUCTIONEER_BROADCAST':
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'AUCTIONEER_TO_TRADER':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'TRADER_TO_AUCTIONEER':
      return 'text-purple-600 bg-purple-50 border-purple-200';
    case 'SYSTEM_BROADCAST':
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'SYSTEM_TO_TRADER':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'SYSTEM_TO_AUCTIONEER':
      return 'text-gray-600 bg-gray-50 border-gray-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

const isOutgoingMessage = (message: MessageElement, isAuctioneer: boolean) => {
  if (isAuctioneer) {
    return message.message_type === 'AUCTIONEER_BROADCAST' || 
           message.message_type === 'AUCTIONEER_TO_TRADER';
  } else {
    return message.message_type === 'TRADER_TO_AUCTIONEER';
  }
};

export const AuctionChat: React.FC<AuctionChatProps> = ({
  is_auctioneer,
  messages,
  outer_height,
  width,
  onSubmitMessage
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const messageHeight = is_auctioneer ? outer_height - 58 : outer_height - 87;

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputMessage.trim() && onSubmitMessage) {
      onSubmitMessage(inputMessage.trim());
      setInputMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div 
      className="flex flex-col bg-background border rounded-lg"
      style={{ width: `${width}px`, height: `${outer_height}px` }}
    >
      {/* Messages Container */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-3"
        style={{ height: `${messageHeight}px` }}
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No Messages</p>
            </div>
          </div>
        ) : (
          messages.map((message, index) => {
            const isOutgoing = isOutgoingMessage(message, is_auctioneer);
            const isTraderMessage = is_auctioneer && message.message_type === 'TRADER_TO_AUCTIONEER';
            const colorClasses = getMessageTypeColor(message.message_type);
            
            return (
              <div
                key={message.id}
                className={cn(
                  "flex animate-in slide-in-from-bottom-2 duration-300",
                  isOutgoing ? "justify-end" : "justify-start"
                )}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <div className={cn(
                  "max-w-[80%] rounded-lg border p-3 shadow-sm transition-all duration-200 hover:shadow-md",
                  colorClasses,
                  isTraderMessage && "ring-2 ring-purple-200 ring-opacity-50"
                )}>
                  {/* Message Header */}
                  <div className="flex items-center gap-2 mb-1">
                    {getMessageTypeIcon(message.message_type)}
                    <span className="text-xs font-medium">
                      {message.timestamp_label} • {message.from} → {message.to}
                    </span>
                  </div>
                  
                  {/* Message Content */}
                  <div className="text-sm whitespace-pre-wrap break-words">
                    {message.message}
                  </div>
                  
                  {/* Message Type Label */}
                  <div className="text-xs opacity-75 mt-1">
                    {message.message_type_label}
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Info Section for Traders */}
      {!is_auctioneer && (
        <div className="px-4 py-2 bg-muted/50 text-xs text-muted-foreground border-t">
          Type message to auctioneer<br />
          (message won't be seen by other traders)
        </div>
      )}

      {/* Input Section */}
      <form onSubmit={handleSubmit} className="p-2 border-t">
        <div className="flex gap-2">
          <Textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            className="resize-none min-h-[40px] max-h-[120px]"
            style={{ width: `${width - 60}px` }}
          />
          <Button 
            type="submit" 
            size="icon"
            disabled={!inputMessage.trim()}
            className="shrink-0"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </form>
    </div>
  );
};
