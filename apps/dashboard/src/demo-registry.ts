import React from 'react';
import { RibbonPlotlyDemo } from './components/RibbonPlotlyDemo';
import { CommonStatusDemo } from './components/CommonStatusDemo';
import { DeRoundTableDemo } from './components/DeRoundTableDemo';
// import { AuctioneerPage } from './components/AuctioneerPage';
import { UserManagementPage } from './components/UserManagementPage';
//import My3DLineChart from './widgets/3d-ribbon/3d-ribbon-rbf';
import ThreeLineChart3Dv1 from './widgets/3d-ribbon/ThreeLineChart3Dv1';
import ThreeLineChart3Dv2 from './widgets/3d-ribbon/ThreeLineChart3Dv2';
import StepChartO1Mini from './widgets/step-chart/StepChartO1Mini';

/**
 * Demo Registry - Central place to manage all demo pages
 *
 * To add a new demo:
 * 1. Import your demo component
 * 2. Add it to the demoPages array (new demos go first)
 * 3. The navigation will automatically update
 */

export type DemoPage = {
  id: string;
  label: string;
  component: React.ComponentType;
  description?: string;
};

function createDemoPage(
  component: React.ComponentType,
  id: string,
  label?: string,
  description?: string
): DemoPage {
  return {
    id,
    label: label || id,
    component,
    description: description || label || id
  };
}

export const demoPages: DemoPage[] = [
  createDemoPage(StepChartO1Mini, 'StepChartO1Mini'),
  createDemoPage(ThreeLineChart3Dv1, 'ThreeLineChart3Dv1'),
  createDemoPage(ThreeLineChart3Dv2, 'ThreeLineChart3Dv2'),
  // {
  //   id: '3d-ribbon-rbf',
  //   label: '3D Ribbon RBF',
  //   component: My3DLineChart,
  //   description: 'Interactive 3D line chart using @react-three/fiber and drei'
  // },  
  createDemoPage(RibbonPlotlyDemo, 'RibbonPlotlyDemo'),
  createDemoPage(CommonStatusDemo, 'CommonStatusDemo'),
  {
    id: 'round-table',
    label: 'Round Table',
    component: DeRoundTableDemo,
    description: 'Interactive round table with real-time updates and scrolling'
  },
  // {
  //   id: 'auctioneer',
  //   label: 'Auctioneer',
  //   component: AuctioneerPage,
  //   description: 'Auctioneer dashboard with various demo components'
  // },
  {
    id: 'users',
    label: 'Users',
    component: UserManagementPage,
    description: 'User and company management interface'
  },
];

/**
 * Get demo page by ID
 */
export const getDemoPage = (id: string): DemoPage | undefined => {
  return demoPages.find(page => page.id === id);
};

/**
 * Get the default demo page (first in the list)
 */
export const getDefaultDemoPage = (): DemoPage => {
  return demoPages[0];
};
