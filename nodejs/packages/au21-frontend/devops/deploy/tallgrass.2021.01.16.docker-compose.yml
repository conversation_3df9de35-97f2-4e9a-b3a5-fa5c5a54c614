# NB: had to download java and register with objectdb manually!
# apt-get update && apt-get upgrade
# apt-get install default-jdk
# java -classpath objectdb.jar com.objectdb.Activator
# username: dmoshal
# password: 007007
# Site license key:  K812-7Y7H-GR1Y-DFCC

# Oct 27, 2020 implement PRICE as default for templates

version: "3.7"

services:

  redis:
    container_name: redis
    image: redis:5.0.10
    restart: unless-stopped
    network_mode: "host"
    volumes:
      - "/etc/timezone:/etc/timezone:ro"
    expose:
      - 6379

  # BACKEND:
  storage-2:
    container_name: storage-2
    image:   build2.auctionologies.com:5000/auctions/pmgroovy:20201027174839
    restart: unless-stopped
    network_mode: "host"
    env_file:
      - .env
    volumes:
      - ./pmgroovy/storage/db:/app/db
      - ./pmgroovy/storage/log:/app/log
      - "/etc/timezone:/etc/timezone:ro"
    depends_on:
      - redis

  # FRONTEND:
  fayeserver:
    container_name: bwp
    image: build2.auctionologies.com:5000/auctions/bwp:20201026175911
    restart: unless-stopped
    network_mode: "host"
    volumes:
      # mapped files need to be absolute!!:
      - ${PWD}/assets/images/tallgrass.png:/app/web/images/logo.png:ro
      #- ${PWD}/assets/images/tallgrass.png:/app/web/flash/assets/logo.png:ro
      - "/etc/timezone:/etc/timezone:ro"
    expose:
      - 9090
    depends_on:
      - redis

  # TODO: the production yml is basically the same as dev + nginx
  #       - so we could implement an 'override' yml file
  nginx:
    container_name: nginx
    image: nginx
    restart: unless-stopped
    network_mode: "host"
    expose:
      - 80
      - 443
    volumes:
      - ./nginx:/etc/nginx
      - "/etc/timezone:/etc/timezone:ro"

