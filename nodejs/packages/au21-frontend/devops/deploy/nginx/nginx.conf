worker_processes auto;

events {
    worker_connections 768;
    # multi_accept on;
}

http {


    server {
        listen 80 default_server; # do we need default_server?
        server_name *.auctionologies.com; # should we use _ or "" instead?

        location / {
            return 301 https://$host$request_uri;
        }
    }

    server {
        listen              443 ssl;
        server_name         *.auctionologies.com; # should we use _ or "" instead?
        keepalive_timeout   70;

        ssl_session_cache   shared:SSL:10m;
        ssl_session_timeout 10m;
        ssl_certificate     /etc/nginx/auctionologies.com.chained.crt;
        ssl_certificate_key /etc/nginx/auctionologies.com.key;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        ssl_ciphers "EECDH+ECDSA+AESGCM EECDH+aRSA+AESGCM EECDH+ECDSA+SHA384 EECDH+ECDSA+SHA256 EECDH+aRSA+SHA384 EECDH+aRSA+SHA256 EECDH+aRSA+RC4 EECDH EDH+aRSA RC4 !aNULL !eNULL !LOW !3DES !MD5 !EXP !PSK !SRP !DSS";

		#
        # Proxy settings
		#

        location / {
            proxy_pass http://localhost:9090/;
            proxy_redirect      off;
            proxy_set_header    Host              $host;
            proxy_set_header    X-Real-IP         $remote_addr;
            proxy_set_header    X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header    X-Forwarded-Proto $scheme;

            # WebSocket specific
            proxy_http_version 1.1;
            proxy_set_header    Upgrade           $http_upgrade;
            proxy_set_header    Connection        "upgrade";

            #
      		# Specific for comet or long running HTTP requests, don't buffer up the
            # response from origin servers but send them directly to the client.
            #

            proxy_buffering     off;

            #
      		# Bump the timeout's so someting sensible so our connections don't
            # disconnect automatically. We've set it to 12 hours.
            #

            proxy_connect_timeout 43200000;
            proxy_read_timeout    43200000;
            proxy_send_timeout    43200000;
        }

        location /Rofr.html {
            return 301 https://$host/flash/Rofr.html;
        }
    }
}


