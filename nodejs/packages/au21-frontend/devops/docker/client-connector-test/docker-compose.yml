# Notes:
# - either use:
#   a) network_mode: "host",and "depends_on", or
#   b) links:

version: '3.9'
services:

  redis:
    image: redis:6.2.1
    container_name: pubsub
    expose:
      - 6379

  client-connector-test:
    image: build2.auctionologies.com:5000/auctions/au21-frontend/client-connector-test:dev
    links:
      - redis
    ports:
      - '9090:9090'
    environment:
      - REDIS_URL=redis://pubsub
      - DIST_DIR=../client-connector-test
