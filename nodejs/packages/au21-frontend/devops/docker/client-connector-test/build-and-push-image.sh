#!/usr/bin/env sh
set -e
# MUST RUN FROM REPOSITORY ROOT!

# 1) Build fayeserver and client-connector-test
#    - Note: for charlize those will be different!
nx run-many --target=build --projects=fayeserver ##,client-connector-test

# 2) Build image
registry="build2.auctionologies.com:5000"
image="${registry}/auctions/au21-frontend/client-connector-test:dev"

echo $image

sudo docker build \
 -t "$image" \
 -f devops/docker/client-connector-test/Dockerfile .


docker login "$registry" -u=admin -p=$AU_DOCKER_PASSWORD

docker push "$image"

# OLD: for tagging:
# get timestamp for the tag
#timestamp=$(date +%Y%m%d%H%M%S)
#echo "timestamp=$timestamp"
#
#tag="$image:$timestamp"
#echo "tag=$tag"
#
#GIT_COMMIT=$(git rev-parse --short HEAD)
#echo "GIT_COMMIT=$GIT_COMMIT"
#
#GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
#echo "GIT_BRANCH=$GIT_BRANCH"
#
## sudo docker build --build-arg GIT_COMMIT=$GIT_COMMIT --build-arg GIT_BRANCH=$GIT_BRANCH --build-arg BUILD_DATE=$BUILD_DATE -t $tag .
#sudo docker build \
# --build-arg GIT_COMMIT=$GIT_COMMIT \
# --build-arg GIT_BRANCH=$GIT_BRANCH \
# --build-arg TIMESTAMP=$timestamp \
# -t "$tag" \
# -f devops/docker/client-connector-test.Dockerfile .
#
