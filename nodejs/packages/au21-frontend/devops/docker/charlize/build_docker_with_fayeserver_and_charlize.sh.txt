#!/bin/bash

TODO !!
NOT READY YET
exit 1

#git add .
#git commit -m "about to build and push dockerfile"
#git push

nx run-many --target=build --projects=fayeserver,charlize

registry="build2.auctionologies.com:5000"
image="${registry}/auctions/au21-frontend/client-connector-test:dev-3"


# specify the image name CHANGE for different projects !!
image="auctions/au21-frontend"

# get timestamp for the tag
timestamp=$(date +%Y%m%d%H%M%S)
echo "timestamp=$timestamp"

tag="$image:$timestamp"
echo "tag=$tag"

GIT_COMMIT=$(git rev-parse --short HEAD)
echo "GIT_COMMIT=$GIT_COMMIT"

GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
echo "GIT_BRANCH=$GIT_BRANCH"

# sudo docker build --build-arg GIT_COMMIT=$GIT_COMMIT --build-arg GIT_BRANCH=$GIT_BRANCH --build-arg BUILD_DATE=$BUILD_DATE -t $tag .
sudo docker build \
 --build-arg GIT_COMMIT=$GIT_COMMIT \
 --build-arg GIT_BRANCH=$GIT_BRANCH \
 --build-arg TIMESTAMP=$timestamp \
 -t $tag .


# push to dockerhub:
sudo docker login build2.auctionologies.com:5000 --username admin --password $DOCKER_PASSWORD

# push:
sudo docker push $image

# remove dangling images:
sudo docker system prune -f

