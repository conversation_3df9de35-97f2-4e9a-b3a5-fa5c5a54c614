<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="all vue-experiment tests" type="JavaScriptTestRunnerJest">
    <config-file value="$PROJECT_DIR$/apps/vue-experiments/jest.config.js"/>
    <node-interpreter value="project"/>
    <node-options value=""/>
    <jest-package value="$PROJECT_DIR$/node_modules/jest"/>
    <working-dir value="$PROJECT_DIR$"/>
    <envs/>
    <scope-kind value="ALL"/>
    <method v="2"/>
  </configuration>
</component>
