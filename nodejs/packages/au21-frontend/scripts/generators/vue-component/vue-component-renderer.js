const path = require('path');
const fs = require('fs');
const ejs = require('ejs');

// NOTE: component directory needs to exist first.
// SET THESE BEFORE RUNNING:

// const dir = 'apps/charlize/src/pages/De/DeTrader/header/children/auction-rules/cards'

const dir = 'apps/charlize/src/pages/De/DeAuctioneer/limits'
const component_name = { name: 'DeLimitsTable'};


// -------------------------
const output_dir = path.join(__dirname, '../../../'+dir)
const vue_component_file = path.join(output_dir, component_name.name + '.vue');
if(fs.existsSync(vue_component_file)) {
  throw new Error('Component exists: ' + vue_component_file)
}

const vue_component_demo_file = path.join(output_dir, component_name.name + '.demo.vue');
if(fs.existsSync(vue_component_demo_file)) {
  throw new Error('Demo exists: ' + vue_component_demo_file)
}

const vue_component_template = path.join(__dirname, 'vue-component.ejs');
const vue_component_demo_template = path.join(__dirname, 'vue-component.demo.ejs');


function render(template, filename) {
  ejs.renderFile(template, component_name, (err, str) => {
    if (err) {
      console.log(err);
    } else {
      console.log(str);
      fs.writeFileSync(filename, str);
    }
  });
}

render(vue_component_template, vue_component_file);
render(vue_component_demo_template, vue_component_demo_file);


