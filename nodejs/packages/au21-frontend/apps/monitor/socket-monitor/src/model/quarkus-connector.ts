import {uuid} from '@au21-frontend/utils';
import {EngineTransaction} from '@au21-frontend/client-connector';

const config = {
  server: "",
  session_id: uuid()
}

export function setServer(_server) {
  config.server = _server
}

export function subscribe_quarkus(data_cb: (data: EngineTransaction) => void,
                                  connection_cb: (isConnected: boolean) => void) {

  // from:
  // - https://stackoverflow.com/questions/22431751/websocket-how-to-automatically-reconnect-after-it-dies

  const ws = new WebSocket(`ws://${config.server}/monitor/${config.session_id}`);

  ws.onopen = () => {
    connection_cb(true);
  };

  ws.onmessage = (e: MessageEvent) => {
    const data: EngineTransaction = JSON.parse(e.data) as EngineTransaction;
    // console.log(JSON.stringify(data, null, 2))
    data_cb(data);
  };


  ws.onclose = function (e: CloseEvent) {
    connection_cb(false);
    console.log('Socket is closed. Reconnect will be attempted in 1 second.', e.reason);
    setTimeout(function () {
      subscribe_quarkus(data_cb, connection_cb);
    }, 1000);
  };

  ws.onerror = function (err: Event) {
    console.error('Socket encountered error: ', {err}, 'Closing socket');
    ws.close();
  };
}


