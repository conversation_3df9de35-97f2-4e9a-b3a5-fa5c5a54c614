<template>
  <div id="app">
    <!--    <QuaruksDiffViewer/>-->
    <NestJSDiffViewer/>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import VueJsonPretty from 'vue-json-pretty';
import 'vue-json-pretty/lib/styles.css';
import QuaruksDiffViewer from './components/quarkus/QuaruksDiffViewer.vue';
import NestJSDiffViewer from './components/nestjs/NestJSDiffViewer.vue';
/*
 * Note VueJsonViewer seems less configurable
 */
@Component({
  components: {
    NestJSDiffViewer,
    QuaruksDiffViewer,
    VueJsonPretty
  }
})
export default class App extends Vue {

}
</script>

<style lang="less" scoped>

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

table {
  width: 100%;
}

table, tr, td {
  border: 1px solid black;
}

td {
  padding: 5px;
  text-align: left;
  vertical-align: top;
}

.pad_width {
  padding: 0 10px;
}

.heading {
  font-size: 12px;
  font-weight: bold;
}
</style>
