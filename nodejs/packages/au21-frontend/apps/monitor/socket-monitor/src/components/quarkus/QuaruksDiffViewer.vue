<template>
  <div id="app">
    <div class="heading" style="text-align: left">
      <span class="pad_width"/>
      <span>Server:</span>
      <span class="pad_width"/>
      <select v-model="server">
        <option>{{ local_url }}</option>
        <option>{{ remote_url }}</option>
      </select>
      <span class="pad_width"/>
      <span :style="{backgroundColor: isConnected ? 'green' : 'red', color: 'white'}">
        {{ isConnected ? "connected" : "disconnected" }}
      </span>
      <span class="pad_width"/>
      <span>Selected command:</span>
      <span class="pad_width"/>
      <span>{{ selected_envelope.engine_command_name }}</span>
      <span class="pad_width"></span>
      <span>{{ client_command_size }}</span>
    </div>
    <table>
      <tr>
        <td>
          <div
            v-for="(envelope, index) in envelopes"
            style="font-size: 12px"
            @click="showEngineCommand(index)"
          >
            <div style="display: inline-block; font-weight: bold">
              {{ envelope.engine_command_name }}
            </div>
            <div>
              <div>
                <div style="display:inline-block; float: right; color: gray">
                  <div
                    v-if="envelope.has_alert"
                    style="display: inline-block; color: indianred"
                  >
                    &nbsp; alert
                  </div>
                  <div
                    v-if="envelope.has_session_differ_error"
                    style="display: inline-block; color: indianred"
                  >
                    &nbsp; diff
                  </div>
                  <div style="display: inline-block; font-size: 12px; font-weight: bold">
                    &nbsp; {{ envelope.duration_ms }} ms
                  </div>
                </div>
              </div>
            </div>
            <hr>
          </div>
        </td>
        <td style="width: 300px">
          <div class="heading">
            {{ selected_envelope.engine_command_name }}
            &nbsp; &nbsp;
            {{ from(selected_envelope) }}
          </div>
          <hr>
          <VueJsonPretty :data="selected_engine_command"/>
        </td>
        <td>
          <div>
            <table style="border-collapse: collapse">
              <tr v-for="command_map in selected_envelope.client_command_maps">
                <td>
                  <div class="heading">
                    <div style="display: inline-block; width: 100px">{{ command_map.command.type }}</div>
                    <div style="display: inline-block;">{{ command_map.command.path }}</div>
                  </div>
                  <hr/>
                  <VueJsonPretty :data="command_map.command"/>
                </td>
                <td>
                  <div class="heading">Target sessions</div>
                  <hr>
                  <div
                    v-for="sid in command_map.sessionIds"
                    style="font-size: 12px"
                  >{{ to_username(sid) }}
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import {Component, Vue, Watch} from 'vue-property-decorator';
import {setServer, subscribe_quarkus} from '../../model/quarkus-connector';
import VueJsonPretty from 'vue-json-pretty';
import 'vue-json-pretty/lib/styles.css';
import {EngineTransaction} from '@au21-frontend/client-connector';
/*
 * Note VueJsonViewer seems less configurable
 */
@Component({
  components: {
    VueJsonPretty
  }
})
export default class QuaruksDiffViewer extends Vue {

  local_url = `localhost:4040`;
  remote_url = 'dev1.auctionologies.com:4040';
  /* SWITCH HERE */
  server = this.local_url;

  envelopes: EngineTransaction[] = [];
  selected_envelope: EngineTransaction | {} = {};

  get selected_engine_command() {
    const json = (this.selected_envelope as EngineTransaction).engine_command_json;
    return json ? JSON.parse(json) : {};
  }

  isConnected = false;

  session_id_username_map = new Map<string, string>();

  to_username(sid): string {
    return this.session_id_username_map.get(sid) || sid;
  }

  from(envelope: EngineTransaction): string {
    if (!envelope)
      return '';
    else if (envelope.isHeartbeat)
      return 'from: heartbeat';
    else
      return 'from: ' + this.to_username(envelope.session_id);
  }

  get client_command_size() {
    const cmds = this.selected_envelope['client_command_maps']?.length;
    return cmds ? cmds + ' client commands' : '';
  }

  @Watch('server', {immediate: true})
  onServerSelected(_server, old) {
    setServer(_server);
    subscribe_quarkus(
      (data: EngineTransaction) => {
        this.envelopes.push(data);
        this.selected_envelope = data;

        // session : username map
        if (data.engine_command_name === 'LoginCommand') {
          const session_id = data.session_id;
          const username = JSON.parse(data.engine_command_json).username;
          this.session_id_username_map.set(session_id, username);
        }
      },
      (is_connected) => this.isConnected = is_connected
    );
  }

  showEngineCommand(index) {
    this.selected_envelope = this.envelopes[index];
  }

}
</script>

<style lang="less" scoped>

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

table {
  width: 100%;
}

table, tr, td {
  border: 1px solid black;
}

td {
  padding: 5px;
  text-align: left;
  vertical-align: top;
}

.pad_width {
  padding: 0 10px;
}

.heading {
  font-size: 12px;
  font-weight: bold;
}
</style>
