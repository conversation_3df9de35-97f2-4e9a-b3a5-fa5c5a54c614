import Vue from 'vue';
import App from './App.vue';

Vue.config.productionTip = false;

const app = new Vue({
  render: (h) => h(App)
});

app.$mount('#app');

// composition api, for use with Vuese Motion:
//import App from "./App.vue";
//import { MotionPlugin } from '@vueuse/motion'
//import createApp from "@vue/composition-api";
//const app = createApp(App)
//app.use(MotionPlugin)
//app.mount('#app')

