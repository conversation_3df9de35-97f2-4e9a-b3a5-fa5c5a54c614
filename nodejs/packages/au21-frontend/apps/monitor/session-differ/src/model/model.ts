export class SessionClientStoreDiffs {
  auction_notice_diff = false;
  auction_rows_diff = false;
  companies_diff = false;
  counterparty_credits_diff = false;
  de_auctioneer_info_diff = false;
  de_matrix_edges_diff = false;
  de_matirx_nodes_diff = false;
  de_round_traders_diff = false;
  de_rounds_diff = false;
  de_settings_diff = false;
  de_status_diff = false;
  de_trader_history_rows_diff = false;
  de_traders_diff = false;
  messages_diff = false;
  session_user_diff = false;
  users_diff = false;

  #hasDiffs = false;
  #store_from_db = {};
  #store_from_updates = {};

  diffs = [];

  diff_str(): string {
    return '[' + this.diffs.join(', ') + ']';
  }

  calculate(hasDiffs: boolean, store_from_db: {}, store_from_updates: {}) {
    this.diffs = [];
    this.#hasDiffs = hasDiffs;
    this.#store_from_db = store_from_db;
    this.#store_from_updates = store_from_updates;

    this.auction_notice_diff = this._compare('auction_notice');
    this.auction_rows_diff = this._compare('auction_rows');
    this.companies_diff = this._compare('companies');
    this.counterparty_credits_diff = this._compare('counterparty_credits');
    this.de_auctioneer_info_diff = this._compare('de_auctioneer_info');
    this.de_matrix_edges_diff = this._compare('de_matrix_edges');
    this.de_matirx_nodes_diff = this._compare('de_matrix_nodes');
    this.de_round_traders_diff = this._compare('round_traders');
    this.de_rounds_diff = this._compare('de_rounds');
    this.de_settings_diff = this._compare('de_settings');
    this.de_status_diff = this._compare('de_status');
    this.de_trader_history_rows_diff = this._compare('de_trader_history_rows');
    this.de_traders_diff = this._compare('de_traders');
    this.messages_diff = this._compare('messages');
    this.session_user_diff = this._compare('session_user');
    this.users_diff = this._compare('users');
  }

  _compare(property): boolean {
    if (this.#hasDiffs === false) {
      return false;
    } else {
      const has_diff = JSON.stringify(this.#store_from_db[property]) !=
        JSON.stringify(this.#store_from_updates[property]);
      if (has_diff) {
        this.diffs.push(property);
        // console.log(property + ' has diff');
      }
      return has_diff;
    }
  }

}





















