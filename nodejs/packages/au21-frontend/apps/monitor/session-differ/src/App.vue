<template>
  <div id="app">
    <div v-if="hasDiffs" style="text-align: left; color:red">
      <div style="display:inline-block; width:150px; font-size: 18px; color: red">HAS DIFFS</div>
      <div style="display: inline-block">{{ diffs.diff_str() }}</div>
    </div>
    <div style="text-align: left;">
      <select v-model="server">
        <option>{{ local_url }}</option>
        <option>{{ remote_url }}</option>
      </select>
      <button @click="compare">compare</button>
      <div style="display:inline-block; width: 40px">&nbsp;</div>
      <span>
        <span>table:</span>
        <input type="radio" v-model="table_or_json" value="table">
        <span>json:</span>
        <input type="radio" v-model="table_or_json" value="json">
      </span>
      <div style="display:inline-block; width: 40px">&nbsp;</div>
      <span v-if="table_or_json === 'json'">
        <span>Expanded:</span>
        <input type="radio" v-model="expanded" value="yes">
        <span>yes</span>
        <input type="radio" v-model="expanded" value="no">
        <span>no</span>
      </span>
    </div>
    <table>
      <tr>
        <td>
          <div class="col_title" :style="{width:table_width}">store from database</div>
        </td>
        <td>
          <div class="col_title" :style="{width:table_width}">store from updates</div>
        </td>
      </tr>
    </table>
    <div v-if="table_or_json === 'table'">
      <ItemRow
        name="Auction Notice"
        item-prop="auction_notice"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="Auction Rows"
        item-prop="auction_rows"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="Companies"
        item-prop="companies"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="Counterparty Credits"
        item-prop="counterparty_credits"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Auctioneer Info"
        itemProp="de_auctioneer_info"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Matrix Edges"
        itemProp="de_matrix_edges"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Matrix Nodes"
        itemProp="de_matrix_nodes"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Round Traders"
        itemProp="de_round_traders"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Rounds"
        itemProp="de_rounds"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Settings"
        itemProp="de_settings"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Status"
        itemProp="de_status"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Trader History Rows"
        itemProp="de_trader_history_rows"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="De Traders"
        itemProp="de_traders"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="Messages"
        itemProp="messages"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="Session User"
        itemProp="session_user"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
      <ItemRow
        name="Users"
        itemProp="users"
        :store_from_updates="store_from_updates"
        :store_from_db="store_from_db"
        @showDiff="showDiff"
      />
    </div>
    <table v-if="table_or_json === 'json'">
      <tr>
        <td>
          <VueJsonViewer
            v-if="table_or_json==='json'"
            style="text-align: left;"
            :value="store_from_db"
            boxed
            sort
            :expand-depth="expand_depth"
            :expanded="true"
            :style="{width: `${table_width}`}"
          ></VueJsonViewer>
        </td>
        <td>
          <VueJsonViewer
            v-if="table_or_json==='json'"
            style="text-align: left;"
            :value="store_from_updates"
            boxed
            sort
            :expand-depth="expand_depth"
            :expanded="true"
            :style="{width: `${table_width}`}"
          ></VueJsonViewer>
        </td>
      </tr>
    </table>
    <Modal
      v-model="showModal"
      :title="modal_title"
      :modal-style="{maxWidth: modal_outer_width, width: modal_outer_width}"
    >
      <VueJsonCompare :oldData="oldData" :newData="newData" :style="{width: modal_inner_width}"/>
    </Modal>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import VueJsonPretty from 'vue-json-pretty';
import VueJsonViewer from 'vue-json-viewer';
import {JSONView} from 'vue-json-component';
import superagent from 'superagent';
import {vueWindowSizeMixin} from 'vue-window-size';
import {SessionClientStoreDiffs} from './model/model';
import 'vue-json-pretty/lib/styles.css';
import VueModal from '@kouts/vue-modal';
import '@kouts/vue-modal/dist/vue-modal.css';
import VueJsonCompare from 'vue-json-compare';
import ItemRow from './components/ItemRow.vue';


Vue.component('Modal', VueModal);

@Component({
  components: {
    ItemRow,
    VueJsonComponent: JSONView,
    VueJsonCompare,
    VueJsonPretty,
    VueJsonViewer
  }
})
export default class App extends Vue {

  local_url = `http://localhost:4040`;
  remote_url = 'http://dev1.auctionologies.com:4040';
  server = this.local_url;

  store_from_db = {};
  store_from_updates = {};

  table_or_json = 'table';
  expanded = 'no';

  hasDiffs = false;
  diffs = new SessionClientStoreDiffs();

  showModal = false;
  modal_title = '';
  oldData = '';
  newData = '';

  get expand_depth() {
    const depth = this.expanded === 'yes' ? 3 : 1;
    // HUGE Hack, to force the reactivity
    setTimeout(() => {
      if (this.table_or_json === 'json')
        this.table_or_json = 'table';
      setTimeout(() => {
        this.table_or_json = 'json';
      }, 1);
    }, 1);
    return depth;
  }

  get table_width(): string {
    return (vueWindowSizeMixin.computed.windowWidth() / 2 - 20) + 'px';
  }

  get modal_inner_width(): string {
    return (vueWindowSizeMixin.computed.windowWidth() * 0.6 - 40) + 'px';
  }

  get modal_outer_width(): string {
    return (vueWindowSizeMixin.computed.windowWidth() * 0.6 + 'px');
  }

  mounted() {
    this.compare();
  }

  async compare() {
    try {

      const store_from_db_json = (await superagent.get(this.server + '/last-diff/from-db')).text;
      this.store_from_db = JSON.parse(store_from_db_json);

      const store_from_updates_json = (await superagent.get(this.server + '/last-diff/from-updates')).text;
      this.store_from_updates = JSON.parse(store_from_updates_json);

      this.hasDiffs = store_from_db_json != store_from_updates_json;

      this.diffs = new SessionClientStoreDiffs();
      this.diffs.calculate(this.hasDiffs, this.store_from_db, this.store_from_updates);

      console.log({
        from_db: this.store_from_db,
        from_updates: this.store_from_updates
      });

    } catch (e) {
      console.log(e);
    }
  }

  showDiff(itemProp) {
    console.log('parent showdiff:', itemProp);
    this.modal_title = itemProp;
    this.oldData = this.store_from_db[itemProp];
    this.newData = this.store_from_updates[itemProp];
    this.showModal = true;
  }

}
</script>

<style lang="less" scoped>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

.main-table {
  width: 100%;
}

table, tr, td {
  vertical-align: top;
}

.col_title {
  font-size: 18px;
  font-weight: bold;
}

.modal-class {
  border: 1px solid red;
}
</style>
