<template>
  <div>
    <div
      style="background-color: #ccc; padding: 5px; margin-top: 20px; color:black; font-weight: bold; text-align: left"
    >
      <div v-if="hasDiff" class="showDiff">
        <div style="display:inline-block; width: 200px">{{ name }}</div>
        <button @click="showDiff">show diff</button>
      </div>
      <div v-else style="display: inline-block; width: 200px;">
        {{ name }}
      </div>
    </div>
    <table>
      <tr>
        <td>
          <ItemTable v-if="isTable" :items="db_entry" :width="table_width"/>
          <ItemNode v-else :item="db_entry" :width="table_width"/>
        </td>
        <td style="background-color: #c3c3c3; width: 5px">

        </td>
        <td>
          <ItemTable v-if="isTable" :items="update_entry" :width="table_width"/>
          <ItemNode v-else :item="update_entry" :width="table_width"/>
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {isArray} from 'lodash';
import ItemTable from './ItemTable.vue';
import {vueWindowSizeMixin} from 'vue-window-size';
import ItemNode from './ItemNode.vue';

@Component({
  components: {ItemNode, ItemTable}
})
export default class ItemRow extends Vue {
  @Prop({required: true}) store_from_db: {};
  @Prop({required: true}) store_from_updates: {};
  @Prop({required: true}) name: string;
  @Prop({required: true}) itemProp: string;

  get db_entry() {
    return this.store_from_db[this.itemProp];
  }

  get update_entry() {
    return this.store_from_updates[this.itemProp];
  }

  get table_width(): string {
    return (vueWindowSizeMixin.computed.windowWidth() / 2 - 20) + 'px';
  }

  get isTable() {
    return isArray(this.update_entry || isArray(this.db_entry));
  }

  get hasDiff(): boolean {
    const has_diff = JSON.stringify(this.db_entry) != JSON.stringify(this.update_entry);
    if (has_diff)
      console.log('has diff:', this.itemProp);
    return has_diff;
  }

  showDiff() {
    this.$emit('showDiff', this.itemProp);
  }
}
</script>

<style scoped>
.showDiff {
  background-color: #af0505;
  color: white;
}
</style>
