<template>
  <div :style="{width: width}">
    <ul style="margin: 0; text-align: left">
      <li v-for="(value, propname) in item">
        {{ propname }}: {{ value }}
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({
  components: {}
})
export default class ItemNode extends Vue {
  @Prop({required: true}) item: {};
  @Prop({required: true}) width: string;

}
</script>

<style scoped>
</style>
