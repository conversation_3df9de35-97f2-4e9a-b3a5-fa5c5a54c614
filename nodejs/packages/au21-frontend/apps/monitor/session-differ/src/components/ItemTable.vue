<template>
  <div class="main" :style="{width: `${width}`, overflowX:'scroll'}">
    <table class="outer">
      <thead>
      <tr>
        <th v-for="(col) in columns">{{ col }}</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="item in items">
        <td v-for="name in columns">{{ item[name] }}</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';

@Component
export default class ItemTable extends Vue {
  @Prop({required: true}) items: {}[];
  @Prop({required: true}) width: string;

  //
  get columns(): string[] {
    if (this.items && this.items.length > 0)
      return Object.keys(this.items[0]).sort();
    return [];
  }
}
</script>

<style scoped>

.main {
  max-height: 400px;
  overflow-y: scroll;
}

table {
  border-collapse: collapse;
}

th, td {
  font-size: 13px;
  text-align: left;
  vertical-align: top;
  border: 1px solid #000;
  border-spacing: 0;
  padding: 0 4px;
}

</style>
