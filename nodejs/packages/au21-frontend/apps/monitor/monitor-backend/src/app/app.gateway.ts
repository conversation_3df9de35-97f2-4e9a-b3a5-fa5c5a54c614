//  https://github.com/nestjs/nest/tree/master/sample/16-gateways-ws


import {SubscribeMessage, WebSocketGateway, WebSocketServer, WsResponse} from '@nestjs/websockets';
import {from, Observable} from 'rxjs';
import {map} from 'rxjs/operators';
import {Server} from 'ws';
import {AppService} from './app.service';
import {DocumentChange} from 'ravendb';
import {CommandDiff} from '../model/models';

@WebSocketGateway(8100)
export class AppGateway {

  constructor(private readonly appService: AppService) {
  }

  @WebSocketServer()
  server: Server;

  @SubscribeMessage('events')
  onEvent(client: WebSocket, data: any): Observable<WsResponse<number>> {
    //   this.clients.set(this.clients.size + 1, client);
    //   console.log({ client: this.clients.size, data });
    return from([1, 2, 3]).pipe(map(item => ({event: 'events', data: item})));
  }

  @SubscribeMessage('connect')
  onConnect(client: WebSocket, data: { session_id: string }) {
    this.appService.getStore()
      // TODO: a bit inefficient like this, as one of these is created per client!
      .changes()
      .forDocumentsInCollection('CommandDiffs')
      .on('data', async (change: DocumentChange) => {
        if (change.type == 'Put') {
          const command_diff = await this.appService.getStore().openSession().load(change.id) as CommandDiff | null;
          if (command_diff != null) {
            delete command_diff['@metadata'];
            client.send(JSON.stringify(command_diff));
          }
        }
      });
  }

}
