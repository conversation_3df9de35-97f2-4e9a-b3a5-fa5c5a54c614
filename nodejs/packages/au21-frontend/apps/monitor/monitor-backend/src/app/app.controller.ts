import {Controller, Get, Param} from '@nestjs/common';

import {AppService} from './app.service';

type SessionDiffSummary = {
  command_id: string
  session_id: string,
  isDifferent: true
}

@Controller('sessions')
export class AppController {
  constructor(private readonly appService: AppService) {
  }


  @Get('commands') // NB: need to remember to encode the parameter on the sender side for the '/' eg: CommandDiffs/2-A -> CommandDiffs%2F2-A
  async allCommands() {
    return (await this.appService.getStore()
      .openSession()
      .query({collection: 'CommandDiffs'})
      .all())
      .map(it => {
        delete it['@metadata'];
        return it;
      });
  }

  @Get('sessions_for_command/:command_id') // NB: need to remember to encode the parameter on the sender side for the '/' eg: CommandDiffs/2-A -> CommandDiffs%2F2-A
  async sessions_for_command(@Param('command_id') command_id: string) {
    console.log({command_id});
    return await this.appService.getStore()
      .openSession()
      .query({collection: 'SessionDiffs'})
      .whereEquals('command_id', command_id)
      .all();
  }

  // NB: need to remember to encode the parameter on the sender side for the '/' eg: CommandDiffs/2-A -> CommandDiffs%2F2-A
  @Get('session_summaries_for_command/:command_id')
  async session_summaries_for_command(
    @Param('command_id') command_id: string
  ): Promise<Array<SessionDiffSummary>> {
    console.log({command_id});
    return (await this.appService.getStore()
        .openSession()
        .query({collection: 'SessionDiffs'})
        .whereEquals('command_id', command_id)
        .selectFields(['command_id', 'session_id', 'isDifferent'])
        .all()
    ).map(it => {
      delete it['__PROJECTED_NESTED_OBJECT_TYPES__'];
      return it as SessionDiffSummary;
    });
  }

  @Get('sessions/:command_id') // NB: need to remember to encode the parameter on the sender side for the '/' eg: CommandDiffs/2-A -> CommandDiffs%2F2-A
  async command_sessions_different(@Param('command_id') command_id: string) {
    console.log({command_id});
    return (await this.appService.getStore()
        .openSession()
        .query({collection: 'SessionDiffs'})
        .whereEquals('command_id', command_id)
        .andAlso()
        .whereEquals('isDifferent', true)
        .selectFields(['command_id', 'session_id', 'isDifferent'])
        .all()
    ).map(it => {
      delete it['__PROJECTED_NESTED_OBJECT_TYPES__'];
      return it;
    });
  }

  @Get('commands_different') // NB: need to remember to encode the parameter on the sender side for the '/' eg: CommandDiffs/2-A -> CommandDiffs%2F2-A
  async commands_different() {
    return (await this.appService.getStore()
        .openSession()
        .query({collection: 'CommandDiffs'})
        .whereEquals('isDifferent', true)
        .all()
    ).map(it => {
      delete it['__PROJECTED_NESTED_OBJECT_TYPES__'];
      return it;
    });
  }

  @Get('sessions_different') // NB: need to remember to encode the parameter on the sender side for the '/' eg: CommandDiffs/2-A -> CommandDiffs%2F2-A
  async sessions_different() {
    return (await this.appService.getStore()
        .openSession()
        .query({collection: 'SessionDiffs'})
        .whereEquals('isDifferent', true)
        .selectFields(['command_id', 'session_id', 'isDifferent'])
        .all()
    ).map(it => {
      delete it['__PROJECTED_NESTED_OBJECT_TYPES__'];
      return it;
    });
  }


}
