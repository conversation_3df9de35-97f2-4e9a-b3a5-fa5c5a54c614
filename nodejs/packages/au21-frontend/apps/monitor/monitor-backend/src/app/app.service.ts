import {Injectable} from '@nestjs/common';
import {DocumentStore} from 'ravendb';

@Injectable()
export class AppService {

  private store = new DocumentStore('http://build2.auctionologies.com:8083', 'sessions');

  constructor() {
    this.store.initialize();
  }

  getData(): { message: string } {
    return {message: 'Welcome to monitor/monitor-backend!'};
  }

  getStore(): DocumentStore {
    return this.store
  }


}
