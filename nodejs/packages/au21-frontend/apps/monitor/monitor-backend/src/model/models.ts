export class CommandDiff {
  id: string;
  name: string;
  from_session: string;
  command_params: string;
  //   sessions: List<SessionDiff> = mutableListOf(),
  isDifferent: boolean;
  timestamp: Date;
}

export class SessionDiff {
  id: string;
  //  val diff_node: DiffNode
  session_id: string;
  store_from_db: {};
  store_from_updates: {};
  isDifferent: boolean;
  json_from_db: string;
  json_from_updates: string;
  command_id: string;
  command_name: string;
}
