June 30, 2021:

- adding NestJS backend
  - because dealing with RavenDB from Kotlin is too painful
- goals will be:
  - merge:
    - socket-monitor, and
    - session-differ
  - ability to pinpoint session diff errors easily
  - ability to measure socket and other performance

-- OLD --

Goals

(1) initially this will monitor the redis bus like:

- au-2019-bwp-redis-monitor project

(2) Next we'll use this to store messages to a RavenDB database, instead of using Papertrail

ie:

- we'll decouple obs (logs, metrics, traces)
  from audit data.

NOTE:

- monitor-redis-server could probably be replaced by:
  - using Fayeserver
  - and some secure way of subscribing to every message on redis
