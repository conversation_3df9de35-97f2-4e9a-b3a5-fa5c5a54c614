
# Note these override the npm script environment variables !
# REMOVED THESE, THE package.json script will set app mode
# - and then configure-webpack.js will use the .app.env or .book.env file, which sets those variables
# - not sure why we are doing that actually?
#APP_MODE=app
#APP_MODE=book
VUE_APP_SHOW_DEBUG=true
SHOW_CONNECTOR_LOG=true

WEBSOCKET_URL='ws://localhost:4040/socket'
#WEBSOCKET_URL='ws://dev1.auctionologies.com:4040/socket'

VUE_APP_SHOW_DEBUG="true"
