// Think of this file as a collection of computeds.
// You can use `process.env` and argv (cli arguments) as sources.

const argv = require('yargs').argv
const {snakeCase, toUpper} = require('lodash')

/**
 * @param name {string}
 * @param defaultValue {string}
 */
const getOptionValue = (name, defaultValue) => {
  const envNameCaps = toUpper(snakeCase(name))
  if (envNameCaps in process.env) {
    return process.env[envNameCaps]
  }
  if (name in argv) {
    return argv[name]
  }
  return defaultValue
}

/**
 * based on `name` param generates env variable and cli argument.
 * So, for 'app-mode', we'll get:
 * * `APP_MODE` - env variable - highest priority if provided
 * * `--app-mode` - cli argument
 *
 *
 *
 * @param name {string} name - option name
 * @param defaultValue {string|undefined} last priority, you can apply custom logic here
 */
const deriveOption = (name, defaultValue) => {
  const value = getOptionValue(name, defaultValue)
  if (value === 'false') {
    return false
  }
  if (value === 'true') {
    return true
  }
}

/**
 * @type {boolean}
 */
const SHOW_CONNECTOR_LOG = deriveOption('show-connector-log', true)
/**
 * @type {boolean}
 */
const WEBSOCKET_URL = deriveOption('websocket-url', undefined)

module.exports = {
  SHOW_CONNECTOR_LOG,
  WEBSOCKET_URL,
}
