import {AuClient, AuClientEvent} from '@au21-frontend/client-connector';
import {OnlyInstantiableByContainer, Singleton} from 'typescript-ioc';

type AuClientCallback<EVENT extends keyof AuClientEvent> = (event: AuClientEvent[EVENT]) => void

type CharlizeClientSubscription<EVENT extends keyof AuClientEvent> = {
  eventName: AuClientEvent[EVENT]['command'],
  callback: AuClientCallback<EVENT>
}

@Singleton
@OnlyInstantiableByContainer
export class CharlizeClient extends AuClient {
  subscriptions: CharlizeClientSubscription<any>[] = []
  subs = new Map<any, AuClientCallback<any>>()

  subscribe<EVENT extends keyof AuClientEvent>(eventName: EVENT, callback: AuClientCallback<EVENT>): void {
    this.subscriptions.push({
      eventName,
      callback,
    })
  }

  unsubscribe<EVENT extends keyof AuClientEvent>(eventName: EVENT, callback: AuClientCallback<EVENT>): void {
    const currentSubscription = this.findSubscription(eventName, callback)
    this.subscriptions = this.subscriptions.filter(subscription => subscription !== currentSubscription)
  }

  private findSubscription<EVENT extends keyof AuClientEvent>(eventName: EVENT, callback: AuClientCallback<EVENT>): CharlizeClientSubscription<EVENT> | null {
    return this.subscriptions.find(
      subscription => eventName === subscription.eventName && callback === subscription.callback,
    ) || null
  }

  onEvent<EVENT extends keyof AuClientEvent>(eventName: EVENT, event: AuClientEvent[EVENT]) {
    const relevantSubscriptions = this.subscriptions.filter(subscription => (eventName === subscription.eventName))
    relevantSubscriptions.forEach(subscription => subscription.callback(event))
  }
}
