import Vue from 'vue';
import CharlizeBus from '../CharlizeBus';

Vue.config.devtools = false
Vue.config.productionTip = false

fdescribe('CharlizeBus', () => {
  it('subscribes and unsubscribes', async() => {
    const bus = new CharlizeBus()
    const callback = jest.fn()
    bus.$on('event', callback)
    bus.$emit('event')
    expect(callback.mock.calls[0][0]).toBeUndefined()
    bus.$emit('event', 'value')
    expect(callback.mock.calls[1][0]).toBe('value')
    bus.$off('event', callback)
    bus.$emit('event', 'value')
    expect(callback.mock.calls[2]).toBeUndefined()
  })
  it('history mode', async() => {
    const bus = new CharlizeBus()

    bus.$emit('event')
    expect(bus.eventLog).toHaveLength(0)

    bus.loggingEnabled = true
    bus.$emit('event')
    expect(bus.eventLog).toHaveLength(1)
  })
})

