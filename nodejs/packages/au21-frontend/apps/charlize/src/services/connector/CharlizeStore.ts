import {LiveClientStore, StaleClientStore} from '@au21-frontend/client-connector';
import {OnlyInstantiableByContainer, Singleton} from 'typescript-ioc';

/*
    Note: we implement the local store as a subclass of the remote store.
 */

@Singleton
@OnlyInstantiableByContainer
export class CharlizeStore {

  live_store = new LiveClientStore(); // this hand latest round info, and gets patched every second
  stale_store = new StaleClientStore(); // Reset rounds and add rounds

  constructor() {
    // super();
    console.log('CharlizeStore constructed');
  }

}

