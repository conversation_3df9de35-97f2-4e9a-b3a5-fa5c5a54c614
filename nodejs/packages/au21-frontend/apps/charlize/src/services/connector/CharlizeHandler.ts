import {Container, OnlyInstantiableByContainer, Singleton} from 'typescript-ioc';
import {CharlizeStore} from './CharlizeStore';
import {patchLiveStore} from './stare-patch-strategies';
import {CharlizeClient} from './CharlizeClient';
import {
  AddElements,
  ClientCommand,
  ClientCommandHandler,
  ClientCommandType,
  CommandSucceeded,
  DeMatrixRoundElement,
  NetworkDown,
  NetworkUp,
  SetLiveStore,
  ShowMessage,
  TerminateSession
} from '@au21-frontend/client-connector';

@Singleton
@OnlyInstantiableByContainer
export class CharlizeHandler extends ClientCommandHandler {

  client = Container.get(CharlizeClient);
  store = Container.get(CharlizeStore);

  showLog: boolean = ['true', undefined].includes(process.env.SHOW_CONNECTOR_LOG);


  private readonly handlers: { [c in ClientCommandType]: (c) => any } = {
    CommandSucceeded: (c: CommandSucceeded) => {
      this.client.onEvent('CommandSucceeded', c);
    },
    ShowMessage: (c: ShowMessage) => {
      this.client.onEvent('ShowMessage', c);
    },
    TerminateSession: (c: TerminateSession) => {
      this.client.onEvent('TerminateSession', c);
    },
    NetworkDown: (c: NetworkDown) => {
    },
    NetworkUp: (c: NetworkUp) => {
    },
    SetLiveStore: (c: SetLiveStore) => {

      //window.requestAnimationFrame( () => {

      //  patch_store_strategy_1_replace_all_by_key(this.store, c.store)
      //  patch_store_strategy_2_jsondffpatcher(this.store, c.store)
      patchLiveStore(this.store.live_store, c.store);

    },
    AddElements: (c: AddElements) => {
      //console.log("AddElements", {c})
      const stale_store = this.store.stale_store;
      if (c.path === 'DeMatrixRoundElement') {
        if (c.elements) {
          const elems = c.elements as DeMatrixRoundElement[];
          // 1) filter out the incoming rounds:
          const new_round_numbers = elems.map((mr: DeMatrixRoundElement) => mr.round_number);
          stale_store.stale_de_matrix_rounds =
            stale_store.stale_de_matrix_rounds.filter(mr => !new_round_numbers.includes(mr.round_number));
          // 2) then add all the incoming rounds:
          elems.forEach(mr => stale_store.stale_de_matrix_rounds.push(mr));
        } else {
          // if null then clear
          this.store.stale_store.stale_de_matrix_rounds = [];
        }
      }

      //   if(c.element_class == )
      ///    this.store.stale_store.stale_de_matrix_rounds
    }
  };

  override handle(c: ClientCommand) {
    const m = this.handlers[c.command];
    if (!m) {
      alert('Unable to find handler for command: ' + c.command);
    } else {
      m(c);
    }

  }

}

//  handlers: { [e in ClientCommandType]: (m: ClientCommand) => void } = {

//
// // Browser Commands:
// // ShowMessage: (c: ShowMessage) =>
// //   this.client.showMessage(c.message, c.browser_message_kind),
// //
// // TerminateSession: (c: TerminateSession) =>
// //   this.client.TerminateSession(c.message),
// //
// // CommandSucceeded: (c: CommandSucceeded) =>
// //   this.client.CommandSucceeded(),
//
// CommandSucceeded: (c: CommandSucceeded) =>
//   this.client.onEvent('CommandSucceeded', c),
//
// ShowMessage: (c: ShowMessage) =>
//   this.client.onEvent('ShowMessage', c),
//
// TerminateSession: (c: TerminateSession) =>
//   this.client.onEvent('TerminateSession', c),
// /**
//  * Note: NetworkDown and NetworkUp won't be called from server
//  * - client side connector only
//  */
//
// NetworkDown: (c: NetworkDown) =>
//   this.client.onEvent('NetworkDown', c),
//
// NetworkUp: (c: NetworkUp) =>
//   this.client.onEvent('NetworkUp', c),
//
// ClearStore: (c: ClearStore) => {
//   store.reset();
// },

// get_store_array(msg: StoreCommand): Element[] {
//   const arr = store[msg.command];
//   if (!Array.isArray(arr))
//     error(`store does not have array on path: ${msg.command}`, null, true);
//   return arr as Element[];
// }

// // hack for showing server events while we await alerting and notifications:
//   display(m: ClientCommand) {
//     //console.log(m);
//     console.log(pretty(m));
//   }

