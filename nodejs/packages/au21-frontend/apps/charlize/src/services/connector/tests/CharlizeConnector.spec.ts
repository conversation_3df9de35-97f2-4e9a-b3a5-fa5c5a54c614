import {sleep} from '@au21-frontend/utils';
import {CharlizeStore} from '../CharlizeStore';
import Vue from 'vue';
import {Container} from 'typescript-ioc';
import {SocketConnector} from '@au21-frontend/client-connector';
import {is_connected} from "../../helpers/de-helpers";

Vue.config.devtools = false
Vue.config.productionTip = false

// NOTE: these could all be in a separate file (ie: for all tests)
Container.configure(
  { bind: CharlizeStore },
  { bind: SocketConnector },
)

fdescribe('Charlize connector Spec', () => {
  it('connects', async() => {
    const store: CharlizeStore = Container.get(CharlizeStore)
    const conn = Container.get(SocketConnector)

    expect(is_connected(store)).toBe(false)
    await sleep(3000)
    expect(is_connected(store)).toBe(true)
    expect(store.live_store.session_user.session_id).not.toBeNull()
  })
})

