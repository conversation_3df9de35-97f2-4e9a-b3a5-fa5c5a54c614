/**
 * TESTS WILL NEED TEST IOC CONTAINER
 */

import { sleep } from "../../../utils";
import { CharlizeStore } from "../../CharlizeStore";
import Vue from "vue";
import CharlizeBus from "../../CharlizeBus";
import { Container } from "typescript-ioc";
import {
  SocketConnector,
  session_terminate_command,
  SessionTerminationReason
} from "@au21-frontend/client-connector";

Vue.config.devtools = false
Vue.config.productionTip = false

const timeout = 2000 // ie: time to wait for server to respond, seems to need 2 seconds ??
const username = 'a2'

// TODO: create ioc test containers
Container.configure({bind: CharlizeStore})

describe('Charlize LoginPage Spec', () => {
  afterAll(async () => {
    const store = new CharlizeStore()
    const conn = new CharlizeConnector(store)
    await sleep(timeout)
    conn.publish(session_terminate_command({
      reason: SessionTerminationReason.SIGNED_OFF
    }))
  })

  it('logs in', async () => {
    const store = new CharlizeStore()
    const conn = new CharlizeConnector(store)
    conn.publish(common_login_request({
      username,
      password: '1',
      force: false,
    }))
    await sleep(timeout)
    expect(store.session_user.username).toEqual(username)
  })

  it('errors on second login', async () => {
    const bus = new CharlizeBus({loggingEnabled: true})
    const connector = new CharlizeConnector(new CharlizeStore(), bus)

    connector.publish(common_login_request({
      username,
      password: '1',
      force: false,
    }))
    await sleep(timeout)
    connector.publish(common_login_request({
      username,
      password: '1',
      force: false,
    }))
    await sleep(timeout)

    const foundMessage = bus.eventLog.find(m => {
      return m[1].content.join('') === `${username} already signed in`
    })
    expect(foundMessage).toBeDefined()
  })
})

