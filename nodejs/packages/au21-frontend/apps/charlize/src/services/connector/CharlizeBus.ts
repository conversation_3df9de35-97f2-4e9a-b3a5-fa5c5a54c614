import Vue from 'vue';

export default class CharlizeBus {
  private vue = new Vue()

  public loggingEnabled: boolean
  public eventLog = []

  constructor({ loggingEnabled }: { loggingEnabled?: boolean } = {}) {
    this.loggingEnabled = !!loggingEnabled
  }

  $on = this.vue.$on.bind(this.vue)
  $off = this.vue.$off.bind(this.vue)

  $emit(...attrs) {
    if (this.loggingEnabled) {
      this.eventLog.push(attrs)
    }
    this.vue.$emit.apply(this.vue, attrs)
  }
}

