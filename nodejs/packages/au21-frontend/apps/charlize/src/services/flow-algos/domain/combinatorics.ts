// from: http://www.math.umbc.edu/~campbell/Computers/JavaScript/combin.html

/*
 * num = the number to encode
 * listlen = the number of elements
 * base = the base
 *
 * eg: for binary: base = 2 and listlen = the binary exponent above the largest number
 */
export const int_to_list = (num: number, listlen: number, base: number):number[] => {
  const digits = [];
  let temp = num;
  const digitnum = 0;
  let i = 0;
  while (temp > 0) {
    digits[i] = (temp % base);
    temp = Math.floor(temp / base);
    i++;
  }
  for (; i < listlen; i++) {
    digits[i] = 0;
  }
  return digits;
};


export const int_to_binary_array = (num: number, listlen: number):boolean[] =>
  int_to_list(num, listlen, 2).map(it => it != 0)
/*
  also this: for(int i = 0; i < 32; ++i)
  Bitarr[i] = (my_int >> i) & 1;

 */

// export const binary_array_to_int = (arr:boolean[]):number => 0
// https://www.tutorialspoint.com/convert-an-array-of-binary-numbers-to-corresponding-integer-in-javascript
