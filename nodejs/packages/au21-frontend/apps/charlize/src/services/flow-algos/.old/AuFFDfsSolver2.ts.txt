import { AuFFDfsSolver, AuFFEdge, AuFFParams, NetworkFlowSolverBase } from './au-ford-fulkerson';

export class AuFFDfsSolver2 extends NetworkFlowSolverBase {
  constructor(params: AuFFParams) {
    super(params);
  }

  /*
   * Attempt to do one algo without need for packages.
   * Concept:
   * - if for next edge:
   *  - 'to' == target
   *  - && there is an edge in this path: source -> 'from'
   *  - then return 0
   */

  solve() {

    const paths: number[] = [];

    for (
      let f = this.dfs(paths, this.params.source, AuFFDfsSolver.INF);
      f != 0;
      f = this.dfs(paths, this.params.source, AuFFDfsSolver.INF)
    ) {
      this.markAllNodesAsUnvisited();
      this.maxFlow += f;
    }

    this.graph.forEach((edges: AuFFEdge[]) => {
      edges.forEach((e: AuFFEdge) => {
        if (e.flow > 0) {
          this.solution.push(e);
        }
      });
    });
  }

  dfs(paths: number[], node: number, flow: number): number {

    if (node == this.params.target) {
      console.log(`returning flow to target flow=${flow}`);
      return flow;
    }

    const p: number[] = [...paths, node];
    //console.log({ p });

    const is_buyer = p.length == 2;
    const is_seller = p.length == 3;
    //console.log({ node, p, is_buyer, is_seller, flow });

    // if(is_buyer && node == )

    const edges: AuFFEdge[] = this.graph[node];
    this.addVisit(node);

    for (let x = 0; x < edges.length; x++) {

      const e: AuFFEdge = edges[x];
      const remaining: number = e.remainingCapacity();
      if (remaining > 0 && !this.hasVisited(e.to)) {

        if (is_seller && e.to != this.params.target) {
          console.log('seller can only goto target, returning flow');
          return remaining;
        }

        if (is_buyer && e.to == this.params.target) {
          console.log('buyer to target is zero');
          return 0;
        }

        // is seller:
        const seller_to_target = is_seller && e.to == this.params.target;
        if (seller_to_target) {
          console.log('seller to non-target');
          return flow;
        } else {
          console.log('seller to non-target');
          return 0;
        }

        const bottleneck: number =
          this.dfs(
            p,
            e.to,
            Math.min(flow, remaining)
          );

        if (bottleneck > 0) {
          e.augment(bottleneck);
          return bottleneck;
        }
      }
    }
    return 0;
  }

}
