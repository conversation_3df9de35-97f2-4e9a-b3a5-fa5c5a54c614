<template>
  <VbDemo>
    <div class="header">
      <div class="cell" style="float: left">Trader count:</div>
      <a-input-number
        style="float: left; margin: 0 10px"
        id="inputNumber"
        v-model="trader_count"
        :min="1"
        :max="100"
      />
      <span
      ><span style="font-weight: bold">last duration:</span>
        {{ duration }} ms</span
      >
      <span style="width: 10px">, </span>
      <span
      ><span style="font-weight: bold">packages:</span> {{ package_count }}
      </span>
      <span style="padding: 5px" v-if="running">RUNNING</span>
    </div>
    <div style="position: relative; top: 30px">
      <ScenarioMatrix
        :trader_names="trader_ids"
      />
    </div>
    <div style="margin-top: 80px">
    </div>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import FlowNetwork from '../ui/FlowNetwork.vue'
import {
  AuFFDfsSolver,
  NetworkFlowSolverBase,
} from '../domain/au-ford-fulkerson'
import ScenarioMatrix from '../ui/ScenarioMatrix.vue';
import { ScenarioGenerator } from './ScenarioGenerator';
import { FlowCalcResult, FlowCalcTrader, PackageFlowResult } from '../domain/model';

@Component({
  components: {
    ScenarioMatrix,
    FlowNetwork,
  },
})
export default class ScenarioMatrixDemo extends Vue {

  trader_limit = 30
  trader_count: number = 0


  get generator(): ScenarioGenerator {
    return new ScenarioGenerator(this.trader_count)
  }

  result: FlowCalcResult = null

  selected_package: PackageFlowResult = null

  running = false

  get ordered_traders(): FlowCalcTrader[] {
    return this.result?.ordered_traders || []
  }

  get trader_ids():string[]{
    return this.ordered_traders.map(t => t.cid)
  }

  get duration() {
    return this.result?.milliseconds || ''
  }

  get package_count(): number {
    return this.result?.package_results?.length || 0
  }

}
</script>

<style scoped>
.result {
  padding: 3px;
  background-color: gray;
  color: yellow;
}

.header {
  position: fixed;
  z-index: 999;
  background-color: #bbcccc;
  width: 800px;
}

.cell {
  font-weight: bold;
}
</style>
