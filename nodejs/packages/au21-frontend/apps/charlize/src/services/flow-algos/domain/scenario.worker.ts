//  /* eslint-disable @typescript-eslint/no-explicit-any, no-restricted-globals, no-extend-native */

import {calculate_result} from './calculator';
import {FlowCalcResult} from './model';
import {expose} from 'threads';

// https://threads.js.org/usage
expose(function(event:MessageEvent):FlowCalcResult{

  const data = event.data

  const { matrix_nodes, matrix_edges } = data

  if (!matrix_nodes) {
    throw Error('no matrix nodes')
  }

  if (!matrix_edges) {
    throw Error('no matrix edges')
  }
  const result: FlowCalcResult = calculate_result(
    matrix_nodes,
    matrix_edges,
    false,
  )
  return result
})

