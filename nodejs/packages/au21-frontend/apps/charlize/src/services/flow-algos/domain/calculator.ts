import {DeMatrixEdgeElement, DeMatrixNodeElement} from '@au21-frontend/client-connector';
import {range} from 'lodash';
import {AuFFDfsSolver, AuFFEdge, AuFFParams} from './au-ford-fulkerson';
import {
  FlowCalcConstraint,
  FlowCalcResult,
  FlowCalcTrader,
  FlowCalcTraderType,
  FlowScenario,
  FlowScenarioMatch
} from './model';
import {int_to_binary_array} from './combinatorics';

/*
 * Basic idea is that, given de matrix nodes and edges we:
 * a) calculate traders and constraints
 * b) create a list of packages
 * c) for each package, find the maxFlow for the traders, given the constraints
 */

// nodes -> FlowCalcTrader []
export const de_matrix_nodes_to_ordered_flow_calc_traders = (
  nodes: DeMatrixNodeElement[],
  use_buy_vol: boolean, // ie: use current buy vol or buy_max?
): FlowCalcTrader[] => []
  // orderBy(nodes, (n) => (use_buy_vol ? n.buy_vol : n.buy_max), 'desc').map(
  //   (n: DeMatrixNodeElement, index: number) => ({
  //     cid: n,
  //     vertex: index, // source is not 0 zero anymore, now second last
  //     buy: use_buy_vol ? n.buy_vol : n.buy_max,
  //     sell: n.sell_max,
  //   }),
  // )

// nodes and edges -> FlowCalcTrader[] and FlowCalcConstraint[]
export function de_matrix_to_flow_calc_traders_and_constraints(
  nodes: DeMatrixNodeElement[],
  edges: DeMatrixEdgeElement[],
  use_buy_vol: boolean,
): {
  ordered_traders: FlowCalcTrader[];
  constraints: FlowCalcConstraint[];
} {
  const ordered_traders: FlowCalcTrader[] =
    de_matrix_nodes_to_ordered_flow_calc_traders(nodes, use_buy_vol)

  const constraints: FlowCalcConstraint[] = edges.flatMap((e) => {
    if (e.capacity == 0) return []

    const b: FlowCalcTrader = ordered_traders.find((t) => t.cid == e.buyer_cid)
    const s: FlowCalcTrader = ordered_traders.find((t) => t.cid == e.seller_cid)
    const match = Math.min(e.capacity, b.buy, s.sell) // this really should already be done on server

    if (match == 0) return []

    return [
      {
        buyer_cid: b.cid,
        seller_cid: s.cid,
        buyer_vertex: b.vertex,
        seller_vertex: s.vertex,
        match: match,
      },
    ]
  })

  return { ordered_traders, constraints }
}

// traders -> packages (sorted by buyers first):
// TODO: write a test to make sure we don't have packages that have a buyer or seller with 0 buy or sell vol !
export function ordered_traders_to_packages(
  ordered_traders: FlowCalcTrader[],
): boolean[][] {
  // Map<FlowCalcTrader, FlowCalcTraderType> [] {
  /*
   * traders are ordered buyers first: eg: BBSS
   * in this case the packages would be :
   * 1000
   * 0100
   * 1100
   * ie: B**2 - 1
   * - so we're basically counting from 1 to B**2-1 using int to bin conversion
   */

  const buyer_count = ordered_traders.filter((t) => t.buy > 0).length

  if (buyer_count == 0) return []

  const packages: boolean[] = [] //Map<FlowCalcTrader, FlowCalcTraderType>[] = [];

  return range(1, buyer_count ** 2).map((i) =>
    int_to_binary_array(i, ordered_traders.length),
  )

  // range(1, last_buyer_package + 1).forEach(i => {
  //   const map = new Map<FlowCalcTrader, FlowCalcTraderType>();
  //   const bin_arr = int_to_binary_array(i, ordered_traders.length);
  //   bin_arr.forEach((b, index) => {
  //     // make sure that we don't somehow add sellers or buyers with no vol!
  //     map.set(ordered_traders[index], b ? FlowCalcTraderType.buyer : FlowCalcTraderType.seller);
  //   });
  //   packages.push(map);
  // });
}

/*
 * HELPERS
 */

export function find_constraint(
  constraints: FlowCalcConstraint[],
  edge: {
    buyer_vertex: number;
    seller_vertex: number;
  },
): FlowCalcConstraint | null {
  return constraints.find(
    (c) =>
      c.seller_vertex === edge.seller_vertex &&
      c.buyer_vertex === edge.buyer_vertex,
  )
}

export function flip(t: FlowCalcTraderType): FlowCalcTraderType {
  return t == FlowCalcTraderType.buyer
    ? FlowCalcTraderType.seller
    : FlowCalcTraderType.buyer
}

export function log_scenario_result(result: FlowScenario) {
  console.log(JSON.stringify({ package: result, maxflow: result.maxFlow }))
  if (result.solution.length) {
    console.log('solution:')
    console.table(result.solution)
  } else {
    console.log('no solution.')
  }
}

export function find_flow(
  solution: AuFFEdge[],
  edge: {
    buyer_vertex: number;
    seller_vertex: number;
  },
): number {
  return (
    solution.find(
      (s) => s.from === edge.buyer_vertex && s.to === edge.seller_vertex,
    )?.flow || 0
  )
}

// create a solver for one package:
export function create_solver(
  constraints: FlowCalcConstraint[],
  ordered_traders: FlowCalcTrader[],
  pkg: boolean[],
  //pkg: Map<FlowCalcTrader, FlowCalcTraderType>
): AuFFDfsSolver | null {
  // console.log({ pkg });
  /*
   * Inputs:
   *
   * 1) list of traders
   *   - ordered with buyers first
   *   - each trader's index + 1 will be the vertex number
   *   - eg: given: [t1, t2] then t1 will have index 1 and t2 will have index 2
   *   - this assumes that SOURCE has vertex number = 0
   *   - and SINK has vertex number = traders.length + 1
   *   - NOTE: using BUY_MAX, so that needs to be send from the server when buy vol changes !
   *
   * 2) constraint edges:
   *   - buyer_vertex, seller_vertex, match
   *   - ASSUMES:
   *    - that there are no buyer constraints > buy max
   *    - and for sellers, there are no contraints > sell max
   *
   * 3) package
   *   - this is specified with booleans
   *   - eg: package [true, true, false] means that t1 and t2 are buyers and t3 is a seller
   *
   */

  // if (ordered_traders.length == 0 || pkg.size == 0)
  //   return null; // no traders
  //
  // if([...pkg].filter(([k, v]) => v == FlowCalcTraderType.buyer).length == 0)
  //   return null; // no buyers
  //
  // if ([...pkg].filter(([k,v]) => v == FlowCalcTraderType.seller).length == 0)
  //   return null; // no sellers

  const params = new AuFFParams(ordered_traders.length + 2)

  // const g = new FlowNetwork(graph_size);
  const g = new AuFFDfsSolver(params)

  const buyers: number[] = []
  const sellers: number[] = []

  // for each trader, add either a source or sink, and also add to cids array
  pkg.forEach((b: boolean, index: number) => {
    const trader = ordered_traders[index]
    if (b && trader.buy > 0) {
      // connect buyers to source:
      buyers.push(trader.vertex)
      g.addEdge(params.source, trader.vertex, trader.buy)
    } else if (!b && trader.sell > 0) {
      // connect traders to sink:
      sellers.push(trader.vertex)
      g.addEdge(trader.vertex, params.target, trader.sell)
    }
  })

  // console.log(JSON.stringify({ buyers, sellers }));

  // for all valid buyer / seller combinations: find all constraints
  constraints.forEach((c) => {
    if (buyers.includes(c.buyer_vertex) && sellers.includes(c.seller_vertex)) {
      g.addEdge(c.buyer_vertex, c.seller_vertex, c.match)
    }
  })

  // debugger
  return g
}

/*
 * called by worker, uses above functions
 * - for all packages
 */

export function calculate_result(
  matrix_nodes: DeMatrixNodeElement[],
  matrix_edges: DeMatrixEdgeElement[],
  logging: boolean,
): FlowCalcResult {
  const start = performance.now()

  const { ordered_traders, constraints } =
    de_matrix_to_flow_calc_traders_and_constraints(
      matrix_nodes,
      matrix_edges,
      false,
    )

  const pkgs: boolean[][] = ordered_traders_to_packages(ordered_traders) // Map<FlowCalcTrader, FlowCalcTraderType>[] =

  if (logging) {
    console.log('worker created ordered traders:')
    console.table(ordered_traders)

    console.log('worker created constraints')
    console.table(constraints)

    console.log('packages:')
    // console.log(JSON.stringify({pkgs}));
    console.table(pkgs)
  }

  const package_results: FlowScenario[] = []

  // 3)
  pkgs.forEach((pkg: boolean[]) => {
    const solver: AuFFDfsSolver = create_solver(
      constraints,
      ordered_traders,
      pkg,
    )
    const maxFlow: number = solver.getMaxFlow()
    const solution: AuFFEdge[] = solver.getSolution()

    const rows: FlowScenarioMatch[] = []
    ordered_traders.forEach((seller: FlowCalcTrader) => {
      ordered_traders.forEach((buyer: FlowCalcTrader) => {
        if (buyer !== seller) {
          const buyer_vertex = buyer.vertex
          const seller_vertex = seller.vertex
          rows.push({
            seller_cid: seller.cid,
            buyer_cid: buyer.cid,
            limit:
              find_constraint(constraints, { buyer_vertex, seller_vertex })
                ?.match || 0,
            flow: find_flow(solution, { buyer_vertex, seller_vertex }),
            current: 0,
          })
        }
      })
    })
    package_results.push({ package: pkg, maxFlow, solution, matches: rows })
  })

  return {
    milliseconds: (performance.now() - start).toFixed(2),
    ordered_traders,
    constraints,
    scenarios: package_results,
  }
}
