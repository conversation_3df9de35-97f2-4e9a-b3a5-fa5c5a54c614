@startuml

class FlowCalcTraderType {
  buyer
  seller
}


class FlowCalcTrader {
 cid  :string
 vertex :number
 buy   :number
 sell  :number
}


class FlowCalcConstraint {
  buyer_cid :string
  seller_cid :string
  buyer_vertex :number
  seller_vertex :number
  capacity :number
}

class PackageFlowResultRow {
  seller_cid :string
  buyer_cid :string
  limit :number
  flow :number
  current :number
}

class PackageFlowResult {
  maxflow :number
  trader package : Map
  rows : []
}

class FlowCalcModel {
  ordered_traders :[]
  constraints :[]
  results :[]
}

FlowCalcModel o-- FlowCalcConstraint : constraints
FlowCalcModel o-- FlowCalcTrader : ordered_traders
FlowCalcModel o-- PackageFlowResult : results
PackageFlowResult o-- PackageFlowResultRow : rows
PackageFlowResult o-- Map : trader packages
Map o-- FlowCalcTrader
Map o-- FlowCalcTraderType
@enduml
