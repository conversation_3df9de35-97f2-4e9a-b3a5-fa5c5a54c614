import { AuFFDfsSolver, AuFFEdge, AuFFParams, NetworkFlowSolverBase } from './au-ford-fulkerson';

export class AuFFDfsSolver3 extends NetworkFlowSolverBase {
  constructor(params: AuFFParams) {
    super(params);
  }

  solve() {

    for (
      let f = this.dfs(this.params.source, AuFFDfsSolver.INF);
      f != 0;
      f = this.dfs(this.params.source, AuFFDfsSolver.INF)
    ) {
      this.markAllNodesAsUnvisited();
      this.maxFlow += f;
    }

    this.graph.forEach((edges: AuFFEdge[]) => {
      edges.forEach((e: AuFFEdge) => {
        if (e.flow > 0) {
          this.solution.push(e);
        }
      });
    });
  }

  dfs(node: number, flow: number): number {

    console.log(`node: ${node}, flow: ${flow}`);
    if (node == this.params.target)
      return flow;

    const edges: AuFFEdge[] = this.graph[node];
    this.addVisit(node);

    for (let x = 0; x < edges.length; x++) {

      const e: AuFFEdge = edges[x];

      const remaining: number = e.remainingCapacity();
      if (remaining > 0 && !this.hasVisited(e.to)) {

        const bottleneck: number = this.dfs(
          e.to,
          Math.min(flow, remaining)
        );

        if (bottleneck > 0) {
          e.augment(bottleneck);
          return bottleneck;
        }
      }
    }
    return 0;
  }


}
