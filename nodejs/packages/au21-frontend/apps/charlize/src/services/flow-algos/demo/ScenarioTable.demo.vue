<template>
  <VbDemo>
    <div class="header">
      <div class="cell" style="float: left">Trader count:</div>
      <a-input-number
        style="float: left; margin: 0 10px"
        id="inputNumber"
        v-model="trader_count"
        :min="1"
        :max="100"
      />
      <span
      ><span style="font-weight: bold">last duration:</span>
        {{ duration }} ms</span
      >
      <span style="width: 10px">, </span>
      <span
      ><span style="font-weight: bold">packages:</span> {{ package_count }}
      </span>
      <span style="padding: 5px" v-if="running">RUNNING</span>
    </div>
    <!--    <FlowNetwork :solver="solver"></FlowNetwork>-->
    <div style="margin-top: 80px">
      <ScenarioResultPanel
        :ordered_traders="ordered_traders"
        :package_flow_result="selected_package"
      />
    </div>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue, Watch} from 'vue-property-decorator';
import {ScenarioGenerator} from './ScenarioGenerator';
import FlowNetwork from '../ui/FlowNetwork.vue';
import ScenarioResultPanel from '../ui/ScenarioResultPanel.vue';
import {FlowCalcResult, FlowCalcTrader, FlowScenario} from '../domain/model';

@Component({
  components: {
    ScenarioResultPanel,
    FlowNetwork
  }
})
export default class ScenarioTableDemo extends Vue {
  table_width = 900;
  table_height = 300;

  trader_limit = 30;
  trader_count: number = 0;


  get generator(): ScenarioGenerator {
    return new ScenarioGenerator(this.trader_count);
  }

  result: FlowCalcResult = null;

  selected_package: FlowScenario = null;

  running = false;

  get ordered_traders(): FlowCalcTrader[] {
    return this.result?.ordered_traders || [];
  }

  get duration() {
    return this.result?.milliseconds || '';
  }

  get package_count(): number {
    return this.result?.scenarios?.length || 0;
  }

  @Watch('trader_count')
  onTraderCount(count) {
    if (count > this.trader_limit) {
      alert(`limiting to ${this.trader_limit} traders`);
      return;
    }

  }

  mounted() {
    this.trader_count = 3;
  }

  destroyed() {
    // this.worker?.terminate();
  }

  onChangeTraderCount() {
  }
}
</script>

<style scoped>
.result {
  padding: 3px;
  background-color: gray;
  color: yellow;
}

.header {
  position: fixed;
  z-index: 999;
  background-color: #bbcccc;
  width: 800px;
}

.cell {
  font-weight: bold;
}
</style>
