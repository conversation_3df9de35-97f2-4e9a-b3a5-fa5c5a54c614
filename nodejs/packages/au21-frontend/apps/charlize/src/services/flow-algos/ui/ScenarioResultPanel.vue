<template>
  <div style="display: flex; flex-wrap: wrap">
    <div>
      <div class="table-heading">Sorted Summary</div>
      <ScenarioTable
        :trader_names="trader_names"
        :rows="rows"
        :height="300"
        :width="600"
      />
    </div>
    <!--    <div>-->
    <!--      <div class="create_scenario_result_rows-heading">Matrix Nodes</div>-->
    <!--      <GenericTable-->
    <!--        :width="table_width"-->
    <!--        :height="table_height"-->
    <!--        :rows="scenario.matrix_nodes"-->
    <!--      />-->
    <!--    </div>-->
    <!--    <div>-->
    <!--      <div class="create_scenario_result_rows-heading">Matrix Edges</div>-->
    <!--      <GenericTable-->
    <!--        :width="table_width"-->
    <!--        :height="table_height"-->
    <!--        :rows="scenario.matrix_edges"-->
    <!--      />-->
    <!--    </div>-->
    <!--    <div>-->
    <!--      <div class="create_scenario_result_rows-heading">Ordered Traders</div>-->
    <!--      <GenericTable-->
    <!--        :width="table_width"-->
    <!--        :height="table_height"-->
    <!--        :rows="scenario.ordered_traders"-->
    <!--      />-->
    <!--    </div>-->
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import ScenarioTable from './ScenarioTable.vue';
import {FlowCalcTrader, FlowScenario, FlowScenarioMatch} from '../domain/model';

@Component({
  components: {
    ScenarioTable,
  },
})
export default class ScenarioResultPanel extends Vue {
  @Prop({ required: true }) ordered_traders: FlowCalcTrader[] | null
  @Prop({ required: true }) package_flow_result: FlowScenario | null

  table_width = 700
  table_height = 400

  get trader_names(): string[] {
    if (this.ordered_traders == null) return []

    return this.ordered_traders.map((t) => t.cid)
  }

  get rows(): FlowScenarioMatch[] {
    return this.package_flow_result?.matches || []
  }

  //   console.log('received from worker: ', data);
  // if (data.result) {
  //   const result: ScenarioResult = data.result as ScenarioResult;
  //   console.log(result);
  //
  //   // TODO: move this to the worker, ie: return proper results:
  //   this.ordered_traders = result.ordered_traders;
  //   this.constraints = result.constraints;
  //
  //   this.sorted_scenario_results.push({
  //     maxFlow_rows: ScenarioResultRow.table(
  //       this.ordered_traders,
  //       this.constraints,
  //       result),
  //     maxFlow: result.maxflow,
  //     result: result
  //   });
  //   this.sorted_scenario_results = sortBy(this.sorted_scenario_results, 'maxFlow').reverse();
  //   this.max_scenario_table_rows = sortBy(this.sorted_scenario_results[0].maxFlow_rows, 'flow').reverse()
  //
  //   console.log('max flows:')
  //   console.table(this.sorted_scenario_results.map(result => ({
  //     maxFlow: result.maxFlow,
  //     package: result.result.pkg
  //   })))
  //
  //   console.log("max scenario")
  //   console.table(this.max_scenario_table_rows.map(row => JSON.parse(JSON.stringify(row))))
  //   //  this.rows =
  //   // const rows:ScenarioResultRow[] = ScenarioResult.flow(result, edge:{
  //   //   buyer_vertex
  //   // })
  //   // this.max_scenario_table_rows.push(result)
  //   // ScenarioResult.log(result)
  //
  //
  // }
  // this.scenario = {...data}
  //  this.scenario = data as TradingScenario;

  // const scenario:TradingScenario = data as TradingScenario
  // this.max_scenario_table_rows = scenario.sorted_summary_table_rows
  // this.ordered_traders = scenario.ordered_traders
  // console.log(scenario)
  //debugger
}
</script>

<style scoped>
.table-heading {
  font-size: 18px;
}
</style>
