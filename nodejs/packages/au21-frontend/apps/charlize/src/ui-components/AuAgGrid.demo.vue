<template>
  <VbDemo>
    <VbCard>
      <AuAgGrid
        ref="auAgGrid"
        :width="350"
        :height="400"
        :columnDefs="columnDefs"
        :rowData="rowData"
        :getRowHeight="() => 32"
        :gridOptions="{getRowNodeId: data => data.make}"
      />
    </VbCard>
    <VbCard title="Edit">
      <input
        type="text"
        v-model="rowData[0].make"
        @input="updateData()"
      >
      <p>
        <button
          @click="columnDefs.push({headerName: 'Column X', field: 'column-x'})"
        >Add column
        </button>
        <button @click="columnDefs.pop()">Remove column</button>
      </p>
      <p>
        <button
          @click="rowData.push({ make: 'Porsche', model: 'Boxter', price: 72000 })"
        >Add row
        </button>
        <button @click="rowData.pop()">Remove row</button>
      </p>
      <p>
        <button @click="flashRow()">Flash row</button>
      </p>
      <p>
        <button @click="scrollToColumn()">Scroll to price column</button>
      </p>
      <pre style="height: 150px; overflow-y: auto">{{ rowData }}</pre>
    </VbCard>

    <VbCard :title="`${bigTableNumberOfRows} x 50 test`">
      <AuAgGrid
        :width="650"
        :height="400"
        :columnDefs="columnDefsBigTable"
        :rowData="rowDataBigTable"
        :gridOptions="{getRowNodeId: data => data.OID}"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuAgGrid from './AuAgGrid.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {ColDef} from 'ag-grid-community';
import {createBigObject} from '../demo-helpers/MrBlotter.helper';

let count = 0

@Component({
  components: { AuAgGrid },
})
export default class AuAgGridDemo extends Vue {

  columnDefs: ColDef[] = [
    {
      headerName: 'Make',
      field: 'make',
      sortable: true,
      filter: true,
      editable: true,
      enableCellChangeFlash: true,
    },
    { headerName: 'Model', field: 'model' },
    { headerName: 'Price', field: 'price' },
  ]

  rowData = [
    { make: 'Toyota', model: 'Celica', price: 35000 },
    { make: 'Ford', model: 'Mondeo', price: 32000 },
    { make: 'Porsche', model: 'Boxter', price: 72000 },
  ]

  bigTableNumberOfRows = 100
  columnDefsBigTable = Object.keys(createBigObject())
    .map(key => ({ headerName: key, field: key, width: 120 }))
  rowDataBigTable = createMultipleByClosure(createBigObject, this.bigTableNumberOfRows)

  updateData() {
    this.rowData = JSON.parse(JSON.stringify(this.rowData))
  }

  flashRow() {
    const gridApi = (this.$refs.auAgGrid as AuAgGrid).gridApi
    const rowNode = gridApi.getDisplayedRowAtIndex(0)

    gridApi.flashCells({ rowNodes: [rowNode] })
  }

  scrollToColumn() {
    const gridApi = (this.$refs.auAgGrid as AuAgGrid).gridApi
    gridApi.ensureColumnVisible('price')
  }
}
</script>
