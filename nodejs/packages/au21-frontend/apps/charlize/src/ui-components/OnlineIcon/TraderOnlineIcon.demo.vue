<template>
  <VbDemo>
    <VbCard>
      <TraderOnlineIcon
        :auction_id="auction_id"
        :user="user"
        :users_that_have_seen_auction="[auction_id]"
      />
    </VbCard>
    <VbCard>
      <TraderOnlineIcon
        :auction_id="auction_id"
        :user="user"
        :users_that_have_seen_auction="[]"
      />
    </VbCard>
    <VbCard>
      <TraderOnlineIcon
        auction_id="other auction"
        :user="user"
        :users_that_have_seen_auction="[auction_id]"
      />
    </VbCard>
    <VbCard>
      <TraderOnlineIcon
        auction_id="other_auction"
        :user="user"
        :users_that_have_seen_auction="[]"
      />
    </VbCard>
    <VbCard>
      <TraderOnlineIcon
        auction_id="other auction"
        :user="{...user, has_connection_problem: true}"
        :users_that_have_seen_auction="[auction_id]"
      />
    </VbCard>
    <VbCard>
      <TraderOnlineIcon
        auction_id="other_auction"
        :user="{...user, has_connection_problem: true}"
        :users_that_have_seen_auction="[]"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import TraderOnlineIcon from './TraderOnlineIcon.vue';
import {create__clearUserElement} from "../../pages/common/UserPage/UserExplorer/UserElement.factory";
import {UserElement} from "@au21-frontend/client-connector";

@Component({
  components: { TraderOnlineIcon },
})
export default class  extends Vue {
  auction_id = 'auction-1'
  user:UserElement = {...create__clearUserElement(),
    current_auction_id: this.auction_id,
    has_connection_problem: false
  }
}
</script>
