<template>
  <svg
    class="TraderOnlineIcon"
    :fill="fill"
    :opacity="opacity"
    x="0px" y="0px"
    width="16px" height="16px" viewBox="0 0 466.146 466.146"
    xml:space="preserve"
  >
<!--     style="enable-background:new 0 0 466.146 466.146;"-->
    <g>
      <path
        d="M289.285,191.86c28.844-18.539,47.995-50.83,47.995-87.654C337.28,46.659,290.621,0,233.088,0
        c-57.559,0-104.207,46.659-104.207,104.207c0,36.824,19.151,69.121,47.996,87.654c-67.959,6.082-121.422,63.331-121.422,132.854
        v108.155l0.274,1.69l7.457,2.328c70.196,21.929,131.195,29.259,181.401,29.259c98.048,0,154.886-27.97,158.408-29.743l6.963-3.534
        h0.732V324.714C410.698,255.197,357.253,197.957,289.285,191.86z"
      />
    </g>
  </svg>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {UserElement} from '@au21-frontend/client-connector';

@Component({})
export default class TraderOnlineIcon extends Vue {
  @Prop({required:true}) auction_id:string;
  @Prop({required: true}) user: UserElement;
  @Prop({required: true}) users_that_have_seen_auction:string[];

  is_on_auction(): boolean {
    return this.user?.current_auction_id == null ? false :
      this.user.current_auction_id === this.auction_id;
  }

  get fill() {
    // TODO: test with wifi dropout: server should sent 'has_connection_problem'
    // - currently I think isOnline could be true and also has_connection_problem
    if (this.user.has_connection_problem) {
      // ie: SocketState == CLOSED && !isTerminated()
      return 'indianred';
    } else if (this.is_on_auction()) {
      return '#77CF63';
    } else if (this.user.isOnline) {
      return 'orange';
    } else if (this.users_that_have_seen_auction.includes(this.user.user_id)) {
      return 'hsl(0, 0%, 40%)';
    } else
      return 'gray';
  }

  get opacity() {
    return 0.8;
    // if (this.user.isOnline === false) {
    //   return 0.5;
    // // } else if (this.user.socket_state === SessionSocketState.CLOSED) {
    // //   return 1.0;
    // } else if (this.is_on_auction(this.user)) {
    //   return 1.0;
    // } else
    //   return 0.4;
  }
}
</script>

<style lang="less" scoped>
.TraderOnlineIcon {
  //fill: #5a9c4b;
  margin-bottom: -4px;
}
</style>
