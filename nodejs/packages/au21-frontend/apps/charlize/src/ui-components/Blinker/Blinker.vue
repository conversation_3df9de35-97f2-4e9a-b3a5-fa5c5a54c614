<template>
  <div
    class="Blinker"
    :class="{
      'Blinker--background': background,
      'Blinker--active': isBlinking,
    }"
    @click="$emit('click')"
  >
    <slot>{{ value }}</slot>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';

const BLINK_TIMEOUT = 400

@Component({
  name: 'Blinker'
})
export default class Blinker extends Vue {
  @Prop({ required: true }) value: any
  @Prop({ type: Boolean }) background: boolean
  @Prop({ type: Boolean }) disabled: boolean // And most importantly, don't blink.
  @Prop({ type: Boolean }) initial: boolean
  isBlinking = false

  @Watch('value', { deep: true })
  onValueChange() {
    this.blink()
  }

  created() {
    if (this.initial) {
      this.blink()
    }
  }

  blink () {
    if (this.disabled) {
      return
    }
    this.isBlinking = true
    setTimeout(() => {
      this.isBlinking = false
    }, BLINK_TIMEOUT)
  }
}
</script>

<style lang="less">
// See .Blinker
</style>
