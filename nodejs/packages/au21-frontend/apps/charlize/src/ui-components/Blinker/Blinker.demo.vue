<template>
  <VbDemo>
    <VbCard title="default">
      <Blinker :value="value">{{ value }}</Blinker>
    </VbCard>
    <VbCard title="background">
      <Blinker :value="value" background>{{ value }}</Blinker>
    </VbCard>
    <VbCard title="disabled">
      <Blinker :value="value" disabled>{{ value }}</Blinker>
    </VbCard>
    <VbCard title="initial">
      <Blinker :value="value" initial>{{ value }}</Blinker>
    </VbCard>
    <VbCard>
      <button @click="refresh">Change value</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Blinker from './Blinker.vue';

export default {
  components: {
    Blinker,
  },
  data() {
    return {
      value: Math.floor(Math.random() * 100000),
    }
  },
  methods: {
    refresh() {
      this.value = Math.floor(Math.random() * 100000)
    },
  },
}
</script>
