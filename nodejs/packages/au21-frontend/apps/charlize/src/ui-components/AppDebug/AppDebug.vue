<template>
  <a-button
    class="AppDebug AppDebug__title au-btn"
    type="danger"
    size="small"
    v-if="!isOpen"
    @click="isOpen = !isOpen"
  >
    Debug
  </a-button>
  <div v-else class="AppDebug AppDebug__body">
    <a-tabs
      :defaultActiveKey="tabNumber"
      @change="tabNumber = $event"
    >
      <a-tab-pane key="1">
        <span slot="tab">
          <a-icon type="upload" />
          Requests
        </span>
        <div>
          <div class="AppDebug__header">
            Bidders
          </div>
          <a-button
            style="background-color: tomato"
            @click="addTestBidders()"
          >
            + bidders
          </a-button>
          <a-button
            style="background-color: tomato"
            @click="removeTestBidders()"
          >
            - bidders
          </a-button>

          <div class="AppDebug__header">
            Other
          </div>
          <a-button
            style="background-color: tomato"
            @click="noCredit()"
          >
            No Credit
          </a-button>
          <a-button
            style="background-color: tomato"
            @click="unlimitedCredit()"
          >
            Unlimited Credit
          </a-button>
          <a-button
            style="background-color: tomato"
            @click="resetDb()"
          >
            Reset DB
          </a-button>
          <br>
          current session WILL be deleted
          <br>
          (need to refresh browser currently but we should do that
          automatically!)
          <br>
          <div class="AppDebug__header">
            Transport English Auctions
          </div>
          <a-button
            v-for="(closure, name) in teAuctionsMap"
            :key="name"
            class="mb-1"
            style="background-color: tomato; display: block"
            @click="closure"
          >
            {{ name }}
          </a-button>

          <AlertDebug />
        </div>
      </a-tab-pane>
      <a-tab-pane key="2">
        <span slot="tab">
          <a-icon type="hdd" />
          Store
        </span>
        <div class="AppDebug__body__container">
          <pre style="overflow: visible">{{ storeWithoutTime }}</pre>
        </div>
      </a-tab-pane>
      <a-tab-pane key="3">
        <span slot="tab">
          Auction Page
        </span>
        <div class="AppDebug__body__container">
          <AuctionPageDebug/>
        </div>
      </a-tab-pane>
      <a-button slot="tabBarExtraContent" @click="isOpen = !isOpen">Close
      </a-button>
    </a-tabs>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

// TODO These commands are missing from new implementation.
// import {
//   common_credit_save_request,
//   common_state_set_request,
//   StateSetInstruction,
//   te_traders_add_request,
//   te_traders_remove_request
// } from "@au21-frontend/client-connector";
// import {
//   publishRequest__TeAuction_aggregate_npv_1,
//   publishRequest__TeAuction_fixed_rate_1,
//   publishRequest__TeAuction_fixed_term_1,
//   publishRequest__TeAuction_per_dth_npv_1,
//   publishRequest__TeAuction_rate_and_term_1
// } from "../demo-helpers/TeAuctionRequest.helper";
// import AlertDebug from "./AlertDebug.vue";
import {Container} from 'typescript-ioc';
import {SocketConnector} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../services/connector/CharlizeStore';
import AlertDebug from '../AlertDebug.vue';
import AuctionPageDebug from './AuctionPageDebug.vue';

@Component({
  components: { AuctionPageDebug, AlertDebug },
})
export default class AppDebug extends Vue {
  isOpen = false
  tabNumber = 1

  charlizeStore = Container.get(CharlizeStore)
  connector = Container.get(SocketConnector)

  resetDb() {
    throw new Error('Not implemented')
    // this.connector.publish(common_state_set_request({ instruction: StateSetInstruction.RESET_DB }));
  }

  get storeWithoutTime() {
    return {
      ...this.charlizeStore,
      time: 'removed from here, as it dropped selection every second',
    }
  }

  addTestBidders() {
    const companies = this.charlizeStore.live_store.companies || []
    const auction_id = this.charlizeStore.live_store.de_auction.auction_id
    if (auction_id == null) {
      alert('You must first select an auction')
    } else {
      throw new Error('Not implemented')
      // this.connector.publish(
      //   te_traders_add_request({
      //     auction_id: auction_id,
      //     company_ids: companies.map(c => c.company_id)
      //   }));
    }
  }

  removeTestBidders() {
    throw new Error('Not implemented')
    // const traders = this.charlizeStore.te_trader_users;
    // const auction_id = this.charlizeStore.live_store.session_user.current_auction_id;
    // if (auction_id == null) {
    //   alert("You must first select an auction");
    // } else {
    //   this.connector.publish(
    //     te_traders_remove_request({
    //       auction_id: auction_id,
    //       company_ids: traders.map(c => c.company_id)
    //     }));
    // }
  }

  noCredit() {
    throw new Error('Not implemented')
// this.connector.publish(
    //   common_credit_save_request({
    //     company_ids: this.charlizeStore.companies.map(c => c.company_id),
    //     credit_limit: "0",
    //     default_risk: "100"
    //   })
    // );
  }

  unlimitedCredit() {
    throw new Error('Not implemented')
    // this.connector.publish(
    //   common_credit_save_request({
    //     company_ids: this.charlizeStore.companies.map(c => c.company_id),
    //     credit_limit: null,
    //     default_risk: null
    //   })
    // );
  }

  get teAuctionsMap() {
    return {
      // "Rate and Term 1 window": () => publishRequest__TeAuction_rate_and_term_1(this.connector),
      // "Fixed Rate 1 window": () => publishRequest__TeAuction_fixed_rate_1(this.connector),
      // "Fixed Term 1 window": () => publishRequest__TeAuction_fixed_term_1(this.connector),
      // "Aggregate NPV 1 window": () => publishRequest__TeAuction_aggregate_npv_1(this.connector),
      // "Per Dth NPV 1 window": () => publishRequest__TeAuction_per_dth_npv_1(this.connector)
    }
  }
}
</script>

<style lang="less" scoped>
.AppDebug {
  position: fixed !important;
  right: 20px;
  top: 20px;
  overflow: auto;
  z-index: 10;

  &__body {
    right: 12px;
    top: 12px;
    bottom: 12px;

    padding: 4px 10px;
    width: 350px;
    background-color: white;
    box-shadow: 0px 4px 5px 4px rgba(0, 0, 0, 0.49);
    overflow: hidden;

    &__container {
      overflow: scroll;
      height: 900px;
    }
  }

  &__header {
    font-weight: 700;
    font-size: 20px;
    margin-top: 12px;
  }
}
</style>
