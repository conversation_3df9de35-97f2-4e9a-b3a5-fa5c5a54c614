<template>
  <div class="AuctionPageDebug">{{ matrixCellsCount }}</div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {CharlizeStore} from '../../services/connector/CharlizeStore';

@Component({
  name: 'AuctionPageDebug'
})
export default class AuctionPageDebug extends Vue {
  store = Container.get(CharlizeStore);

  get matrixCellsCount() {
    return [
      `Edges: ${this.store.live_store.de_auction.matrix_last_round?.edges?.length}`,
      `Nodes: ${this.store.live_store.de_auction.matrix_last_round?.nodes?.length}`,
      `Traders: ${this.store.live_store.de_auction.blotter.traders.length}`
    ].join('\n');
  }
}
</script>

<style lang="less" scoped>
.AuctionPageDebug {
  white-space: pre;
}
</style>
