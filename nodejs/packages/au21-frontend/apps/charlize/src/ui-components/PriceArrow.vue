<template>
  <a-icon
    v-if="price_direction"
    class="PriceArrow"
    :class="{
      'PriceArrow--down': price_direction === PriceDirection.DOWN,
      'PriceArrow--up': price_direction === PriceDirection.UP,
    }"
    :type="icon"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {PriceDirection} from '@au21-frontend/client-connector';

@Component({
  name: 'PriceArrow'
})
export default class PriceArrow extends Vue {
  @Prop({ required: true }) price_direction: PriceDirection | null;
  //@Prop({ default: false }) big: boolean;

  PriceDirection = PriceDirection;

  get icon(): string | null {
    switch (this.price_direction) {
      case PriceDirection.UP:
        // return 'arrow-up';
        return 'arrow-up'; // 'up-circle'
      case PriceDirection.DOWN:
        return 'arrow-down'; // 'down-circle';
      default:
        return null;
    }
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../au-styles/variables.less';

.PriceArrow {
  vertical-align: middle !important;

  svg {
    width: 18px;
  }

  //&--big {
  //  svg {
  //    width: 28px;
  //  }
  //}

  //&--up {
  //  color: @au_buy_color !important;
  //}
  //
  //&--down {
  //  color: @au_sell_color !important;
  //}
}
</style>
