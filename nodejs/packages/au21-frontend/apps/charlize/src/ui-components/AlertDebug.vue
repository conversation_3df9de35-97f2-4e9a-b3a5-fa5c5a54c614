<template>
  <div class="AlertDebug">
    <div class="AppDebug__header">
      Transport English Auctions
    </div>
    <a-button
      class="au-btn"
      size="small"
      type="primary"
      @click="createAlert()">Create Alert</a-button>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {SocketConnector} from '@au21-frontend/client-connector';

@Component({})
export default class AlertDebug extends Vue {
  connector = Container.get(SocketConnector)

  createAlert() {
    // TODO Reimplement bus.

    // this.connector.bus.$emit('BrowserMessageEvent', {
    //   type: 'BrowserMessageEvent',
    //   browser_message_kind: BrowserMessageKind.ALERT,
    //   content: ['one', 'two', 'three', random_string(5000)],
    // })
  }
}
</script>

<style lang="less" scoped>
.AlertDebug {

}
</style>
