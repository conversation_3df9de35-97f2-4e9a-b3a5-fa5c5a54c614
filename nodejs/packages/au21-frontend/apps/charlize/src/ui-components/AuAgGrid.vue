<template>
  <ag-grid-vue
    :class="`${theme} AuAgGrid`"
    :style="{
      height: height && height + 'px',
      width: width && width + 'px',
      'user-select': 'none !important',
      'outline': 'none !important',
    }"
    :rowData="rowData"
    :columnDefs="columnDefs"
    :pinnedTopRowData="pinnedTopRowData"
    :pinnedBottomRowData="pinnedBottomRowData"
    @gridSizeChanged="onGridSizeChanged()"
    :gridOptions="gridOptionsLocal"
    @grid-ready="onGridReady"
    :getRowHeight="getRowHeight"
  />
</template>

<script lang="ts">
import {ColDef, ColumnApi, GridApi, GridOptions} from 'ag-grid-community';
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import {AgGridVue} from 'ag-grid-vue';
import {Deferred, isTestEnvironment} from '@au21-frontend/utils';

@Component({
  components: {AgGridVue}
})
export default class AuAgGrid extends Vue {
  @Prop({type: Number}) height: number;
  @Prop({type: Number}) width: number;
  @Prop({default: null}) gridOptions: GridOptions | null;
  @Prop({required: true}) rowData: any[];
  @Prop() columnDefs: ColDef[];
  @Prop() pinnedTopRowData: any;
  @Prop() pinnedBottomRowData: any;
  @Prop() gridSizeChanged: (GridSizeChangedEvent) => void;
  @Prop() turnOffAutoColSize: boolean;
  @Prop({default: () => 27}) getRowHeight: (any) => number;
  @Prop({default: true}) auto_refresh: boolean

  theme = 'ag-theme-balham-dark';

  gridApi: GridApi = null;
  columnApi: ColumnApi = null;
  gridOptionsLocal: GridOptions = null;
  gridReadyDeferred = new Deferred();

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.columnApi = params.columnApi;
    this.gridReadyDeferred.resolve();
  }

  beforeMount() {
    const optionsDefault = {

      animateRows: false,

      // onGridReady not set??
      enableCellChangeFlash: true,

      getRowNodeId: (data: { id: string }) => data.id,

      // Disable by default
      alwaysShowVerticalScroll: true,
      // suppressHorizontalScroll: true,
      suppressCellSelection: true,
      suppressContextMenu: true,
      suppressLoadingOverlay: true,
      suppressMenuHide: true, // so we can see and remove with suppressMenu: true on the col
      suppressMovableColumns: true,
      suppressNoRowsOverlay: true,
      suppressScrollOnNewData: true,
    };

    this.gridOptionsLocal = <GridOptions>{
      ...optionsDefault,
      ...this.gridOptions
    };

    this.gridOptionsLocal.api?.setRowData(this.rowData || [])
  }

  mounted () {
    if (isTestEnvironment()) {
      // Needed so that we'll be able to scroll AG grid,
      // which is not easy to do without access to api
      // as AG grid virtualizes rows.
      (this.$el as any).test_grid_api = {
        scrollToBottom: this.scrollToBottom
      }
    }
  }

  onGridSizeChanged() {
    this.$emit('gridSizeChanged', this.gridApi);

    if (!this.turnOffAutoColSize) {
      this.gridApi.sizeColumnsToFit();
    }
    // this.gridApi.refreshCells()
    //e.api.doLayout();
  }

  public async scrollToBottom() {
    const count = this.rowData.length;
    if (!count) return;
    await this.gridReadyDeferred.promise;
    if (!this.gridApi) return;
    this.gridApi.ensureIndexVisible(count - 1, 'bottom');
  }

  public async ensureColumnVisible(key: string) {
    await this.gridReadyDeferred.promise;
    if (!this.gridApi) return;
    this.gridApi.ensureColumnVisible(key);
  }

  public async refresh() {
    await this.gridReadyDeferred.promise;
    if (!this.gridApi) return;
    this.gridApi.refreshHeader();
  }

  @Watch('rowData', {deep: true})
  onSelectedRoundChange() {
    if (this.auto_refresh && this.gridApi) {
      this.gridApi.refreshCells({suppressFlash: true})
    }
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../au-styles/variables";

.AuAgGrid {

  // border: 2px solid #111 !important;

  /deep/ .ag-body-viewport {
    // Vertical scroll should be visible at all times
    // Solution is taken from docs https://www.ag-grid.com/javascript-grid-scrolling-scenarios/#make-scrollbars-always-visible
    overflow-y: scroll !important;
  }

  /deep/ .ag-cell-reset {
    border: 0;
    margin: 0;
    overflow: hidden;
  }

  /deep/ .ag-cell {
    //line-height: inherit !important;
    border-right: 1px solid hsl(0, 0%, 23%) !important;
    overflow: hidden !important;
    margin: 0;
    padding: 0 2px;
  }

  /deep/ .ag-cell-value {
  }

  /deep/ .ag-floating-bottom {
    overflow: hidden !important;
  }

  /deep/ .ag-header {
    background-color: #262626; //@au-background !important;
  }

  /deep/ .ag-header-cell {
    border-right: 1px solid hsl(0, 0%, 23%) !important;
    padding: 2px;
  }

  /deep/ .ag-horizontal-left-spacer {
    overflow: hidden !important;
  }

  /deep/ .ag-pinned-left-cols-container {
    border-right: 1px solid hsl(0, 0%, 23%) !important;
  }

  /deep/ .ag-row-even {
    // original:
    // background-color: hsl(193, 9.1%, 19.4%) !important;
    border-bottom: 1px solid hsl(0, 0%, 23%) !important;
    background-color: hsl(203, 6%, 9%) !important;
  }

  /deep/ .ag-row-odd {
    // original:
    // background-color: hsl(195, 9.5%, 16.5%) !important;
    border-bottom: 1px solid hsl(0, 0%, 23%) !important;
    background-color: hsl(183, 6%, 11%) !important;
  }

  /deep/ .ag-root-wrapper{
    border-top: 1px solid hsl(0, 0%, 23%) !important;
    border-bottom: 1px solid hsl(0, 0%, 23%) !important;
  }

}
</style>
