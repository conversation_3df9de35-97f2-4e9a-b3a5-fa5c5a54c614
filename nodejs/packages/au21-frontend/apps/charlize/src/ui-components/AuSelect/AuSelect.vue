<template>
  <a-select
    class="AuSelect au-input"
    v-model="valueProxy"
    :disabled="disabled"
    :size="dense ? 'small' : 'default'"
    :class="{
      //'au-input--output': output,
      'au-input--disabled': disabled,
    }"
  >
    <a-select-option
      v-for="(option, key) in options"
      :key="key"
      :value="valueBy(option, key)"
    >
      {{ textBy(option) }}
    </a-select-option>
  </a-select>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({})
export default class AuSelect extends Vue {
  @Prop() value: any
  @Prop() options: any
//  @Prop({ type: Boolean }) output: boolean
  @Prop({ type: Boolean }) disabled: boolean
  @Prop({ type: Boolean }) dense: boolean
  @Prop({ type: Function, default: (value, key) => value }) valueBy: Function
  @Prop({ type: Function, default: (value, key) => value }) textBy: Function

  get valueProxy() {
    return this.value
  }

  set valueProxy(value) {
    this.$emit('input', value)
  }
}
</script>

<style lang="less"  scoped>
@import (reference) './../../../src/au-styles/variables.less';

.AuSelect {
  min-width: 60px;
  padding: 0 !important;
}
</style>
