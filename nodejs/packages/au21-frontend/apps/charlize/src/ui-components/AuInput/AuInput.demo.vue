<template>
  <VbDemo>
    <VbCard>
      <AuInput v-model="value" />
    </VbCard>
    <VbCard title="disabled">
      <AuInput disabled v-model="value" />
    </VbCard>
    <VbCard title="output">
      <AuInput output v-model="value" />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuInput from './AuInput.vue';

@Component({
  components: { AuInput },
})
export default class AuInputDemo extends Vue {
  value = 'some-value'
}
</script>
