<template>
  <a-input
    class="AuInput"
    size="small"
    :style="{
      'font-size': font_size + 'px',
       height: height + 'px',
    }"
    :placeholder="placeholder"
    :class="classComputed"
    :disabled="disabled || output"
    v-model="valueProxy"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({})
export default class AuInput extends Vue {
  @Prop() value: string;
  @Prop({ default: false, type: Boolean }) disabled: boolean;
  @Prop({ type: Boolean }) output: boolean
  @Prop({ default: false }) left: boolean;
  @Prop({ type: Boolean }) block: boolean;
  @Prop({ default: 13 }) font_size: number; // default set to AuInput font-size of 13 for now, so as not to break too much
  @Prop({ default: 30 }) height: number;
  @Prop({ type: String, default: '' }) placeholder: string;

  get classComputed() {
    return {
      'AuInput--output': this.output,
      'AuInput--disabled': this.disabled,
      'AuInput--left': this.left,
      'AuInput--block': this.block
    };
  }

  get valueProxy() {
    return this.value;
  }

  set valueProxy(value) {
    this.$emit('input', value);
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../au-styles/variables.less';

.AuInput {
  //  display: inline-block;
  //  //box-shadow: inset 0 1px 3px #111;
  border-radius: 4px;
  //  border: 1px solid #444;
  //  color: black !important;
  //  font-size: 13px;
  //  height: 30px;
  //  margin: 0;
  //  padding: 0.5em 0.6em;
    background-color: @inputColor;
  //  text-align: right !important;
  //
  //  &:focus {
  //    outline: 0;
  //    border-color: #129FEA;
  //  }

  /deep/ &--output {
      cursor: default !important;
      background-color: @outputColor !important;
    }

  /deep/ &--disabled {
    // Disabled is supposed to have higher priority than output, thus goes second.
    cursor: not-allowed;
    color: transparent !important;
    background-color: @disabledColor !important;
  }

  /deep/ &--left {
    text-align: left !important;
  }

  // That's for IE11
  line-height: 0.6;
}
</style>
