<template>
  <div style='padding: 3px;'>
    <div
      class='AuOutput'
      :style="{
        'font-size': font_size + 'px',
        height: height + 'px',
        width: width + 'px',
        'text-align': text_align
      }"
      :class='classComputed'
    >
      <slot>{{ value }}</slot>
    </div>
  </div>
</template>

<script lang='ts'>
import {Component, Prop, Vue} from 'vue-property-decorator';
import {TextAlign} from '../../au-styles/au-style-utils'


@Component({})
export default class AuOutput extends Vue {
  @Prop() value: string;
  @Prop({type: String, default: 'left'}) text_align: TextAlign;
  @Prop({type: Boolean}) block: boolean;
  @Prop({default: 13}) font_size: number; // default set to AuOutput font-size of 13 for now, so as not to break too much
  @Prop({default: 30}) height: number;
  @Prop({required: true}) width: number;

  get classComputed() {
    return {
      'AuOutput--block': this.block
    };
  }
}
</script>

<style lang='less' scoped>
@import (reference) '../../au-styles/variables.less';

.AuOutput {
  display: inline-block;
  background-color: @outputColor !important;
  box-shadow: inset 0 1px 3px hsl(0, 0%, 10%);
  // border-radius: 4px;
  // border: 1px solid #555;
  color: #eee !important;
  font-size: 12px;
  height: 30px;
  margin: 0;
  padding: 0.7em 0.5em;
  text-align: right;

  // TODO: get wrapping to work (these don't seem to do it):
  //overflow-wrap: break-word;
  //word-wrap: break-word;
  //hyphens: auto;
  // inline-size: 150px !important;
  // word-wrap: break-word !important;

  // That's for IE11
  line-height: 0.6;
}
</style>
