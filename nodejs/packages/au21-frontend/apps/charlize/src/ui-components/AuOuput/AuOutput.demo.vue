<template>
  <VbDemo>
    <VbCard>
      <AuOutput
        :value="value"
        :width="130"
      />
    </VbCard>
    <VbCard title="slot">
      <AuOutput
        :value="value"
        :width="130"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuOutput from './AuOutput.vue';

@Component({
  components: {AuOutput},
})
export default class AuOutputDemo extends Vue {
  value = ''
}
</script>
