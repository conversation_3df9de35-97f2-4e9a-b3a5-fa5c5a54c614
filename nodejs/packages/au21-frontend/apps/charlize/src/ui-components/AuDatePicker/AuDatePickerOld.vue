<template>
  <a-date-picker
    ref="picker"
    class="AuDatePickerOld au-input"
    :class="classComputed"
    size="small"
    showTime
    format="MM/DD/YYYY [at] hh:mm a"
    v-model="valueProxy"
    :disabled="disabled || output"
    placeholder=""
    @open="open"
    @openChange="openChange($event)"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {dateTimeValue_to_moment, moment_to_DateTimeValue} from '../../entity-helpers/DateTimeValue';
import moment, {Moment} from 'moment';
import {Crud, DateTimeValue} from '@au21-frontend/client-connector';

// Component is old and poorly implemented.
// @deprecated
@Component({
  name: 'AuDatePickerOld'
})
export default class AuDatePickerOld extends Vue {
  @Prop() starting_time: DateTimeValue | null;
  @Prop({ type: Boolean }) output: boolean;
  @Prop({ type: Boolean }) disabled: boolean;
  @Prop({ required: true }) crud: Crud;

  open = false;

  get valueProxy():Moment | null {
    if (!this.starting_time) {
      return null;
    }
    return dateTimeValue_to_moment(this.starting_time);
  }

  set valueProxy(m: Moment) {
    if (!m) {
      this.$emit('starting_time', null);
    }
    else{
      this.$emit('starting_time', moment_to_DateTimeValue(m, true));
    }
  }

  get classComputed() {
    return {
      'au-input--output': this.output,
      'au-input--disabled': this.disabled
    };
  }

  openChange(open) {
    // We need picker to select Houston timezone as default, by default it's selecting browser timezone.
    if (open && !this.starting_time && this.crud === Crud.CREATE) {
      this.valueProxy = moment().add(1, 'minute');
    } else {
      // this seems to suppress error when clearing the auction settings form ??
      //this.valueProxy = null;
    }

    this.open = open;
  }

  click(e) {
    // console.log(e);
  }
}
</script>

<style lang="less" scoped>
.AuDatePickerOld {
  width: 100%;

  /deep/ .ant-input {
    border: none !important;
    margin-top: 0;
  }

  /deep/ &.au-input--output, /deep/ &.au-input--disabled {
    .ant-input {
      color: black !important;
      background-color: transparent;
    }
  }
}
</style>
