<template>
  <VbDemo>
    <VbCard>
      <AuDatePickerOld
        v-model="value"
        :crud="Crud.UPDATE"
      />
    </VbCard>
    <VbCard title="output">
      <AuDatePickerOld
        v-model="value"
        output
        :crud="Crud.UPDATE"
      />
    </VbCard>
    <VbCard title="disabled">
      <AuDatePickerOld
        v-model="value"
        disabled
        :crud="Crud.UPDATE"
      />
    </VbCard>
    <VbCard title="with default date time">
      <AuDatePickerOld
        v-model="value"
        :crud="Crud.UPDATE"
      />
    </VbCard>
    <VbCard>
      <pre v-if="value">{{ JSON.stringify(value, null, 2) }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuDatePickerOld from './AuDatePickerOld.vue';
import {Crud, DateTimeValue} from '@au21-frontend/client-connector';

@Component({
  components: {AuDatePickerOld},
})
export default class AuDatePickerDemo extends Vue {
  value: DateTimeValue = null
  Crud = Crud
}
</script>
