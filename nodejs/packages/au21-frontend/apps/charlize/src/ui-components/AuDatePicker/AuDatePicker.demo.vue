<template>
  <VbDemo>
    <VbCard>
      <AuDatePicker v-model="value" />
      <pre v-if="value">{{ JSON.stringify(value, null, 2) }}</pre>
    </VbCard>
    <VbCard title="time">
      <AuDatePicker v-model="value" time/>
    </VbCard>
    <VbCard title="range">
      <AuDatePicker v-model="valueRange" range/>
      <pre v-if="valueRange">{{ JSON.stringify(valueRange, null, 2) }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuDatePicker from './AuDatePicker.vue';
import {DateTimeValue} from '@au21-frontend/client-connector';

@Component({
  components: {AuDatePicker},
})
export default class AuDatePickerDemo extends Vue {
  value: DateTimeValue = null
  valueRange: DateTimeValue = null
}
</script>
