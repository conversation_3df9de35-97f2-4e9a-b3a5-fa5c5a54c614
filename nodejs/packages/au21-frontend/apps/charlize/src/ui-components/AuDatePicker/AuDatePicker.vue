<template>
  <a-date-picker
    :is="range ? 'a-range-picker' : 'a-date-picker'"
    ref="picker"
    class="AuDatePicker au-input"
    :class="classComputed"
    size="small"
    :show-time="time"
    :format="format"
    v-model="valueProxy"
    :disabled="disabled || output"
    :placeholder="range ? null : ''"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {dateTimeValue_to_moment, moment_to_DateTimeValue,} from '../../entity-helpers/DateTimeValue';
import {Moment} from 'moment';
import {DateTimeValue} from '@au21-frontend/client-connector';

@Component({
  name: 'AuDatePicker',
})
export default class AuDatePicker extends Vue {
  @Prop() value: DateTimeValue | null;
  // TODO output/disabled are not tested (due to not being used for now)
  @Prop({ type: Boolean }) output: boolean;
  @Prop({ type: Boolean }) disabled: boolean;
  @Prop({ type: Boolean }) range: boolean;
  @Prop({ type: Boolean }) time: boolean;

  open = false;

  get format () {
    if (this.time) {
      return 'MM/DD/YYYY [at] hh:mm a'
    }
    return 'MM/DD/YYYY'
  }

  get valueProxy (): null | [] | Moment | [Moment, Moment] {
    if (!this.value) {
      if (this.range) {
        return [];
      }
      return null;
    }
    if (Array.isArray(this.value)) {
      return [
        dateTimeValue_to_moment(this.value[0]),
        dateTimeValue_to_moment(this.value[1]),
      ]
    }
    return dateTimeValue_to_moment(this.value);
  }

  set valueProxy (value: null | [] | Moment | [Moment, Moment]) {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      this.$emit('input', value);
      return;
    }

    if (Array.isArray(value)) {
      this.$emit('input', value.map(i => moment_to_DateTimeValue(i, true)));
      return;
    }

    this.$emit('input', moment_to_DateTimeValue(value, true));
  }

  get classComputed () {
    return {
      'au-input--output': this.output,
      'au-input--disabled': this.disabled,
    };
  }

  openChange (open) {
    // // We need picker to select Houston timezone as default, by default it's selecting browser timezone.
    // if (open && !this.value && !(this.disabled || this.output)) {
    //   this.valueProxy = moment().add(1, 'minute');
    // } else {
    //   // this seems to suppress error when clearing the auction settings form ??
    //   //this.valueProxy = null;
    // }

    this.open = open;
  }
}
</script>

<style lang="less" scoped>
.AuDatePicker {
  width: 100%;

  /deep/ .ant-input {
    border: none !important;
    margin-top: 0;
  }

  /deep/ &.au-input--output, /deep/ &.au-input--disabled {
    .ant-input {
      color: black !important;
      background-color: transparent;
    }
  }
}
</style>
