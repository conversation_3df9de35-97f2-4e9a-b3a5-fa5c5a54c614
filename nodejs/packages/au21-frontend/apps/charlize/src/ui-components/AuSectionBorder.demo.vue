<template>
  <VbDemo>
    <VbCard>
      <AuSectionBorder style="width: 300px; height: 300px;">Something inside!
      </AuSectionBorder>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuSectionBorder from './AuSectionBorder.vue';

@Component({
  components: { AuSectionBorder },
})
export default class AuSectionBorderDemo extends Vue {

}
</script>
