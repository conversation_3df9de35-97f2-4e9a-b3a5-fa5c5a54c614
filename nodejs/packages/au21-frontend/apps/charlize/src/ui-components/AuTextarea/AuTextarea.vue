<template>
  <a-textarea
    class="AuTextarea au-input"
    size="small"
    style="border-radius: 4px;"
    :style="styleComputed"
    :class="classComputed"
    :disabled="disabled || output"
    :rows="rows"
    v-model="valueProxy"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({})
export default class AuTextarea extends Vue {
  @Prop() value: string
  @Prop({ type: Boolean }) output: boolean
  @Prop({ type: Boolean }) disabled: boolean
  @Prop({ type: Boolean }) left: boolean
  @Prop({ default: 13 }) font_size: number // default set to au-input font-size of 13 for now, so as not to break too much
  @Prop({ default: 30 }) height: number
  @Prop({ default: 2 }) rows: number
  @Prop({ default: false }) resize: boolean

  get classComputed() {
    return {
      'au-input--output': this.output,
      'au-input--disabled': this.disabled,
      'au-input--left': this.left,
    }
  }

  get styleComputed() {
    return {
      'fontSize': this.font_size + 'px',
      'height': this.height + 'px',
      'resize': !this.resize ? 'none' : null,
    }
  }

  get valueProxy() {
    return this.value
  }

  set valueProxy(value) {
    this.$emit('input', value)
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../au-styles/variables.less';

.AuTextarea {
  overflow: hidden;
  padding-top: 0;
  resize: none;

  margin: 0;

  // That's for IE11
  line-height: 0.6;
}
</style>
