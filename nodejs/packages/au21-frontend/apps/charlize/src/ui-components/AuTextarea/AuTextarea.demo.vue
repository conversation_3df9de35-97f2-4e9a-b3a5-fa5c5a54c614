<template>
  <VbDemo>
    <VbCard>
      <AuTextarea v-model="value" />
    </VbCard>
    <VbCard title="disabled">
      <AuTextarea disabled v-model="value" />
    </VbCard>
    <VbCard title="output">
      <AuTextarea output v-model="value" />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuTextarea from './AuTextarea.vue';

@Component({
  components: { AuTextarea },
})
export default class AuInputDemo extends Vue {
  value = ''
}
</script>
