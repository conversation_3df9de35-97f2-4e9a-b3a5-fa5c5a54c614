<template>
  <div class="au-section-header">
    <slot></slot>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';

@Component
export default class AuSectionHeader extends Vue {

}
</script>
<style lang="less" scoped>
@import (reference) "../au-styles/variables.less";

.au-section-header {
  background-color: @light-blue-header-color;
  color: @bright-blue-font-color;
  font-size: 14px;
  font-weight: 400;
  //height: 25px;
  margin: 0 0 0 5px;
  text-align: left;
}
</style>
