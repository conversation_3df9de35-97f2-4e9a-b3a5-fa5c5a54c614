<template>
  <VueCleaveComponent
    ref="input"
    class="NumberInput au-input ant-input"
    :class="classComputed"
    :style="{'font-size': font_size + 'px', height: height + 'px'}"
    v-model="valueProxy"
    :options="cleaveOptions"
    :disabled="disabled || output"
    @blur="onBlur"
  />
</template>

<script lang="ts">
import {Component, Prop, Ref, Vue} from 'vue-property-decorator';
import VueCleaveComponent from 'vue-cleave-component';
import {forceDecimals} from '@au21-frontend/utils';

@Component({
  components: {
    VueCleaveComponent,
  },
})
export default class NumberInput extends Vue {
  @Prop({ default: 'small' }) size: 'small' | 'default'
  @Prop({ type: [Number, String] }) decimalPlaces: number
  @Prop({ type: Boolean }) disabled: boolean
  @Prop({ type: Boolean }) output: boolean
  @Prop({ default: 13 }) font_size: number // default set to au-input font-size of 13 for now, so as not to break too much
  @Prop({ default: 30 }) height: number     // default set to au-input height of 30 for now, again, not to break too much
  @Prop() value: any
  @Ref() readonly input: VueCleaveComponent

  onBlur() {
    this.$emit('blur')
    const result = forceDecimals(this.valueProxy, this.decimalPlaces)
    this.valueProxy = result
  }

  // HACK vue-cleave-component doesn't pass through events. So we have to squeeze our way in.
  mounted () {
    this.inputElement?.addEventListener('keyup', this.onKeyup)
  }
  beforeDestroy () {
    this.inputElement?.removeEventListener('keyup', this.onKeyup)
  }
  onKeyup (event: KeyboardEvent) {
    if (event.code === 'Enter') {
      this.$emit('enter')
    }
    if (event.code === 'Escape') {
      this.$emit('escape')
    }
  }

  get classComputed() {
    return {
      'au-input--output': this.output,
      'au-input--disabled': this.disabled,
    }
  }

  get valueProxy(): string {
    if (this.value === null) {
      return ''
    }
    return this.value
  }

  set valueProxy(value: string) {
    // console.log('value', value);
    // HACK This input triggers @input on external change.
    if (this.valueProxy === value) {
      return
    }
    this.$emit('input', value)
  }

  get cleaveOptions() {
    return {
      numeral: true,
      numeralThousandsGroupStyle: 'thousand',
      numeralDecimalScale: this.decimalPlaces || 0,
      numeralPositiveOnly: true
    }
  }

  get inputElement(): HTMLInputElement | undefined {
    return this.input?.$el
  }

  select (): void {
    this.inputElement.select()
  }
  focus (): void {
    this.inputElement.focus()
  }
  blur (): void {
    this.inputElement.blur()
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../au-styles/variables.less";

.NumberInput {
  border-radius: 4px;
  padding: 0 5px;
  text-align: right;

  // That's for IE11:
  line-height: 0.6;
}
</style>
