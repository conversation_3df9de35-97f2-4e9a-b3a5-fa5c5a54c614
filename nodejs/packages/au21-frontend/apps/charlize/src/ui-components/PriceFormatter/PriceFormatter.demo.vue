<template>
  <VbDemo>
    <VbCard>
      <PriceFormatter
        price="100.375"
        :price_direction="PriceDirection.UP"
        :integer_font_size="14"
        :integer_bold="false"
        :fraction_font_size="12"
        :fraction_bold="true"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import PriceFormatter from './PriceFormatter.vue';
import {PriceDirection} from '@au21-frontend/client-connector';

@Component({
  components: {PriceFormatter},
})
export default class PriceFormatterDemo extends Vue {
  PriceDirection = PriceDirection
}
</script>
