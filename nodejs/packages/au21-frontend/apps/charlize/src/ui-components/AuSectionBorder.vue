<template>
  <div class="AuSectionBorder">
    <slot></slot>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';

@Component({
  name: 'AuSectionBorder',
  components: {}
})
export default class AuSectionBorder extends Vue {

}
</script>

<style lang="less" scoped>
/* NOTE: not scoped !*/
@import (reference) "../au-styles/variables.less";

.AuSectionBorder {
//  background-color: @au-background;
  background-color: hsl(0, 0%, 30%);
//  border: 1px solid #666;
 // border: 1px solid @au-background;
  border: 1px solid hsl(0, 0%, 30%) !important;
  border-radius: @border-radius-panel;
  color: #ccc; // @bright-blue-font-color;
  display: inline-block;
  //margin: 0;
  //margin: 2px;
  overflow: hidden;
  //padding: 2px;
}
</style>
