<template>
  <VbDemo>
    <VbCard title="true">
      <PriceArrow :price_direction="PriceDirection.UP"/>
    </VbCard>
    <VbCard title="false">
      <PriceArrow :price_direction="PriceDirection.DOWN"/>
    </VbCard>
    <VbCard title="null">
      <PriceArrow :price_direction="null"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import PriceArrow from './PriceArrow.vue';
import {PriceDirection} from '@au21-frontend/client-connector';

@Component({
  name: 'PriceArrowDemo',
  components: { PriceArrow },
})
export default class PriceArrowDemo extends Vue {
  get PriceDirection () {
    return PriceDirection
  }
}
</script>
