<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    :viewBox="`0 0 ${radius * 2} ${radius * 2}`"
    :width="radius * 2"
    :height="radius * 2"
    role="presentation"
  >
    <circle id="" :cx="radius" :cy="radius" :r="radius" :fill="fill">
    </circle>
    <text class="text" :x="+radius - 10" :y="+radius +2">{{ label }}</text>
  </svg>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeAuctioneerState} from '@au21-frontend/client-connector';

@Component({
  components: {},
})
export default class StateIcon extends Vue {

  @Prop({ required: true }) radius: number
  @Prop({ required: true }) label: string
  @Prop({ required: true }) state: DeAuctioneerState
  @Prop({ required: true }) current_state: DeAuctioneerState


  get fill() {
    return this.current_state == this.state ?
      'red' : 'black'
  }
}

</script>

<style lang="less" scoped>
.text {
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 12px;
  fill: white;
}
</style>
