<template>
  <div>
    <div>De Status Box</div>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      :viewBox="`0 0 ${width} ${height}`"
      :width="width"
      :height="height"
      role="presentation"
    >
      <rect x="0" y="0" :height="height" :width="width" fill="#efefef" />
      <StateIcon
        :x="0"
        :y="height/2 -radius"
        :radius="radius"
        label="setup"
        state="INIT"
        :current_state="current_state"
      >
      </StateIcon>
      <StateIcon
        :x="radius*2 + 5"
        :y="height/2 -radius"
        :radius="radius"
        fill="gray"
        label="ready"
        state="ROUND_READY"
        :current_state="current_state"
      >
      </StateIcon>
      <StateIcon
        :x="radius*4 + 5"
        :y="height*0.25 -radius"
        :radius="radius"
        fill="gray"
        label="open"
        state="ROUND_RUNNING"
        :current_state="current_state"
      >
      </StateIcon>
      <StateIcon
        :x="radius*4 + 5"
        :y="height*0.75 -radius"
        :radius="radius"
        fill="gray"
        label="paused"
        state="ROUND_PAUSED"
        :current_state="current_state"
      >
      </StateIcon>
      <StateIcon
        :x="radius*6 + 5"
        :y="height*0.5 -radius"
        :radius="radius"
        fill="gray"
        label="end"
        state="ROUND_CLOSED"
        :current_state="current_state"
      >
      </StateIcon>
      <StateIcon
        :x="radius*8 + 10"
        :y="height*0.5 -radius"
        :radius="radius"
        fill="gray"
        label="awarded"
        state="CLOSED"
        :current_state="current_state"
      >
      </StateIcon>
    </svg>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import StateIcon from './StateIcon.vue';
import {DeCommonStatusValue} from '@au21-frontend/client-connector';

@Component({
  components: { StateIcon },
})
export default class DeStatusBox extends Vue {

  /*
      export type DeAuctioneerState =
       | "INIT"
       | "ROUND_SETUP"
       | "ROUND_READY"
       | "ROUND_RUNNING"
       | "ROUND_PAUSED"
       | "ROUND_CLOSED"
       | "CLOSED"
   */

  @Prop({ required: true }) current_state: DeCommonStatusValue

  radius = 20 // for all icons
  height = 100
  width = 220

}

</script>

<style lang="less" scoped>
.text {
  font-family: Helvetica Neue, Arial, sans-serif;
  font-size: 10px;
  fill: #666;
}
</style>
