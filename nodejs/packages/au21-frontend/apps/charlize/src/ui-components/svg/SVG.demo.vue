<template>
  <div>
    <DeStatusBox :current_state="state"></DeStatusBox>
    <div>
      <button @click="next_state">next state</button>
      <span>  State: {{ state }}</span>
    </div>
    <br>
    <hr>
    <AnimatedIconExperiment
      :height="100"
      :width="100"
    >
    </AnimatedIconExperiment>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeStatusBox from './DeStatusBox.vue';
import AnimatedIconExperiment from './AnimatedIconExperiment.vue';
import StateIcon from './StateIcon.vue';
import {DeAuctioneerState} from '@au21-frontend/client-connector';


@Component({
  components: {
    AnimatedIconExperiment,
    DeStatusBox,
    StateIcon
  }
})
export default class SVGDemo extends Vue {

  state: DeAuctioneerState = DeAuctioneerState.STARTING_PRICE_NOT_SET;

  next_state(e) {
    const states: DeAuctioneerState[] =
      Object.keys(DeAuctioneerState).map(e => e as DeAuctioneerState);

    const index = states.indexOf(this.state);
    this.state = states[(index + 1) % states.length];
  }

}
</script>
