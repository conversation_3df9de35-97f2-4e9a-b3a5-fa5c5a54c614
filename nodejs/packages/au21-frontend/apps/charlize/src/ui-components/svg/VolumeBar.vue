<template>
  <div>
    <svg
      style="position: absolute; bottom: 0"
      :width="width"
      :height="50"
      :viewBox="`0 0 ${width} 50`"
    >

      <pattern
        :id="pattern_name"
        width="3"
        height="3"
        patternTransform="rotate(45 0 0)"
        patternUnits="userSpaceOnUse"
      >
        <line
          x1="0"
          y1="0"
          x2="0"
          y2="10"
          :style="`stroke:${color}; stroke-width:1`"
        />
      </pattern>

      <rect
        x="0"
        :y="50 - vol_max"
        :height="vol_max"
        :width="width"
        stroke="#999"
        stroke-width="1"
        fill="#444"
      />
      <rect
        x="0"
        :y="50 - vol_hatched"
        :height="vol_hatched"
        :width="width"
        :fill="`url(#${pattern_name})`"
      />
      />
      <!--            :fill="colors.au_buy_dimmedX2()"-->
      <rect
        x="0"
        :y="50 - vol_solid"
        :height="vol_solid"
        :width="width"
        :fill="color"
      />
    </svg>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {random_string} from '@au21-frontend/utils';

@Component({
  components: {}
})
export default class VolumeBar extends Vue {
  @Prop({ required: true }) height: number;
  @Prop({ required: true }) width: number;

  @Prop({ required: true }) vol_max: number;
  @Prop({ required: true }) vol_hatched: number;
  @Prop({ required: true }) vol_solid: number;

  @Prop({ required: true }) color: string;

  get pattern_name():string {
    return "p-"+random_string(6)
  }
}

</script>

<style lang="less" scoped>
</style>
