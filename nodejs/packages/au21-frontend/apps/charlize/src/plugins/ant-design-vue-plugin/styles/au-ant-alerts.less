@import (reference) "../ant-design-vue-plugin";


// NOTIFICATION
.ant-notification-notice-description {
  white-space: normal;
}





// MODAL

.ant-modal-header {
  background-color: #333;
}

.ant-modal-title {
  color: @au-label-color;
  font-weight: bold;
}


// ##### Notification

.ant-notification-notice {
  background-color: @au-notification-background;
  border-radius: 8px;
  color: white;
  border: #9aa6b1 2px solid;
  overflow-wrap: break-word;

  .ant-notification-notice-content {
    // Prevent overlap with close icon.
    padding-right: 12px;
  }

  .ant-notification-notice-message {
    // We don't really need ant notification title anywhere.
    display: none !important;
  }

  .ant-notification-notice-close {
    color: inherit;
  }

  &.notification--error {
    background-color: @au-notification-error-background;
  }
}

.ant-modal-content {
  background-color: @au-modal-background;
}

.ant-modal-body {
  background-color: @au-modal-background;
  border-radius: 8px;
  color: white;

  .ant-modal-confirm-title {
    // We don't really need ant title anywhere.
    display: none !important;
  }

  .ant-modal-confirm-content {
    color: inherit;
    white-space: pre-wrap;
  }

  &.modal--error {
    background-color: #5e2525;
  }
}

.ant-popover {
  .ant-popover-inner {
    box-shadow: 0 2px 8px @au-background-dark !important;
    background-color: @au-popconfirm-background;
  }
  .ant-popover-arrow {
    box-shadow: -2px -2px 5px @au-background-dark !important;
    border-top-color:  @au-popconfirm-background !important;
    border-left-color:  @au-popconfirm-background !important;
  }
  .ant-popover-inner-content {
    border-radius: @border-radius-panel;
    .ant-popover-message-title {
      color: @au-text-color !important;
    }
  }
}
