export enum ApplicationMode {
  APP = 'app',
  BOOK = 'book',
}

export class ApplicationConfig {
  mode: ApplicationMode
  footerText: string

  constructor(props: Record<string, string>) {
    this.mode = props.APP_MODE as ApplicationMode
    if (!(Object.values(ApplicationMode).includes(this.mode))) {
      throw new Error(`APP_MODE should be set in env and be one of ${Object.values(ApplicationMode).join(', ')}`)
    }
    this.footerText = props.FOOTER_TEXT
  }
}

export const applicationConfig = new ApplicationConfig({
  APP_MODE: process.env.APP_MODE,
  FOOTER_TEXT: process.env.FOOTER_TEXT,
})
