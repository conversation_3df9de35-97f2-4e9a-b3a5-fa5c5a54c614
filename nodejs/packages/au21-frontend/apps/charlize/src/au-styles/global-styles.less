/**
 * NB: THIS FILE SHOULD BE IMPORTED INTO MAIN
 *     - and also refenced by components +/- other stylesheet files
 */

@import 'reset';
@import "variables";
@import "spacers";
@import "scrollbar";


body {
  //font-size: 95%; // dm
  //background: #ffffff url(../au-styles/images/back.png);
  background-color: @dark-bg-color;
  border: 0;
  // color: NB CAN'T SET BODY COLOR TO WHITE OTHER WISE WONT SEE Vue-book !
  /// font-family: <PERSON><PERSON><PERSON>, "Trebuchet MS", Helvicita, Arial, sans-serif;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; // from ag
  font-size: 13px;
  font-weight: 400;
  line-height: 18.2px; // 1em;
  margin: 0;
  height: 100vh;
  padding: 0;
  text-rendering: optimizeLegibility;
  white-space: nowrap;
  -webkit-font-smoothing: antialiased;
}

// BORDERS (inc for debugging):
.border-red{
  border: 1px solid red !important;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ToolbarButton {
  font-size: 13px;
  //  height: 26px !important;
  margin: 2px;
  border: none !important;
  // color: @au-text-color !important;
  // &:disabled {
  //   color: red; // @au-button-color !important;
  // }
}

.au-label {
  color: @au-label-color; // hsl(77, 10%, 70%); //@au-label-color;
  font-size: 12px;
  font-weight: bold;
  margin-right: 3px;
}

.au-legend-small {
  color: @au-label-color !important; // hsl(77, 10%, 70%); //@au-label-color;
  font-size: 13px !important;
  font-weight: bold;
}

textarea.au-input {
  text-align: left;
}

// TODO Overhaul this.
.text-link-inverted {
  color: black;
  text-decoration: underline;

  &:hover {
    color: #444444;
  }
}

.au-header {
  background-color: @au-background;
  font-size: 18px;
  margin: 3px;
  padding: 3px;
}

.text-bold {
  font-weight: 700;
}

.dropdown-overlay {
  background-color: @au-body-background;
  color: @au-text-color;
  padding: 2px 8px;
  border: #9aa6b1 1px solid;
}

.text-small {
  font-size: 12px;
}

.align-center {
  text-align: center;
}

.align-right {
  text-align: right;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.fill-container {
  width: 100%;
  height: 100%;
}

.spacer {
  flex: 1 1;
}


.table-cell-wrapper {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// PAGE (25 Jan 2021: renamed form-block to page)

.page {
  background-color: @au-background;
  border-radius: @border-radius-panel;
  margin: 2px;
  padding: 4px;
  position: relative;
  top: -8px;
}



// TEXT

.text-link {
  color: @au-text-color;
  text-decoration: underline;

  &:hover {
    color: #e3dddd;
  }
}

// BORDER

.border-left {
  border-left: 1px solid #696969;
}

.border-right {
  border-right: 1px solid #696969;
}

.border-bottom {
  border-bottom: 1px solid #696969;
}

// TABLE

.table--border-center {
  td + td, th + th {
    border-left: 1px solid #696969;
  }
}

// MARKUP

.au-markup-table {
  color: rgba(0, 0, 0, 0.65);
  background-color: white;

  td {
    padding: 2px;

    &:not(:first-child) {
      //border-left: solid 1px rgb(134, 128, 128);
    }
  }

  tr {
    background-color: white;

    &:nth-child(even) {
      //background-color: rgb(239, 243, 250);
    }
  }
}

.content {
  font-family: Helvetica;

  i {
    font-style: italic;
  }

  a {
    color: inherit;
  }

  li {
    padding-left: 1rem;
    position: relative;
    list-style: disc;
    //color: @au-text-color-secondary;
  }
}

.au-fade {
  &-enter-active {
    transition: all .1s ease;
  }

  &-leave-active {
    transition: all .1s ease;
  }

  &-enter, &-leave-to {
    opacity: 0.4;
  }
}

// Blinker.
// Mostly used in Blinker.vue component, but also in a couple of other places as a class.
.Blinker {
  transition: color, background-color 0.3s ease-out;
  &--background {
    &.Blinker--active {
      background-color: rgba(255, 0, 0, 0.2);
    }
  }
  &--active {
    color: red;
  }
}
