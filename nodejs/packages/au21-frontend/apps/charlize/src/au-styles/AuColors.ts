/**************************************************************
 * This file needs to be kept in sync with ./colors.less
 *
 * NOTE: styles.less overlaps with this;
 **************************************************************/

import {OnlyInstantiableByContainer, Singleton} from 'typescript-ioc';
import chroma from 'chroma-js';
import {OrderType, PriceDirection} from "@au21-frontend/client-connector";

// great color picker: http://hslpicker.com/


enum COLOR_THEME {
  RED_GREEN, PINK_GREEN
}

@Singleton
@OnlyInstantiableByContainer
export class AuColors {

  private current_theme = COLOR_THEME.RED_GREEN

  switch_theme() {
    this.current_theme = this.current_theme === COLOR_THEME.RED_GREEN ?
      COLOR_THEME.PINK_GREEN : COLOR_THEME.RED_GREEN
  }

  // app

  // Round:
  au_purple = 'hsl(262, 71%, 72%)'; //'hsl(262, 21%, 52%)'; //'#7D6A9E'
  au_round = this.au_purple;

  //
  // BUY / SELL COLORS:
  //

  order_bright(side:OrderType):string{
    switch (side) {
      case OrderType.BUY:
        return this.au_buy_bright();
      case OrderType.SELL:
        return this.au_sell_bright();
      default:
        return 'hsl(0, 0%, 90%)';
    }
  }

  order_quantity_text_color(side:OrderType):string {
    switch (side) {
      case OrderType.BUY:
        return this.au_buy();
      case OrderType.SELL:
        return this.au_sell();
      default:
        return this.au_none();
    }
  }

  order_quantity_opposite_text_color(side:OrderType):string {
    switch (side) {
      case OrderType.BUY:
        return this.au_sell();
      case OrderType.SELL:
        return this.au_buy();
      default:
        return this.au_none();
    }
  }

  // sell:
  au_red = 'hsl(0, 92%, 75%)';
  sell_pink_4 = 'hsl(331, 95%, 70%)'; // '#FB6AB0';

  au_sell(){
    return this.current_theme === COLOR_THEME.RED_GREEN ? this.au_red : this.sell_pink_4;
  }

  au_sell_bright() {
    return this.current_theme === COLOR_THEME.RED_GREEN ? 'hsl(0, 100%, 60%)' : 'hsl(331, 100%, 75%)';
  }

  // TODO Using alpha in colors is not great. As you can't move it with opacity back to 100%.
  au_sell_dim() {
    return chroma(this.au_sell()).alpha(0.50).hex();
  }

  au_sell_dimmedX2(){
    return chroma(this.au_sell()).alpha(0.15).hex();
  }

  au_sell_dark() {
    return chroma(this.au_sell()).luminance(0.1).hex();
  }

  // buy:
  au_green = 'hsl(120, 47%, 64%)';
  buy_green_4 = 'hsl(91, 74%, 60%)';  // '#96E44E';

  au_buy() {
    return this.current_theme === COLOR_THEME.RED_GREEN ? this.au_green : this.buy_green_4; // this.buy_green_4; // .au_red; //  .au_yellow;
  }

  au_buy_bright() {
    return this.current_theme === COLOR_THEME.RED_GREEN ? 'hsl(120, 100%, 50%)' : 'hsl(91, 84%, 70%)';
  }

  au_buy_dimmed() {
    return chroma(this.au_buy()).alpha(0.50).hex();
  }

  au_buy_dimmedX2(){
    return chroma(this.au_buy()).alpha(0.15).hex();
  }

  au_buy_dark() {
    return chroma(this.au_buy()).luminance(0.1).hex();
  }

  // NONE:

  au_none(){
    return 'hsl(0,0%,90%)';
  }

  // match:
  au_blue = 'hsl(187, 50%, 60%)';
  au_blue_bright = 'hsla(187, 73.2%, 70.8%, 1)'; // '#7EDEEB';

  au_match(){
    return this.current_theme === COLOR_THEME.RED_GREEN ? this.au_blue : this.au_blue_bright;
  } //'#86EBE3'; //'hsl(200, 100%, 42%)'; // 'hsl(44, 100%, 42%)';

  au_match_dimmed(){
    return chroma(this.au_match()).alpha(0.30).hex();
  }

  au_match_dimmedX2(){
    return chroma(this.au_match()).alpha(0.15).hex();
  } // 'hsl(204, 31%, 41%)';

  au_match_dark() {
    return chroma(this.au_match()).luminance(0.1).hex();
  }

  // EXCESS

  au_excess(){
    return 'hsl(50, 10%, 60%)';
  }

  // MISC (TODO: move to colors.less)

  au_text_color = 'ccc';

  // white = '#ddd';
  // disabled = '#666';
  // au_background = '#262626'
  // light_border = 'hsl(0, 0%, 80%)';
  // layout_color = 'hsl(230, 10%, 15%)';
  // au_beige: '#E4CDA7'
//
//   //auMatched = 'hsla(14, 92%, 66%, 0.86)'; //chroma(this.logo_orange).darken(0.3); //'#F56F46';
//   auExcess = '#F5EC83';
//   auPotential = 'ddd';
//   auOrange = 'orange';
//   auYellow = 'yellow';
//   auWarning = '#ffff00';
//
//   // TODO: more this and colors.less back into charlize
//
//   //logo_green = 'hsl(94, 100%, 50%)';
//   logo_orange = 'hsl(44, 100%, 45%)'; //''hsla(273, 87%, 74%, 1)'; // 'hsla(44, 91%, 58%, 1)'; // 'hsl(44, 100%, 50%)';
//   prime_color = 'hsl(77, 10%, 70%)';
//   prime_color_disabled = 'hsl(77, 5%, 42%)';
//
//   auHighlight = 'hsla(273, 60%, 74%, 1)'
//
//   au_label_color = `darken(${this.logo_orange}, 10%)`;


  //   auRound = 'white';
//
//
//   auctioneer_state_round_open_waiting_for_bids = 'hsla(273, 87%, 74%, 1)'; // 'hsla(44, 91%, 58%, 1)'
//   state_round_closed_awardable = 'hsl(44, 100%, 50%)';
//   state_auction_closed = 'hsl(44, 100%, 50%)';
//
// //  auBtnDisabled = "darken(@au-primary-color, 2.75)"; //chroma(this.primary_color).darken(2.75);
// //  auBtnEnabled = this.primary_color; // chroma('#3E755B').alpha(0.80)
//
//   price_up_color = '#77ce77';
//   price_down_color = '#fa8787';
//
//   NORMAL = '#6de56d';
//   WARNING = '#f1bf60';
//   ERROR = '#ff7075';
//   DISABLED = '#777';

  getPriceColor(price_direction: PriceDirection): string {
    switch (price_direction) {
      case PriceDirection.DOWN:
        return this.au_sell(); // price_down_color;
      case PriceDirection.UP:
        return this.au_buy(); // price_up_color;
      default:
        return this.au_text_color;
    }
  }
}



// if (module.hot) {
//   module.hot.accept();
//   debugger
//   // module.hot.accept(() =>{
//   //
//   // });
// }


// au_yellow = 'hsla(59, 75%, 58%, 1)';
// au_green = '#77ce77';
// au_red = '#fa8787';
//
// sell_pink_1 = 'hsl(331, 97%, 45%)';
// buy_green_1 = 'hsl(78, 100%, 33%)';
//
// sell_pink_2 = 'hsl(344, 47%, 51%)';
// buy_green_2 = 'hsl(91, 50%, 48%)';
//
// sell_pink_3 = 'hsl(331, 94%, 45%)';
// buy_green_3 = 'hsl(110, 38%, 51%)';
