<template>
  <VbDemo>
    <VbCard title="default">
      <button @click="openNotification()">Open</button>
    </VbCard>
    <VbCard title="long text">
      <button @click="openLongNotification()">Open</button>
    </VbCard>
    <VbCard title="error">
      <button @click="openErrorNotification()">Open</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({})
export default class NotificationDemo extends Vue {
  openNotification() {
    this.$notification.open({
      message: '',
      description: 'Some message',
      duration: 50,
      placement: 'topRight',
    })
  }

  openLongNotification() {
    this.$notification.open({
      message: '',
      description: this.$vb.lorem(200) + 'long©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©©',
      duration: 50,
      placement: 'topRight',
    })
  }

  openErrorNotification() {
    this.$notification.open({
      message: '',
      class: 'notification--error',
      description: 'Some error message',
      duration: 50,
      placement: 'topRight',
    })
  }
}
</script>

