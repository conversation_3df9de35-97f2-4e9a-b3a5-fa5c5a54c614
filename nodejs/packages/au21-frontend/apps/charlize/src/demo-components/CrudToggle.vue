<template>
  <div style="display: inline-block;">
    <button
      @click="toggleCrud()"
      style="width: 100px"
    >
      Crud: {{ value }}
    </button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';
import {Crud} from '@au21-frontend/client-connector';

// Rotates through 3 CRUD states: create, read, update.

@Component({ components: {} })
export default class CrudToggle extends Vue {
  @Prop() value: Crud

  toggleCrud() {
    const value: Crud = (() => {
      const { UPDATE, READ, CREATE } = Crud
      switch (this.value) {
        case CREATE:
          return READ
        case READ:
          return UPDATE
        case UPDATE:
          return CREATE
        default:
          return CREATE
      }
    })()
    this.$emit('input', value)
  }
}
</script>

<style lang="less" scoped>

</style>
