<template>
  <VbDemo>
    <VbCard>
      <CrudToggle v-model="value" />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import CrudToggle from './CrudToggle.vue';
import {Crud} from '@au21-frontend/client-connector';

@Component({
  components: { CrudToggle },
})
export default class CrudToggleDemo extends Vue {
  value: Crud = Crud.CREATE
}
</script>
