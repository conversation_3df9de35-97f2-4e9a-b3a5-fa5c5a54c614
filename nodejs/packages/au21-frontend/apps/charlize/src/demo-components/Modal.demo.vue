<template>
  <VbDemo>
    <VbCard title="default">
      <button @click="openModal()">Open</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({})
export default class NotificationDemo extends Vue {
  openModal() {
    this.$modal.warning({
      title: '',
      content: this.$vb.lorem(200),
    })

    // modal.destroy();
  }
}
</script>

