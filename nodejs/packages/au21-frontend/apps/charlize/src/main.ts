import 'reflect-metadata'; // this MUST be the first line of the application
import {Container} from 'typescript-ioc';
import {
  AuClient,
  AuStore,
  ClientCommandHandler,
  ClientConnectorConfig,
  SocketConnector
} from '@au21-frontend/client-connector';
import {CharlizeStore} from './services/connector/CharlizeStore';
import {CharlizeClient} from './services/connector/CharlizeClient';
import {CharlizeHandler} from './services/connector/CharlizeHandler';
import './services/test-services/v-test-directive'
import {AuColors} from './au-styles/AuColors';
import {pretty} from '@au21-frontend/utils';
import {detect} from "detect-browser";

// import { CharlizeHandler } from './services/connector/CharlizeHandler';

// TODO: this could be moved to each app, ie: apps/book/book_main.ts and app/main/app_main.ts
// - but for now these are the same so leaving here

// debug('>>> ClientConnectorConfig:');
// debug(pretty(config));
// debug('<<<');


Container.configure(
  {bind: AuClient, to: CharlizeClient},   // this seems to work without binding AuClient
  {bind: AuColors},
  // {bind: AuScreen},
  {bind: AuStore, to: CharlizeStore},                 // this seems to work with binding AuStore
  {bind: ClientCommandHandler, to: CharlizeHandler},  // this binding is definitely needed !
  {bind: ClientConnectorConfig, to: ClientConnectorConfig},
  {bind: SocketConnector},
);

const config = Container.get(ClientConnectorConfig);
const connector = Container.get(SocketConnector)

const {name, version, os} = detect()

config.setURL(window.location, {name, version, os})
connector.connect().catch(console.error)

console.log('************* ClientConnectorConfig **************');
console.log(pretty(config));
console.log('**************************************************');

const store = Container.get(CharlizeStore)
;(window as any).remoteStore = store;

// Can't use enum as it doesn't get tree-shaked :<
console.log('APP_MODE='+process.env.APP_MODE)

if (process.env.APP_MODE === 'book') {
  import('./apps/book/book_main')
} else {
  import('./apps/main/app_main')
}

declare let module;

if (module.hot) {
  //debugger
  module.hot.accept();
  // module.hot.accept(() =>{
  //
  // });
}
