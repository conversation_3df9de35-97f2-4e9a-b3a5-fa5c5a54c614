import moment, {Moment} from 'moment';
import {DateTimeValue} from '@au21-frontend/client-connector';

/*
TODO This function should strictly do conversion, no mapping for specific UIs.
    NB: DateTimeValue is now 0-based, like the date-time-picker
 */
export const moment_to_DateTimeValue = (moment: Moment, zero_seconds = false): DateTimeValue => {
  return {
    year: moment.year(),
    month: moment.month(), // reverted to zero based months
    day_of_month: moment.date(),
    day_of_week: moment.day(),
    hour: moment.hour(),
    minutes: moment.minutes(),
    seconds: zero_seconds ? 0 : moment.seconds(),
  }
}

export const dateTimeValue_to_moment = (dateTimeValue: DateTimeValue): Moment => {
  return moment({
    year: dateTimeValue.year,
    month: dateTimeValue.month,
    day: dateTimeValue.day_of_month,
    hours: dateTimeValue.hour,
    minutes: dateTimeValue.minutes,
    seconds: dateTimeValue.seconds,
  })
}

export const nowDateTimeWithZeroSeconds = (): DateTimeValue => {
  return moment_to_DateTimeValue(moment(), true);
}
