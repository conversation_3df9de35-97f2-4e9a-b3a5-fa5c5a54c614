import {createMultipleByClosure, random_bool, random_number_old} from '@au21-frontend/utils';
import {DeTraderHistoryRowElement, OrderSubmissionType, PriceDirection} from '@au21-frontend/client-connector';
import {OrderType} from "@au21-frontend/client-connector";

export function createDemo__DeTraderHistoryRowElement(count: number): DeTraderHistoryRowElement {
  const quantity = random_number_old({ rand: 10, mult: 10 }) - 50;
  return {
    //seller_range: ' > 80',
    auction_id: '1',
    bid_constraints: {
      min_buy_quantity: 0,
      max_buy_quantity: 30,
      min_sell_quantity: 0,
      max_sell_quantity: 50
    },
    company_id: '2',
    excess_side: OrderType.SELL,
    excess_level: '4+',
    id: `ROUND.${count + 1}`,
    order_submission_type: OrderSubmissionType.MANUAL,
    order_type: quantity > 0 ? OrderType.SELL : OrderType.BUY,
    price_direction: (count > 1) ? null : PriceDirection.UP,
    price_has_reversed: random_bool(),
    price_suffix: 'cpp',
    round_number: count + '',
    round_price: '20.000',
    order_submitted_by: 'fred',
    value: '',
    quantity: '' + Math.abs(quantity)
  };
}

export function createDemo__DeTraderHistoryRowSequence(length: number): DeTraderHistoryRowElement[] {
  let currentPrice = '101.500';
  let incrementPrice = -0.5;
  let currentQuantity = 30;
  let incrementQuantityMax = -5;
  let isReversed = false;
  const reversalRound = length - 3;

  return createMultipleByClosure((roundNumber: number) => {
    const excess = Math.ceil((roundNumber < reversalRound ? reversalRound - roundNumber : (roundNumber - reversalRound) / 4) / reversalRound * 5) || '+';

    const result = {
      auction_id: '1',
      bid_constraints: {
        min_buy_quantity: 0,
        max_buy_quantity: 30,
        min_sell_quantity: 0,
        max_sell_quantity: 50
      },
      company_id: '2',
      excess_side: OrderType.SELL,
      excess_level: '3+',
      id: `ROUND.${roundNumber + 1}`,
      order_submission_type: OrderSubmissionType.MANUAL,
      order_submitted_by: 'b1',
      order_type: currentQuantity > 0 ? OrderType.SELL : OrderType.NONE, // TODO: what is this?
      price_direction: (roundNumber > 1) ? null : PriceDirection.UP,
      price_has_reversed: isReversed,
      price_suffix: 'cpp',
      round_number: roundNumber + '',
      round_price: currentPrice,
      username: 'fred',
      value: '',
      quantity: '' + Math.abs(currentQuantity)
    };

    if (roundNumber === reversalRound) {
      isReversed = true;
      incrementPrice = 0.125;
      incrementQuantityMax = 1;
    }
    currentPrice = (+currentPrice + incrementPrice).toFixed(3);
    currentQuantity = currentQuantity + incrementQuantityMax;

    return result;
  }, length);
}

