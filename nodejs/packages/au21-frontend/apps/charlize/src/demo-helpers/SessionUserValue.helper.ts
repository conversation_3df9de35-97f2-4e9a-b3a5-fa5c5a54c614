import {random_bool, random_enum, random_number_string, random_string} from '@au21-frontend/utils';
import {AuUserRole, ClientSocketState, PageName, SessionUserValue} from '@au21-frontend/client-connector';

export function createDemo__SessionUserValue(): SessionUserValue {
  return {
    company_id: random_string(),
    company_longname: random_string(),
    company_shortname: random_string(),
    current_auction_id: random_string(),
    current_page: random_enum(PageName),
    isAuctioneer: random_bool(),
    isOnline: random_bool(),
    role: random_enum(AuUserRole),
    session_id: random_string(),
    socket_state: ClientSocketState.OPENED,
    user_id: random_number_string(),
    username: 'user_' + random_string()
  };
}
