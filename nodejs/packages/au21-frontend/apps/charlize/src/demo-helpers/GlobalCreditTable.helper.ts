import {random_bool, random_number, random_number_string,} from '@au21-frontend/utils';
import {GlobalCreditTableRow,} from '../pages/De/DeAuctioneer/credit_edit/GlobalCreditTable.types';

export const createDemo__GlobalCreditTableRow = (): GlobalCreditTableRow => {
  const company_id = 'c.' + random_number_string();
  const buy_value = random_bool() ? 'no limit' : `$${random_number(999_999_999).toLocaleString()}.00`;
  const sell_value = random_bool() ? 'no limit' : random_number_string(50);

  return {
    company_id,
    company_name: `company ${company_id}`,
    current_max_buy: buy_value,
    new_max_buy: buy_value,
    current_max_sell: sell_value,
    new_max_sell: sell_value,
  };
};
