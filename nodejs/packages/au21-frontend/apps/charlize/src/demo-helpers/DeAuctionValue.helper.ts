import {CompanyElement, DeAuctionValue, UserElement} from "@au21-frontend/client-connector";
import {createDefault__DeSettingsValue} from "./DeSettingsValue.helper";
import {createDemo__DeCommonStatusValue} from "./DeCommonStatusValue.helper";
import {createDemo__DeTraderInfoValue} from "./DeTrader.helper";
import {createDemo__DeBlotter} from "./DeRoundTable.helper";
import {createDemo__DeAuctioneerStatusValue} from "./DeAuctioneerStatusValue.helper";

export function createDemo__DeAuctionValue_for_trader(): DeAuctionValue {
  return {
    auction_counterparty_credits: [],
    auction_id: 'AUCTION_1',
    auctioneer_info: null,
    auctioneer_status: null,
    award_value: null,
    blotter: null,
    matrix_last_round: null,
    messages: [],
    notice: '',
    settings: createDefault__DeSettingsValue(),
    common_status: createDemo__DeCommonStatusValue(),
    trader_history_rows: [],
    trader_info: createDemo__DeTraderInfoValue(),
    users_that_have_seen_auction: []
  }
}

export function createDemo__DeAuctionValue_for_auctioneer(
  companies: CompanyElement[],
  users: UserElement[]
): DeAuctionValue {

  return {
    auction_counterparty_credits: [],
    auction_id: 'auction-1',
    auctioneer_info: null,
    auctioneer_status: createDemo__DeAuctioneerStatusValue(),
    award_value: null,
    blotter: createDemo__DeBlotter(companies, 1),
    common_status: createDemo__DeCommonStatusValue(),
    matrix_last_round: null,
    messages: [],
    notice: '',
    settings: createDefault__DeSettingsValue(),
    trader_history_rows: [],
    trader_info: createDemo__DeTraderInfoValue(),
    users_that_have_seen_auction: [],

  }

}
