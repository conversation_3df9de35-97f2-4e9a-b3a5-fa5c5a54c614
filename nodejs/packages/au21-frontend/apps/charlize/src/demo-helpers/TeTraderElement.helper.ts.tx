import { TeTraderUserElement } from '../_generated/generated'

import { random_number, random_bool } from '@au21-frontend/utils'

export function createDemo__TeTraderUserElement (): TeTraderUserElement {
  const company_id = random_number({ rand: 10, mult: 10 }) + ''

  return {
    has_seen_auction: random_bool(),
    id: random_number({ rand: 10, mult: 10 }) + '',
    company_id: company_id,
    user_id: company_id + 1
  }
}
