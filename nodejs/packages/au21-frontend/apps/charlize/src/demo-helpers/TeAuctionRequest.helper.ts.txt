import {
  te_settings_save_request,
  TeBidRankingRule,
  TeDesign,
  TeSettingsValue,
  DateTimeValue,
} from '../_generated/generated'
import { random_digits } from '@au21-frontend/utils'
import moment from 'moment'
import {
  moment_to_DateTimeValue,
  dateTimeValue_to_moment,
} from '../services/utils'
import { CharlizeConnector } from "@au21-frontend/client-connector";

export function createDemo__TeSettingsValue(): TeSettingsValue {
  return {
    annual_discount_rate: '3.20',
    auction_id: '',
    auction_name: `aggregate ${random_digits(4)}`,
    bid_constraints: [],
    bid_must_improve_highest: true,
    bid_must_improve_own: false,
    bid_quiet_period: 2,
    bid_ranking_rule: TeBidRankingRule.AGGREGATE_NPV,
    contract_start_month: null,
    contract_start_year: null,
    has_second_window: false,
    te_design: TeDesign.NPV,
    demand_max: '2,000,000',
    demand_min: '1',
    rate_decimal_places: 5,
    rate_min: '0.00001',
    rate_threshold_1: '1.00000',
    rate_threshold_2: '2.00000',
    rate_threshold_3: '4.00000',
    rate_threshold_4: '12.00000',
    term_max_at_max_rate: 500,
    term_min: 3,
    term_threshold_1: 12,
    term_threshold_2: 24,
    term_threshold_3: 36,
    term_threshold_4: 48,
    threshold_count: 4,
    use_constant_days_per_month: true,
    window_one_closes: moment_to_DateTimeValue(moment().add(5, 'minute')),
    window_one_opens: moment_to_DateTimeValue(moment().add(1, 'minute')),
    window_two_closes: null,
    window_two_opens: null,
    withdrawals_allowed: null,
  }
}

// TODO We likely want to move this elsewhere.

export function publishRequest__TeAuction_rate_and_term_1(connector: CharlizeConnector) {
  // NOTE: Typescript can't guarenteed Ints vs Longs (not without schema validation)
  //       - so for now numbers are sent as strings. (I guess we could send Doubles as numbers)
  connector.publish(te_settings_save_request({
    annual_discount_rate: null,
    auction_id: null,
    auction_name: `rate and term: ${random_digits(4)}`,
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null,
    bid_ranking_rule: TeBidRankingRule.RATE_THEN_TERM,
    contract_start_month: null,
    contract_start_year: null,
    has_second_window: false,
    te_design: TeDesign.RATE_AND_TERM,
    demand_max: '2,000,000',
    demand_min: '1',
    rate_decimal_places: '5',
    rate_min: '0.0001',
    rate_threshold_1: '10.00000',
    rate_threshold_2: null,
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: '500',
    term_min: '1',
    term_threshold_1: '36',
    term_threshold_2: null,
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: '1',
    use_constant_days_per_month: false,
    window_one_opens: moment_to_DateTimeValue(moment().add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(moment().add(6, 'minute')),
    window_two_opens: null,
    window_two_closes: null,
    withdrawals_allowed: null,
  }))
}

export function publishRequest__TeAuction_fixed_rate_1(connector: CharlizeConnector) {
  // NOTE: Typescript can't guarenteed Ints vs Longs (not without schema validation)
  //       - so for now numbers are sent as strings. (I guess we could send Doubles as numbers)
  connector.publish(te_settings_save_request({
    annual_discount_rate: null,
    auction_id: null,
    auction_name: `fixed rate: ${random_digits(4)}`,
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null,
    bid_ranking_rule: TeBidRankingRule.RATE_THEN_TERM,
    contract_start_month: null,
    contract_start_year: null,
    has_second_window: false,
    te_design: TeDesign.FIXED_RATE,
    demand_max: '2,000,000',
    demand_min: '1',
    rate_decimal_places: '5',
    rate_min: '10.50000',
    rate_threshold_1: null,
    rate_threshold_2: null,
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: '36',
    term_min: '1',
    term_threshold_1: '36',
    term_threshold_2: null,
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: '1',
    use_constant_days_per_month: false,
    window_one_opens: moment_to_DateTimeValue(moment().add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(moment().add(6, 'minute')),
    window_two_opens: null,
    window_two_closes: null,
    withdrawals_allowed: null,
  }))
}

export function publishRequest__TeAuction_fixed_term_1(connector: CharlizeConnector) {
  // NOTE: Typescript can't guarenteed Ints vs Longs (not without schema validation)
  //       - so for now numbers are sent as strings. (I guess we could send Doubles as numbers)
  connector.publish(te_settings_save_request({
    annual_discount_rate: null,
    auction_id: null,
    auction_name: `fixed term: ${random_digits(4)}`,
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null,
    bid_ranking_rule: TeBidRankingRule.RATE_THEN_TERM,
    contract_start_month: null,
    contract_start_year: null,
    has_second_window: false,
    te_design: TeDesign.FIXED_TERM,
    demand_max: '2,000,000',
    demand_min: '1',
    rate_decimal_places: '5',
    rate_min: '0.00001',
    rate_threshold_1: '10.0000',
    rate_threshold_2: null,
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: '36',
    term_min: '36',
    term_threshold_1: null,
    term_threshold_2: null,
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: '1',
    use_constant_days_per_month: false,
    window_one_opens: moment_to_DateTimeValue(moment().add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(moment().add(6, 'minute')),
    window_two_opens: null,
    window_two_closes: null,
    withdrawals_allowed: null,
  }))

}


export function publishRequest__TeAuction_aggregate_npv_1(connector: CharlizeConnector) {
  // NOTE: Typescript can't guarenteed Ints vs Longs (not without schema validation)
  //       - so for now numbers are sent as strings. (I guess we could send Doubles as numbers)
  connector.publish(te_settings_save_request({
    annual_discount_rate: '10',
    auction_id: null,
    auction_name: `aggregate npv: ${random_digits(4)}`,
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null,
    bid_ranking_rule: TeBidRankingRule.AGGREGATE_NPV,
    contract_start_month: 'Feb',
    contract_start_year: '2020',
    has_second_window: true,
    te_design: TeDesign.NPV,
    demand_max: '2,000,000',
    demand_min: '1',
    rate_decimal_places: '5',
    rate_min: '0.00001',
    rate_threshold_1: '10.10000',
    rate_threshold_2: '10.20000',
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: '500',
    term_min: '1',
    term_threshold_1: '12',
    term_threshold_2: '24',
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: '2',
    use_constant_days_per_month: false,
    window_one_opens: moment_to_DateTimeValue(moment().add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(moment().add(6, 'minute')),
    window_two_opens: moment_to_DateTimeValue(moment().add(7, 'minute')),
    window_two_closes: moment_to_DateTimeValue(moment().add(10, 'minute')),
    withdrawals_allowed: null,
  }))

}


export function publishRequest__TeAuction_per_dth_npv_1(connector: CharlizeConnector) {
  // NOTE: Typescript can't guarenteed Ints vs Longs (not without schema validation)
  //       - so for now numbers are sent as strings. (I guess we could send Doubles as numbers)
  connector.publish(te_settings_save_request({
    annual_discount_rate: '10',
    auction_id: null,
    auction_name: `per dth npv: ${random_digits(4)}`,
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null,
    bid_ranking_rule: TeBidRankingRule.PER_DTH,
    contract_start_month: 'Feb',
    contract_start_year: '2020',
    has_second_window: false,
    te_design: TeDesign.NPV,
    demand_max: '2,000,000',
    demand_min: '1',
    rate_decimal_places: '5',
    rate_min: '1.00001',
    rate_threshold_1: '10.10000',
    rate_threshold_2: '10.20000',
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: '500',
    term_min: '1',
    term_threshold_1: '12',
    term_threshold_2: '24',
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: '2',
    use_constant_days_per_month: false,
    window_one_opens: moment_to_DateTimeValue(moment().add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(moment().add(6, 'minute')),
    window_two_opens: null,
    window_two_closes: null,
    withdrawals_allowed: null,
  }))

}

export function createFilled__TeSettingsValue__1 (now: DateTimeValue): TeSettingsValue {
  return {
    annual_discount_rate: null,
    auction_id: null,
    auction_name: `auction ${random_digits(4)}`,
    bid_constraints: [],
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null, // could be zero
    bid_ranking_rule: TeBidRankingRule.AGGREGATE_NPV,
    contract_start_month: null,
    contract_start_year: null,
    demand_max: '1,000,000',
    demand_min: '1',
    te_design: TeDesign.RATE_AND_TERM,
    rate_decimal_places: 5,
    rate_min: '0.0001',
    rate_threshold_1: '10.500',
    rate_threshold_2: null,
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: 500,
    term_min: 1,
    term_threshold_1: 12,
    term_threshold_2: null,
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: 1,
    use_constant_days_per_month: false,
    has_second_window: false,
    window_one_opens: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(2, 'minute')),
    window_two_opens: null,
    window_two_closes: null,
    withdrawals_allowed: null,
  }
}

export function createFilled__TeSettingsValue__2 (now: DateTimeValue): TeSettingsValue {
  return {
    annual_discount_rate: null,
    auction_id: null,
    auction_name: `rate and term ${random_digits(4)}`,
    bid_constraints: [],
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null, // could be zero
    bid_ranking_rule: TeBidRankingRule.AGGREGATE_NPV,
    contract_start_month: null,
    contract_start_year: null,
    demand_max: '1,000,000',
    demand_min: '1',
    te_design: TeDesign.RATE_AND_TERM,
    rate_decimal_places: 5,
    rate_min: '0.0001',
    rate_threshold_1: '10.500',
    rate_threshold_2: null,
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: 500,
    term_min: 1,
    term_threshold_1: 12,
    term_threshold_2: null,
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: 1,
    use_constant_days_per_month: false,
    has_second_window: true,
    window_one_opens: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(2, 'minute')),
    window_two_opens: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(3, 'minute')),
    window_two_closes: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(4, 'minute')),
    withdrawals_allowed: null,
  }
}

export function createFilled__TeSettingsValue__3 (now: DateTimeValue): TeSettingsValue {
  return {
    annual_discount_rate: '10.00',
    auction_id: null,
    auction_name: `aggregate npv ${random_digits(4)}`,
    bid_constraints: [],
    bid_must_improve_highest: true,
    bid_must_improve_own: true,
    bid_quiet_period: null, // could be zero
    bid_ranking_rule: TeBidRankingRule.AGGREGATE_NPV,
    contract_start_month: 'Feb',
    contract_start_year: 2020,
    demand_max: '1,000,000',
    demand_min: '1',
    te_design: TeDesign.NPV,
    rate_decimal_places: 5,
    rate_min: '0.0001',
    rate_threshold_1: '10',
    rate_threshold_2: '20',
    rate_threshold_3: '30',
    rate_threshold_4: '40',
    term_max_at_max_rate: 300,
    term_min: 1,
    term_threshold_1: 12,
    term_threshold_2: 24,
    term_threshold_3: 36,
    term_threshold_4: 48,
    threshold_count: 4,
    use_constant_days_per_month: false,
    has_second_window: true,
    window_one_opens: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(1, 'minute')),
    window_one_closes: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(2, 'minute')),
    window_two_opens: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(3, 'minute')),
    window_two_closes: moment_to_DateTimeValue(dateTimeValue_to_moment(now).add(20, 'minute')),
    withdrawals_allowed: null,
  }
}
