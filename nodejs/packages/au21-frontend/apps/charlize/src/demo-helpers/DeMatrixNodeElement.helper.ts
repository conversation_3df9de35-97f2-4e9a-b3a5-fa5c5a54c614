import {DeMatrixNodeElement, OrderType} from '@au21-frontend/client-connector';
import {random_bool, random_number} from '@au21-frontend/utils';

let count = 0

export function createDemo__DeMatrixNodeElement(
  round: number,
  shortname:string,
  cid:string = null
): DeMatrixNodeElement {

  const next_cid = cid || ++count

  const is_buyer = random_bool()

  const buy_max = is_buyer ? 50 : random_number(50)
  const sell_max = is_buyer ? random_number(50) : 0

  const buy_vol = is_buyer ? random_number(buy_max) : 0
  const sell_vol = is_buyer ? 0 : random_number(sell_max)

  const match = random_number(Math.max(buy_vol, sell_vol))

  return {
    buy_match: is_buyer ? random_number(buy_vol) : 0,
    buy_max,
    buy_min: 0,
    buy_quantity: 0,
    cid: next_cid + "",
    cost: match * 1_000_000,
    cost_str: `$${match}.00`,
    id: next_cid + '',
    round: round,
    sell_match: is_buyer ? 0 : random_number(sell_vol),
    sell_max,
    sell_min: 0,
    sell_quantity: 0,
    shortname,
    side:  buy_vol == 0 && sell_vol == 0 ?
      OrderType.NONE :
        is_buyer ?
          OrderType.BUY :
          OrderType.SELL,
  }
}
