import {ClientCreditTableRow} from '../pages/De/DeAuctioneer/credit_edit/ClientCreditTable.types';
import {random_bool, random_number_string,} from '@au21-frontend/utils';

export const createDemo__ClientCreditTableRow = (): ClientCreditTableRow => {
  const companyId = 'c.' + random_number_string()
  const currentCreditStr = random_bool() ? 'no limit' : `$${random_number_string()}.00`
  return {
    companyId,
    companyName: `company ${companyId}`,
    currentCreditStr: currentCreditStr,
    newCreditStr: currentCreditStr // we'll start with the same value
  }
}
