import {DeAuctionSaveCommand, DeSettingsValue} from '@au21-frontend/client-connector';
import {nowDateTimeWithZeroSeconds} from '../entity-helpers/DateTimeValue';

export function createEmpty__DeSettingsValue(): DeSettingsValue {
  return {
    auction_name: '',
    excess_level_0_label: '',
    cost_multiplier: "",
    excess_level_1_label: '',
    excess_level_1_quantity: '',
    excess_level_2_label: '',
    excess_level_2_quantity: '',
    excess_level_3_label: '',
    excess_level_3_quantity: '',
    excess_level_4_label: '',
    excess_level_4_quantity: '',
    price_change_initial: '',
    price_change_post_reversal: '',
    price_decimal_places: 0,
    price_label: '',
    quantity_label: '',
    quantity_minimum: '',
    quantity_step: '',
    round_closed_min_secs: 0,
    round_open_min_secs: 0,
    round_orange_secs: 0,
    round_red_secs: 0,
    starting_price_announcement_mins: 0,
    starting_time: {
      day_of_month: 0,
      day_of_week: 0,
      hour: 0,
      minutes: 0,
      month: 0,
      seconds: 0,
      year: 0,
    },
    use_counterparty_credits: false
  };
}

export function createDefault__DeSettingsValue(): DeSettingsValue {
  return {
    auction_name: new Date().toString(),
    default_buyer_credit_limit: 10000000,
    default_seller_quantity_limit: '50',
    excess_level_0_label: '1+',
    excess_level_1_label: "2+",
    excess_level_1_quantity: '10',
    excess_level_2_label: '3+',
    excess_level_2_quantity: '20',
    excess_level_3_label: '4+',
    excess_level_3_quantity: '30',
    excess_level_4_label: '5+',
    excess_level_4_quantity: '50',
    price_change_initial: '0.500',
    price_change_post_reversal: '0.125',
    price_decimal_places: 3,
    price_label: 'cpp',
    quantity_label: 'MMLB',
    quantity_minimum: '1',
    quantity_step: '1',
    round_closed_min_secs: 10,
    round_open_min_secs: 10,
    round_orange_secs: 15,
    round_red_secs: 30,
    starting_price: null,
    starting_price_announcement_mins: 1,
    starting_price_set: false,
    starting_time: nowDateTimeWithZeroSeconds(),
    use_counterparty_credits: false,
    value_multiplier: '10_000.0',
  } as any;
}


export function convert_DeSettingsValue_to_DeAuctionSaveCommand(
  auction_id: string | null,
  settings: DeSettingsValue
): DeAuctionSaveCommand {
  return {
    auction_id: auction_id,
    auction_name: settings.auction_name,
    cost_multiplier: settings.cost_multiplier,
    use_counterparty_credits: settings.use_counterparty_credits?.toString() || 'true',
   // default_buyer_credit_limit: settings.default_buying_credit_limit,
   // default_seller_quantity_limit: settings.default_seller_quantity_limit,
    excess_level_0_label: settings.excess_level_0_label,
    excess_level_1_label: settings.excess_level_1_label,
    excess_level_1_quantity: settings.excess_level_1_quantity,
    excess_level_2_label: settings.excess_level_2_label,
    excess_level_2_quantity: settings.excess_level_2_quantity,
    excess_level_3_label: settings.excess_level_3_label,
    excess_level_3_quantity: settings.excess_level_3_quantity,
    excess_level_4_label: settings.excess_level_4_label,
    excess_level_4_quantity: settings.excess_level_4_quantity,
    month_is_1_based: false,
    price_change_initial: settings.price_change_initial,
    price_change_post_reversal: settings.price_change_post_reversal,
    price_decimal_places: settings.price_decimal_places.toString(),
    price_label: settings.price_label,
    round_closed_min_secs: settings.round_closed_min_secs?.toString(), // not currently used
    round_open_min_seconds: settings.round_open_min_secs?.toString(), // not currently used
    round_orange_secs: settings.round_orange_secs.toString(),
    round_red_secs: settings.round_red_secs.toString(),
    starting_day: settings.starting_time?.day_of_month?.toString() || '',
    starting_hour: settings.starting_time?.hour?.toString() || '',
    starting_mins: settings.starting_time?.minutes?.toString() || '',
    starting_month: settings.starting_time?.month?.toString() || '',
    starting_price_announcement_mins: settings.starting_price_announcement_mins?.toString() || '0',
    starting_year: settings.starting_time?.year?.toString() || '',
    quantity_label: settings.quantity_label,
    quantity_minimum: settings.quantity_minimum,
    quantity_step: settings.quantity_step
  };
}
