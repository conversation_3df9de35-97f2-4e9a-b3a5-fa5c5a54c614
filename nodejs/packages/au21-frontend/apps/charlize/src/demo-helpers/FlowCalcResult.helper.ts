import {FlowCalcResult, FlowCalcTrader, FlowScenario} from '../services/flow-algos/domain/model';
import {
  createMultipleByClosure,
  random_bool,
  random_number,
  random_number_string,
  random_string
} from '@au21-frontend/utils';

export function createDemo__FlowCalcTrader(): FlowCalcTrader {
  return {
    cid: random_string(),
    vertex: random_number(),
    buy: random_number(),
    sell: random_number(),
  }
}

export function createDemo__PackageFlowResult(numberOfTraders: number): FlowScenario {
  return {
    package: createMultipleByClosure(random_bool, numberOfTraders),
    maxFlow: random_number(),
    solution: [],
    matches: [],
  }
}

export function createDemo__FlowCalcResult(): FlowCalcResult {
  const numberOfTraders = 5

  return {
    milliseconds: random_number_string(),
    ordered_traders: createMultipleByClosure(createDemo__FlowCalcTrader, numberOfTraders),
    constraints: [],
    scenarios: createMultipleByClosure(() => createDemo__PackageFlowResult(numberOfTraders), numberOfTraders)
  }
}

