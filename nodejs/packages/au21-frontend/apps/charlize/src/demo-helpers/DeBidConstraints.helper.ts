import {DeBidConstraints} from '@au21-frontend/client-connector';
import {random_number_old} from '@au21-frontend/utils';

export function createDemo__DeBidConstraints(): DeBidConstraints {
  const quantity = random_number_old({ rand: 10, mult: 10 }) - 50
  return  {
    max_buy_quantity: quantity + 10,
    max_sell_quantity: quantity,
    min_buy_quantity: 0,
    min_sell_quantity: quantity + 5,
  }
}
