<template>
  <router-view/>
</template>

<script lang="ts">
import {createRoute, VueBookComponents} from 'vue-book';
import {Component, Vue} from 'vue-property-decorator';
import VueRouter from 'vue-router';

Vue.use(VueRouter);
Vue.use(VueBookComponents);

const router = new VueRouter({
  //base: '/book/',
  routes: [
    createRoute({
      requireContext: require.context('../..', true, /.demo.vue$/),
      path: '',
      hideFileExtensions: true
    })
  ]
});

@Component({
  router
})
export default class BookApp extends Vue {

}
</script>
