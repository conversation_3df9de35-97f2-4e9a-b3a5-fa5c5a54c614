import '../../polyfills/polyfills';
import Vue from 'vue';
import BookApp from './BookApp.vue';

import '../../au-styles/global-styles.less';
import '../../plugins/ag-grid/ag-grid-plugin';
import '../../plugins/ant-design-vue-plugin/ant-design-vue-plugin';
import '../../plugins/vue-quill-editor-plugin/quill-editor-plugin';
import './book-overrides.less';
import {Container} from 'typescript-ioc';
import {SocketConnector,} from '@au21-frontend/client-connector';
import {SocketConnectorMock} from '../../../../../libs/client-connector/src/lib/SocketConnectorMock';

Vue.config.productionTip = false
Vue.config.devtools = true
Vue.config.performance = true

Container.bind(SocketConnector).to(SocketConnectorMock);

new Vue({
  render: h => h(BookApp),
}).$mount('#app')

