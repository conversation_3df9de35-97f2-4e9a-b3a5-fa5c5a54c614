<template>
  <div class="MainApp" :style="`height: ${screen.layout_height}px`">
    <div
      class="_container"
      :style="`width: ${screen.app_width}px;`"
    >
      <PageLayout
        :isLoading="!hasInited"
        :store="store"
      >
        <LoginPage
          v-if="currentPage === null || currentPage === PageName.LOGIN_PAGE"
        />
        <HomePage
          v-if="currentPage === PageName.HOME_PAGE"
          :store="store"
        />
        <Credit
          v-if="currentPage === PageName.CREDITOR_AUCTIONEER_PAGE"
          :store="store"
        />
        <UserPage
          v-if="currentPage === PageName.USER_PAGE"
          :companies="store.live_store.companies"
          :users="store.live_store.users"
        />
        <DeAuctioneerPage
          v-if="currentPage === PageName.DE_AUCTIONEER_PAGE"
          :store="store"
        />
        <DeTraderPage
          v-if="currentPage === PageName.DE_TRADER_PAGE"
          :store="store"
        />
      </PageLayout>

      <div class="_footer">
        {{ footer }}
      </div>
    </div>

    <!-- Debug component (shown on VUE_APP_SHOW_DEBUG="true") -->
    <component v-if="debugComponent" :is="debugComponent"/>
    <AlertModal/>
    <ReloadModal/>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import LoginPage from '../../pages/common/LoginPage/LoginPage.vue';
import {CharlizeStore} from '../../services/connector/CharlizeStore';
import PageLayout from '../../pages/common/PageLayout/PageLayout.vue';
import HomePage from '../../pages/common/HomePage/HomePage.vue';
import Credit from '../../pages/common/CreditPage/CreditPage.vue';
import AppDebug from '../../ui-components/AppDebug/AppDebug.vue';
import AlertModal from '../../pages/common/components/AlertModal/AlertModal.vue';
import ReloadModal from '../../pages/common/components/ReloadModal/ReloadModal.vue';
import {
  BrowserMessageKind,
  PageName,
  ShowMessage,
  SocketConnector,
  TerminateSession
} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import DeAuctioneerPage from '../../pages/De/DeAuctioneer/DeAuctioneerAuctionPage.vue';
import DeTraderPage from '../../pages/De/DeTrader/DeTraderPage.vue';
import {CharlizeClient} from '../../services/connector/CharlizeClient';
import UserPage from '../../pages/common/UserPage/UserPage.vue';
import {AuScreen} from "../../plugins/screen-plugin/AuScreen";
import {applicationConfig} from "../../plugins/app-config/ApplicationConfig";
import {is_connected} from "../../services/helpers/common-helpers";

@Component({
  components: {
    UserPage,
    ReloadModal,
    AlertModal,
    AppDebug,
    Credit,
    LoginPage,
    HomePage,
    PageLayout,
    DeAuctioneerPage,
    DeTraderPage
  }
})
export default class MainApp extends Vue {
  client = Container.get(CharlizeClient);
  store = Container.get(CharlizeStore);
  connector = Container.get(SocketConnector);

  get screen():AuScreen {
    return new AuScreen(this.store?.live_store?.session_user?.isAuctioneer || false);
  }

  get footer() {
    return applicationConfig.footerText;
  }

  // get height() {
  //   return this.screen.page_height
  // }

  get currentPage(): PageName {
    return this.store.live_store?.session_user?.current_page;
  }

  get appWidthPx() {
    return this.screen.app_width + 'px';
  }

  get PageName() {
    return PageName;
  }

  get hasInited() {
    return is_connected(this.store?.live_store?.session_user);
  }

  get debugComponent() {
    if (process.env.VUE_APP_SHOW_DEBUG) {
      return require('../../ui-components/AppDebug/AppDebug.vue').default;
    }
  }

  created() {
    // this.connector.publish(session_create_command()) // NOW BEING HANDLED BY CONNECTOR
    this.client.subscribe('ShowMessage', this.showNotification);
    this.client.subscribe('TerminateSession', this.terminateSession);
  }

  beforeDestroy() {
    this.client.unsubscribe('ShowMessage', this.showNotification);
    this.client.unsubscribe('TerminateSession', this.terminateSession);
  }

  showNotification(event: ShowMessage): void {
    if (event.browser_message_kind === BrowserMessageKind.ALERT) {
      this.$modal.warning({
        title: '',
        content: event.message.join(', '),
        class: 'modal--error'
      });
    }

    if (event.browser_message_kind === BrowserMessageKind.NOTIFICATION) {
      this.$notification.open({
        message: '',
        description: event.message.join(', '),
        duration: 3,
        placement: 'topRight'
      });
    }
  }

  terminateSession(e: TerminateSession) {
    if (!e.message || e.message == 'SERVER_REBOOT') {
      // No message means log out.
      window.location.reload();
      return;
    }

    // If message is passed - we have to let user see it before we reload the page.
    const timeout = 10;
    const onConfirm = () => window.location.reload();

    this.$modal.warning({
      title: '',
      class: 'modal--error',
      content: e.message,
      maskClosable: false,
      onOk: onConfirm
    });

    // Reload anyway if user takes his time thinking.
    setTimeout(onConfirm, timeout * 1000);
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../au-styles/variables";

.MainApp {
  background-color: @main-color;
  margin: 0;
  padding: 0;

  ._container {
    margin-left: auto;
    margin-right: auto;
  }

  ._footer {
    color: #ccc;
    font-size: 12px;
    position: relative;
    text-align: center;
    top: 10px;
  }


}
</style>
