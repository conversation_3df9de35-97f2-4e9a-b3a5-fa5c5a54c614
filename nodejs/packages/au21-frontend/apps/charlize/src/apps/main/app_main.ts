import '../../polyfills/polyfills';
import Vue from 'vue';
import MainApp from './MainApp.vue';

import '../../au-styles/global-styles.less';
import '../../plugins/ag-grid/ag-grid-plugin';
import '../../plugins/ant-design-vue-plugin/ant-design-vue-plugin';
import '../../plugins/vue-quill-editor-plugin/quill-editor-plugin';
// import {MotionPlugin} from "@vueuse/motion";

Vue.config.productionTip = false;
Vue.config.devtools = true;
Vue.config.performance = true;

// currently not using:
// Vue.use(MotionPlugin)


const app = new Vue({
  render: h => h(MainApp)
});
app.$mount('#app');


if (module.hot) {
  //debugger
  app.$forceUpdate(); // TODO: trying suppress: "root or manually mounted instance modified. full reload required"

  // module.hot.accept();

  // module.hot.accept(() => {
  //   const data = module.hot.data
  //   console.log({data})
  //   debugger
  // });
}
