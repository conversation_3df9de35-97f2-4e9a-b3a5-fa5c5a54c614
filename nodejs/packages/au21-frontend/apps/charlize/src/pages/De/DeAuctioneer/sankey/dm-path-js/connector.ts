import {average} from './ops';
import Path from './path';

export default function (o: { start: number[]; end: number[] }) {
  const tension = 0.05
  const [a, b] = o.start
  const [c, d] = o.end
  const length = (c - a) * tension
  const mid1 = [a + length, b]
  //const mid2    = [c - length, d]

  return {
    path: Path()
      .moveto(a, b)
      .lineto(mid1[0], mid1[1])
      .curveto(a + 5 * length, b, c - 5 * length, d, c - length, d)
      .lineto(c, d),
    centroid: average([o.start, o.end]),
  }
}
