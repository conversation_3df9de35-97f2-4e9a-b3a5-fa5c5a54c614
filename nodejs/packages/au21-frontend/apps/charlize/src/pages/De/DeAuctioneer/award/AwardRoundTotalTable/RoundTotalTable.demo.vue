<template>
  <VbDemo>
    <VbCard style="width: 500px">
      <RoundTotalTable
        :roundResults="roundResults"
        :height="300"
        :selectedRound.sync="selectedRound"
      />
      <pre>selectedRound: {{ selectedRound }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import RoundTotalTable from './RoundTotalTable.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__CompanyElement} from '../../../../../demo-helpers/CompanyElement.helper';
import {createDemo__DeRoundResultVM} from '../../../../../demo-helpers/FlowScenarioRow.helper';

@Component({
  components: {RoundTotalTable},
})
export default class RoundTotalTableDemo extends Vue {
  selectedRound = null

  companies = createMultipleByClosure(createDemo__CompanyElement, 10)

  get companyIds(): string[] {
    return this.companies.map(company => company.company_id)
  }

  roundResults = [
    createDemo__DeRoundResultVM(0, this.companyIds),
    createDemo__DeRoundResultVM(1, this.companyIds),
    createDemo__DeRoundResultVM(2, this.companyIds),
  ]
}
</script>


