<template>
  <a-button
    class='ToolbarButton'
    @click="$emit('click')"
    :icon="icon"
    :disabled="disabled"
    :class="{'au-btn-disabled': disabled}"
    type="primary"
  >
    <!--    :style="`background-color: ${disabled ? 'hsl(77, 5%, 45%)':  'hsl(77, 10%, 70%)' }`"-->

    <slot/>
  </a-button>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  name: 'ToolbarButton',
})
export default class ToolbarButton extends Vue {
  @Prop({type: String}) icon: string
  @Prop({type: Boolean}) disabled: string

  colors = Container.get(AuColors)
}
</script>

<style lang="less" scoped>
@import './../../../../au-styles/variables.less';
.ToolbarButton{
  height: 30px;
  padding-left: 10px;
  padding-right: 10px;
}
</style>
