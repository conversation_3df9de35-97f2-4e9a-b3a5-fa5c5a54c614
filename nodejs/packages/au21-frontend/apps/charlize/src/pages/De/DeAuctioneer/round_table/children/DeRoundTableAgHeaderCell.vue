<template>
  <div class="DeRoundTableAgHeaderCell flex-center" @click="onClick()">
    {{ displayName }}

    <div v-if="is_trader_column">
      {{ sort_params.title }}
      <a-icon
        style="width: 12px; height: 12px"
        :type="sort_params.sort_direction === 'asc' ? 'down' : 'up'"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeRoundTableCellParams, DeRoundTableSortBy,} from '../de-round-table-ag-helpers';

@Component
export default class DeRoundTableAgHeaderCell extends Vue {
  params = null

  get displayName(): string {
    return this.params ? this.params.displayName : ''
  }

  get cellParams (): DeRoundTableCellParams {
    return this.params.column.colDef.cellRendererParams
  }

  get sort_params (): DeRoundTableSortBy {
    return this.cellParams.getSortBy()
  }

  get is_trader_column () {
    return this.params.column.colDef.headerName === 'Traders'
  }

  onClick(): void {
    if (this.is_trader_column) {
      this.cellParams.onTradersClick()
      return
    }
    this.cellParams.onRoundClick()
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.DeRoundTableAgHeaderCell {
  display: flex;
  cursor: pointer;
  justify-content: space-around;
  width: 100%;
  height: 100%;
}
</style>
