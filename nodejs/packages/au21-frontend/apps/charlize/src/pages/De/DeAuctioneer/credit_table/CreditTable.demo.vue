<template>
  <VbDemo>
    <VbCard title="default">
      <CreditTable
        :companies="companies"
        :counterparty_credits="counterpartyCredits"
        :height="400"
        :width="400"
      />
    </VbCard>
    <VbCard title="no credits">
      <CreditTable
        :companies="companies"
        :counterparty_credits="[]"
        :height="400"
        :width="400"
      />
    </VbCard>
    <VbCard title="not editable">
      <CreditTable
        :companies="companies"
        :counterparty_credits="[]"
        :height="400"
        :width="400"
        :editable="false"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import CreditTable from './CreditTable.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';
import {
  createDemo__CounterpartyCreditElementForCompanies
} from '../../../../demo-helpers/CounterpatryCreditElement.helper';

@Component({
  components: {CreditTable: CreditTable},
})
export default class CreditTableDemo extends Vue {
  companies = createMultipleByClosure(createDemo__CompanyElement, 20)
  counterpartyCredits = createDemo__CounterpartyCreditElementForCompanies(this.companies)
}
</script>
