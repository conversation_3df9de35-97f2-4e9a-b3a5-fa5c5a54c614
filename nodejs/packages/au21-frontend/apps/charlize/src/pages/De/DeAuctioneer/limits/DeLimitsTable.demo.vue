<template>
    <VbDemo>
        <VbCard>
            <DeLimitsTable
              :height="400"
              :width="400"
              :round_trader_elements="round_trader_elements"
              :round_number="round_number"
              :readonly="readonly"
              :quantity_label="quantity_label"
              @allow="$vb.log('allow', $event)"
              @save="$vb.log('save', $event)"/>
        </VbCard>
    </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeLimitsTable from './DeLimitsTable.vue';

@Component({
  components: { DeLimitsTable },
})
export default class DeLimitsTableDemo extends Vue {
  round_number = 1
  round_trader_elements = [] // TODO
  readonly = false
  quantity_label = 'MMlb'
}
</script>
