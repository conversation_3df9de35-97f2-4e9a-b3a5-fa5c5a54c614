<template>
  <div class="ActivityFeedbackRuleCard pl-3">
    <ul>
      <li>Traders will not be able to see each others orders.</li>
      <li>
        However at the end of each round, the difference between supply
        and demand will be shown as follows:

        <table style="width: 400px; text-align: center">
          <tr>
            <th>Absolute supply-demand difference</th>
            <th>Feedback</th>
          </tr>
          <tr v-for="n in [4,3,2,1]" :key="n">
            <td>
              > {{ de_settings_value[`excess_level_${n}_quantity`] }}
              {{ de_settings_value.quantity_label }}
            </td>
            <td>
              {{ de_settings_value[`excess_level_${n}_label`] }}
            </td>
          </tr>
          <tr>
            <td>
              0 - {{ minExcessValue }}
              {{ de_settings_value.quantity_label }}
            </td>
            <td>
              {{ de_settings_value.excess_level_0_label }}
            </td>
          </tr>
        </table>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeSettingsValue} from '@au21-frontend/client-connector';
import NumberInput from '../../../../../../../ui-components/NumberInput/NumberInput.vue';
import AuInput from '../../../../../../../ui-components/AuInput/AuInput.vue';

@Component({
  name: 'ActivityFeedbackRuleCard',
  components: { AuInput, NumberInput },
})
export default class ActivityFeedbackRuleCard extends Vue {
  @Prop() de_settings_value: DeSettingsValue;

  get minExcessValue (): string {
    return this.de_settings_value.excess_level_1_quantity;
  }
}
</script>

<style lang="less" scoped>
.ActivityFeedbackRuleCard {

}
</style>
