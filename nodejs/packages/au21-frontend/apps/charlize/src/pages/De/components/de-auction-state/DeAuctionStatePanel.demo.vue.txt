<template>
  <VbDemo>
    <VbCard>
      <div
        v-for="status in statusOptions"
        :key="status"
      >
        <br>
        {{ status }}
        <DeAuctionStatePanel
          :commonStatus="{
            ...commonStatus,
            auction_state: status,
          }"
          :auctioeer-info="auctioneerStatus"
          color="white"
        />
      </div>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import DeAuctionStatePanel from './DeAuctionStatePanel.vue';
import { createDemo__DeCommonStatusValue } from '../../../../demo-helpers/DeCommonStatusValue.helper';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import { DeAuctionState } from '../../../../../../../libs/client-connector/src';

@Component({
  components: { AuSelect, DeAuctionStatePanel },
})
export default class DeAuctionStatePanelDemo extends Vue {
  commonStatus = createDemo__DeCommonStatusValue()
  statusOptions = Object.values(DeAuctionState)
  auctioneerStatus = {}
}
</script>
