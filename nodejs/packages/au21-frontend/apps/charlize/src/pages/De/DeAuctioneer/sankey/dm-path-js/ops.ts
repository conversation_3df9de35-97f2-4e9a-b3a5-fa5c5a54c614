const sum = xs => xs.reduce((a, b) => a + b, 0)

const min = xs => xs.reduce((a, b) => Math.min(a, b))

const max = xs => xs.reduce((a, b) => Math.max(a, b))

const sumBy = (xs, f) => xs.reduce((a, b) => a + f(b), 0)

const minBy = (xs, f) => xs.reduce((a, b) => Math.min(a, f(b)), Infinity)

const maxBy = (xs, f) => xs.reduce((a, b) => Math.max(a, f(b)), -Infinity)

const plus = ([a, b], [c, d]) => [a + c, b + d]

const minus = ([a, b], [c, d]) => [a - c, b - d]

const times = (k, [a, b]) => [k * a, k * b]

const length = ([a, b]) => Math.sqrt(a * a + b * b)

const sumVectors = xs => xs.reduce(plus, [0, 0])

const average = points => times(1 / points.length, points.reduce(plus))

const onCircle = (r, angle) => times(r, [Math.sin(angle), -Math.cos(angle)])

const enhance = (compute, curve) => {
  const obj = compute || {}
  for (const key in obj) {
    const method = obj[key]
    curve[key] = method(curve.index, curve.item, curve.group)
  }
  return curve
}

const range = (a, b, inclusive) => {
  const result = []
  for (let i = a; i < b; i++) {
    result.push(i)
  }
  if (inclusive) {
    result.push(b)
  }
  return result
}

const mapObject = (obj, f) => {
  const result = []
  for (const k of Object.keys(obj)) {
    const v = obj[k]
    result.push(f(k, v))
  }
  return result
}

const pairs = obj => mapObject(obj, (k, v) => [k, v])

const id = x => x

export {
  sum,
  min,
  max,
  sumBy,
  minBy,
  maxBy,
  plus,
  minus,
  times,
  id,
  length,
  sumVectors,
  average,
  onCircle,
  enhance,
  range,
  mapObject,
  pairs,
}

export default {
  sum,
  min,
  max,
  sumBy,
  minBy,
  maxBy,
  plus,
  minus,
  times,
  id,
  length,
  sumVectors,
  average,
  onCircle,
  enhance,
  range,
  mapObject,
  pairs,
}
