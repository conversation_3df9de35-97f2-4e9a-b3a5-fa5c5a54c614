<template>
  <div class="RulesOverviewCard pl-3">
    <ul>
      <li>
        The Auction proceeds in a series of rounds.
      </li>
      <li>
        At the start of each round the auctioneer announces a <b>Round Price</b>.
      </li>
      <li>
        Traders submit the quantity they wish to buy or sell at the current round
        price.
      </li>
      <li>
        These quantities are subject to the <b>Quantity Constraints Rule</b> as
        described later.
      </li>
      <li>
        Note: Traders never submit prices, rather they submit quantities at the
        current round price.
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({
  name: 'RulesOverviewCard',
})
export default class RulesOverviewCard extends Vue {

}
</script>

<style lang="less" scoped>
.RulesOverviewCard {

}
</style>
