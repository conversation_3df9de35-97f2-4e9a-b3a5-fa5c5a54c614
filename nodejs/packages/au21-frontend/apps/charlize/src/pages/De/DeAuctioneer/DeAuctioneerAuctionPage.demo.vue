<template>
  <VbDemo>
    <VbCard title="setup">
      <DeAuctioneerAuctionPage :store="store"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {CharlizeStore} from "../../../services/connector/CharlizeStore";
import {CompanyElement, UserElement} from "@au21-frontend/client-connector";
import DeAuctioneerAuctionPage from "./DeAuctioneerAuctionPage.vue";
import {createDemo__CompanyElement} from "../../../demo-helpers/CompanyElement.helper";
import {createDemo__UserElement_for_trader} from "../../../demo-helpers/UserElement.helper";
import {range} from "lodash";
import {createDemo__store_for_auctioneer} from "../../../demo-helpers/CharlizeStore.helper";

@Component({
  components: {DeAuctioneerAuctionPage},
})
export default class DeAuctioneerAuctionPageDemo extends Vue {
  companies: CompanyElement[] =
    range(1, 5).map(i => createDemo__CompanyElement('trader ' + i))

  users: UserElement[] = this.companies.map(c =>
    createDemo__UserElement_for_trader(c.company_id, 'user-' + c.company_id))

  store: CharlizeStore = createDemo__store_for_auctioneer(this.companies, this.users)
}
</script>
