<template>
  <AwardMatchesTable
    :matches="matches"
    :height="300"
  />
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AwardMatchesTable from './AwardMatchesTable.vue';
import {random_number_string} from '@au21-frontend/utils';
import {AwardMatchesTableMatch} from './AwardMatchesTable.types';
import {createDemo__DeScenarioMatchVM} from '../../../../demo-helpers/FlowScenarioRow.helper';
import {DeScenarioMatchVM} from '@au21-frontend/client-connector';

const createMatch = (): AwardMatchesTableMatch => ({
  id: random_number_string(),
  buyer: 'b_' + random_number_string(),
  seller: 'a_' + random_number_string(),
  quantity: random_number_string(),
  value: `${random_number_string()}.00$`,
})

@Component({
  components: {AwardMatchesTable},
})
export default class AwardMatchesTableDemo extends Vue {
  // TODO Replace with better data
  matches: DeScenarioMatchVM[] = [
    createDemo__DeScenarioMatchVM('one', 'two'),
    createDemo__DeScenarioMatchVM('one', 'three'),
    createDemo__DeScenarioMatchVM('two', 'one'),
    createDemo__DeScenarioMatchVM('two', 'three'),
    createDemo__DeScenarioMatchVM('three', 'one'),
    createDemo__DeScenarioMatchVM('three', 'two'),
  ]
}
</script>
