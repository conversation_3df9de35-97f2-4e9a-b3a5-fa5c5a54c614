<template>

  <div class="DeRoundTableAgBodyCell">

    <div v-if="rowType === 'PRICE'" style="text-align: center;">
      <!--    <div style="display: inline-block; width: 13px;">-->
      <!--      <PriceArrow :price_direction="priceDirection" />-->
      <!--    </div>-->
      <PriceFormatter
        :price="round_price_str"
        :price_direction="priceDirection"
        :integer_font_size="14"
        :integer_bold="false"
        :fraction_font_size="13"
        :fraction_bold="true"
      />
    </div>

    <BuySellVBar
      v-else-if="rowType === 'TRADER' && cellElement"
      :width="width"
      :height="21"
      :buyMax="buyMax"
      :sellMax="sellMax"
      :match="cellElement.match"
      :order_quantity_int='cellElement.quantity_int'
      :order_quantity_str='cellElement.quantity_str'
      :order_type="cellElement.order_type"
      :order_submission_type="cellElement.order_submission_type"
    />

    <Blinker
      background
      :value="excess_level"
      v-else-if="rowType === 'EXCESS_DEMAND'"
      style="width: 100%; text-align:center; color: yellow;"
    />

    <DeRoundTotalCell
      v-else-if="rowType === 'FOOTER'"
      :round="roundElement"
      :maxValue="footerMaxValue"
    />

  </div>

</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeBlotterRowAg, DeRoundTableAgRowType} from '../de-round-table-ag-helpers';
import {
  DeRoundElement,
  DeRoundTraderElement,
  OrderSubmissionType,
  OrderType,
  PriceDirection
} from '@au21-frontend/client-connector';
import type DeRoundTable from '../DeRoundTable.vue';
import BuySellVBar from '../../../../common/components/BuySellBars/BuySellVBar.vue';
import Blinker from '../../../../../ui-components/Blinker/Blinker.vue';
import PriceArrow from '../../../../../ui-components/PriceArrow.vue';
import {Container} from 'typescript-ioc';
import {AuColors} from 'apps/charlize/src/au-styles/AuColors';
import PriceFormatter from '../../../../../ui-components/PriceFormatter/PriceFormatter.vue';
import DeRoundTotalCell from "./DeRoundTotalCell.vue";

@Component({
  components: {
    Blinker,
    BuySellVBar,
    DeRoundTotalCell,
    PriceArrow,
    PriceFormatter,
  }
})
export default class DeRoundTableAgBodyCell extends Vue {
  params = null;
  width = 70;

  PriceDirection = PriceDirection;
  OrderType = OrderType;
  OrderSubmissionType = OrderSubmissionType;

  colors = Container.get(AuColors);

  get row(): DeBlotterRowAg {
    return this.params.data;
  }

  get roundTable(): DeRoundTable {
    return this.$parent.$parent.$parent as DeRoundTable;
  }

  // get rounds(): DeRoundElement[] {
  //   return (this.$parent.$parent.$parent as DeRoundTable).rounds;
  // }

  get rowType(): DeRoundTableAgRowType {
    return this.row.rowType;
  }

  get roundNumber(): number {
    return +this.params.colDef.headerName;
  }

  get excess_side(): string | null {
    return this.roundElement?.excess_side;
  }

  get excess_level(): string | null {
    return this.roundElement?.excess_indicator;
  }

  // PRICE

  get round_price_str(): string {
    return this.roundElement?.round_price_str || ''
  }

  get roundElement(): DeRoundElement {
    return this.roundTable.blotter.rounds[this.roundNumber - 1];
  }

  get priceDirection(): PriceDirection {
    return this.roundElement?.round_direction;
  }

  // TRADER

  get cells(): DeRoundTraderElement[] {
    return this.roundTable.blotter.round_traders;
  }

  get cellElement(): DeRoundTraderElement | null {
    return this.row.cells[this.roundNumber - 1];
  }

  get buyMax(): number {
    return this.roundTable.buyMax;
  }

  get sellMax(): number {
    return this.roundTable.sellMax;
  }

  // FOOTER

  get footerMaxValue(): number {
    return this.roundTable.maxValue;
  }

}
</script>


<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.DeRoundTableAgBodyCell {
  border: 0;
  bottom: -1px;
  font-size: 14px;
  left: -1px;
  margin: 0;
  padding: 0;
  position: relative;
  right: 0;
}

</style>
