<template>
  <div class="BuySellSwitchingCard pl-3"
       @click="click">
    <ul>
      <VueTextTransition tag="li" name="fade" :show="count > 0" :interval="1">
        Buyer cannot switch to Seller at a lower price.
      </VueTextTransition>
      <VueTextTransition tag="li" name="fade" :show="count > 1" :interval="1">
        Se<PERSON> cannot switch to Buyer buyer at a higher price.
      </VueTextTransition>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import VueTextTransition from 'vue-text-transition'

Vue.component('VueTextTransition', VueTextTransition)


@Component({
  name: 'QuantityConstraintsCard',
})
export default class QuantityConstraintsCard extends Vue {
  count = 0

  click() {
    this.count++
    console.log({count: this.count})
  }
}
</script>

<style lang="less">
.QuantityConstraintsCard {

}

.v--vtt-fade {
  will-change: opacity;
  transition: opacity 0.1s ease-in-out;
}

.v--vtt-fade_visible {
  opacity: 1;
}

.v--vtt-fade_hidden {
  opacity: 0.38;
}
</style>
