<template>
  <VbDemo>
    <VbCard>
      <DeAuctioneerClock
        :de_settings="de_settings"
        :auctioneer_info="auctioneerInfo"
        :auctioneer_status="auctioneerStatus"
        :common_status="commonStatus"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createDemo__DeCommonStatusValue} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import {DeAuctioneerInfoValue, DeAuctioneerStatusValue, DeSettingsValue} from '@au21-frontend/client-connector';
import {createDemo__DeAuctioneerStatusValue} from '../../../../demo-helpers/DeAuctioneerStatusValue.helper';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';
import {createDemo__DeAuctioneerInfoValue} from '../../../../demo-helpers/DeAuctioneerInfoValue.helper';
import DeAuctioneerClock from './DeAuctioneerClock.vue';

@Component({
  components: {DeAuctioneerClock},
})
export default class DeAuctioneerClockDemo extends Vue {
  commonStatus = createDemo__DeCommonStatusValue();
  auctioneerStatus: DeAuctioneerStatusValue = createDemo__DeAuctioneerStatusValue();
  auctioneerInfo: DeAuctioneerInfoValue = createDemo__DeAuctioneerInfoValue();
  de_settings: DeSettingsValue = createDefault__DeSettingsValue();
}
</script>
