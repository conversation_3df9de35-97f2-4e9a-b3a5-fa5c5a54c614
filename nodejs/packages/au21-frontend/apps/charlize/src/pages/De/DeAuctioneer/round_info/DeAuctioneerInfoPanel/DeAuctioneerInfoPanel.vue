<template>
  <div
    class="DeAuctioneerInfoPanel"
    :class="auctioneer_state"
  >
    <div
      v-if="auction_not_started"
      style="color: white"
      class="_box"
    >
      <div>Auction</div>
      <div>not</div>
      <div>Started</div>
    </div>
    <div v-else-if="auctioneer_state === auctioneer_states.ROUND_OPEN_ALL_ORDERS_NOT_IN"
         class="_box">
      <div>Round open</div>
      <div>waiting</div>
      <div>for bids</div>
    </div>
    <div v-else-if="auctioneer_state === auctioneer_states.ROUND_OPEN_ALL_ORDERS_IN"
         class="_box">
      <div>Round open</div>
      <div>all bids</div>
      <div>received</div>
    </div>
    <div v-else-if="auctioneer_state === auctioneer_states.ROUND_CLOSED_NOT_AWARDABLE"
         class="_box">
      <div>Round closed</div>
      <div>not</div>
      <div>awardable</div>
    </div>
    <div v-if="auctioneer_state === auctioneer_states.ROUND_CLOSED_AWARDABLE"
         class="_box">
      <div>Round closed</div>
      <div style="margin-top: 6px">awardable</div>
    </div>
    <div
      v-else-if="auctioneer_state === auctioneer_states.AUCTION_CLOSED"
      style="color: white"
      class="_box"
    >
      <div style="height: 7px">&nbsp;</div>
      <div>Auction</div>
      <div>Closed</div>
    </div>
  </div>
</template>
<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeAuctioneerState, DeCommonState} from '@au21-frontend/client-connector';
import chroma from 'chroma-js';
import ThreeStateDisplay from '../../../../common/components/ThreeStateDisplay/ThreeStateDisplay.vue';
import {has_de_auction_started} from '../../../components/de-utils';
import {Container} from 'typescript-ioc';
import {AuColors} from '../../../../../au-styles/AuColors';

@Component({
  components: {ThreeStateDisplay}
})
export default class DeAuctioneerInfoPanel extends Vue {

  @Prop({required: true}) auctioneer_state: DeAuctioneerState;

  auctioneer_states = DeAuctioneerState;
  trader_states = DeCommonState;

  colors = Container.get(AuColors);
  auRBGScale = chroma.scale(['#0f0', '#F00']).mode('lrgb');
  disabled_color = '#444';

  // colors: { [key in DeAuctioneerState]: string } = {
  //   STARTING_PRICE_NOT_SET: this.disabled_color,
  //   STARTING_PRICE_SET: this.disabled_color,
  //   STARTING_PRICE_ANNOUNCED: this.disabled_color,
  //   ROUND_OPEN_ALL_ORDERS_NOT_IN: this.colors.NORMAL,
  //   ROUND_OPEN_ALL_ORDERS_IN: this.colors.WARNING,
  //   ROUND_CLOSED_NOT_AWARDABLE: this.colors.NORMAL,
  //   ROUND_CLOSED_AWARDABLE: this.colors.ERROR,
  //   AUCTION_CLOSED: this.disabled_color
  // };

  // colors: { [key in DeAuctioneerState]: string } = {
  //   STARTING_PRICE_NOT_SET: this.disabled_color,
  //   STARTING_PRICE_SET: this.disabled_color,
  //   STARTING_PRICE_ANNOUNCED: this.disabled_color,
  //   ROUND_OPEN_ALL_ORDERS_NOT_IN: this.colors.NORMAL,
  //   ROUND_OPEN_ALL_ORDERS_IN: this.colors.WARNING,
  //   ROUND_CLOSED_NOT_AWARDABLE: this.colors.NORMAL,
  //   ROUND_CLOSED_AWARDABLE: this.colors.logo_orange, //this.colors.logo_orange,
  //   AUCTION_CLOSED: this.disabled_color
  // };

  // get bgColor(): string {
  //   return ({
  //     STARTING_PRICE_NOT_SET: 'price_not_set', //this.disabled_color,
  //     STARTING_PRICE_SET: 'price_set', // this.disabled_color,
  //     STARTING_PRICE_ANNOUNCED: 'price_announced', // this.disabled_color,
  //     ROUND_OPEN_ALL_ORDERS_NOT_IN: 'round_open_all_orders_not_it, // this.colors.auctioneer_state_round_open_waiting_for_bids,
  //     ROUND_OPEN_ALL_ORDERS_IN: 'round_open_all_orders_in', // this.colors.WARNING,
  //     ROUND_CLOSED_NOT_AWARDABLE: 'round_closed_not_awardable', // this.colors.NORMAL,
  //     ROUND_CLOSED_AWARDABLE: 'round_closed_awardable', // this.colors.auHighlight, //this.colors.logo_orange,
  //     AUCTION_CLOSED: 'auction_closed', // this.disabled_color
  //   } as {[key in DeAuctioneerState]: string})[this.auctioneer_state];
  // }

  get auction_not_started(): boolean {
    return has_de_auction_started(this.auctioneer_state) === false;
  }
}
</script>

<style lang="less" scoped>
.DeAuctioneerInfoPanel {
  border-radius: 5px;
  //border: 1px solid red; // #666;
  color: black;
  font-size: 11px;
  font-weight: bold;
  height: 40px;
  line-height: 1.1em;
  margin: 0;
  overflow: hidden;
  padding: 3px;
  text-align: center;
  // width: 120px !important;

}

.STARTING_PRICE_NOT_SET {
  background-color: hsl(30, 20%, 70%); // hsl(327, 30%, 60%);
}

.STARTING_PRICE_SET {
  background-color: hsl(30, 20%, 70%); // hsl(46, 30%, 60%);
}

.STARTING_PRICE_ANNOUNCED {
  background-color: hsl(30, 20%, 70%); //  hsl(154, 30%, 60%);
}

.ROUND_OPEN_ALL_ORDERS_NOT_IN {
  background-color: hsl(30, 20%, 70%); //  hsl(46, 30%, 60%);
}

.ROUND_OPEN_ALL_ORDERS_IN {
  background-color: hsl(30, 20%, 70%); // hsl(154, 30%, 60%);
}

.ROUND_CLOSED_NOT_AWARDABLE {
  background-color: hsl(30, 20%, 70%); //  hsl(30, 30%, 60%);
}

.ROUND_CLOSED_AWARDABLE {
  background-color: hsl(30, 20%, 70%); //  hsl(327, 30%, 60%);
}

.AUCTION_CLOSED {
  background-color: hsl(30, 20%, 70%);
}

</style>
