<template>
  <VbDemo>
    <VbCard>
      <DeOrderBook
        :height="500"
        :round_trader_elements="cells"
        :companies="companies"
        :quantity_label="deSettings.quantity_label"
      />
      <pre>{{ cells }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeOrderBook from './DeOrderBook.vue';
import {createDemo__DeRoundTraderElement} from '../../../../demo-helpers/DeRoundTable.helper';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';

@Component({
  components: {DeOrderBook},
})
export default class DeOrderBookDemo extends Vue {
  companies = createMultipleByClosure(createDemo__CompanyElement, 10)
  cells = this.companies.map(company => createDemo__DeRoundTraderElement(1, company))
  deSettings = createDefault__DeSettingsValue()
}
</script>
