<template>
  <VbDemo>
    <VbCard>
      <DeAuctionRules :de_settings_value="de_settings_value" />
    </VbCard>
  </VbDemo>
</template>
<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeAuctionRules from './DeAuctionRules.vue';
import {createDefault__DeSettingsValue,} from '../../../../../../demo-helpers/DeSettingsValue.helper';

@Component({
  name: 'DeAuctionRulesDemo',
  components: {DeAuctionRules}
})
export default class DeAuctionRulesDemo extends Vue {
  de_settings_value = createDefault__DeSettingsValue()
}
</script>

