<template>
  <Blinker
    :value="blinkerValue"
    background
    v-if="buyer"
  >
    <DeMatrixDataPanel
      class="DeMatrixLeftCellParams"
      :shortname="buyer.shortname"
      label_max="max"
      label_hatched="order"
      label_solid="match"
      :vol_max="buyer.buy_max"
      :vol_hatched="buyer.buy_quantity"
      :vol_solid="buyer.buy_match"
      :au_text_color="colors.au_buy()"
      :bar_color="colors.au_buy()"
    />
  </Blinker>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeMatrixEdgeElement, DeMatrixNodeElement} from '@au21-frontend/client-connector';
import {TraderMatrixRow} from './DeMatrixRows';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {Container} from 'typescript-ioc';
import {AuColors} from 'apps/charlize/src/au-styles/AuColors';
import VolumeBar from '../../../../ui-components/svg/VolumeBar.vue';
import DeMatrixDataPanel from './DeMatrixDataPanel.vue';

export type DeMatrixLeftCellParams = {
  getBuyer: (edges: DeMatrixEdgeElement[]) => DeMatrixNodeElement | null
}

@Component({
  name: 'DeMatrixLeftCell',
  components: {DeMatrixDataPanel, VolumeBar, Blinker}
})
export default class DeMatrixLeftCell extends Vue {

  params = null;
  colors = Container.get(AuColors);

  get row(): TraderMatrixRow {
    return this.params.data;
  }

  get buyer(): DeMatrixNodeElement {
    return (this.params.column.colDef.cellRendererParams as DeMatrixLeftCellParams).getBuyer(this.row.edges);
  }

  get blinkerValue() {
    return this.buyer;
  }
}
</script>


<style lang="less" scoped>
.DeMatrixLeftCellParams {
  overflow: hidden;
}
</style>
