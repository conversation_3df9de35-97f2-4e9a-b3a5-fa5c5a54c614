<template>
  <VbDemo>
    <VbCard dark>
      <DeRoundController
        v-model="currentRound"
        :lastRound="lastRound"
      />
      <br>
      <pre>
        currentRound: {{ currentRound }}
        lastRound: {{ lastRound }}
      </pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeRoundController from './DeRoundController.vue';

@Component({
  components: {DeRoundController},
})
export default class DeRoundControllerDemo extends Vue {
  currentRound = 2
  lastRound = 4
}
</script>
