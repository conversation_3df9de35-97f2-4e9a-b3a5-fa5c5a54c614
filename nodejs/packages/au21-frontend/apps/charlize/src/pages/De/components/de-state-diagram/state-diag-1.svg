<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.2" width="177.8mm" height="76.2mm" viewBox="0 0 17780 7620" preserveAspectRatio="xMidYMid"
     fill-rule="evenodd" stroke-width="28.222" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"
     xmlns:ooo="http://xml.openoffice.org/svg/export"
     xml:space="preserve">
 <defs class="ClipPathGroup">
  <clipPath id="presentation_clip_path" clipPathUnits="userSpaceOnUse">
   <rect x="0" y="0" width="17780" height="7620"/>
  </clipPath>
   <clipPath id="presentation_clip_path_shrink" clipPathUnits="userSpaceOnUse">
   <rect x="17" y="7" width="17745" height="7605"/>
  </clipPath>
 </defs>
  <defs>
  <font id="EmbeddedFont_1" horiz-adv-x="2048">
   <font-face font-family="Liberation Sans embedded" units-per-em="2048" font-weight="bold" font-style="normal"
              ascent="1863" descent="445"/>
    <missing-glyph horiz-adv-x="2048" d="M 0,0 L 2047,0 2047,2047 0,2047 0,0 Z"/>
    <glyph unicode="w" horiz-adv-x="1631"
           d="M 1313,0 L 1016,0 844,660 C 836,690 820,764 797,882 L 745,658 571,0 274,0 -6,1082 258,1082 436,255 450,329 475,446 645,1082 946,1082 1112,446 C 1121,411 1135,348 1153,255 L 1181,387 1337,1082 1597,1082 1313,0 Z"/>
    <glyph unicode="u" horiz-adv-x="996"
           d="M 408,1082 L 408,475 C 408,285 472,190 600,190 668,190 723,219 765,278 806,336 827,411 827,502 L 827,1082 1108,1082 1108,242 C 1108,150 1111,69 1116,0 L 848,0 C 840,96 836,168 836,215 L 831,215 C 794,133 746,73 689,36 631,-1 562,-20 483,-20 368,-20 280,15 219,86 158,156 127,259 127,395 L 127,1082 408,1082 Z"/>
    <glyph unicode="t" horiz-adv-x="657"
           d="M 420,-18 C 337,-18 274,5 229,50 184,95 162,163 162,254 L 162,892 25,892 25,1082 176,1082 264,1336 440,1336 440,1082 645,1082 645,892 440,892 440,330 C 440,277 450,239 470,214 490,189 521,176 563,176 585,176 616,181 657,190 L 657,16 C 588,-7 509,-18 420,-18 Z"/>
    <glyph unicode="s" horiz-adv-x="995"
           d="M 1055,316 C 1055,211 1012,129 927,70 841,10 722,-20 571,-20 422,-20 309,4 230,51 151,98 98,171 72,270 L 319,307 C 333,256 357,219 392,198 426,177 486,166 571,166 650,166 707,176 743,196 779,216 797,247 797,290 797,325 783,352 754,373 725,393 675,410 606,424 447,455 340,485 285,512 230,539 188,574 159,617 130,660 115,712 115,775 115,878 155,959 235,1017 314,1074 427,1103 573,1103 702,1103 805,1078 884,1028 962,978 1011,906 1030,811 L 781,785 C 773,829 753,862 722,884 691,905 641,916 573,916 506,916 456,908 423,891 390,874 373,845 373,805 373,774 386,749 412,731 437,712 480,697 541,685 626,668 701,650 767,632 832,613 885,591 925,566 964,541 996,508 1020,469 1043,429 1055,378 1055,316 Z"/>
    <glyph unicode="r" horiz-adv-x="657"
           d="M 143,0 L 143,828 C 143,887 142,937 141,977 139,1016 137,1051 135,1082 L 403,1082 C 405,1070 408,1034 411,973 414,912 416,871 416,851 L 420,851 C 447,927 472,981 493,1012 514,1043 540,1066 569,1081 598,1096 635,1103 679,1103 715,1103 744,1098 766,1088 L 766,853 C 721,863 681,868 646,868 576,868 522,840 483,783 444,726 424,642 424,531 L 424,0 143,0 Z"/>
    <glyph unicode="p" horiz-adv-x="1059"
           d="M 1167,546 C 1167,365 1131,226 1059,128 986,29 884,-20 752,-20 676,-20 610,-3 554,30 497,63 454,110 424,172 L 418,172 C 422,152 424,91 424,-10 L 424,-425 143,-425 143,833 C 143,935 140,1018 135,1082 L 408,1082 C 411,1070 414,1046 417,1011 419,976 420,941 420,906 L 424,906 C 487,1039 603,1105 770,1105 896,1105 994,1057 1063,960 1132,863 1167,725 1167,546 Z M 874,546 C 874,789 800,910 651,910 576,910 519,877 480,812 440,747 420,655 420,538 420,421 440,331 480,268 519,204 576,172 649,172 799,172 874,297 874,546 Z"/>
    <glyph unicode="o" horiz-adv-x="1122"
           d="M 1171,542 C 1171,367 1122,229 1025,130 928,30 793,-20 621,-20 452,-20 320,30 224,130 128,230 80,367 80,542 80,716 128,853 224,953 320,1052 454,1102 627,1102 804,1102 939,1054 1032,958 1125,861 1171,723 1171,542 Z M 877,542 C 877,671 856,764 814,822 772,880 711,909 631,909 460,909 375,787 375,542 375,421 396,330 438,267 479,204 539,172 618,172 791,172 877,295 877,542 Z"/>
    <glyph unicode="n" horiz-adv-x="1017"
           d="M 844,0 L 844,607 C 844,797 780,892 651,892 583,892 528,863 487,805 445,746 424,671 424,580 L 424,0 143,0 143,840 C 143,898 142,946 141,983 139,1020 137,1053 135,1082 L 403,1082 C 405,1069 408,1036 411,981 414,926 416,888 416,867 L 420,867 C 458,950 506,1010 563,1047 620,1084 689,1103 768,1103 883,1103 971,1068 1032,997 1093,926 1124,823 1124,687 L 1124,0 844,0 Z"/>
    <glyph unicode="m" horiz-adv-x="1589"
           d="M 780,0 L 780,607 C 780,797 725,892 616,892 559,892 513,863 478,805 442,747 424,672 424,580 L 424,0 143,0 143,840 C 143,898 142,946 141,983 139,1020 137,1053 135,1082 L 403,1082 C 405,1069 408,1036 411,981 414,926 416,888 416,867 L 420,867 C 455,950 498,1010 550,1047 601,1084 663,1103 735,1103 900,1103 1001,1024 1036,867 L 1042,867 C 1079,951 1123,1011 1174,1048 1225,1085 1291,1103 1370,1103 1475,1103 1556,1067 1611,996 1666,924 1694,821 1694,687 L 1694,0 1415,0 1415,607 C 1415,797 1360,892 1251,892 1196,892 1152,866 1117,813 1082,760 1062,686 1059,593 L 1059,0 780,0 Z"/>
    <glyph unicode="l" horiz-adv-x="319" d="M 143,0 L 143,1484 424,1484 424,0 143,0 Z"/>
    <glyph unicode="i" horiz-adv-x="319"
           d="M 143,1277 L 143,1484 424,1484 424,1277 143,1277 Z M 143,0 L 143,1082 424,1082 424,0 143,0 Z"/>
    <glyph unicode="g" horiz-adv-x="1059"
           d="M 596,-434 C 464,-434 358,-409 278,-359 197,-308 148,-236 129,-143 L 410,-110 C 420,-153 442,-187 475,-212 508,-237 551,-249 604,-249 682,-249 739,-225 775,-177 811,-129 829,-58 829,37 L 829,94 831,201 829,201 C 767,68 651,2 481,2 355,2 257,49 188,144 119,239 84,374 84,550 84,727 120,863 191,959 262,1055 366,1103 502,1103 659,1103 768,1038 829,908 L 834,908 C 834,931 836,963 839,1003 842,1043 845,1069 848,1082 L 1114,1082 C 1110,1010 1108,927 1108,832 L 1108,33 C 1108,-121 1064,-237 977,-316 890,-395 763,-434 596,-434 Z M 831,556 C 831,667 811,754 772,817 732,879 675,910 602,910 452,910 377,790 377,550 377,315 451,197 600,197 675,197 732,228 772,291 811,353 831,441 831,556 Z"/>
    <glyph unicode="e" horiz-adv-x="1017"
           d="M 586,-20 C 423,-20 298,28 211,125 124,221 80,361 80,546 80,725 124,862 213,958 302,1054 427,1102 590,1102 745,1102 864,1051 946,948 1028,845 1069,694 1069,495 L 1069,487 375,487 C 375,382 395,302 434,249 473,195 528,168 600,168 699,168 762,211 788,297 L 1053,274 C 976,78 821,-20 586,-20 Z M 586,925 C 520,925 469,902 434,856 398,810 379,746 377,663 L 797,663 C 792,750 771,816 734,860 697,903 648,925 586,925 Z"/>
    <glyph unicode="d" horiz-adv-x="1059"
           d="M 844,0 C 841,10 838,35 835,76 831,116 829,149 829,176 L 825,176 C 764,45 649,-20 479,-20 353,-20 256,29 187,128 118,226 84,363 84,540 84,719 120,858 193,956 265,1053 367,1102 500,1102 577,1102 643,1086 699,1054 754,1022 797,974 827,911 L 829,911 827,1089 827,1484 1108,1484 1108,236 C 1108,169 1111,91 1116,0 L 844,0 Z M 831,547 C 831,664 812,754 773,817 734,880 676,911 600,911 525,911 469,881 432,820 395,759 377,665 377,540 377,295 451,172 598,172 672,172 729,205 770,270 811,335 831,427 831,547 Z"/>
    <glyph unicode="c" horiz-adv-x="1038"
           d="M 594,-20 C 430,-20 303,29 214,127 125,224 80,360 80,535 80,714 125,853 215,953 305,1052 433,1102 598,1102 725,1102 831,1070 914,1006 997,942 1050,854 1071,741 L 788,727 C 780,782 760,827 728,860 696,893 651,909 592,909 447,909 375,788 375,546 375,297 449,172 596,172 649,172 694,189 730,223 766,256 788,306 797,373 L 1079,360 C 1069,286 1043,220 1000,162 957,104 900,59 830,28 760,-4 681,-20 594,-20 Z"/>
    <glyph unicode="b" horiz-adv-x="1059"
           d="M 1167,545 C 1167,366 1131,228 1060,129 988,30 885,-20 752,-20 675,-20 609,-3 553,30 497,63 454,111 424,174 L 422,174 C 422,151 421,119 418,78 415,37 411,11 408,0 L 135,0 C 140,62 143,144 143,247 L 143,1484 424,1484 424,1070 420,894 424,894 C 487,1033 603,1102 770,1102 898,1102 996,1054 1065,957 1133,860 1167,722 1167,545 Z M 874,545 C 874,668 856,759 820,818 784,877 728,907 653,907 577,907 519,875 480,812 440,748 420,656 420,536 420,421 440,332 479,268 518,204 575,172 651,172 800,172 874,296 874,545 Z"/>
    <glyph unicode="a" horiz-adv-x="1123"
           d="M 393,-20 C 288,-20 207,9 148,66 89,123 60,203 60,306 60,418 97,503 170,562 243,621 348,651 487,652 L 720,656 720,711 C 720,782 708,834 683,869 658,903 618,920 562,920 510,920 472,908 448,885 423,861 408,822 402,767 L 109,781 C 127,886 175,966 254,1021 332,1075 439,1102 574,1102 711,1102 816,1068 890,1001 964,934 1001,838 1001,714 L 1001,320 C 1001,259 1008,218 1022,195 1035,172 1058,160 1090,160 1111,160 1132,162 1152,166 L 1152,14 C 1135,10 1120,6 1107,3 1094,0 1080,-3 1067,-5 1054,-7 1040,-9 1025,-10 1010,-11 992,-12 972,-12 901,-12 849,5 816,40 782,75 762,126 755,193 L 749,193 C 670,51 552,-20 393,-20 Z M 720,501 L 576,499 C 511,496 464,489 437,478 410,466 389,448 375,424 360,400 353,368 353,328 353,277 365,239 389,214 412,189 444,176 483,176 527,176 567,188 604,212 640,236 668,269 689,312 710,354 720,399 720,446 L 720,501 Z"/>
    <glyph unicode="T" horiz-adv-x="1229"
           d="M 773,1181 L 773,0 478,0 478,1181 23,1181 23,1409 1229,1409 1229,1181 773,1181 Z"/>
    <glyph unicode="S" horiz-adv-x="1250"
           d="M 1286,406 C 1286,268 1235,163 1133,90 1030,17 880,-20 682,-20 501,-20 360,12 257,76 154,140 88,237 59,367 L 344,414 C 363,339 401,285 457,252 513,218 591,201 690,201 896,201 999,264 999,389 999,429 987,462 964,488 940,514 907,536 864,553 821,570 738,591 616,616 511,641 437,661 396,676 355,691 317,708 284,729 251,749 222,773 199,802 176,831 158,864 145,903 132,942 125,986 125,1036 125,1163 173,1261 269,1329 364,1396 503,1430 686,1430 861,1430 992,1403 1080,1348 1167,1293 1224,1203 1249,1077 L 963,1038 C 948,1099 919,1144 874,1175 829,1206 764,1221 680,1221 501,1221 412,1165 412,1053 412,1016 422,986 441,963 460,940 488,920 525,904 562,887 638,867 752,842 887,813 984,787 1043,763 1101,738 1147,710 1181,678 1215,645 1241,607 1259,562 1277,517 1286,465 1286,406 Z"/>
    <glyph unicode="R" horiz-adv-x="1335"
           d="M 1105,0 L 778,535 432,535 432,0 137,0 137,1409 841,1409 C 1009,1409 1139,1373 1230,1301 1321,1228 1367,1124 1367,989 1367,890 1339,805 1283,734 1227,662 1151,615 1056,592 L 1437,0 1105,0 Z M 1070,977 C 1070,1112 983,1180 810,1180 L 432,1180 432,764 818,764 C 901,764 963,783 1006,820 1049,857 1070,910 1070,977 Z"/>
    <glyph unicode="O" horiz-adv-x="1461"
           d="M 1507,711 C 1507,564 1478,435 1420,324 1362,213 1279,128 1171,69 1063,10 937,-20 793,-20 572,-20 398,45 273,176 147,306 84,484 84,711 84,937 147,1113 272,1240 397,1367 572,1430 795,1430 1018,1430 1193,1366 1319,1238 1444,1110 1507,934 1507,711 Z M 1206,711 C 1206,863 1170,982 1098,1069 1026,1155 925,1198 795,1198 663,1198 561,1155 489,1070 417,984 381,864 381,711 381,556 418,435 492,346 565,257 666,212 793,212 925,212 1027,255 1099,342 1170,429 1206,552 1206,711 Z"/>
    <glyph unicode="N" horiz-adv-x="1229"
           d="M 995,0 L 381,1085 C 393,980 399,895 399,831 L 399,0 137,0 137,1409 474,1409 1097,315 C 1085,416 1079,507 1079,590 L 1079,1409 1341,1409 1341,0 995,0 Z"/>
    <glyph unicode="M" horiz-adv-x="1462"
           d="M 1307,0 L 1307,854 C 1307,873 1307,893 1308,912 1308,931 1311,1014 1317,1161 1270,982 1235,857 1212,786 L 958,0 748,0 494,786 387,1161 C 395,1006 399,904 399,854 L 399,0 137,0 137,1409 532,1409 784,621 806,545 854,356 917,582 1176,1409 1569,1409 1569,0 1307,0 Z"/>
    <glyph unicode="F" horiz-adv-x="1059"
           d="M 432,1181 L 432,745 1153,745 1153,517 432,517 432,0 137,0 137,1409 1176,1409 1176,1181 432,1181 Z"/>
    <glyph unicode="C" horiz-adv-x="1376"
           d="M 795,212 C 973,212 1097,301 1166,480 L 1423,383 C 1368,247 1287,146 1180,80 1073,13 944,-20 795,-20 568,-20 393,44 270,173 146,301 84,480 84,711 84,942 144,1120 263,1244 382,1368 555,1430 782,1430 947,1430 1082,1397 1186,1331 1290,1264 1363,1167 1405,1038 L 1145,967 C 1123,1038 1080,1094 1016,1136 951,1177 875,1198 788,1198 655,1198 554,1157 485,1074 416,991 381,870 381,711 381,549 417,425 488,340 559,255 661,212 795,212 Z"/>
    <glyph unicode="A" horiz-adv-x="1398"
           d="M 1133,0 L 1008,360 471,360 346,0 51,0 565,1409 913,1409 1425,0 1133,0 Z M 739,1192 L 733,1170 C 726,1146 718,1119 709,1088 700,1057 642,889 537,582 L 942,582 803,987 760,1123 739,1192 Z"/>
    <glyph unicode=" " horiz-adv-x="572"/>
  </font>
 </defs>
  <defs class="TextShapeIndex">
  <g ooo:slide="id1"
     ooo:id-list="id3 id4 id5 id6 id7 id8 id9 id10 id11 id12 id13 id14 id15 id16 id17 id18 id19 id20 id21 id22 id23 id24 id25 id26 id27 id28 id29 id30 id31 id32 id33 id34 id35 id36 id37 id38 id39 id40 id41 id42 id43 id44 id45 id46 id47 id48 id49 id50 id51 id52 id53 id54 id55 id56 id57 id58 id59 id60 id61 id62"/>
 </defs>
  <defs class="EmbeddedBulletChars">
  <g id="bullet-char-template-57356" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 580,1141 L 1163,571 580,0 -4,571 580,1141 Z"/>
  </g>
    <g id="bullet-char-template-57354" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 8,1128 L 1137,1128 1137,0 8,0 8,1128 Z"/>
  </g>
    <g id="bullet-char-template-10146" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 174,0 L 602,739 174,1481 1456,739 174,0 Z M 1358,739 L 309,1346 659,739 1358,739 Z"/>
  </g>
    <g id="bullet-char-template-10132" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 2015,739 L 1276,0 717,0 1260,543 174,543 174,936 1260,936 717,1481 1274,1481 2015,739 Z"/>
  </g>
    <g id="bullet-char-template-10007" transform="scale(0.00048828125,-0.00048828125)">
   <path
     d="M 0,-2 C -7,14 -16,27 -25,37 L 356,567 C 262,823 215,952 215,954 215,979 228,992 255,992 264,992 276,990 289,987 310,991 331,999 354,1012 L 381,999 492,748 772,1049 836,1024 860,1049 C 881,1039 901,1025 922,1006 886,937 835,863 770,784 769,783 710,716 594,584 L 774,223 C 774,196 753,168 711,139 L 727,119 C 717,90 699,76 672,76 641,76 570,178 457,381 L 164,-76 C 142,-110 111,-127 72,-127 30,-127 9,-110 8,-76 1,-67 -2,-52 -2,-32 -2,-23 -1,-13 0,-2 Z"/>
  </g>
    <g id="bullet-char-template-10004" transform="scale(0.00048828125,-0.00048828125)">
   <path
     d="M 285,-33 C 182,-33 111,30 74,156 52,228 41,333 41,471 41,549 55,616 82,672 116,743 169,778 240,778 293,778 328,747 346,684 L 369,508 C 377,444 397,411 428,410 L 1163,1116 C 1174,1127 1196,1133 1229,1133 1271,1133 1292,1118 1292,1087 L 1292,965 C 1292,929 1282,901 1262,881 L 442,47 C 390,-6 338,-33 285,-33 Z"/>
  </g>
    <g id="bullet-char-template-9679" transform="scale(0.00048828125,-0.00048828125)">
   <path
     d="M 813,0 C 632,0 489,54 383,161 276,268 223,411 223,592 223,773 276,916 383,1023 489,1130 632,1184 813,1184 992,1184 1136,1130 1245,1023 1353,916 1407,772 1407,592 1407,412 1353,268 1245,161 1136,54 992,0 813,0 Z"/>
  </g>
    <g id="bullet-char-template-8226" transform="scale(0.00048828125,-0.00048828125)">
   <path
     d="M 346,457 C 273,457 209,483 155,535 101,586 74,649 74,723 74,796 101,859 155,911 209,963 273,989 346,989 419,989 480,963 531,910 582,859 608,796 608,723 608,648 583,586 532,535 482,483 420,457 346,457 Z"/>
  </g>
    <g id="bullet-char-template-8211" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M -4,459 L 1135,459 1135,606 -4,606 -4,459 Z"/>
  </g>
    <g id="bullet-char-template-61548" transform="scale(0.00048828125,-0.00048828125)">
   <path
     d="M 173,740 C 173,903 231,1043 346,1159 462,1274 601,1332 765,1332 928,1332 1067,1274 1183,1159 1299,1043 1357,903 1357,740 1357,577 1299,437 1183,322 1067,206 928,148 765,148 601,148 462,206 346,322 231,437 173,577 173,740 Z"/>
  </g>
 </defs>
  <g>
  <g id="id2" class="Master_Slide">
   <g id="bg-id2" class="Background"/>
    <g id="bo-id2" class="BackgroundObjects"/>
  </g>
 </g>
  <g class="SlideGroup">
  <g>
   <g id="container-id1">
    <g id="id1" class="Slide" clip-path="url(#presentation_clip_path)">
     <g class="Page">
      <defs class="SlideBackground">
       <g id="bg-id1" class="Background">
        <path fill="rgb(102,102,102)" stroke="none" d="M 8890,7619 L 0,7619 0,0 17779,0 17779,7619 8890,7619 Z"/>
       </g>
      </defs>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id3">
        <rect class="BoundingBox" stroke="none" fill="none" x="2268" y="2268" width="639" height="639"/>
         <path fill="rgb(255,182,108)" stroke="none"
               d="M 2905,2587 C 2905,2643 2890,2698 2862,2746 2834,2794 2794,2834 2746,2862 2698,2890 2643,2905 2587,2905 2531,2905 2476,2890 2428,2862 2380,2834 2340,2794 2312,2746 2284,2698 2269,2643 2269,2587 2269,2531 2284,2476 2312,2428 2340,2380 2380,2340 2428,2312 2476,2284 2531,2269 2587,2269 2643,2269 2698,2284 2746,2312 2794,2340 2834,2380 2862,2428 2890,2476 2905,2531 2905,2587 L 2905,2587 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 2905,2587 C 2905,2643 2890,2698 2862,2746 2834,2794 2794,2834 2746,2862 2698,2890 2643,2905 2587,2905 2531,2905 2476,2890 2428,2862 2380,2834 2340,2794 2312,2746 2284,2698 2269,2643 2269,2587 2269,2531 2284,2476 2312,2428 2340,2380 2380,2340 2428,2312 2476,2284 2531,2269 2587,2269 2643,2269 2698,2284 2746,2312 2794,2340 2834,2380 2862,2428 2890,2476 2905,2531 2905,2587 L 2905,2587 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id4">
        <rect class="BoundingBox" stroke="none" fill="none" x="1317" y="1318" width="2389" height="889"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="1597" y="1701"><tspan fill="rgb(255,219,182)" stroke="none">Starting price</tspan></tspan><tspan
           class="TextPosition" x="2016" y="2019"><tspan fill="rgb(255,219,182)" stroke="none">Not Set</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id5">
        <rect class="BoundingBox" stroke="none" fill="none" x="3540" y="4810" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="4254" y="5193"><tspan fill="rgb(221,232,203)" stroke="none">Autopilot ON</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id6">
        <rect class="BoundingBox" stroke="none" fill="none" x="4808" y="3538" width="639" height="639"/>
         <path fill="rgb(255,182,108)" stroke="none"
               d="M 5445,3857 C 5445,3913 5430,3968 5402,4016 5374,4064 5334,4104 5286,4132 5238,4160 5183,4175 5127,4175 5071,4175 5016,4160 4968,4132 4920,4104 4880,4064 4852,4016 4824,3968 4809,3913 4809,3857 4809,3801 4824,3746 4852,3698 4880,3650 4920,3610 4968,3582 5016,3554 5071,3539 5127,3539 5183,3539 5238,3554 5286,3582 5334,3610 5374,3650 5402,3698 5430,3746 5445,3801 5445,3857 L 5445,3857 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 5445,3857 C 5445,3913 5430,3968 5402,4016 5374,4064 5334,4104 5286,4132 5238,4160 5183,4175 5127,4175 5071,4175 5016,4160 4968,4132 4920,4104 4880,4064 4852,4016 4824,3968 4809,3913 4809,3857 4809,3801 4824,3746 4852,3698 4880,3650 4920,3610 4968,3582 5016,3554 5071,3539 5127,3539 5183,3539 5238,3554 5286,3582 5334,3610 5374,3650 5402,3698 5430,3746 5445,3801 5445,3857 L 5445,3857 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id7">
        <rect class="BoundingBox" stroke="none" fill="none" x="4808" y="2903" width="639" height="639"/>
         <path fill="rgb(119,188,101)" stroke="none"
               d="M 5445,3222 C 5445,3278 5430,3333 5402,3381 5374,3429 5334,3469 5286,3497 5238,3525 5183,3540 5127,3540 5071,3540 5016,3525 4968,3497 4920,3469 4880,3429 4852,3381 4824,3333 4809,3278 4809,3222 4809,3166 4824,3111 4852,3063 4880,3015 4920,2975 4968,2947 5016,2919 5071,2904 5127,2904 5183,2904 5238,2919 5286,2947 5334,2975 5374,3015 5402,3063 5430,3111 5445,3166 5445,3222 L 5445,3222 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 5445,3222 C 5445,3278 5430,3333 5402,3381 5374,3429 5334,3469 5286,3497 5238,3525 5183,3540 5127,3540 5071,3540 5016,3525 4968,3497 4920,3469 4880,3429 4852,3381 4824,3333 4809,3278 4809,3222 4809,3166 4824,3111 4852,3063 4880,3015 4920,2975 4968,2947 5016,2919 5071,2904 5127,2904 5183,2904 5238,2919 5286,2947 5334,2975 5374,3015 5402,3063 5430,3111 5445,3166 5445,3222 L 5445,3222 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id8">
        <rect class="BoundingBox" stroke="none" fill="none" x="4808" y="4173" width="639" height="639"/>
         <path fill="rgb(241,13,12)" stroke="none"
               d="M 5445,4492 C 5445,4548 5430,4603 5402,4651 5374,4699 5334,4739 5286,4767 5238,4795 5183,4810 5127,4810 5071,4810 5016,4795 4968,4767 4920,4739 4880,4699 4852,4651 4824,4603 4809,4548 4809,4492 4809,4436 4824,4381 4852,4333 4880,4285 4920,4245 4968,4217 5016,4189 5071,4174 5127,4174 5183,4174 5238,4189 5286,4217 5334,4245 5374,4285 5402,4333 5430,4381 5445,4436 5445,4492 L 5445,4492 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 5445,4492 C 5445,4548 5430,4603 5402,4651 5374,4699 5334,4739 5286,4767 5238,4795 5183,4810 5127,4810 5071,4810 5016,4795 4968,4767 4920,4739 4880,4699 4852,4651 4824,4603 4809,4548 4809,4492 4809,4436 4824,4381 4852,4333 4880,4285 4920,4245 4968,4217 5016,4189 5071,4174 5127,4174 5183,4174 5238,4189 5286,4217 5334,4245 5374,4285 5402,4333 5430,4381 5445,4436 5445,4492 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id9">
        <rect class="BoundingBox" stroke="none" fill="none" x="2852" y="2358" width="1959" height="461"/>
         <path fill="none" stroke="rgb(255,182,108)" stroke-width="106" stroke-linejoin="round"
               d="M 2905,2588 L 4382,2588"/>
         <path fill="rgb(255,182,108)" stroke="none" d="M 4351,2359 L 4810,2588 4351,2818 4351,2359 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id10">
        <rect class="BoundingBox" stroke="none" fill="none" x="14968" y="2903" width="639" height="639"/>
         <path fill="rgb(119,188,101)" stroke="none"
               d="M 15605,3222 C 15605,3278 15590,3333 15562,3381 15534,3429 15494,3469 15446,3497 15398,3525 15343,3540 15287,3540 15231,3540 15176,3525 15128,3497 15080,3469 15040,3429 15012,3381 14984,3333 14969,3278 14969,3222 14969,3166 14984,3111 15012,3063 15040,3015 15080,2975 15128,2947 15176,2919 15231,2904 15287,2904 15343,2904 15398,2919 15446,2947 15494,2975 15534,3015 15562,3063 15590,3111 15605,3166 15605,3222 L 15605,3222 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 15605,3222 C 15605,3278 15590,3333 15562,3381 15534,3429 15494,3469 15446,3497 15398,3525 15343,3540 15287,3540 15231,3540 15176,3525 15128,3497 15080,3469 15040,3429 15012,3381 14984,3333 14969,3278 14969,3222 14969,3166 14984,3111 15012,3063 15040,3015 15080,2975 15128,2947 15176,2919 15231,2904 15287,2904 15343,2904 15398,2919 15446,2947 15494,2975 15534,3015 15562,3063 15590,3111 15605,3166 15605,3222 L 15605,3222 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id11">
        <rect class="BoundingBox" stroke="none" fill="none" x="4808" y="2268" width="639" height="639"/>
         <path fill="rgb(153,153,153)" stroke="none"
               d="M 5445,2587 C 5445,2643 5430,2698 5402,2746 5374,2794 5334,2834 5286,2862 5238,2890 5183,2905 5127,2905 5071,2905 5016,2890 4968,2862 4920,2834 4880,2794 4852,2746 4824,2698 4809,2643 4809,2587 4809,2531 4824,2476 4852,2428 4880,2380 4920,2340 4968,2312 5016,2284 5071,2269 5127,2269 5183,2269 5238,2284 5286,2312 5334,2340 5374,2380 5402,2428 5430,2476 5445,2531 5445,2587 L 5445,2587 Z"/>
         <path fill="none" stroke="rgb(204,204,204)"
               d="M 5445,2587 C 5445,2643 5430,2698 5402,2746 5374,2794 5334,2834 5286,2862 5238,2890 5183,2905 5127,2905 5071,2905 5016,2890 4968,2862 4920,2834 4880,2794 4852,2746 4824,2698 4809,2643 4809,2587 4809,2531 4824,2476 4852,2428 4880,2380 4920,2340 4968,2312 5016,2284 5071,2269 5127,2269 5183,2269 5238,2284 5286,2312 5334,2340 5374,2380 5402,2428 5430,2476 5445,2531 5445,2587 L 5445,2587 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id12">
        <rect class="BoundingBox" stroke="none" fill="none" x="5392" y="2993" width="1959" height="461"/>
         <path fill="none" stroke="rgb(119,188,101)" stroke-width="106" stroke-linejoin="round"
               d="M 5445,3223 L 6922,3223"/>
         <path fill="rgb(119,188,101)" stroke="none" d="M 6891,2994 L 7350,3223 6891,3453 6891,2994 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id13">
        <rect class="BoundingBox" stroke="none" fill="none" x="5392" y="3628" width="1959" height="461"/>
         <path fill="none" stroke="rgb(255,182,108)" stroke-width="106" stroke-linejoin="round"
               d="M 5445,3858 L 6922,3858"/>
         <path fill="rgb(255,182,108)" stroke="none" d="M 6891,3629 L 7350,3858 6891,4088 6891,3629 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id14">
        <rect class="BoundingBox" stroke="none" fill="none" x="5392" y="4263" width="1959" height="461"/>
         <path fill="none" stroke="rgb(241,13,12)" stroke-width="106" stroke-linejoin="round"
               d="M 5445,4493 L 6922,4493"/>
         <path fill="rgb(241,13,12)" stroke="none" d="M 6891,4264 L 7350,4493 6891,4723 6891,4264 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id15">
        <rect class="BoundingBox" stroke="none" fill="none" x="7348" y="2268" width="639" height="639"/>
         <path fill="rgb(153,153,153)" stroke="none"
               d="M 7985,2587 C 7985,2643 7970,2698 7942,2746 7914,2794 7874,2834 7826,2862 7778,2890 7723,2905 7667,2905 7611,2905 7556,2890 7508,2862 7460,2834 7420,2794 7392,2746 7364,2698 7349,2643 7349,2587 7349,2531 7364,2476 7392,2428 7420,2380 7460,2340 7508,2312 7556,2284 7611,2269 7667,2269 7723,2269 7778,2284 7826,2312 7874,2340 7914,2380 7942,2428 7970,2476 7985,2531 7985,2587 L 7985,2587 Z"/>
         <path fill="none" stroke="rgb(204,204,204)"
               d="M 7985,2587 C 7985,2643 7970,2698 7942,2746 7914,2794 7874,2834 7826,2862 7778,2890 7723,2905 7667,2905 7611,2905 7556,2890 7508,2862 7460,2834 7420,2794 7392,2746 7364,2698 7349,2643 7349,2587 7349,2531 7364,2476 7392,2428 7420,2380 7460,2340 7508,2312 7556,2284 7611,2269 7667,2269 7723,2269 7778,2284 7826,2312 7874,2340 7914,2380 7942,2428 7970,2476 7985,2531 7985,2587 L 7985,2587 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id16">
        <rect class="BoundingBox" stroke="none" fill="none" x="6538" y="1317" width="2400" height="888"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="6823" y="1700"><tspan fill="rgb(255,255,255)" stroke="none">Starting price</tspan></tspan><tspan
           class="TextPosition" x="6959" y="2018"><tspan fill="rgb(255,255,255)" stroke="none">Announced</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id17">
        <rect class="BoundingBox" stroke="none" fill="none" x="1635" y="2905" width="2541" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="1959" y="3288"><tspan fill="rgb(255,219,182)" stroke="none">Autopilot OFF</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id18">
        <rect class="BoundingBox" stroke="none" fill="none" x="3869" y="1318" width="2529" height="953"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="4219" y="1701"><tspan fill="rgb(222,231,229)" stroke="none">Starting price</tspan></tspan><tspan
           class="TextPosition" x="4913" y="2019"><tspan fill="rgb(222,231,229)" stroke="none">Set</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id19">
        <rect class="BoundingBox" stroke="none" fill="none" x="3540" y="5022" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="4182" y="5405"><tspan fill="rgb(255,219,182)" stroke="none">Autopilot OFF</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id20">
        <rect class="BoundingBox" stroke="none" fill="none" x="3222" y="5445" width="3812" height="888"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="3551" y="5828"><tspan fill="rgb(255,215,215)" stroke="none">Missed Announce Time</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id21">
        <rect class="BoundingBox" stroke="none" fill="none" x="6396" y="4810" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="7110" y="5193"><tspan fill="rgb(221,232,203)" stroke="none">Autopilot ON</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id22">
        <rect class="BoundingBox" stroke="none" fill="none" x="7348" y="3538" width="639" height="639"/>
         <path fill="rgb(255,182,108)" stroke="none"
               d="M 7985,3857 C 7985,3913 7970,3968 7942,4016 7914,4064 7874,4104 7826,4132 7778,4160 7723,4175 7667,4175 7611,4175 7556,4160 7508,4132 7460,4104 7420,4064 7392,4016 7364,3968 7349,3913 7349,3857 7349,3801 7364,3746 7392,3698 7420,3650 7460,3610 7508,3582 7556,3554 7611,3539 7667,3539 7723,3539 7778,3554 7826,3582 7874,3610 7914,3650 7942,3698 7970,3746 7985,3801 7985,3857 L 7985,3857 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 7985,3857 C 7985,3913 7970,3968 7942,4016 7914,4064 7874,4104 7826,4132 7778,4160 7723,4175 7667,4175 7611,4175 7556,4160 7508,4132 7460,4104 7420,4064 7392,4016 7364,3968 7349,3913 7349,3857 7349,3801 7364,3746 7392,3698 7420,3650 7460,3610 7508,3582 7556,3554 7611,3539 7667,3539 7723,3539 7778,3554 7826,3582 7874,3610 7914,3650 7942,3698 7970,3746 7985,3801 7985,3857 L 7985,3857 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id23">
        <rect class="BoundingBox" stroke="none" fill="none" x="7348" y="2903" width="639" height="639"/>
         <path fill="rgb(119,188,101)" stroke="none"
               d="M 7985,3222 C 7985,3278 7970,3333 7942,3381 7914,3429 7874,3469 7826,3497 7778,3525 7723,3540 7667,3540 7611,3540 7556,3525 7508,3497 7460,3469 7420,3429 7392,3381 7364,3333 7349,3278 7349,3222 7349,3166 7364,3111 7392,3063 7420,3015 7460,2975 7508,2947 7556,2919 7611,2904 7667,2904 7723,2904 7778,2919 7826,2947 7874,2975 7914,3015 7942,3063 7970,3111 7985,3166 7985,3222 L 7985,3222 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 7985,3222 C 7985,3278 7970,3333 7942,3381 7914,3429 7874,3469 7826,3497 7778,3525 7723,3540 7667,3540 7611,3540 7556,3525 7508,3497 7460,3469 7420,3429 7392,3381 7364,3333 7349,3278 7349,3222 7349,3166 7364,3111 7392,3063 7420,3015 7460,2975 7508,2947 7556,2919 7611,2904 7667,2904 7723,2904 7778,2919 7826,2947 7874,2975 7914,3015 7942,3063 7970,3111 7985,3166 7985,3222 L 7985,3222 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id24">
        <rect class="BoundingBox" stroke="none" fill="none" x="7348" y="4173" width="639" height="639"/>
         <path fill="rgb(241,13,12)" stroke="none"
               d="M 7985,4492 C 7985,4548 7970,4603 7942,4651 7914,4699 7874,4739 7826,4767 7778,4795 7723,4810 7667,4810 7611,4810 7556,4795 7508,4767 7460,4739 7420,4699 7392,4651 7364,4603 7349,4548 7349,4492 7349,4436 7364,4381 7392,4333 7420,4285 7460,4245 7508,4217 7556,4189 7611,4174 7667,4174 7723,4174 7778,4189 7826,4217 7874,4245 7914,4285 7942,4333 7970,4381 7985,4436 7985,4492 L 7985,4492 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 7985,4492 C 7985,4548 7970,4603 7942,4651 7914,4699 7874,4739 7826,4767 7778,4795 7723,4810 7667,4810 7611,4810 7556,4795 7508,4767 7460,4739 7420,4699 7392,4651 7364,4603 7349,4548 7349,4492 7349,4436 7364,4381 7392,4333 7420,4285 7460,4245 7508,4217 7556,4189 7611,4174 7667,4174 7723,4174 7778,4189 7826,4217 7874,4245 7914,4285 7942,4333 7970,4381 7985,4436 7985,4492 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id25">
        <rect class="BoundingBox" stroke="none" fill="none" x="7932" y="2993" width="1959" height="461"/>
         <path fill="none" stroke="rgb(119,188,101)" stroke-width="106" stroke-linejoin="round"
               d="M 7985,3223 L 9462,3223"/>
         <path fill="rgb(119,188,101)" stroke="none" d="M 9431,2994 L 9890,3223 9431,3453 9431,2994 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id26">
        <rect class="BoundingBox" stroke="none" fill="none" x="7932" y="3628" width="1959" height="461"/>
         <path fill="none" stroke="rgb(255,182,108)" stroke-width="106" stroke-linejoin="round"
               d="M 7985,3858 L 9462,3858"/>
         <path fill="rgb(255,182,108)" stroke="none" d="M 9431,3629 L 9890,3858 9431,4088 9431,3629 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id27">
        <rect class="BoundingBox" stroke="none" fill="none" x="7932" y="4263" width="1959" height="461"/>
         <path fill="none" stroke="rgb(241,13,12)" stroke-width="106" stroke-linejoin="round"
               d="M 7985,4493 L 9462,4493"/>
         <path fill="rgb(241,13,12)" stroke="none" d="M 9431,4264 L 9890,4493 9431,4723 9431,4264 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id28">
        <rect class="BoundingBox" stroke="none" fill="none" x="6078" y="5022" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="6720" y="5405"><tspan fill="rgb(255,219,182)" stroke="none">Autopilot OFF</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id29">
        <rect class="BoundingBox" stroke="none" fill="none" x="3539" y="5828" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="3914" y="6211"><tspan fill="rgb(255,215,215)" stroke="none">Missed Start Time</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id30">
        <rect class="BoundingBox" stroke="none" fill="none" x="1000" y="3222" width="3811" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="1328" y="3605"><tspan fill="rgb(255,215,215)" stroke="none">Missed Announce Time</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id31">
        <rect class="BoundingBox" stroke="none" fill="none" x="1316" y="3540" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="1691" y="3923"><tspan fill="rgb(255,215,215)" stroke="none">Missed Start Time</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id32">
        <rect class="BoundingBox" stroke="none" fill="none" x="6079" y="5339" width="3812" height="888"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="6408" y="5722"><tspan fill="rgb(255,215,215)" stroke="none">Missed Announce Time</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id33">
        <rect class="BoundingBox" stroke="none" fill="none" x="6080" y="6146" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="6455" y="6529"><tspan fill="rgb(255,215,215)" stroke="none">Missed Start Time</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id34">
        <rect class="BoundingBox" stroke="none" fill="none" x="9254" y="1383" width="1907" height="888"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="9759" y="1766"><tspan fill="rgb(255,255,255)" stroke="none">Round</tspan></tspan><tspan
           class="TextPosition" x="9845" y="2084"><tspan fill="rgb(255,255,255)" stroke="none">Open</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id35">
        <rect class="BoundingBox" stroke="none" fill="none" x="9888" y="2269" width="639" height="639"/>
         <path fill="rgb(153,153,153)" stroke="none"
               d="M 10525,2588 C 10525,2644 10510,2699 10482,2747 10454,2795 10414,2835 10366,2863 10318,2891 10263,2906 10207,2906 10151,2906 10096,2891 10048,2863 10000,2835 9960,2795 9932,2747 9904,2699 9889,2644 9889,2588 9889,2532 9904,2477 9932,2429 9960,2381 10000,2341 10048,2313 10096,2285 10151,2270 10207,2270 10263,2270 10318,2285 10366,2313 10414,2341 10454,2381 10482,2429 10510,2477 10525,2532 10525,2588 L 10525,2588 Z"/>
         <path fill="none" stroke="rgb(204,204,204)"
               d="M 10525,2588 C 10525,2644 10510,2699 10482,2747 10454,2795 10414,2835 10366,2863 10318,2891 10263,2906 10207,2906 10151,2906 10096,2891 10048,2863 10000,2835 9960,2795 9932,2747 9904,2699 9889,2644 9889,2588 9889,2532 9904,2477 9932,2429 9960,2381 10000,2341 10048,2313 10096,2285 10151,2270 10207,2270 10263,2270 10318,2285 10366,2313 10414,2341 10454,2381 10482,2429 10510,2477 10525,2532 10525,2588 L 10525,2588 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id36">
        <rect class="BoundingBox" stroke="none" fill="none" x="11794" y="1383" width="1907" height="888"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="12299" y="1766"><tspan fill="rgb(255,255,255)" stroke="none">Round</tspan></tspan><tspan
           class="TextPosition" x="12275" y="2084"><tspan fill="rgb(255,255,255)" stroke="none">Closed</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id37">
        <rect class="BoundingBox" stroke="none" fill="none" x="8621" y="4876" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="9335" y="5259"><tspan fill="rgb(221,232,203)" stroke="none">Autopilot ON</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id38">
        <rect class="BoundingBox" stroke="none" fill="none" x="8620" y="5114" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="9262" y="5497"><tspan fill="rgb(255,219,182)" stroke="none">Autopilot OFF</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id39">
        <rect class="BoundingBox" stroke="none" fill="none" x="9888" y="3538" width="639" height="639"/>
         <path fill="rgb(255,182,108)" stroke="none"
               d="M 10525,3857 C 10525,3913 10510,3968 10482,4016 10454,4064 10414,4104 10366,4132 10318,4160 10263,4175 10207,4175 10151,4175 10096,4160 10048,4132 10000,4104 9960,4064 9932,4016 9904,3968 9889,3913 9889,3857 9889,3801 9904,3746 9932,3698 9960,3650 10000,3610 10048,3582 10096,3554 10151,3539 10207,3539 10263,3539 10318,3554 10366,3582 10414,3610 10454,3650 10482,3698 10510,3746 10525,3801 10525,3857 L 10525,3857 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 10525,3857 C 10525,3913 10510,3968 10482,4016 10454,4064 10414,4104 10366,4132 10318,4160 10263,4175 10207,4175 10151,4175 10096,4160 10048,4132 10000,4104 9960,4064 9932,4016 9904,3968 9889,3913 9889,3857 9889,3801 9904,3746 9932,3698 9960,3650 10000,3610 10048,3582 10096,3554 10151,3539 10207,3539 10263,3539 10318,3554 10366,3582 10414,3610 10454,3650 10482,3698 10510,3746 10525,3801 10525,3857 L 10525,3857 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id40">
        <rect class="BoundingBox" stroke="none" fill="none" x="9888" y="2903" width="639" height="639"/>
         <path fill="rgb(119,188,101)" stroke="none"
               d="M 10525,3222 C 10525,3278 10510,3333 10482,3381 10454,3429 10414,3469 10366,3497 10318,3525 10263,3540 10207,3540 10151,3540 10096,3525 10048,3497 10000,3469 9960,3429 9932,3381 9904,3333 9889,3278 9889,3222 9889,3166 9904,3111 9932,3063 9960,3015 10000,2975 10048,2947 10096,2919 10151,2904 10207,2904 10263,2904 10318,2919 10366,2947 10414,2975 10454,3015 10482,3063 10510,3111 10525,3166 10525,3222 L 10525,3222 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 10525,3222 C 10525,3278 10510,3333 10482,3381 10454,3429 10414,3469 10366,3497 10318,3525 10263,3540 10207,3540 10151,3540 10096,3525 10048,3497 10000,3469 9960,3429 9932,3381 9904,3333 9889,3278 9889,3222 9889,3166 9904,3111 9932,3063 9960,3015 10000,2975 10048,2947 10096,2919 10151,2904 10207,2904 10263,2904 10318,2919 10366,2947 10414,2975 10454,3015 10482,3063 10510,3111 10525,3166 10525,3222 L 10525,3222 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id41">
        <rect class="BoundingBox" stroke="none" fill="none" x="10472" y="2993" width="1959" height="461"/>
         <path fill="none" stroke="rgb(119,188,101)" stroke-width="106" stroke-linejoin="round"
               d="M 10525,3223 L 12002,3223"/>
         <path fill="rgb(119,188,101)" stroke="none" d="M 11971,2994 L 12430,3223 11971,3453 11971,2994 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id42">
        <rect class="BoundingBox" stroke="none" fill="none" x="10472" y="3628" width="1959" height="461"/>
         <path fill="none" stroke="rgb(255,182,108)" stroke-width="106" stroke-linejoin="round"
               d="M 10525,3858 L 12002,3858"/>
         <path fill="rgb(255,182,108)" stroke="none" d="M 11971,3629 L 12430,3858 11971,4088 11971,3629 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id43">
        <rect class="BoundingBox" stroke="none" fill="none" x="8619" y="5749" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="9526" y="6132"><tspan fill="rgb(221,232,203)" stroke="none">All bids in</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id44">
        <rect class="BoundingBox" stroke="none" fill="none" x="8620" y="5431" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="9266" y="5814"><tspan fill="rgb(255,219,182)" stroke="none">All bids not in</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id45">
        <rect class="BoundingBox" stroke="none" fill="none" x="8937" y="6080" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="9266" y="6463"><tspan fill="rgb(255,216,206)" stroke="none">Auction awardable</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id46">
        <rect class="BoundingBox" stroke="none" fill="none" x="12428" y="2269" width="639" height="639"/>
         <path fill="rgb(153,153,153)" stroke="none"
               d="M 13065,2588 C 13065,2644 13050,2699 13022,2747 12994,2795 12954,2835 12906,2863 12858,2891 12803,2906 12747,2906 12691,2906 12636,2891 12588,2863 12540,2835 12500,2795 12472,2747 12444,2699 12429,2644 12429,2588 12429,2532 12444,2477 12472,2429 12500,2381 12540,2341 12588,2313 12636,2285 12691,2270 12747,2270 12803,2270 12858,2285 12906,2313 12954,2341 12994,2381 13022,2429 13050,2477 13065,2532 13065,2588 L 13065,2588 Z"/>
         <path fill="none" stroke="rgb(204,204,204)"
               d="M 13065,2588 C 13065,2644 13050,2699 13022,2747 12994,2795 12954,2835 12906,2863 12858,2891 12803,2906 12747,2906 12691,2906 12636,2891 12588,2863 12540,2835 12500,2795 12472,2747 12444,2699 12429,2644 12429,2588 12429,2532 12444,2477 12472,2429 12500,2381 12540,2341 12588,2313 12636,2285 12691,2270 12747,2270 12803,2270 12858,2285 12906,2313 12954,2341 12994,2381 13022,2429 13050,2477 13065,2532 13065,2588 L 13065,2588 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id47">
        <rect class="BoundingBox" stroke="none" fill="none" x="14968" y="2268" width="639" height="639"/>
         <path fill="rgb(153,153,153)" stroke="none"
               d="M 15605,2587 C 15605,2643 15590,2698 15562,2746 15534,2794 15494,2834 15446,2862 15398,2890 15343,2905 15287,2905 15231,2905 15176,2890 15128,2862 15080,2834 15040,2794 15012,2746 14984,2698 14969,2643 14969,2587 14969,2531 14984,2476 15012,2428 15040,2380 15080,2340 15128,2312 15176,2284 15231,2269 15287,2269 15343,2269 15398,2284 15446,2312 15494,2340 15534,2380 15562,2428 15590,2476 15605,2531 15605,2587 L 15605,2587 Z"/>
         <path fill="none" stroke="rgb(204,204,204)"
               d="M 15605,2587 C 15605,2643 15590,2698 15562,2746 15534,2794 15494,2834 15446,2862 15398,2890 15343,2905 15287,2905 15231,2905 15176,2890 15128,2862 15080,2834 15040,2794 15012,2746 14984,2698 14969,2643 14969,2587 14969,2531 14984,2476 15012,2428 15040,2380 15080,2340 15128,2312 15176,2284 15231,2269 15287,2269 15343,2269 15398,2284 15446,2312 15494,2340 15534,2380 15562,2428 15590,2476 15605,2531 15605,2587 L 15605,2587 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id48">
        <rect class="BoundingBox" stroke="none" fill="none" x="11159" y="4876" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="11873" y="5259"><tspan fill="rgb(221,232,203)" stroke="none">Autopilot ON</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id49">
        <rect class="BoundingBox" stroke="none" fill="none" x="11158" y="5114" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="11800" y="5497"><tspan fill="rgb(255,219,182)" stroke="none">Autopilot OFF</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id50">
        <rect class="BoundingBox" stroke="none" fill="none" x="12428" y="3538" width="639" height="639"/>
         <path fill="rgb(255,182,108)" stroke="none"
               d="M 13065,3857 C 13065,3913 13050,3968 13022,4016 12994,4064 12954,4104 12906,4132 12858,4160 12803,4175 12747,4175 12691,4175 12636,4160 12588,4132 12540,4104 12500,4064 12472,4016 12444,3968 12429,3913 12429,3857 12429,3801 12444,3746 12472,3698 12500,3650 12540,3610 12588,3582 12636,3554 12691,3539 12747,3539 12803,3539 12858,3554 12906,3582 12954,3610 12994,3650 13022,3698 13050,3746 13065,3801 13065,3857 L 13065,3857 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 13065,3857 C 13065,3913 13050,3968 13022,4016 12994,4064 12954,4104 12906,4132 12858,4160 12803,4175 12747,4175 12691,4175 12636,4160 12588,4132 12540,4104 12500,4064 12472,4016 12444,3968 12429,3913 12429,3857 12429,3801 12444,3746 12472,3698 12500,3650 12540,3610 12588,3582 12636,3554 12691,3539 12747,3539 12803,3539 12858,3554 12906,3582 12954,3610 12994,3650 13022,3698 13050,3746 13065,3801 13065,3857 L 13065,3857 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id51">
        <rect class="BoundingBox" stroke="none" fill="none" x="12428" y="2903" width="639" height="639"/>
         <path fill="rgb(119,188,101)" stroke="none"
               d="M 13065,3222 C 13065,3278 13050,3333 13022,3381 12994,3429 12954,3469 12906,3497 12858,3525 12803,3540 12747,3540 12691,3540 12636,3525 12588,3497 12540,3469 12500,3429 12472,3381 12444,3333 12429,3278 12429,3222 12429,3166 12444,3111 12472,3063 12500,3015 12540,2975 12588,2947 12636,2919 12691,2904 12747,2904 12803,2904 12858,2919 12906,2947 12954,2975 12994,3015 13022,3063 13050,3111 13065,3166 13065,3222 L 13065,3222 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 13065,3222 C 13065,3278 13050,3333 13022,3381 12994,3429 12954,3469 12906,3497 12858,3525 12803,3540 12747,3540 12691,3540 12636,3525 12588,3497 12540,3469 12500,3429 12472,3381 12444,3333 12429,3278 12429,3222 12429,3166 12444,3111 12472,3063 12500,3015 12540,2975 12588,2947 12636,2919 12691,2904 12747,2904 12803,2904 12858,2919 12906,2947 12954,2975 12994,3015 13022,3063 13050,3111 13065,3166 13065,3222 L 13065,3222 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id52">
        <rect class="BoundingBox" stroke="none" fill="none" x="12428" y="4490" width="639" height="639"/>
         <path fill="rgb(241,13,12)" stroke="none"
               d="M 13065,4809 C 13065,4865 13050,4920 13022,4968 12994,5016 12954,5056 12906,5084 12858,5112 12803,5127 12747,5127 12691,5127 12636,5112 12588,5084 12540,5056 12500,5016 12472,4968 12444,4920 12429,4865 12429,4809 12429,4753 12444,4698 12472,4650 12500,4602 12540,4562 12588,4534 12636,4506 12691,4491 12747,4491 12803,4491 12858,4506 12906,4534 12954,4562 12994,4602 13022,4650 13050,4698 13065,4753 13065,4809 L 13065,4809 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 13065,4809 C 13065,4865 13050,4920 13022,4968 12994,5016 12954,5056 12906,5084 12858,5112 12803,5127 12747,5127 12691,5127 12636,5112 12588,5084 12540,5056 12500,5016 12472,4968 12444,4920 12429,4865 12429,4809 12429,4753 12444,4698 12472,4650 12500,4602 12540,4562 12588,4534 12636,4506 12691,4491 12747,4491 12803,4491 12858,4506 12906,4534 12954,4562 12994,4602 13022,4650 13050,4698 13065,4753 13065,4809 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id53">
        <rect class="BoundingBox" stroke="none" fill="none" x="13012" y="2993" width="1959" height="461"/>
         <path fill="none" stroke="rgb(119,188,101)" stroke-width="106" stroke-linejoin="round"
               d="M 13065,3223 L 14542,3223"/>
         <path fill="rgb(119,188,101)" stroke="none" d="M 14511,2994 L 14970,3223 14511,3453 14511,2994 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id54">
        <rect class="BoundingBox" stroke="none" fill="none" x="13012" y="3628" width="1959" height="461"/>
         <path fill="none" stroke="rgb(255,182,108)" stroke-width="106" stroke-linejoin="round"
               d="M 13065,3858 L 14542,3858"/>
         <path fill="rgb(255,182,108)" stroke="none" d="M 14511,3629 L 14970,3858 14511,4088 14511,3629 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id55">
        <rect class="BoundingBox" stroke="none" fill="none" x="13012" y="4580" width="1959" height="461"/>
         <path fill="none" stroke="rgb(241,13,12)" stroke-width="106" stroke-linejoin="round"
               d="M 13065,4810 L 14542,4810"/>
         <path fill="rgb(241,13,12)" stroke="none" d="M 14511,4581 L 14970,4810 14511,5040 14511,4581 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id56">
        <rect class="BoundingBox" stroke="none" fill="none" x="14335" y="1383" width="1907" height="888"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="14761" y="1766"><tspan fill="rgb(255,255,255)" stroke="none">Auction</tspan></tspan><tspan
           class="TextPosition" x="14816" y="2084"><tspan fill="rgb(255,255,255)" stroke="none">Closed</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id57">
        <rect class="BoundingBox" stroke="none" fill="none" x="11477" y="5894" width="3177" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="11806" y="6277"><tspan fill="rgb(255,216,206)" stroke="none">Auction awardable</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id58">
        <rect class="BoundingBox" stroke="none" fill="none" x="10525" y="4580" width="1959" height="461"/>
         <path fill="none" stroke="rgb(241,13,12)" stroke-width="106" stroke-linejoin="round"
               d="M 12430,4810 L 10953,4810"/>
         <path fill="rgb(241,13,12)" stroke="none" d="M 10984,5040 L 10525,4810 10984,4581 10984,5040 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.CustomShape">
       <g id="id59">
        <rect class="BoundingBox" stroke="none" fill="none" x="9888" y="4173" width="639" height="639"/>
         <path fill="rgb(241,13,12)" stroke="none"
               d="M 10525,4492 C 10525,4548 10510,4603 10482,4651 10454,4699 10414,4739 10366,4767 10318,4795 10263,4810 10207,4810 10151,4810 10096,4795 10048,4767 10000,4739 9960,4699 9932,4651 9904,4603 9889,4548 9889,4492 9889,4436 9904,4381 9932,4333 9960,4285 10000,4245 10048,4217 10096,4189 10151,4174 10207,4174 10263,4174 10318,4189 10366,4217 10414,4245 10454,4285 10482,4333 10510,4381 10525,4436 10525,4492 L 10525,4492 Z"/>
         <path fill="none" stroke="rgb(52,101,164)"
               d="M 10525,4492 C 10525,4548 10510,4603 10482,4651 10454,4699 10414,4739 10366,4767 10318,4795 10263,4810 10207,4810 10151,4810 10096,4795 10048,4767 10000,4739 9960,4699 9932,4651 9904,4603 9889,4548 9889,4492 9889,4436 9904,4381 9932,4333 9960,4285 10000,4245 10048,4217 10096,4189 10151,4174 10207,4174 10263,4174 10318,4189 10366,4217 10414,4245 10454,4285 10482,4333 10510,4381 10525,4436 10525,4492 Z"/>
       </g>
      </g>
       <g class="com.sun.star.drawing.LineShape">
       <g id="id60">
        <rect class="BoundingBox" stroke="none" fill="none" x="10472" y="4262" width="1959" height="461"/>
         <path fill="none" stroke="rgb(241,13,12)" stroke-width="106" stroke-linejoin="round"
               d="M 10525,4492 L 12002,4492"/>
         <path fill="rgb(241,13,12)" stroke="none" d="M 11971,4263 L 12430,4492 11971,4722 11971,4263 Z"/>
       </g>
      </g>
       <g class="TextShape">
       <g id="id61">
        <rect class="BoundingBox" stroke="none" fill="none" x="8600" y="5687" width="3811" height="636"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="9041" y="6070"><tspan fill="rgb(255,216,206)" stroke="none">Round Open seconds</tspan></tspan></tspan></text>
       </g>
      </g>
       <g class="TextShape">
       <g id="id62">
        <rect class="BoundingBox" stroke="none" fill="none" x="11160" y="5511" width="3811" height="570"/>
         <text class="SVGTextShape"><tspan class="TextParagraph" font-family="Liberation Sans, sans-serif" font-size="282px" font-weight="700"><tspan class="TextPosition" x="11490" y="5894"><tspan fill="rgb(255,216,206)" stroke="none">Round Closed seconds</tspan></tspan></tspan></text>
       </g>
      </g>
     </g>
    </g>
   </g>
  </g>
 </g>
</svg>
