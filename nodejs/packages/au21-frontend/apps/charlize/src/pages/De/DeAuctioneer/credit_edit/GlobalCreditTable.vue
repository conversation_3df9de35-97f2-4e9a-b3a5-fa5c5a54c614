<template>
  <AuAgGrid
    class="ClientCreditTable"
    :height="height"
    :width="width"
    :columnDefs="columnDefs"
    :rowData="rows"
    :gridOptions="gridOptions"
    :auto_refresh="false"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {ICellRendererParams} from 'ag-grid-community/dist/lib/rendering/cellRenderers/iCellRenderer';
import {AuScreen} from "../../../../plugins/screen-plugin/AuScreen";
import {GlobalCreditTableRow} from './GlobalCreditTable.types';
import {DeSettingsValue} from '@au21-frontend/client-connector';
import DeLimitsTableHeader from '../limits/DeLimitsTableHeader.vue';
import {ColGroupDef} from 'ag-grid-community/dist/lib/entities/colDef';
import GlobalCreditCell from './GlobalCreditCell.vue';

@Component({
  components: {AuAgGrid},
})
export default class GlobalCreditTableCreditTable extends Vue {
  @Prop({required: true}) rows: GlobalCreditTableRow[]
  @Prop({required: true}) de_settings_value: DeSettingsValue

  width = 615

  screen = new AuScreen()

  gridOptions: GridOptions = {
    headerHeight: 28,
    defaultColDef: {
      headerComponentFramework: AuAgGridCenteredHeader,
      cellStyle: () => ({padding: '0', border: '0'}),
      sortable: true,
    },
    getRowNodeId: (data: GlobalCreditTableRow) => data.company_id,
    rowHeight: 24,
    suppressHorizontalScroll: true,
  }

  get height() {
    return this.screen.modal_height
  }

  get columnDefs(): (ColDef | ColGroupDef)[] {
    return [
      {
        headerName: 'Buyer',
        cellRendererParams: {name: 'buyer'},
        cellRenderer: (params: ICellRendererParams) => {
          const row: GlobalCreditTableRow = params.data
          return `<div style="padding-left: 5px;">${row.company_name}</div>`
        },
        sort: 'desc',
      },
      {
        headerName: `Max Buy ($)`,
        children: [
          {
            headerName: 'Current',
            headerComponentFramework: DeLimitsTableHeader,
            width: 155,
            cellRendererParams: {name: 'current_max_buy'},
            cellRendererFramework: GlobalCreditCell,
          },
          {
            headerName: 'New',
            headerComponentFramework: DeLimitsTableHeader,
            width: 200,
            cellRendererParams: {name: 'new_max_buy'},
            cellRendererFramework: GlobalCreditCell,
          }
        ],
      },
      {
        headerName: `Max Sell (${this.de_settings_value.quantity_label})`,
        children: [
          {
            headerName: 'Current',
            headerComponentFramework: DeLimitsTableHeader,
            width: 100,
            cellRendererParams: {name: 'current_max_sell'},
            cellRendererFramework: GlobalCreditCell,
          },
          {
            headerName: 'New',
            headerComponentFramework: DeLimitsTableHeader,
            width: 155,
            cellRendererParams: {name: 'new_max_sell'},
            cellRendererFramework: GlobalCreditCell,
          }
        ],
      },
    ]
  }
}
</script>

<style lang="less" scoped>
.ClientCreditTable {

}
</style>
