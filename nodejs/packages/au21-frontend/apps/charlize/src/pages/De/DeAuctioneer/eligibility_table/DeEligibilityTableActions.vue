<template>
  <div class="DeEligibilityTableActions">
    <a-button
      class="au-btn"
      style="height: 24px"
      @click="allow"
    >
      allow
    </a-button>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';
import {DeEligibilityTableActionsParams, DeEligibilityTableRow} from './DeEligibilityTable.types';

@Component({
  name: 'DeEligibilityTableActions',
  components: {NumberInput},
})
export default class DeEligibilityTableActions extends Vue {
  params = null

  get row(): DeEligibilityTableRow {
    return this.params.data
  }

  get isAllowable(): boolean {
    return this.cellParams.isRowAllowable(this.row)
  }

  async allow() {
    this.cellParams.allow(this.row)
  }

  get cellParams(): DeEligibilityTableActionsParams {
    return this.params.column.colDef.cellRendererParams;
  }
}
</script>

<style lang="less" scoped>
.DeEligibilityTableActions {

}
</style>
