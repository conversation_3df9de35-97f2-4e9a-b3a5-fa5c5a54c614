<template>
  <div class='RoundConstraintsTableRow'>
    <div v-if="params.colDef.headerName === 'Company'">
      {{ row.company_shortname }}
    </div>
    <div v-if="params.colDef.headerName === 'Constraints'">
      <DeOrderConstraintsBar
        v-if="row.order_type"
        :width="120"
        :height="12"
        :tick_font_size="9"
        :constraints="row.constraints"
        :order_quantity='row.quantity_int'
        :order_type="row.order_type"
      />
    </div>
    <div v-if="params.colDef.headerName === 'Order'"
         :style="{color: order_color}">
      &nbsp;<div class="_order_vol">{{ row.quantity_int }}</div>
      &nbsp;<div class="_order_type">{{ row.order_submission_type }}</div>
    </div>
    <div v-if="params.colDef.headerName === 'Match'">
      &nbsp;
    </div>
  </div>
</template>

<script lang='ts'>
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeRoundTraderElement, OrderSubmissionType, OrderType} from '@au21-frontend/client-connector';
import DeOrderConstraintsBar from '../../DeTrader/constraints/DeOrderConstraintsBar.vue';
import {Container} from "typescript-ioc";
import {AuColors} from "../../../../au-styles/AuColors";

@Component({
  components: {DeOrderConstraintsBar}
})
export default class RoundConstraintsTableRow extends Vue {
  params = null;
  OrderType = OrderType
  OrderSubmissionType = OrderSubmissionType
  colors = Container.get(AuColors)

  get row(): DeRoundTraderElement {
    return this.params.data
  }

  get order_color(): string {
    return this.colors.order_quantity_text_color(this.row.order_type)
  }

}
</script>


<style lang="less" scoped>
.RoundConstraintsTableRow {
  ._order_vol {
    display: inline-block;
    left: 3px;
    overflow: hidden;
    position: absolute;
    text-align: right;
    width: 20px;
  }

  ._order_type {
    display: inline-block;
    font-size: 9px;
    left: 30px;
    overflow: hidden;
    position: absolute;
  }
}
</style>
