<template>
  <div class="QuantityConstraintsRuleCard pl-3">
    <p>
      <b>1) Buy Quantity Rule</b>:
    </p>
    <div class="pl-3">
      <ul>
        <li>
          You cannot buy more at a higher price than you were willing to buy
          at a lower price.
        </li>
        <li>
          You cannot buy less at a lower price than you were willing to buy
          at a higher price.
          <br/>
        </li>
      </ul>
    </div>
    <p>
      <b>2) Sell Quantity Rule</b>:
    </p>
    <div class="pl-3">
      <ul>
        <li>
          You cannot sell more at a lower price than you were willing to
          sell at a higher price.
        </li>
        <li>
          You cannot sell less at a higher price than you were willing to
          sell at a lower price.
          <br>
        </li>
      </ul>
    </div>
    <p>
      <b>3) Switching Rule</b> (ie: from buyer to seller or vice-versa):
    </p>
    <div class="pl-3">
      <ul>
        <li>
          You cannot buy at a higher price than you were willing to sell at.
        </li>
        <li>
          You cannot sell at a lower price than you well willing to buy at.
        </li>
      </ul>
    </div>

    <div>
      <p>
        <b>4) The Quantity Constraints Rule ensures that</b>:
      </p>
      <table style="width: 500px;">
        <tr>
          <th class="_center">When the round price</th>
          <th class="_center">demand</th>
          <td class="_center">supply</td>
        </tr>
        <tr>
          <td class="_center">increases</td>
          <td class="_center">cannot increase</td>
          <td class="_center">cannot decrease</td>
        </tr>
        <tr>
          <td class="_center">decreases</td>
          <td class="_center">cannot decrease</td>
          <td class="_center">cannot increase</td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({
  name: 'QuantityEligibilityRuleCard',
})
export default class QuantityConstraintsRuleCard extends Vue {

}
</script>

<style lang="less" scoped>
.QuantityConstraintsRuleCard {
  ._center {
    text-align: center;
  }
}
</style>
