<template>
  <div class='DeTraderPage'>

    <div class="_top_row mb-1">
      <AuSectionBorder class='_auction_name_panel'>
        <div>
          <TableHeading>
            Auction Name
          </TableHeading>
          <AuOutput
            style="margin: 0; padding: 0"
            text_align="left"
            :height='40'
            :width="599 + (show_credit_limits ? 0 : 159)"
          >
            <strong v-if="category_name">{{category_name}} -</strong>
            {{auction_name}}
          </AuOutput>
        </div>
      </AuSectionBorder>

      <AuSectionBorder
        v-if="show_credit_limits"
        class="_credit_limit_panel"
      >
        <div>
          <TableHeading>
            Credit Limit
          </TableHeading>
          <AuOutput
            style="margin: 0; padding: 0;"
            text_align="center"
            :height='40'
            :value='credit_limit'
            :width="153"
          />
        </div>
      </AuSectionBorder>

      <AuSectionBorder class="_button_panel">
        <!-- Buttons-->
        <ToolbarButton
          class='_button'
          icon='exception'
          @click='showRulesModal = true'
        >
          Auction Rules
          <DeAuctionRulesModal
            v-if='showRulesModal'
            :store='store'
            @close='showRulesModal = false'
          />
        </ToolbarButton>

        <ToolbarButton
          class='_button'
          icon='exception'
          @click='showRulesModal = true'
        >
          Auction Settings
          <DeAuctionRulesModal
            v-if='showRulesModal'
            :store='store'
            @close='showRulesModal = false'
          />
        </ToolbarButton>
      </AuSectionBorder>
    </div>

    <AuSectionBorder class="_middle_row">
      <DeTraderHeader
        :store="store"
        :traderInfo='trader_info'
        :commonStatus='common_status'
        :settings='settings'
      />
    </AuSectionBorder>

    <div class='_bottom_row'>
      <AuSectionBorder v-if='auction_closed'>
        <div>
          <TableHeading>
            Awarded Volumes
          </TableHeading>
          <!-- TODO: what is this?-->
          <AwardMatchesTableDemo style='height: 171px'/>
        </div>
      </AuSectionBorder>

      <div>

        <AuSectionBorder>
          <DeTraderHistoryTable
            :height='bottom_height - (auction_closed ? 200 : 0)'
            :historyRows='history_rows'
            :settings='settings'
          />
        </AuSectionBorder>

        <AuSectionBorder style="margin-left: 5px;">
          <AuctionChatConnected
            :height="bottom_height + 24"
            :width='313'
            :store='store'
            :is_auctioneer="false"
          />
        </AuSectionBorder>

      </div>

    </div>

  </div>
</template>

<script lang='ts'>
import {Component, Prop, Vue} from 'vue-property-decorator';
import DeTraderHeader from './header/DeTraderHeader.vue';
import DeTraderHistoryTable from './history_table/DeTraderHistoryTable.vue';
import AuctionChatConnected from '../../common/components/Chat/AuctionChatConnected.vue';
import {Container} from 'typescript-ioc';
import {CharlizeStore} from '../../../services/connector/CharlizeStore';
import {
  DeAuctionValue,
  DeCommonStatusValue,
  DeSettingsValue,
  DeTraderHistoryRowElement,
  DeTraderInfoValue,
  SocketConnector
} from '@au21-frontend/client-connector';
import {AuScreen} from '../../../plugins/screen-plugin/AuScreen';
import AuSectionBorder from '../../../ui-components/AuSectionBorder.vue';
import AwardMatchesTableDemo from '../DeAuctioneer/award/AwardMatchesTable.demo.vue';
import ToolbarButton from '../DeAuctioneer/toolbar/ToolbarButton.vue';
import DeAuctionRulesModal from './header/children/auction-rules/DeAuctionRulesModal.vue';
import AuOutput from '../../../ui-components/AuOuput/AuOutput.vue';
import TableHeading from '../../common/components/TableHeading/TableHeading.vue';

@Component({
  name: 'DeTraderPage',
  components: {
    AuOutput,
    AuSectionBorder,
    AuctionChatConnected,
    AwardMatchesTableDemo,
    DeAuctionRulesModal,
    DeTraderHeader,
    DeTraderHistoryTable,
    TableHeading,
    ToolbarButton
  }
})
export default class DeTraderPage extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  connector = Container.get(SocketConnector);
  screen = new AuScreen(false);

  showRulesModal = false;

  get auction(): DeAuctionValue | null {
    return this.store.live_store.de_auction;
  }

  get settings(): DeSettingsValue | null {
    return this.auction?.settings;
  }

  get common_status(): DeCommonStatusValue | null {
    return this.auction?.common_status;
  }

  get category_name(): string {
    // TODO Use store.
    return 'CATEGORY_NAME'
  }

  get trader_info(): DeTraderInfoValue | null {
    return this.auction?.trader_info;
  }

  get credit_limit(): string {
    return this.trader_info?.initial_limits.initial_buying_cost_limit_str || '';
  }

  get common_status_label() {
    return this.auction?.common_status?.common_state_text || '';
  }

  get auction_closed(): boolean {
    return this.common_status?.isClosed || false;
  }

  get price_label() {
    return this.settings?.price_label;
  }

  get auction_name() {
    return this.settings?.auction_name;
  }

  get history_rows(): DeTraderHistoryRowElement[] {
    return this.auction?.trader_history_rows;
  }

  get bottom_height() {
    return this.screen.page_height - 210;
  }

  get show_credit_limits (): boolean {
    // TODO Should use global settings
    return this.store.live_store.de_auction.settings.use_counterparty_credits
  }
}
</script>

<style lang='less' scoped>
@import (reference) "../../../au-styles/variables.less";

.DeTraderPage {

  ._top_row {
    display: flex;
    ._auction_name_panel {
      height: 66px;
      margin-right: 6px;
      width: 601px;
      flex-grow: 1;
    }

    ._credit_limit_panel {
      display: inline-block;
      width: 155px;
      margin-right: 5px;
    }

    ._button_panel {
      display: inline-block;
      height: 65px;
      //left: -5px;
    }

    ._button {
      display: block;
      height: 28px;
      width: 140px;
    }
  }

  ._group-box-heading {
    background-color: #333;
    border-radius: 4px;
    border: 1px solid #1a1a1a;
    text-align: center;
  }

  ._middle_row {
    width: 100%; //905px;
  }

  ._bottom_row {
    //position: relative;
    //top: -6px;
    //justify-content: stretch;
    //align-content: stretch;
    //align-items: stretch;
  }


}
</style>
