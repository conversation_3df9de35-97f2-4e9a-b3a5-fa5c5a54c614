<template>
  <div class="MandatoryOrderRuleCard pl-3">
    <ul>
      <li>
        If the minimum and maximum quantities are the same,
        then a <b>Mandatory Order</b> is submitted.
      </li>
      <li>
        That would happen for example:
        <div class="pl-3">
          <ul>
            <li>
              if you bid the same quantity (or no quantity) in consecutive rounds and then the
              price reverses.
            </li>
          </ul>
        </div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({
  name: 'MandatoryOrderRuleCard',
})
export default class MandatoryOrderRuleCard extends Vue {

}
</script>

<style lang="less" scoped>
.MandatoryOrderRuleCard {

}
</style>
