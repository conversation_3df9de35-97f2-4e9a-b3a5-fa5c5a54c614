<template>
  <div>
    <svg
      viewBox="0 0 70 60"
      width="70"
      height="60"
    >

      <defs>
        <marker
          id="arrowhead"
          :markerWidth="arrow * 2"
          :markerHeight="arrow * 2"
          refX="0"
          :refY="arrow"
          orient="auto"
        >
          <polygon
            :points="`0 0, ${arrow * 2} ${arrow}, 0 ${arrow * 2}`"
            :fill="line_color"
          />
        </marker>
      </defs>

      <g>
        <rect
          :x="10"
          :y="0"
          height="18"
          width="55"
          :rx="rect_r"
          :ry="rect_r"
          :style="left_style"
        />

        <rect
          :x="10"
          :y="20"
          height="18"
          width="55"
          :rx="rect_r"
          :ry="rect_r"
          :style="center_style"
        />

        <rect
          :x="10"
          :y="40"
          height="18"
          width="55"
          :rx="rect_r"
          :ry="rect_r"
          :style="right_style"
        />

        <text
          :y="13"
          fill="#111"
        >
          <tspan :x="10 + 8" font-size="12" font-weight="bold">
            {{ isSetup ? "Setup" : "Ready" }}
          </tspan>
        </text>

        <text
          :y="32"
          fill="#111"
        >
          <tspan :x="10 + 3" font-size="12" font-weight="bold">
            {{ "Open" }}
          </tspan>
        </text>

        <text
          :y="52"
          fill="#111"
        >
          <tspan :x="10+ 5" font-size="12" font-weight="bold">Closed</tspan>
        </text>
      </g>
    </svg>
  </div>
</template>

<script lang="ts">

import { Component, Prop, Vue } from 'vue-property-decorator';
import { DeAuctionState, DeCommonStatusValue } from '@au21-frontend/client-connector';
import chroma from 'chroma-js';

/**
 *  NOTE: this panel is used by both auctioneers and traders
 *  - therefore it can't use DeAuctioneerStatusValue
 *   (previously named DeAuctioneerInfoValue)
 */

@Component({
  components: {}
})
export default class DeAuctionStatePanel extends Vue {

  @Prop({ required: true }) commonStatus: DeCommonStatusValue;

  @Prop() color: string;

  auRBGScale = chroma.scale(['#0f0', '#F00']).mode('lrgb');

  state_colors: { [state in DeAuctionState]: string } = {
    STARTING_PRICE_NOT_SET: this.auRBGScale(0.5).hex(),
    STARTING_PRICE_SET: this.auRBGScale(0.9).hex(), //this.auRBGScale(0.50).hex(), //'#ddad00',
    STARTING_PRICE_ANNOUNCED: this.auRBGScale(0.5).hex(), //  this.auRBGScale(0.50).hex(), // '#ddad00',
    ROUND_OPEN: this.auRBGScale(0.25).hex(), // this.auRBGScale(0.50).hex(),  // 'rgba(52, 168, 83, 1.000)', //'#68c675',
    ROUND_CLOSED: this.auRBGScale(0.90).hex(),
    AUCTION_CLOSED: '#999'
  };

  // round_closed_color(): string {
  //   return this.auctioneerStatus.isAwardable ?
  //     this.auRBGScale(0.90).hex() //  this.auRBGScale(0.50).hex(),
  //     : this.auRBGScale(0.75).hex() //// this.auRBGScale(0.50).hex(),//'rgba(251, 188, 5, 1.000)',
  // }

  // PROPS:

  // @Prop({ required: true, default: DeAuctionState.AUCTION_INIT }) state: DeAuctionState;

  get state(): DeAuctionState | null {
    return this.commonStatus?.auction_state;
  }

  get isSetup(): boolean {
    return this.state === DeAuctionState.STARTING_PRICE_NOT_SET;
  }

  get paused(): boolean {
    return false; // this.commonStatus?.auction_state == DeAuctionState.ROUND_PAUSED
  }

  state_color(states: DeAuctionState[]): string {
    return states.includes(this.state) ?
      this.state_colors[this.state] : '#999';
  }

  disabled_color = '#aaa';

  // STYLE:

  baseStyle = {
    strokeWidth: 3,
    fillOpacity: '1.0'
  };

  get left_style() {
    return {
      ...this.baseStyle,
      fill:
        this.state == DeAuctionState.STARTING_PRICE_NOT_SET ?
          this.color :
          this.disabled_color
    };
  }

  get center_style() {
    return {
      ...this.baseStyle,
      fill:
        this.state in [DeAuctionState.ROUND_OPEN] ?
          this.color :
          this.disabled_color
    };
  }

  get right_style() {
    return {
      ...this.baseStyle,
      fill:
        this.state in [DeAuctionState.ROUND_CLOSED] ?
          this.color :
          this.disabled_color
    };
  }

  line_color = '#666';


  // POSITION:

  arrow = 2;
  x1 = 5;
  x2 = 60;
  x3 = 128;

  r = 10;
  cy = 35;
  rect_r = 3;

  cx1 = 50;
  cx2 = 110;
}
</script>

<style scoped>
</style>
