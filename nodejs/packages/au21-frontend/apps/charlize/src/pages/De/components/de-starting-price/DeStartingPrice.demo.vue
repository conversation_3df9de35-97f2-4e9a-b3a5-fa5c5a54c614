<template>
  <div>
    <VbDemo>
      <VbCard>
        Actions:
        <a-button @click="set_starting_price">Set starting price</a-button>
        <a-button @click="announce_starting_price">Announce</a-button>
        <a-button @click="start_auction">Start auction</a-button>
        <a-button @click="close_round">Close round</a-button>
        <a-button @click="next_round">Next round</a-button>
        <a-button @click="award_auction">Award auction</a-button>
      </VbCard>
      <VbCard>
        States:
        <a-button @click="setAuctionState('STARTING_PRICE_NOT_SET')">STARTING_PRICE_NOT_SET</a-button>
        <a-button @click="setAuctionState('STARTING_PRICE_SET')">STARTING_PRICE_SET</a-button>
        <a-button @click="setAuctionState('STARTING_PRICE_ANNOUNCED')">STARTING_PRICE_ANNOUNCED</a-button>
        <a-button @click="setAuctionState('ROUND_OPEN')">ROUND_OPEN</a-button>
      </VbCard>
      <VbCard>
        current state: {{ commonStatus.auctioneer_state }}
      </VbCard>
    </VbDemo>
    <VbDemo>
      <VbCard>
        <DeStartingPrice
          :commonStatus="commonStatus"
          :auctioneerStatus="auctioneerStatus"
        />
      </VbCard>
    </VbDemo>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createDemo__DeCommonStatusValue} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import {DeAuctioneerStatusValue, DeCommonState} from '@au21-frontend/client-connector';
import {createDemo__DeAuctioneerStatusValue} from '../../../../demo-helpers/DeAuctioneerStatusValue.helper';
import DeStartingPrice from './DeStartingPrice.vue';

@Component({
  components: {DeStartingPrice}
})
export default class DeStartingPricePanelDemo extends Vue {
  commonStatus = createDemo__DeCommonStatusValue();
  auctioneerStatus: DeAuctioneerStatusValue = createDemo__DeAuctioneerStatusValue();

  set_starting_price() {
    if (this.commonStatus.common_state == DeCommonState.SETUP) {
      this.commonStatus.round_price = '100.000';
      this.commonStatus.common_state = DeCommonState.SETUP
    }
  }

  setAuctionState(s) {
    this.commonStatus.common_state = s;
  }

  announce_starting_price() {
    if (this.commonStatus.common_state == DeCommonState.SETUP) {
      this.commonStatus.common_state = DeCommonState.STARTING_PRICE_ANNOUNCED;
    }
  }

  start_auction() {
    if (this.commonStatus.common_state == DeCommonState.STARTING_PRICE_ANNOUNCED) {
      this.commonStatus.common_state = DeCommonState.ROUND_OPEN;
    }
  }

  close_round() {
    if (this.commonStatus.common_state == DeCommonState.ROUND_OPEN) {
      this.commonStatus.common_state = DeCommonState.ROUND_CLOSED;
    }
  }

  next_round() {
    if (this.commonStatus.common_state == DeCommonState.ROUND_CLOSED) {
      this.commonStatus.common_state = DeCommonState.ROUND_OPEN;
    }
  }

  award_auction() {
    if (this.commonStatus.common_state == DeCommonState.ROUND_CLOSED) {
      this.commonStatus.common_state = DeCommonState.AUCTION_CLOSED;
    }
  }
}
</script>
