<template>

  <div class="DeOrderBookCell">

    <!-- -------------------- BUY SIDE -------------------->

    <!-- ------------ Column 1: BUYER -------------->
    <div v-if=" row.hasBuy && columnName ===  'buyCompanyShort'">
      <div :style="{color: colors.au_buy()}">
        <div class="_trader_cell">{{ row.buyCompanyShort }}</div>
        <div class="_trader_cell">({{ row.buySubmittedBy }})</div>
      </div>
    </div>

    <!-- ------------ Column 2: BUY QUANTITY -------------->
    <div
      v-else-if="row.hasBuy && columnName === 'buyQuantity'">
      <BuySellHBar
        :width="67"
        :height="32"
        :buy-max="50"
        :sell-max="50"
        :match="row.match"
        :order_submission_type="OrderSubmissionType.MANUAL"
        :order_quantity_int="row.buyQuantityInt"
        :order_quantity_str="row.buyQuantityStr"
        :order_type="OrderType.BUY"
      />
    </div>


    <!-- -------------------- SELL SIDE -------------------->

    <!-- ------------ Column 3: SELL QUANTITY -------------->
    <div v-else-if="row.hasSell && columnName === 'sellQuantity'">
      <!--    <div-->
      <!--      class="DeOrderBookCell__quantity"-->
      <!--      :style="{color: colors.au_sell()}"-->
      <!--    >{{ row.sellQuantity }}-->
      <!--    </div>-->
      <BuySellHBar
        :width="67"
        :height="32"
        :buy-max="50"
        :sell-max="50"
        :match="row.match"
        :order_submission_type="OrderSubmissionType.MANUAL"
        :order_quantity_int="row.sellQuantityInt"
        :order_quantity_str="row.sellQuantityStr"
        :order_type="OrderType.SELL"
      />
    </div>

    <!-- ------------ Column 4: SELLER -------------->
    <div v-else-if="row.hasSell && columnName === 'sellCompanyShort'">
      <div :style="{color: colors.au_sell()}">
        <div class="_trader_cell">{{ row.sellCompanyShort }}</div>
        <div class="_trader_cell"
             v-if="row.sellSubmittedBy"
        >({{ row.sellSubmittedBy }})
        </div>
      </div>
    </div>


  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeOrderBookRow} from './DeOrderBookRow';
import {Container} from 'typescript-ioc';
import {AuColors} from '../../../../au-styles/AuColors';
import BuySellHBar from "../../../common/components/BuySellBars/BuySellHBar.vue";
import {OrderSubmissionType, OrderType} from "@au21-frontend/client-connector"

@Component({
  components: {BuySellHBar}
})
export default class DeOrderBookCell extends Vue {
  params = null;
  colors = Container.get(AuColors);

  OrderSubmissionType = OrderSubmissionType
  OrderType = OrderType

  get row(): DeOrderBookRow {
    return this.params.data;
  }

  get columnName(): string {
    return this.params.column.colId;
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";


.DeOrderBookCell {
  color: #222222;
  // height: 24px;
  text-align: center;
  position: absolute;

  // TODO: is this used? Use in header cell component?
  //
  //.de-order-book-header {
  //  background-color: rgb(34, 34, 34); //rgb(54, 62, 63);
  //  font-size: 11px;
  //  font-weight: 200;
  //  height: 18px;
  //  overflow: hidden;
  //  position: relative;
  //  top: -1px;
  //}


  ._trader_cell {
    //border: 1px solid red;
    line-height: 1.1em;
    margin: 5px;
  }

  ._quantity {
    color: #222;
    font-size: 14px;
    margin-right: 20px;
    margin-top: 5px;
    text-align: right;
    width: 40px;
  }

}
</style>
