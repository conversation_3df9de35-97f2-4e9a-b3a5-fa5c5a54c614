<template>
  <AuAgGrid
    class="RoundConstraintsTable"
    :height="height"
    :width="width"
    :columnDefs="column_defs"
    :rowData="rows"
    :getRowHeight="() => row_height"
    :gridOptions="grid_options"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {ColDef, GridOptions} from 'ag-grid-community';
import {DeRoundTraderElement,} from '@au21-frontend/client-connector';
import RoundConstraintsTableRow from './RoundConstraintsTableRow.vue';
import AuAgGridCenteredHeader from "../../../../ui-components/AuAgGridCenteredHeader.vue";
import {range} from "lodash";
import {AuScreen} from "../../../../plugins/screen-plugin/AuScreen";

@Component({
  name: 'ConstraintsTable',
  components: {AuAgGrid, RoundConstraintsTableRow}
})
export default class RoundConstraintsTable extends Vue {
  @Prop({required: true, type: Number}) height_offset;
  @Prop({
    required: true,
    type: Array
  }) round_trader_elements_for_round: DeRoundTraderElement[];

  row_height = 24;
  width = 400;

  screen = new AuScreen(true);

//  filter: 'all' | 'sellers' | 'buyers' = 'all'

  get height(): number {
    return this.screen.page_height - this.height_offset
  }

  grid_options: GridOptions = {
    defaultColDef: {
      cellRendererFramework: RoundConstraintsTableRow,
      //autoHeight: false, // this is the default
      headerComponentFramework: AuAgGridCenteredHeader,

    },
    animateRows: false
  };

  get column_defs(): ColDef[] {
    return [
      {
        headerName: 'Company',
        width: 75
      },
      {
        headerName: 'Constraints',
        width: 120
      },
      {
        headerName: 'Order', // vol and type
        width: 85
      },
      {
        headerName: 'Match', // match and potential
        width: 85
      },
    ];
  }


  get empty_row_count(): number {
    const body_height = this.height - 40; // ie: - header_height
    const rows_height = this.round_trader_elements_for_round.length * this.row_height;
    const empty_height = body_height - rows_height;
    return empty_height > 0 ?
      Math.ceil(empty_height / this.row_height)
      : 0;
    //  console.log({body_height, rows_height, empty_height, empty_rows})
    // return empty_rows
  }


  get rows(): DeRoundTraderElement[] {
    const rows = this.round_trader_elements_for_round;
    range(this.empty_row_count).forEach(index => {
      rows.push({
        bid_while_closed: false,
        buyer_credit_limit: 30_000_000.0,
        buyer_credit_limit_str: "$30,000,000.00",
        changed: false,
        cid: 'empty_row_' + index,
        company_shortname: '',
        constraints: null,
        id: 'empty_row_' + index,
        match: null,
        order_submission_type: null,
        order_submitted_by: '',
        order_type: null,
        round: null,
        timestamp_formatted: '',
        quantity_int: null,
        quantity_str: ''
      });
    });
    return rows;
  }

}
</script>

<style lang="less" scoped>
.RoundConstraintsTable {


}
</style>
