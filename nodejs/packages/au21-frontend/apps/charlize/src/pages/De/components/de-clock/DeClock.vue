<template>
  <div
    class="DeClock"
    :style="{
      width: diameter + 'px',
      height: diameter + 'px',
    }"
    :class="{
      'DeClock--color-warning':  value >= warningSecs && dangerSecs > value,
      'DeClock--color-alert': value >= dangerSecs,
    }"
  >
    <svg
      class="DeClock__svg" :viewBox="`0 0 50 50`"
      :height="diameter"
      :width="diameter"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g class="DeClock__circle">
        <circle
          class="DeClock__path-elapsed"
          :cx="25"
          :cy="25"
          :r="25 - 6"
        ></circle>
      </g>
    </svg>
    <div class="DeClock__label">{{ value }}</div>
    <span class="DeClock__units-label">secs</span>
  </div>
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({
  name: 'De<PERSON><PERSON>',
})
export default class DeClock extends Vue {
  @Prop({type: Number, required: true}) value: number
  @Prop({type: Number, required: true}) warningSecs: number
  @Prop({type: Number, required: true}) dangerSecs: number

  diameter = 60
}
</script>

<style lang="less" scoped>
.DeClock {
  overflow: hidden;
  position: relative;

  &__path-elapsed {
    transition: stroke 0.5s ease-in-out;
  }

  &--color-warning .DeClock__path-elapsed {
    stroke: orange;
  }

  &--color-alert .DeClock__path-elapsed {
    stroke: red;
    animation: DeClock--red-pulse 2s infinite;
  }

  /* TODO: not sure why we have scaleX and rotate(90deg) below ?? */

  &__svg {
    transform: scaleX(-1);
  }

  &__circle {
    fill: none;
    stroke: none;
  }

  &__path-elapsed {
    stroke-width: 3px;
    stroke: grey;
  }

  &__path-remaining {
    fill-rule: nonzero;
    stroke-linecap: round;
    stroke-width: 3px;
    stroke: currentColor;
    transform-origin: center;
    transform: rotate(90deg);
    transition: 1s linear all;
  }

  &__label {
    text-align: center;
    align-items: center;
    font-size: 24px;
    justify-content: center;
    left: 0px;
    position: absolute;
    top: 8px;
    width: 60px;
    color: white;
  }

  /* from DeAuctionSummaryPanel */

  &__units-label {
    font-size: 9px;
    font-weight: bold;
    left: -39px;
    padding-right: 2px;
    position: relative;
    text-align: center;
    top: -15px;
    width: 100%;
    color: white;
  }
}

@keyframes DeClock--red-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
  }
}
</style>
