<template>
  <AuAgGrid
    class="AwardScenarioTable"
    :columnDefs="columnDefs"
    :rowData="flowScenarioRows"
    :height="height"
    turnOffAutoColSize
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ColDef} from 'ag-grid-community';
import AuAgGrid from '../../../../../ui-components/AuAgGrid.vue';
import AuSectionHeader from '../../../../../ui-components/AuSectionHeader.vue';
import AuAgGridCenteredHeader from '../../../../../ui-components/AuAgGridCenteredHeader.vue';
import {FlowScenarioRow, FlowScenarioRowItem} from './FlowScenarioRow';

type ScenarioTableRow = {
  id: string,
  text: string,
}

// @deprecated This component is not used right now, but might find its uses.
@Component({
  name: 'AwardScenarioTable',
  components: {
    AuSectionHeader,
    AuAgGrid,
  },
})
export default class AwardScenarioTable extends Vue {
  height = 161
  @Prop({required: true, type: Array}) flowScenarioRows: FlowScenarioRow[]
  @Prop({required: true, type: Array}) companies: FlowScenarioRow[]
  @Prop({type: String}) selectedCompanyId: string

  get columnDefs() {
    const flows = ['100', '75', '50', '25']

    const cols: ColDef[] = [
      {
        headerName: 'Max Flow',
        width: 140,
        valueFormatter: (value) => {
          const row: FlowScenarioRow = value.data
          return row.company_id
        },
        cellStyle: {paddingTop: '3px'},
        cellClass: 'align-right',
        headerClass: 'align-center',
        headerComponentFramework: AuAgGridCenteredHeader,
      },
    ]

    flows.forEach((flow) => {
      cols.push({
        headerName: flow,
        width: 100,
        cellClass: 'align-center',
        suppressMenu: true,
        valueFormatter: (value) => {
          const row: FlowScenarioRow = value.data
          const item: FlowScenarioRowItem = row[flow]
          return `${item.isBuy ? 'Buy' : 'Sell'} ${item.amount}`
        },
        headerComponentFramework: AuAgGridCenteredHeader,
      })
    })

    return cols
  }

  get rowData(): ScenarioTableRow[] {
    return [
      {
        id: '100',
        text: '100',
      },
      {
        id: '75',
        text: '75',
      },
      {
        id: '50',
        text: '50',
      },
      {
        id: '25',
        text: '25',
      },
    ]
  }

  setSelectedRound(round: number) {
    this.$emit('update:selectedRound', round)
  }
}
</script>

<style lang="less" scoped>

</style>
