<template>
  <div class="DeSupplyDemandChart"/>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import Plotly from 'plotly.js-dist-min';
import {Data, Layout, newPlot, PlotData} from 'plotly.js';
import {DeRoundElement} from '@au21-frontend/client-connector';
import {DeRoundChartNonQuantityAxis, DeRoundChartQuantityAxis} from '../DeRoundChart/DeRoundChart-types';

// @archieved Used plotly, which we disposed of, so was archieved
@Component({
  name: 'DeSupplyDemandChart',
})
export default class DeSupplyDemand<PERSON>hart extends Vue {
  @Prop({required: true, type: Array}) rounds: DeRoundElement[]
  @Prop({required: true}) height: number
  @Prop({required: true}) width: number
  @Prop({type: String, required: true}) quantity_axis: DeRoundChartQuantityAxis
  @Prop({type: String, required: true}) non_quantity_axis: DeRoundChartNonQuantityAxis

  async mounted() {
    await this.drawChart()
  }

  watch: {
    quantity_axis: 'drawChart',
    non_quantity_axis: 'drawChart'
  }

  @Watch('quantity_axis')
  @Watch('non_quantity_axis')
  async drawChart() {
    const parseRoundPrice = (rounds: DeRoundElement[], field: 'buy_quantity' | 'sell_quantity') => {
      const nonQuantityValues: number[] = []
      const quantityValues: number[] = []
      const roundPrice = this.non_quantity_axis === DeRoundChartNonQuantityAxis.PRICE ? 'round_price' : 'round_number'
      rounds.forEach(current => {
        nonQuantityValues.push(current[roundPrice])
        quantityValues.push(+current[field])
      })
      return this.quantity_axis === DeRoundChartQuantityAxis.X ? {
        x: quantityValues,
        y: nonQuantityValues
      } : {x: nonQuantityValues, y: quantityValues}
    }

    const trace1: Partial<PlotData> = {
      ...parseRoundPrice(this.rounds, 'buy_quantity'),
      type: 'scatter',
      line: {
        color: '#a5ca56',
      },
      marker: {
        size: 5,
        color: '#a5ca56',
      },
      textfont: {
        color: 'white',
      },
      name: 'Demand',
    }

    const trace2: Partial<PlotData> = {
      ...parseRoundPrice(this.rounds, 'sell_quantity'),
      type: 'scatter',
      line: {
        color: '#73c9e1',
      },
      textfont: {
        color: 'white',
      },
      // label
      name: 'Supply',
    }

    const data: Data[] = [trace1, trace2]

    const quantityAxis = {
      color: 'white',
      title: 'Quantity',
    }
    const sequenceAxis = {
      color: 'white',
      linecolor: 'white',
      title: this.non_quantity_axis === DeRoundChartNonQuantityAxis.PRICE ? 'Price' : 'Round',
    }

    const layout: Partial<Layout> = {
      paper_bgcolor: 'rgb(45, 52, 54)',
      plot_bgcolor: 'rgba(255,255,255,0)',
      font: {
        color: 'white',
      },
      width: this.width,
      height: this.height,
      margin: {
        t: 70,
        b: 70,
        l: 70,
        r: 70,
        pad: 0,
      },
      xaxis: this.quantity_axis === DeRoundChartQuantityAxis.X ? quantityAxis : sequenceAxis,
      yaxis: this.quantity_axis === DeRoundChartQuantityAxis.X ? sequenceAxis : quantityAxis,
      scene: {
        xaxis: {title: 'Trader', color: 'white'},
        yaxis: {title: 'Price', color: 'white'},
      },
    }

    await (Plotly.newPlot as typeof newPlot)(this.$el as HTMLElement, data as Data[], layout)
  }
}
</script>

<style lang="scss">

</style>
