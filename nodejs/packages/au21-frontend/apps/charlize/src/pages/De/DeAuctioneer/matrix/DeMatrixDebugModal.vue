<template>
  <div>
    <button @click="show = true"
            style="position: absolute; left: 5px">debug
    </button>
    <a-modal
      v-if="show"
      class="DeMatrixDebugModal"
      title="DeMatrix Debug Panel"
      @cancel="close"
      visible
      closable
      centered
      width="800px"
    >
      <a-tabs
        default-active-key="1"
        :animated="false"
        style="height: 600px; color: white">

        <a-tab-pane key="1" tab="Nodes" primary>
          <a-table
            :columns="node_columns"
            rowKey="id"
            :data-source="params.nodes"
            :scroll="{ x: 600, y: 300 }"
            :pagination="false"
          >
          </a-table>
        </a-tab-pane>

        <a-tab-pane key="2" tab="Edges" primary>
          <a-table
            :columns="edge_columns"
            rowKey="id"
            :data-source="params.edges"
            :scroll="{ x: 600, y: 300 }"
            :pagination="false"
          >
          </a-table>
        </a-tab-pane>

        <!--        <a-tab-pane key="3" tab="Edges" primary>-->
        <!--          <a-table-->
        <!--            :columns="edge_columns"-->
        <!--            rowKey="id"-->
        <!--            :data-source="params.edgesSorted || []"-->
        <!--            :scroll="{ x: 600, y: 300 }"-->
        <!--            :pagination="false"-->
        <!--          >-->
        <!--          </a-table>-->
        <!--        </a-tab-pane>-->

      </a-tabs>

      <a-button
        slot="footer"
        type="primary"
        @click="close"
      >
        Close
      </a-button>
    </a-modal>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({
  name: 'DeMatrixDebugModal',
  components: {}
})
export default class DeMatrixDebugModal extends Vue {

  params = null;
  show = false;

  node_columns = [
    {title: 'shortname', dataIndex: 'shortname', width: 100, fixed: 'left'},
    {title: 'buy_max', dataIndex: 'buy_max', width: 85},
    {title: 'buy_min', dataIndex: 'buy_min', width: 85},
    {title: 'sell_min', dataIndex: 'sell_min', width: 85},
    {title: 'sell_max', dataIndex: 'sell_max', width: 85},
    {title: 'buy_vol', dataIndex: 'buy_vol', width: 85},
    {title: 'sell_vol', dataIndex: 'sell_vol', width: 85},
    {title: 'buy_match', dataIndex: 'buy_match', width: 85},
    {title: 'sell_match', dataIndex: 'sell_match'}
  ];

  edge_columns = [
    {title: 'buyer', dataIndex: 'b_shortname', width: 100},
    {title: 'seller', dataIndex: 's_shortname', width: 100},
    {title: 'actual match', dataIndex: 'match', width: 100},
    {title: 'potential', dataIndex: 'capacity', width: 100},
    {title: 'credit', dataIndex: 'credit_str'}
  ];

  close() {
    this.show = false;
  }
}
</script>

<style lang="less" scoped>
.DeMatrixDebugModal {
}
</style>
