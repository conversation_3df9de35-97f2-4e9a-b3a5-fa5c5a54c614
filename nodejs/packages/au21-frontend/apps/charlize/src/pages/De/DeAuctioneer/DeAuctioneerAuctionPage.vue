<template>
  <div
    class="DeAuctioneerAuctionPage"
    :class="{'-highlighted': !is_last_round_selected}"
  >
    <DeAuctioneerPageHeader
      :store="store"
      :selected_round="selected_round"
    />

    <DeAuctioneerToolbar
      style="position: relative; top: 3px"
      :store="store"
      :show_only_seen.sync="showOnlySeen"
      @update:showOnlySeen="showOnlySeen"
    />

    <div :style="{ height: this.bottomSectionHeight + 8 + 'px' }">
      <AuSectionBorder style="float:left; margin-right: 3px">
        <div style="float:left">
          <TableHeading>
            <div style='display: inline-block'>Round History</div>
          </TableHeading>
          <DeRoundTable
            :auction_id="this.auction.auction_id"
            :online_users="online_users"
            :users_that_have_seen_auction="auction.users_that_have_seen_auction"
            :blotter="auction.blotter"
            :selected_round="selected_round"
            @update:selected_round="updateSelectedRound"
            :height="bottomSectionHeight"
            :buyMax="buyMax"
            :sellMax="sellMax"
          />
        </div>
      </AuSectionBorder>

      <!--      <div style="float: left; overflow: hidden;"-->
      <!--           :style="`height: ${bottomSectionHeight + 7}px`">-->

      <AuSectionBorder style="float:left; margin-right: 3px">
        <DeOrderBook
          :round_trader_elements="round_trader_elements_selected_round"
          :companies="companies"
          :height="bottomSectionHeight"
          :quantity_label="deSettingsValue.quantity_label"
        />
      </AuSectionBorder>

      <AuSectionBorder style="float:left; margin-right: 3px">
        <DeSankeyDiagram
          :round_trader_elements="round_trader_elements_selected_round"
          :users="users"
          :matrix_edges="matrixEdges"
          :height="bottomSectionHeight"
          :width="310"
        />
      </AuSectionBorder>

      <!--      </div>-->

      <!--      <AuSectionBorder style="margin-left: 3px">-->
      <!--        <DeMatrix-->
      <!--          style="float:left;"-->
      <!--          :height="bottomSectionHeight"-->
      <!--          :nodes="matrixNodes"-->
      <!--          :edges="matrixEdges"-->
      <!--          :round="roundNumber"-->
      <!--        />-->
      <!--        <ConstraintsTable-->
      <!--          :height_offset="130"-->
      <!--          :round_trader_elements_for_round="round_trader_elements_selected_round"-->
      <!--        />-->
      <!--      </AuSectionBorder>-->

      <AuSectionBorder>
        <AuctionChatConnected
          :height="bottomSectionHeight + 24"
          :width="305"
          :store="store"
          :is_auctioneer="true"
        />
      </AuSectionBorder>

    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import DeAuctioneerToolbar from './toolbar/DeAuctioneerToolbar.vue';
import DeOrderBook from './order_book/DeOrderBook.vue';
import DeMatrix from './matrix/DeMatrix.vue';
import DeRoundTable from './round_table/DeRoundTable.vue';
import {Container} from 'typescript-ioc';
import {
  de_round_history_command,
  DeAuctionValue,
  DeMatrixEdgeElement,
  DeMatrixNodeElement,
  DeMatrixRoundElement,
  DeRoundElement,
  DeRoundTraderElement,
  DeTraderElement,
  SocketConnector,
  UserElement
} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../services/connector/CharlizeStore';
import {AuScreen} from '../../../plugins/screen-plugin/AuScreen';
import AuSectionBorder from '../../../ui-components/AuSectionBorder.vue';
import AuctionChatConnected from '../../common/components/Chat/AuctionChatConnected.vue';
import DeAuctioneerPageHeader from './header/DeAuctioneerPageHeader.vue';
import DeSankeyDiagram from './sankey/DeSankeyDiagram.vue';
import DeEligibilityModal from './eligibility_table/DeEligibilityModal.vue';
import {
  company_ids,
  round_trader_elements_for_round,
  sort_by_shortname,
  traders_that_have_seen_auction
} from '../../../services/helpers/de-helpers';
import ConstraintsTable from './round_constraints/RoundConstraintsTable.vue';
import TableHeading from '../../common/components/TableHeading/TableHeading.vue';

@Component({
  name: 'DeAuctioneerAuctionPage',
  components: {
    TableHeading,
    ConstraintsTable,
    DeSankeyDiagram,
    AuSectionBorder,
    AuctionChatConnected,
    DeAuctioneerPageHeader,
    DeAuctioneerToolbar,
    DeOrderBook,
    DeRoundTable,
    DeEligibilityModal,
    DeMatrix
  }
})
export default class DeAuctioneerAuctionPage extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  connector = Container.get(SocketConnector);
  screen = new AuScreen(true);

  selected_round = this.auction.common_status.round_number; // || 0; // seems better than passing in a null
  showOnlySeen = false;

  @Watch('round_number')
  onRoundNumberChange(round_number_new: number, round_number_old: number) {
    if (this.selected_round === round_number_old) {
      // We only move selected round if last round is already selected
      this.updateSelectedRound(round_number_new);
    }
  }

  get auction(): DeAuctionValue {
    return this.store.live_store.de_auction;
  }

  get auctioneerInfo() {
    return this.auction.auctioneer_info;
  }

  get deSettingsValue() {
    return this.auction.settings;
  }

  get bottomSectionHeight() {
    return this.screen.page_height - 135;
  }

  get commonStatus() {
    return this.auction.common_status;
  }

  get roundNumber() {
    return this.auction.common_status.round_number;
  }

  get auctioneerStatus() {
    return this.auction.auctioneer_status;
  }

  get companies() {
    return this.store.live_store.companies;
  }

  get users() {
    return this.store.live_store.users;
  }

  get round_number() {
    return this.auction.common_status.round_number;
  }

  get rounds(): DeRoundElement[] {
    return this.auction.blotter.rounds;
  }

  get traders(): DeTraderElement[] {
    return sort_by_shortname(this.auction.blotter.traders)
  }

  get tradersComputed(): DeTraderElement[] {
    let result = this.auction.blotter.traders
    if (this.showOnlySeen) {
      result = traders_that_have_seen_auction(result)
    }
    return sort_by_shortname(result)
  }

  get companyIdSeenSet(): Set<string> {
    return company_ids(this.tradersComputed);
  }

  get round_trader_elements(): DeRoundTraderElement[] {
    return this.auction.blotter.round_traders;
  }

  get round_trader_elements_selected_round(): DeRoundTraderElement[] {
    let result = round_trader_elements_for_round(
      this.round_trader_elements,
      this.selected_round
    );
    if (this.showOnlySeen) {
      result = result.filter(traderElement => this.companyIdSeenSet.has(traderElement.cid))
    }
    return result
  }

  get is_last_round_selected (): boolean {
    return this.selected_round === this.round_number
  }

  get currentMatrixRoundElement(): DeMatrixRoundElement | null {
    if (this.is_last_round_selected) {
      return this.auction.matrix_last_round;
    }
    return this.store.stale_store.stale_de_matrix_rounds.find(
      round => round.round_number === this.selected_round
    );
  }

  get matrixEdges(): DeMatrixEdgeElement[] {
    if (!this.currentMatrixRoundElement) {
      return [];
    }

    return this.currentMatrixRoundElement.edges.filter(
      edge => this.companyIdSeenSet.has(edge.buyer_cid) &&
        this.companyIdSeenSet.has(edge.seller_cid)
    );
  }

  get matrixNodes(): DeMatrixNodeElement[] {
    if (!this.currentMatrixRoundElement) {
      return [];
    }

    return this.currentMatrixRoundElement.nodes.filter(
      node => this.companyIdSeenSet.has(node.cid)
    );
  }

  get buyMax() {
    return 50 // +this.auction.settings.default_buyer_credit_limit;
  }

  get sellMax() {
    return 50 // +this.auction.settings.default_seller_quantity_limit;
  }

  get currentRoundNodes(): DeMatrixNodeElement[] {
    return this.matrixNodes
      .filter(node => node.round === this.selected_round);
  }

  get currentRoundTraders(): DeRoundTraderElement[] {
    return this.round_trader_elements
      .filter(
        roundTrader => roundTrader.round === this.selected_round
          && this.companyIdSeenSet.has(roundTrader.cid)
      );
  }

  roundLoaded(roundNumber: number) {
    return this.store.stale_store.stale_de_matrix_rounds
      ?.some(matrix_round => matrix_round.round_number === roundNumber);
  }

  updateSelectedRound(roundNumber: number): void {
    this.selected_round = roundNumber;
    if (this.roundLoaded(roundNumber)) return;
    this.connector.publish(de_round_history_command({
        auction_id: this.auction.auction_id,
        round_number: roundNumber.toString()
      })
    );
  }

  get online_users(): UserElement[] {
    return this.store?.live_store?.users?.filter(u => u.isOnline) || []
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../au-styles/variables.less";

.DeAuctioneerAuctionPage {
  border-radius: @border-radius-panel;
  &.-highlighted {
    background-color: red;
  }
  position: relative;
}

</style>
