<template>
  <Blinker
    :value="blinkerValue"
    background
    v-if="showResults">

    <DeMatrixDataPanel
      shortname=""
      label_max="limit"
      label_hatched="capacity"
      label_solid="match"
      :vol_max="vol_max"
      :vol_hatched="edge.capacity"
      :vol_solid="edge.match"
      :au_text_color="colors.au_match()"
      :bar_color="colors.au_match()"
    />
  </Blinker>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeMatrixEdgeElement, DeMatrixNodeElement, OrderType} from '@au21-frontend/client-connector';
import {TraderMatrixRow} from './DeMatrixRows';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {Container} from 'typescript-ioc';
import {AuColors} from 'apps/charlize/src/au-styles/AuColors';
import DeMatrixDataPanel from './DeMatrixDataPanel.vue';

@Component({
  components: {Blinker, DeMatrixDataPanel}
})
export default class DeMatrixBodyCell extends Vue {
  params = null;

  colors = Container.get(AuColors);

  get row(): TraderMatrixRow {
    return this.params.data;
  }

  get edge(): DeMatrixEdgeElement {
    // TODO: not sure why we're just looking at seller??
    return this.row.edges.find(node => node.seller_cid === this.node.cid);
  }

  get node(): DeMatrixNodeElement {
    return this.params.column.colDef.cellRendererParams.trader;
  }

  get vol_max(): number {
    switch (this.node.side) {
      case OrderType.BUY:
        return this.node.buy_max
      case OrderType.SELL:
        return this.node.sell_max
      default:
        return 0
    }
  }

  get showResults() {
    return this.edge && this.edge.capacity && this.edge.buyer_cid !== this.node.cid;
  }

  get blinkerValue() {
    return `${this.edge?.capacity}-${this.edge?.match}`;
  }
}
</script>


<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.DeMatrixDataPanel {
}
</style>
