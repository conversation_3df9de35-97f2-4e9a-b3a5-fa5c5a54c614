<template>
  <VbDemo>
    <VbCard title="price">
      <DeRoundChart
        :rounds="rounds"
        :traders="traders"
        :cells="cells"
        :width="500"
        :height="500"
        :non_volume_axis="DeRoundChartNonVolumeAxis.PRICE"
        :volume_axis="DeRoundChartVolumeAxis.Y"
      />
    </VbCard>
    <VbCard title="round">
      <DeRoundChart
        :rounds="rounds"
        :traders="traders"
        :cells="cells"
        :width="500"
        :height="500"
        :non_volume_axis="DeRoundChartNonVolumeAxis.ROUND"
        :volume_axis="DeRoundChartVolumeAxis.Y"
      />
    </VbCard>
    <VbCard title="price + X">
      <DeRoundChart
        :rounds="rounds"
        :traders="traders"
        :cells="cells"
        :width="500"
        :height="500"
        :non_volume_axis="DeRoundChartNonVolumeAxis.PRICE"
        :volume_axis="DeRoundChartVolumeAxis.X"
      />
    </VbCard>
    <VbCard title="round + X">
      <DeRoundChart
        :rounds="rounds"
        :traders="traders"
        :cells="cells"
        :width="500"
        :height="500"
        :non_volume_axis="DeRoundChartNonVolumeAxis.ROUND"
        :volume_axis="DeRoundChartVolumeAxis.X"
      />
    </VbCard>
    <VbCard title="is reactive">
      <button @click="nonVolumeAxis = nonVolumeAxis === DeRoundChartNonVolumeAxis.PRICE ? DeRoundChartNonVolumeAxis.ROUND : DeRoundChartNonVolumeAxis.PRICE">
        nonVolumeAxis: {{nonVolumeAxis}}
      </button>
      <button @click="volumeAxis = volumeAxis === DeRoundChartVolumeAxis.Y ? DeRoundChartVolumeAxis.X : DeRoundChartVolumeAxis.Y">
        volumeAxis: {{volumeAxis}}
      </button>
      <DeRoundChart
        :rounds="rounds"
        :traders="traders"
        :cells="cells"
        :width="500"
        :height="500"
        :non_volume_axis="nonVolumeAxis"
        :volume_axis="volumeAxis"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import DeRoundChart from './DeRoundChart.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {
  createDemo__DeRoundElement,
  createDemo__DeRoundTraderElement
} from '../../../../../demo-helpers/DeRoundTable.helper';
import {flatten, range} from 'lodash';
import {createDemo__CompanyElement} from '../../../../../demo-helpers/CompanyElement.helper';
import {DeRoundChartNonVolumeAxis, DeRoundChartVolumeAxis} from './DeRoundChart-types';

@Component({
  components: {
    DeRoundChart
  }
})
export default class DeRoundChartDemo extends Vue {
  rounds = createMultipleByClosure(createDemo__DeRoundElement, 5, true)
  traders = range(1, 5).map(i => createDemo__CompanyElement('trader ' + i))

  nonVolumeAxis = DeRoundChartNonVolumeAxis.ROUND
  volumeAxis = DeRoundChartVolumeAxis.Y

  DeRoundChartNonVolumeAxis = DeRoundChartNonVolumeAxis
  DeRoundChartVolumeAxis = DeRoundChartVolumeAxis

  cells = flatten(
    this.rounds.map(round =>
      this.traders.map(trader => createDemo__DeRoundTraderElement(round.round_number, trader))
    )
  );
}
</script>

<style lang="less" scoped>

</style>
