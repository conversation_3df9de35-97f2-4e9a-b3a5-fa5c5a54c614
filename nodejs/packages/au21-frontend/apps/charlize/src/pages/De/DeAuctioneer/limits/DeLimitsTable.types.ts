export type DeLimitsTableRow = {
  company_id: string,
  company_name: string,
  id: string,
  buyer_credit_limit: number,
  buyer_credit_limit_str: string,
  max_buy_quantity: number,
  min_buy_quantity: number,
  min_sell_quantity: number,
  max_sell_quantity: number,
}

export type LimitsField =
  'buyer_credit_limit' |
  'buyer_credit_limit_str' |
  'max_buy_quantity' |
  'min_buy_quantity' |
  'min_sell_quantity' |
  'max_sell_quantity'

export type DeLimitsTableCellParams = {
  field: LimitsField,
  getReadonly: () => boolean,
  saveValue: (value: string, field: LimitsField) => void,
}

export type DeLimitsTableActionsParams = {
  isRowAllowable: (row: DeLimitsTableRow) => boolean,
  allow: (row: DeLimitsTableRow) => void,
}
