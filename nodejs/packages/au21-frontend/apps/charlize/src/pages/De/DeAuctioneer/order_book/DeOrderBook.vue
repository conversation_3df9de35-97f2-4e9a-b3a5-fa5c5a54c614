<template>
  <div>
    <TableHeading>
      <div style='display: inline-block'>Order Book</div>
    </TableHeading>
  <AuAgGrid
    class="DeOrderBook"
    :width="width"
    :height="height"
    :columnDefs="columnDefs"
    :rowData="rows"
    :gridOptions="gridOptions"
    :getRowHeight="() => row_height"
  />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import DeOrderBookCell from './DeOrderBookCell.vue';
import {ColDef, GridApi, GridOptions} from 'ag-grid-community';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';
import {CompanyElement, DeRoundTraderElement, OrderType} from '@au21-frontend/client-connector';
import {DeOrderBookRow} from './DeOrderBookRow';
import {range} from 'lodash';
import TableHeading from '../../../common/components/TableHeading/TableHeading.vue';

function createCellDefinition(headerName: string, width: number): ColDef {
  return {headerName: headerName, width: width};
}

@Component({
  components: {AuAgGrid, TableHeading}
})
export default class DeOrderBook extends Vue {
  // TODO: this seems wrong, we should be sending in the correct data
  @Prop({required: true}) height: number;
  @Prop({required: true, type: Array}) round_trader_elements: DeRoundTraderElement[];
  @Prop({required: true, type: Array}) companies: CompanyElement[];
  @Prop({required: true}) quantity_label: string;

  width = 330;
  row_height = 40;

  gridApi: GridApi = null;
  gridOptions: GridOptions = {
    defaultColDef: {
      //
      cellRendererFramework: DeOrderBookCell,
      headerComponentFramework: AuAgGridCenteredHeader,
      cellStyle: () => ({padding: '0', border: '0'})
    },
    headerHeight: 33,
    suppressHorizontalScroll: true
  };

  getCompany(companyId: string): CompanyElement {
    const result = this.companies.find(c => c.company_id === companyId);
    if (!result) {
      throw new Error(`Company with id ${companyId} is not found`);
    }
    return result;
  }

  get cells_by_side(): {
    sellCells: DeRoundTraderElement[],
    buyCells: DeRoundTraderElement[],
    maxCellList: DeRoundTraderElement[]
  } {
    const sellCells: DeRoundTraderElement[] = [];
    const buyCells: DeRoundTraderElement[] = [];
    this.round_trader_elements.forEach(cell => {
      if (cell.order_type == OrderType.SELL) { // && cell.quantity_int > 0) {
        sellCells.push(cell);
      } else if (cell.order_type == OrderType.BUY) { // && cell.quantity_int > 0) {
        buyCells.push(cell);
      }
    });
    return {
      sellCells,
      buyCells,
      maxCellList: sellCells.length > buyCells.length ? sellCells : buyCells
    };
  }

  get empty_row_count(): number {
    const body_height = this.height - 40; // ie: - header_height
    const rows_height = this.cells_by_side.buyCells.length * this.row_height;
    const empty_height = body_height - rows_height;
    return empty_height > 0 ?
      Math.ceil(empty_height / this.row_height)
      : 0;
    //  console.log({body_height, rows_height, empty_height, empty_rows})
    // return empty_rows
  }

  get rows() {
    const rows = [];

    this.cells_by_side.maxCellList.forEach((value, index) => {
      // TODO Handle user name.

      const row: DeOrderBookRow = {
        buyCompanyShort: '',
        buySubmittedBy: '',
        buyTime: '',
        buyTimeStr: '',
        buyQuantityInt: 0,
        buyQuantityStr: '',
        hasBuy: false,
        hasSell: true,
        match: 0,
        id: index,
        sellCompanyShort: '',
        sellSubmittedBy: '',
        sellTime: '',
        sellQuantityInt: 0,
        sellQuantityStr: '',
      };

      const sellCell: DeRoundTraderElement = this.cells_by_side.sellCells[index];

      if (sellCell) {
        row.sellTime = sellCell.timestamp_formatted;
        row.sellCompanyShort = this.getCompany(sellCell.cid).company_shortname;
        row.sellSubmittedBy = sellCell.order_submitted_by;
        row.sellQuantityInt = sellCell.quantity_int;
        row.sellQuantityStr = sellCell.quantity_str;
        row.match = 10; // sellCell.match;
        row.hasSell = true;
      }

      const buyCell: DeRoundTraderElement = this.cells_by_side.buyCells[index];
      if (buyCell) {
        row.buyTime = buyCell.timestamp_formatted;
        row.buyCompanyShort = this.getCompany(buyCell.cid).company_shortname;
        row.buySubmittedBy = buyCell.order_submitted_by;
        row.buyQuantityInt = buyCell.quantity_int;
        row.buyQuantityStr = buyCell.quantity_str;
        row.match = 10; // buyCell.match;
        row.hasBuy = true;
      }
      rows.push(row);
    });

    range(this.empty_row_count).forEach(index => {
      rows.push({
        id: 'empty-row.' + index
      });
    });

    return rows;
  }


  get columnDefs(): ColDef[] {
    return [
      {
        headerName: 'Buyer',
        field: 'buyCompanyShort',
        width: 110
      },
      {
        headerName: `Buy (${this.quantity_label})`,
        field: 'buyQuantity',
        width: 69
      },
      {
        headerName: `Sell (${this.quantity_label})`,
        field: 'sellQuantity',
        width: 69
      },
      {
        headerName: 'Seller',
        field: 'sellCompanyShort',
        width: 110
      }
    ];
  }
}
</script>

<style lang="less" scoped>
.DeOrderBook {

}
</style>
