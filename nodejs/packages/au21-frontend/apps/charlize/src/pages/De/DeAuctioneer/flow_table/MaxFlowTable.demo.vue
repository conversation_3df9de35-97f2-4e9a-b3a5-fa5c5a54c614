<template>
  <VbDemo>
    <VbCard>
      <MaxFlowTable
        :width="500"
        :height="500"
        :flowCalcResult="flowCalcResult"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import MaxFlowTable from './MaxFlowTable.vue';
import {createDemo__FlowCalcResult} from '../../../../demo-helpers/FlowCalcResult.helper';

@Component({
  components: {MaxFlowTable},
})
export default class DeOrderConstraintsDemo extends Vue {
  flowCalcResult = createDemo__FlowCalcResult()
}
</script>
