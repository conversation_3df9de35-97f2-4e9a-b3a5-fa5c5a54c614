<template>
  <a-modal
    class="AuctionSettingsModal"
    title="Auction settings"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="570px"
  >
    <AuctionSettingsEdit
      :style="{maxHeight: modalHeight + 'px'}"
      class="_form"
      :crud="crud"
      :settings="settings"
      :store="store"
    />

    <a-button
      slot="footer"
      type="primary"
      class="au-btn"
      @click="$emit('close')"
    >
      Cancel
    </a-button>

    <a-button
      v-if="isEditable"
      slot="footer"
      @click="clear()"
      class="au-btn"
      type="primary"
    >
      Clear
    </a-button>

    <a-button
      v-if="showFill"
      slot="footer"
      @click="fill()"
      class="au-btn"
      type="primary"
    >
      Default
    </a-button>

    <a-button
      v-if="isEditable"
      slot="footer"
      class="au-btn"
      type="primary"
      @click="$emit('saveAuction', settings)"
    >
      Save
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import AuctionSettingsEdit from './AuctionSettingsEdit.vue';
import {Crud, DeSettingsValue} from '@au21-frontend/client-connector';
import {isCrudEditable} from '../../../../entity-helpers/Crud';
import {
  createDefault__DeSettingsValue,
  createEmpty__DeSettingsValue
} from "../../../../demo-helpers/DeSettingsValue.helper";
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';

@Component({
  components: {AuctionSettingsEdit},
})
export default class AuctionSettingsModal extends Vue {
  @Prop({required: true}) settings: DeSettingsValue
  @Prop({required: true}) crud: Crud
  @Prop({required: true}) store: CharlizeStore;

  screen = new AuScreen() // same for auctioneer or trader

  get modalHeight() {
    return this.screen.modal_height
  }

  Crud = Crud

  get isEditable() {
    return isCrudEditable(this.crud)
  }

  get showFill(): boolean {
    if (process.env.VUE_APP_SHOW_DEBUG) {
      return this.isEditable
    }
  }

  clear() {
    // TODO
    this.$emit('update:settings', createEmpty__DeSettingsValue())
  }

  fill() {
    this.$emit('update:settings', createDefault__DeSettingsValue())
  }
}
</script>

<style lang="less" scoped>
.AuctionSettingsModal {
  ._form {
    overflow-y: auto;
  }
}
</style>
