<template>
  <div class="DeSankeyDiagram">
    <TableHeading>
      <div style="display: inline-block">Matches</div>
    </TableHeading>

    <svg
      v-if="curvedRectangles.length"
      class="_diagram"
      :width="width + 'px'"
      :height="height + 'px'"
      :viewBox="`0 0 ${width} ${height}`"
    >
      <g v-if="hovered_rectangle">
        <path
          :d="hovered_rectangle.curve.path.print()"
          :fill="hovered_rectangle.item.fill"
          class="_match -hovered"
        />
      </g>

      <!-- edges -->
      <g v-for="rectangle in curvedRectangles">
        <path
          :d="rectangle.curve.path.print()"
          :fill="rectangle.item.fill"
          :class="{
            '_match': true,
            '-hovered-near': is_hovered_near(rectangle),
          }"
          @mouseenter="mouse_enter(rectangle)"
          @mouseleave="mouse_leave(rectangle)"
        />
      </g>

      <!-- Edge labels -->
      <g v-for="rectangle in curvedRectangles.filter(r => r.item.label)">
        <text
          class="_label"
          v-if="['buyer', 'seller'].includes(rectangle.item.type)"
          :x="rectangle.curve.centroid[0] - 40"
          :y="rectangle.curve.centroid[1]"
          :fill="rectangle.item.color"
          alignment-baseline="middle"
        >
          {{ rectangle.item.label }}
        </text>
        <template v-if="rectangle.item.type === 'match'">
          <text
            class="_label"
            :x="rectangle.curve.centroid[0] - 60"
            :y="curvedRectangleLabelPositions.get(rectangle)[0] + 1"
            :fill="rectangle.item.color"
            text-anchor="start"
            alignment-baseline="hanging"
          >
            {{ rectangle.item.label }}
          </text>
          <text
            class="_label"
            :x="rectangle.curve.centroid[0] + 60"
            :y="curvedRectangleLabelPositions.get(rectangle)[1] + 1"
            :fill="rectangle.item.color"
            text-anchor="end"
            alignment-baseline="hanging"
          >
            {{ rectangle.item.label }}
          </text>
        </template>
      </g>

      <!-- Nodes -->
      <g v-for="rectangle in rectangles">
        <path
          :d="rectangle.curve.path.print()"
          :fill="palette[rectangle.group]"
        />
      </g>
    </svg>

    <div
      class="_no-content"
      :style="{width: width + 'px', height: height + 'px'}"
      v-else
    >
      Sankey Diagram will be shown <br>after bids are placed
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';
import Sankey, {ISankey} from './dm-path-js/sankey';
import {Container} from 'typescript-ioc';
import {DeMatrixEdgeElement, DeRoundTraderElement, OrderType,} from '@au21-frontend/client-connector';
import {AuColors} from '../../../../au-styles/AuColors';
import {flatten} from 'lodash';
import TableHeading from '../../../common/components/TableHeading/TableHeading.vue';

// const flatten = require('array-flatten');

type Order = {
  isBuy: boolean;
  companyId: string,
  orderId: string;
  time: string;
  shortname: string,
  //order_submitted_by: string;
  quantity_int: number;
  quantity_str: string;
};

type SankeyDiagramItem = {
  type: 'buyer' | 'seller' | 'match'
  label: string
  color: string
  fill: string
  start: string
  end: string
  weight: number
}

@Component({
  name: 'DeSankeyDiagram',
  components: { TableHeading },
})
export default class DeSankeyDiagram extends Vue {
  @Prop({ type: Number }) width: number;
  @Prop({ type: Number }) height: number;
  @Prop({ required: true, type: Array }) matrix_edges: DeMatrixEdgeElement[];
  @Prop({
    required: true,
    type: Array,
  }) round_trader_elements: DeRoundTraderElement[];

  colors = Container.get(AuColors);
  hovered_rectangle = null;

  get palette () {
    return [
      this.colors.au_buy_dimmed(),
      this.colors.au_buy(),
      this.colors.au_sell(),
      this.colors.au_sell_dim(),
    ]; // from colorbrewer 2107-03-06 ['#707b82', '#7881c2', '#3e90f0'];
  }

  getRte (companyId: string): DeRoundTraderElement | null {
    return this.round_trader_elements.find((rte: DeRoundTraderElement) => rte.cid === companyId);
  }

  get sankey (): ISankey {
    return Sankey({
      data: this.sankey_data,
      width: this.width,
      height: this.height,
      gutter: 5,
      rectWidth: 5,
      nodeaccessor: x => x.id,
      linkaccessor: null,
      compute: null,
    });
  }

  get sellOrders (): Order[] {
    return this.round_trader_elements
      .filter((rte: DeRoundTraderElement) => rte.order_type == OrderType.SELL)
      .map((rte: DeRoundTraderElement) => {
        //  const rte = this.getCell(rte.cid);
        return {
          isBuy: false,
          companyId: rte.cid,
          orderId: rte.id,
          time: rte.timestamp_formatted,
          // order_submitted_by: rte.order_submitted_by,
          shortname: rte.company_shortname,
          quantity_int: rte.quantity_int,
          quantity_str: rte.quantity_str,
        };
      });
  }

  get buyOrders (): Order[] {
    return this.round_trader_elements
      .filter((rte: DeRoundTraderElement) => rte.order_type == OrderType.BUY) //  && rte.quantity_int> 0) // by definition quantity is > 0 for this type
      .map((rte: DeRoundTraderElement) => {
        return {
          isBuy: true,
          companyId: rte.cid,
          orderId: rte.id,
          time: rte.timestamp_formatted,
          // order_submitted_by: rte.order_submitted_by,
          shortname: rte.company_shortname,
          quantity_int: rte.quantity_int,
          quantity_str: rte.quantity_str,
        };
      });
  }

  parseWeight (wt) {
    return parseInt(wt) || 0;
  }

  get sankey_data () {
    const edges: DeMatrixEdgeElement[] = this.matrix_edges;

    const result = {
      nodes: [
        [{ id: 'buyers' }],

        this.buyOrders.map((buyer: Order) => {
          return { id: 'BUYER_' + buyer.shortname };
        }),

        this.sellOrders.map((seller: Order) => {
          return { id: 'SELLER_' + seller.shortname };
        }),

        [{ id: 'sellers' }],
      ],
      links: [],
    };

    this.buyOrders.forEach((buyer: Order) => {
      this.sellOrders.forEach((seller: Order) => {

        const edge: DeMatrixEdgeElement | null = edges.find(
          (edge: DeMatrixEdgeElement) => edge.buyer_cid === buyer.companyId && edge.seller_cid === seller.companyId,
        ) || null;

        if (!edge) {
          return;
        }

        const wt = this.parseWeight(edge.match);

        if (wt > 0)
          result.links.push({
            label: wt,
            type: 'match',
            color: 'white',
            fill: this.colors.au_match_dark(),
            start: 'SELLER_' + seller.shortname,
            end: 'BUYER_' + buyer.shortname,
            weight: wt,
          });
      });
    });

    result.links = flatten((result.links as any).flat(999)); // TODO Figure level of nesting requried.

    this.buyOrders.forEach((o: Order) => {
      if (o.quantity_int > 0)
        result.links.push({
          type: 'buyer',
          label: `${o.shortname}: ${o.quantity_int}`,
          color: this.colors.au_buy(),
          fill: this.colors.au_buy_dark(),
          start: 'BUYER_' + o.shortname,
          end: 'buyers',
          weight: o.quantity_int,
        });
    });

    this.sellOrders.forEach((o: Order) => {
      if (o.quantity_int > 0)
        result.links.push({
          type: 'seller',
          label: `${o.shortname}: ${o.quantity_int}`,
          color: this.colors.au_sell(),
          fill: this.colors.au_sell_dark(),
          start: 'sellers',
          end: 'SELLER_' + o.shortname,
          weight: o.quantity_int,
        });
    });

    return result;
  }

  mouse_enter (r) {
    this.hovered_rectangle = r;
  }

  is_hovered_near (rectangle): boolean {
    if (!this.hovered_rectangle) {
      return false;
    }
    const hoveredItem = this.hovered_rectangle.item
    if (hoveredItem.type === 'match') {
      return this.hovered_rectangle.item.end === rectangle.item.start
        || this.hovered_rectangle.item.start === rectangle.item.end;
    }
    if (hoveredItem.type === 'seller' || hoveredItem.type === 'buyer') {
      if (rectangle.item.type === 'match') {
        return this.hovered_rectangle.item.end === rectangle.item.start
          || this.hovered_rectangle.item.start === rectangle.item.end;
      }
      if (rectangle.item.type === 'seller' && hoveredItem.type === 'buyer') {
        return this.curvedRectangles.some(
          curved_rectangle =>
            curved_rectangle.item.type === 'match'
            && curved_rectangle.item.end === this.hovered_rectangle.item.start
            && curved_rectangle.item.start === rectangle.item.end
        )
      }
      if (rectangle.item.type === 'buyer' && hoveredItem.type === 'seller') {
        return this.curvedRectangles.some(
          curved_rectangle =>
            curved_rectangle.item.type === 'match'
            && curved_rectangle.item.start === this.hovered_rectangle.item.end
            && curved_rectangle.item.end === rectangle.item.start
        )
      }
    }
  }

  mouse_leave () {
    this.hovered_rectangle = null
  }

  get curvedRectangleLabelPositions () {
    const rectangles = this.curvedRectangles.filter(rectangle => (rectangle.item as SankeyDiagramItem).type === 'match');

    const middleOrTop = (min, max) => {
      const FONT_SIZE = 12;
      if (max - min < FONT_SIZE) {
        return min;
      }
      return min + (max - min) / 2 - FONT_SIZE / 2;
    };


    return new Map(rectangles.map((rectangle) => [
      rectangle,
      [
        middleOrTop(rectangle.curve.coordinates.topright[1], rectangle.curve.coordinates.bottomright[1]),
        middleOrTop(rectangle.curve.coordinates.topleft[1], rectangle.curve.coordinates.bottomleft[1]),
      ],
    ]));
  }

  get curvedRectangles () {
    return this.sankey.curvedRectangles;
  }

  get rectangles () {
    // TODO: why do we get nulls?
    return this.sankey.rectangles.filter(x => {
      return (
        x &&
        x.curve &&
        x.curve.centroid &&
        !isNaN(x.curve.centroid[0]) &&
        !isNaN(x.curve.centroid[1])
      );
    });
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeSankeyDiagram {
  background-color: @layout-color;

  ._diagram {
    border-top: 1px solid hsl(0, 0%, 30%);
    margin-bottom: -6px;
    overflow-y: scroll;
  }

  ._no-content {
    padding: 50px 30px;
    color: @au-primary-color;
    white-space: normal;
    text-align: center;
  }

  ._match {
    opacity: 0.5;
  }

  .-hovered {
    opacity: 1;
  }

  .-hovered-near {
    opacity: 0.85;
  }

  ._label {
    pointer-events: none;
  }
}
</style>
