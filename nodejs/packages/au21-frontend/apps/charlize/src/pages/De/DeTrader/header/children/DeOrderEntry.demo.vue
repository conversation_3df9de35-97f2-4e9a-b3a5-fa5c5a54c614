<template>
  <VbDemo>
    <VbCard>
      <DeOrderEntry
        :de_trader_info_value="de_trader_info_value"
        :common_status="common_status"
        :price_direction="PriceDirection.UP"
        :price_has_reversed="false"
      />

      <div>
        Changing round number should close modal (button is delayed by 5 sec)
      </div>
      <button @click="delayedUpdateRoundNumber">Round: {{ de_trader_info_value.round_number }}</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeOrderEntry from './DeOrderEntry.vue';
import {createDemo__DeTraderInfoValue} from '../../../../../demo-helpers/DeTrader.helper';
import {sleep} from '@au21-frontend/utils';
import {createDemo__DeCommonStatusValue} from "../../../../../demo-helpers/DeCommonStatusValue.helper";
import {PriceDirection} from "@au21-frontend/client-connector";

@Component({
  name: 'DeOrderEntryDemo',
  components: {DeOrderEntry}
})
export default class DeOrderEntryDemo extends Vue {
  de_trader_info_value = createDemo__DeTraderInfoValue();
  common_status = createDemo__DeCommonStatusValue()

  PriceDirection = PriceDirection

  async delayedUpdateRoundNumber() {
    await sleep(5000);
    this.de_trader_info_value.round_number++;
  }
}
</script>
