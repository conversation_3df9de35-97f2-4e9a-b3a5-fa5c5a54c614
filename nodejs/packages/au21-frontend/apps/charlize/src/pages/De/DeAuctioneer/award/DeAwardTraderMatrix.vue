<template>
  <AuAgGrid
    class="DeAwardTraderMatrixTable"
    v-if="edges.length"
    :height="height"
    :columnDefs="columnDefs"
    :rowData="edgesSorted"
    :gridOptions="gridOptions"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';
import AuSectionHeader from '../../../../ui-components/AuSectionHeader.vue';
import {CompanyElement, DeMatrixEdgeElement} from '@au21-frontend/client-connector';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import {ICellRendererParams} from 'ag-grid-community/dist/lib/rendering/cellRenderers/iCellRenderer';

// TODO There is some duplication and sorting logic is not implemented perfectly.

@Component({
  components: {AuSectionBorder, AuSectionHeader, AuAgGrid},
})
export default class DeAwardTraderMatrixTable extends Vue {
  @Prop({required: true, type: Array}) companies: CompanyElement[]
  @Prop({required: true, type: Array}) edges: DeMatrixEdgeElement[]
  @Prop({type: Number, default: 404}) height: number

  first_col_width = 100
  body_col_width = 50

  get gridOptions(): GridOptions {
    return {
      headerHeight: 85,
      defaultColDef: {
        //
        headerComponentFramework: AuAgGridCenteredHeader,
        cellStyle: () => ({padding: '0', border: '0'}),
      },
      rowHeight: 50,
      suppressHorizontalScroll: false,
    }
  }

  get edgesSorted(): DeMatrixEdgeElement[][] {
    const buyerToNodes: Record<string, DeMatrixEdgeElement[]> = {}

    return []

    /* TODO: need to fix this
    this.solution.forEach(node => {
      const id = node.buyer_cid;
      if (!buyerToNodes[id]) {
        buyerToNodes[id] = [];
      }
      buyerToNodes[id].push(node);
    });

    // Sort by company name in 2 directions.
    for (const id in buyerToNodes) {
      // Sort rows
      buyerToNodes[id].sort((a, b) => this.getCompanyName(a.seller_cid) > this.getCompanyName(b.seller_cid) ? 1 : -1);
    }
    const edgesSorted = Object.values(buyerToNodes);
    // Sort columns
    return edgesSorted.sort((a, b) => this.getCompanyName(a[0].buyer_cid) > this.getCompanyName(b[0].buyer_cid) ? 1 : -1);

     */
  }

  get companiesSorted() {
    return [...this.companies].sort((a, b) => a.company_shortname > b.company_shortname ? 1 : -1)
  }

  get columnDefs(): ColDef[] {
    const leftFixedColumn = {
      headerName: 'Buyer rows vs Seller columns',
      headerComponentFramework: AuAgGridCenteredHeader,
      pinned: true,
      // We need 3 widths set for create_scenario_result_rows to stay consistent.
      width: this.first_col_width,
      minWidth: this.first_col_width,
      maxWidth: this.first_col_width,

      cellRenderer: (params: ICellRendererParams) => {
        const edges: DeMatrixEdgeElement[] = params.data
        return `<div style="padding-left: 5px;">Buyer: ${this.getCompanyName(edges[0].buyer_cid)}</div>`
      },
    }

    const bodyColumns = this.companiesSorted.map((company: CompanyElement) => ({
      headerName: company.company_shortname,

      // We need 3 widths set for create_scenario_result_rows to stay consistent.
      width: this.body_col_width,
      minWidth: this.body_col_width,
      maxWidth: this.body_col_width,

      cellRenderer: params => {
        const row: DeMatrixEdgeElement[] = params.data
        const node: DeMatrixEdgeElement = row.find(node => node.seller_cid === company.id)
        if (!node) {
          return ''
        }
        return `<div style="text-align: right; padding-right: 5px;">${node.match}</div>`
      },
      cellRendererParams: {
        company,
      },
    }))

    return [
      leftFixedColumn,
      ...bodyColumns,
    ]
  }

  getCompanyName(id: string) {
    return this.idToCompany[id]?.company_shortname
  }

  get idToCompany(): Record<string, CompanyElement> {
    const idToCompany = {}
    this.companies.forEach(company => {
      idToCompany[company.id] = company
    })
    return idToCompany
  }
}
</script>

<style lang="less" scoped>
.DeAwardTraderMatrixTable {
  .ag-header-cell {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }
}
</style>
