<template>
  <div class="component">

    <!-- Autopilot -->
    <!--    <div class="box" style="width: 65px;">-->
    <!--      <div class="box-heading">-->
    <!--        Autopilot-->
    <!--      </div>-->
    <!--      <DeAutopilotDisplay-->
    <!--        :autopilot="auctioneerStatus.autopilot"-->
    <!--        :level="auctioneerStatus.info_level"-->
    <!--      />-->
    <!--    </div>-->


    <!-- Starting price -->
    <div class="box">
      <!--      <DeStartingPrice-->
      <!--        :auctioneerStatus="auctioneerStatus"-->
      <!--        :commonStatus="commonStatus"-->
      <!--      />-->
      <InfoPanel
        heading="Starting"
        :contents="auctioneer_status.starting_price"
        :units="de_settings.price_label"
        :width="77"
        color="white"
      />

    </div>


    <!-- Announce -->
    <!--    <div class="box" style="position: relative; top: -3px; width: 72px; margin-left: 15px">-->
    <!--      <div class="box-heading au-label">-->
    <!--        Announced-->
    <!--      </div>-->
    <!--      <DeAnnounceDisplay-->
    <!--        style="margin-top: 9px"-->
    <!--        :auctioneerStatus="auctioneerStatus"-->
    <!--      />-->
    <!--    </div>-->

    <!-- price direction -->
    <!--    <div class="box" style="width: 95px">-->
    <!--      <div class="box-heading">-->
    <!--        Direction-->
    <!--      </div>-->
    <!--      <div class="output-box" style="font-weight: normal">-->
    <!--        <div>Price</div>-->
    <!--        <div>Increases</div>-->
    <!--        <div>every round</div>-->
    <!--      </div>-->
    <!--    </div>-->

    <!-- Awardable -->
    <!--      <div class="box" style="width: 85px;">-->
    <!--        <div class="box-heading">-->
    <!--          Awardable-->
    <!--        </div>-->
    <!--        <DeAwardableDisplay-->
    <!--          :awardable="awardable"-->
    <!--          :level="auctioneerStatus.info_level"-->
    <!--        />-->
    <!--      </div>-->


  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import {
  DeAuctioneerInfoValue,
  DeAuctioneerStatusValue,
  DeCommonStatusValue,
  DeSettingsValue
} from '@au21-frontend/client-connector';
import DeAwardableDisplay from '../award_display/DeAwardableDisplay.vue';
import InfoPanel from '../../../common/components/InfoPanel/InfoPanel.vue';
import DeAnnounceDisplay from '../price/DeAnnounceDisplay/DeAnnounceDisplay.vue';

// export type CLOCK_STATUS = 'NOT_STARTED' | 'RUNNING' | 'PAUSED' | 'ENDED' // TODO: add pause / running
// export type LEVEL_STATUS = 'NOT_STARTED' | 'NORMAL' | 'WARNING' | 'ALERT'

@Component({
  components: {
    DeAnnounceDisplay,
    AuSectionBorder,
    DeAwardableDisplay,
    DeAuctioneerClock,
    InfoPanel,
  }
})
export default class DeAuctioneerClock extends Vue {
  @Prop({required: true}) common_status: DeCommonStatusValue;
  @Prop({required: true}) auctioneer_status: DeAuctioneerStatusValue;
  @Prop({required: true}) de_settings: DeSettingsValue;
  @Prop({required: true}) auctioneer_info: DeAuctioneerInfoValue;

  // colors = Container.get(AuColors)

  thresholds = {
    alert: 5,
    warning: 10
  };

  get roundNumber(): number | null {
    return this.common_status?.round_number;
  }

  get roundSeconds(): number | null {
    return 1; // this.commonStatus?.initial_time
  }

  get remainingTime(): number | null {
    return 2; // this.commonStatus?.remaining_time
  }

  get announced() {
    return this.auctioneer_status?.announced;
  }

  get awardable() {
    return this.auctioneer_status?.awardable
  }

  // get level_status(): DeAuctioneerInfoLevel | null {
  //   return this.auctioneerStatus?.info_level
  // }
}

</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

//.blinking {
//  animation: pulse 2s infinite;
//}
//
//@keyframes pulse {
//  0% {
//    background-color: red;
//  }
//
//  70% {
//    background-color: blue;
//  }
//
//  100% {
//    background-color:yellow;
//  }
//}

.component {
  //height: 60px;
  border: 1px solid purple;
  display: flex;
  color: white;
  overflow: hidden;
  padding: 8px;
  width: 100px;

  .box {
    text-align: center;
    position: relative;
    height: 60px;
  }

  .box-heading {
    border: 0;
    margin: 0;
    padding: 0;
    text-align: center;
    width: 100%;
  }

  .output-box {
    //height: 80px;
    margin: 0;
    padding: 0;
    text-align: center;
    width: 60px;
  }

  .box-data {
    font-size: 18px;
    position: relative;
    text-align: center;
    width: 100%;
  }

  .box-data-round-number {
    color: white;
    font-size: 28px;
    position: relative;
    text-align: center;
  }

  .units-label {
    font-size: 9px;
    padding-right: 2px;
    position: relative;
    text-align: center;
    width: 100%;
  }

}
</style>

