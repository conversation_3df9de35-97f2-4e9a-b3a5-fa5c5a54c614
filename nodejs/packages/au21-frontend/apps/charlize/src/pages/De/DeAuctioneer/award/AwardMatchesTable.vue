<template>
  <AuAgGrid
    :columnDefs="columnDefs"
    :gridOptions="gridOptions"
    :rowData="matchesSorted"
    :height="height"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import RoundTotalTableAgBodyCell from './AwardRoundTotalTable/RoundTotalTableAgBodyCell.vue';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import AuSectionHeader from '../../../../ui-components/AuSectionHeader.vue';
import {ColDef, GridOptions} from 'ag-grid-community';
import {DeScenarioMatchVM} from '@au21-frontend/client-connector';
import AwardMatchesTableHeader from './AwardMatchesTableHeader.vue';

@Component({
  name: 'AwardMatchesTable',
  components: {
    AuSectionHeader,
    AuAgGrid,
    RoundTotalTableAgBodyCell,
  },
})
export default class AwardMatchesTable extends Vue {
  @Prop({required: true, type: Array}) matches: DeScenarioMatchVM[]
  @Prop({type: Number}) height: number

  sortBy: 'buyer_shortname' | 'seller_shortname' | 'quantity_int' = 'buyer_shortname'
  sortDirection: 'asc' | 'desc' = 'asc'

  get gridOptions(): GridOptions {
    return {
      getRowNodeId: (match: DeScenarioMatchVM) => match.buyer_id + match.seller_id,
      defaultColDef: {
        headerComponentFramework: AwardMatchesTableHeader,
        cellClass: 'align-center',
        width: 100,
      },
    }
  }

  get matchesSorted() {
    const result = [...this.matches]
      .sort((a, b) => {
        return a[this.sortBy] > b[this.sortBy] ? 1 : -1
      })
    return this.sortDirection === 'desc' ? result : result.reverse()
  }

  get columnDefs(): ColDef[] {
    return [
      {
        headerName: 'Buyer',
        field: 'buyer_shortname',
        headerComponentParams: {
          sortBy: 'buyer_shortname',
        }
      },
      {
        headerName: 'Seller',
        field: 'seller_shortname',
        headerComponentParams: {
          sortBy: 'seller_shortname',
        }
      },
      {
        headerName: 'Quantity',
        field: 'quantity_str',
        headerComponentParams: {
          sortBy: 'quantity_int',
        }
      },
      // TODO Reenable
      // {
      //   headerName: 'Value',
      //   field: 'value',
      // },
    ]
  }
}
</script>

<style lang="less" scoped>

</style>
