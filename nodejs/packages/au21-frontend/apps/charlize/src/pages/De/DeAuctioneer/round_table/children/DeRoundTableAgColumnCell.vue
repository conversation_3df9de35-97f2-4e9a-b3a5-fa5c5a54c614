<template>
  <div
    class="DeRoundTableAgColumnCell"
    :class="{'float-right': !isTraderCell}"
  >
    <span
      @click="onClick()"
      :style="{ color: 'white' }"
    >
      {{ text }}
    </span>
    <div v-if="onlineUsers.length" style="position: absolute; right: 3px; top: 3px">
      <a-dropdown
        v-for="user in onlineUsers"
        :key="user.user_id"
      >
        <TraderOnlineIcon
          :user="user"
          :auction_id="table.auction_id"
          :users_that_have_seen_auction="table.users_that_have_seen_auction"/>

        <div class="dropdown-overlay" slot="overlay">
          {{ user.username }}
        </div>
      </a-dropdown>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeBlotterRowAg} from '../de-round-table-ag-helpers';
import {UserElement} from '@au21-frontend/client-connector';
import TraderOnlineIcon from '../../../../../ui-components/OnlineIcon/TraderOnlineIcon.vue';
import type DeRoundTable from '../DeRoundTable.vue';
import {getOnlineUsersForCompany} from "../../../../../services/helpers/common-helpers";

@Component({
  components: {TraderOnlineIcon},
})
export default class DeRoundTableAgColumnCell extends Vue {
  params = null

  get table(): DeRoundTable {
    return (this.$parent.$parent.$parent as DeRoundTable)
  }

  get row(): DeBlotterRowAg {
    return this.params ? this.params.data : null
  }

  get isTraderCell(): boolean {
    return !!this.row.trader
  }

  get onlineUsers(): UserElement[] {
    if (!this.isTraderCell) {
      return []
    }
    return getOnlineUsersForCompany(this.row.trader.company_id, this.table?.online_users)
  }

  get text(): string {
    if (this.isTraderCell) {
      return `${this.row.trader.shortname}`
    }

    switch (this.row.rowType) {
      case 'PRICE':
        return 'Price:'
      // case 'EXCESS':
      //   return 'Excess:'
      case 'FOOTER':
        return 'Totals:'
    }
  }
}
</script>


<style lang="less" scoped>
.float-right {
  text-align: right;
}

.DeRoundTableAgColumnCell {
  overflow: hidden;
  padding: 3px;
  position: relative;
  width: 100%;
}
</style>
