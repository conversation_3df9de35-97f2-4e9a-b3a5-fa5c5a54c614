import {DeAuctioneerState, DeCommonState} from '@au21-frontend/client-connector';

export const auctioner_state_to_trader_state: { [key in DeAuctioneerState]: DeCommonState } = {
  STARTING_PRICE_NOT_SET: DeCommonState.SETUP,
  STARTING_PRICE_SET: DeCommonState.SETUP,
  STARTING_PRICE_ANNOUNCED: DeCommonState.STARTING_PRICE_ANNOUNCED,
  ROUND_OPEN_ALL_ORDERS_NOT_IN: DeCommonState.ROUND_OPEN,
  ROUND_OPEN_ALL_ORDERS_IN: DeCommonState.ROUND_OPEN,
  ROUND_CLOSED_NOT_AWARDABLE: DeCommonState.ROUND_CLOSED,
  ROUND_CLOSED_AWARDABLE: DeCommonState.ROUND_CLOSED,
  AUCTION_CLOSED: DeCommonState.AUCTION_CLOSED
};

export function has_de_auction_started(s: DeAuctioneerState): boolean {
  return ![
    DeAuctioneerState.STARTING_PRICE_NOT_SET,
    DeAuctioneerState.STARTING_PRICE_SET,
    DeAuctioneerState.STARTING_PRICE_ANNOUNCED
  ].includes(s as any); // not sure why the casting is needed?
}
