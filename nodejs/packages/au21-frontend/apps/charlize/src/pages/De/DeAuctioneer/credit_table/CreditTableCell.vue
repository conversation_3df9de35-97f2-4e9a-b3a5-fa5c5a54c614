<template>
  <div class='CreditTableCell'>{{ value }}</div>
</template>

<script lang='ts'>
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {CompanyElement, CounterpartyCreditElement} from '@au21-frontend/client-connector';

@Component
export default class CreditTableCell extends Vue {
  params = null;

  get company(): CompanyElement {
    return this.params.colDef.cellRendererParams.company;
  }

  get value(): string {
    const items: CounterpartyCreditElement[] = this.params.data.nodes;
    const element: CounterpartyCreditElement = items[this.params.data.companyId][this.company.company_id];
    return element ? element.limit_str : ''
  }
}
</script>


<style lang="less" scoped>
.CreditTableCell {
  font-size: 12px;
  text-align: right;
  width: 90px;
}
</style>
