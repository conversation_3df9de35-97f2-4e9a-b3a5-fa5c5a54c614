<template>
  <AuAgGrid
    class="DeEligibilityTable"
    :height="height"
    :width="width"
    :rowData="rows"
    :gridOptions="gridOptions"
  />
</template>

<script lang="ts">
import {ColDef, GridOptions} from 'ag-grid-community';
import Vue from 'vue';
import {Component, Prop, Ref} from 'vue-property-decorator';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import AuSectionHeader from '../../../../ui-components/AuSectionHeader.vue';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import {DeRoundTraderElement, StoreElement} from '@au21-frontend/client-connector';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {
  DeEligibilityTableActionsParams,
  DeEligibilityTableCellParams,
  DeEligibilityTableRow,
  EligibilityField
} from './DeEligibilityTable.types';
import DeEligibilityTableInput from './DeEligibilityTableInput.vue';
import DeEligibilityTableActions from './DeEligibilityTableActions.vue';

@Component({
  name: 'DeEligibilityTable',
  components: {Blinker, AuSectionBorder, AuSectionHeader, AuAgGrid}
})
export default class DeEligibilityTable extends Vue {
  @Prop({required: true}) height: number;
  @Prop({
    required: true,
    type: Array
  }) round_trader_elements: DeRoundTraderElement[];
  @Prop({required: true}) round_number: number;
  @Prop({type: Boolean}) readonly: boolean;

  get rows(): DeEligibilityTableRow[] {
    // return this.historyRows.map(historyRow => ({
    //   company_id: historyRow.company_id,
    //   company_name: historyRow.company_id, // TODO Use company name.
    //   max_buy_quantity: historyRow.bid_constraints.max_buy_quantity + '' || '0', // TODO Think of fallbacks for null.
    //   min_buy_quantity: historyRow.bid_constraints.min_buy_quantity + '' || '0', // TODO Backend should provide strings.
    //   min_sell_quantity: historyRow.bid_constraints.min_sell_quantity + '' || '0',
    //   max_sell_quantity: historyRow.bid_constraints.max_sell_quantity + '' || '0'
    // }));
    return this.round_trader_elements.map(rte => ({
      id: rte.cid,
      company_id: rte.cid,
      company_name: rte.company_shortname,
      max_buy_quantity: rte.constraints.max_buy_quantity + '' || '0', // TODO Think of fallbacks for null.
      min_buy_quantity: rte.constraints.min_buy_quantity + '' || '0', // TODO Backend should provide strings.
      min_sell_quantity: rte.constraints.min_sell_quantity + '' || '0',
      max_sell_quantity: rte.constraints.max_sell_quantity + '' || '0'
    }));
  }

  @Ref() readonly auAgGrid: AuAgGrid;
  width = 725;
  gridOptions: GridOptions = {
    getRowNodeId: (data: StoreElement) => data.id,
    defaultColDef: {
      suppressMenu: true
    },
    suppressHorizontalScroll: true,
    columnDefs: <ColDef[]>[
      {
        headerName: 'Company',
        width: 120,
        valueGetter: (value) => {
          const row = value.node.data as DeEligibilityTableRow;
          return row.company_name; // company_id;
        },
        field: 'company_name'
        // cellRendererParams: {
        //   field: 'company_name'
        // }
      },
      {
        headerName: 'Constraints Bar',
        width: 200,
        cellRendererParams: this.getCellRendererParamsForColumn('max_sell_quantity'),
        cellRendererFramework: DeEligibilityTableInput
      },
      {
        headerName: 'Max buy',
        width: 70,
        cellRendererParams: this.getCellRendererParamsForColumn('max_buy_quantity'),
        cellRendererFramework: DeEligibilityTableInput
      },
      {
        headerName: 'Min buy',
        width: 70,
        cellRendererParams: this.getCellRendererParamsForColumn('min_buy_quantity'),
        cellRendererFramework: DeEligibilityTableInput
      },
      {
        headerName: 'Min sell',
        width: 70,
        cellRendererParams: this.getCellRendererParamsForColumn('min_sell_quantity'),
        cellRendererFramework: DeEligibilityTableInput
      },
      {
        headerName: 'Max sell',
        width: 70,
        cellRendererParams: this.getCellRendererParamsForColumn('max_sell_quantity'),
        cellRendererFramework: DeEligibilityTableInput
      },
      {
        headerName: '',
        width: 85,
        cellRendererParams: {
          isRowAllowable: () => {
            // TODO Show only if current round missed, and round closed.
            return true;
          },
          allow: (row: DeEligibilityTableRow) => {
            this.$emit('allow', row.company_id);
          }
        } as DeEligibilityTableActionsParams,
        cellRendererFramework: DeEligibilityTableActions
      }
    ]
  };

  getCellRendererParamsForColumn(field: EligibilityField): DeEligibilityTableCellParams {
    return {
      field,
      getReadonly: () => this.readonly,
      saveValue(value, field) {
        // TODO Should save value properly.
        // console.log('save', value, field);
      }
    };
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeEligibilityTable {

}
</style>
