<template>
  <VbDemo>
    <VbCard>
      <a-checkbox style="color: white" v-model="readonly">Readonly</a-checkbox>
      <br>
      <DeEligibilityTable
        :height="400"
        :width="400"
        :round_trader_elements="round_trader_elements"
        :round_number="round_number"
        :readonly="readonly"
        @allow="$vb.log('allow', $event)"
        @save="$vb.log('save', $event)"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeEligibilityTable from './DeEligibilityTable.vue';

@Component({
  components: {DeEligibilityTable}
})
export default class DeEligibilityTableDemo extends Vue {
  // historyRows = createMultipleByClosure(createDemo__DeTraderHistoryRowElement, 10)
  round_number = 1
  round_trader_elements = [] // TODO
  readonly = false
}
</script>
