<template>
  <div class="NewCreditCell">
    <div
      v-if="row_name === 'buyer'"
      class="_buyer"
    >
      {{ row.companyName }}
    </div>
    <div
      v-if="row_name === 'credit-limit'"
      class="_current-credit"
    >
      {{ row.currentCreditStr ? '' + row.currentCreditStr : '' }}
    </div>
    <CreditSelector
      v-if="row_name === 'new-credit'"
      :width="120"
      v-model="row.newCreditStr"
      :decimal-places="2"
    />
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';
import {ClientCreditTableColumnParams, ClientCreditTableRow,} from './ClientCreditTable.types';
import CreditSelector from "../../../common/components/CreditSelector/CreditSelector.vue";

@Component({
  components: {CreditSelector, NumberInput},
})
export default class NewCreditCell extends Vue {
  params = null

  get row(): ClientCreditTableRow | null {
    return this.params.data
  }

  get row_name() {
    return this.cellParams.name
  }

  get cellParams (): ClientCreditTableColumnParams {
    return this.params.column.colDef.cellRendererParams
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.NewCreditCell {
  text-align: center;
  ._buyer {
    padding-left: 5px; padding-top: 6px
  }
  ._current-credit {
    padding-right: 5px;
    padding-top: 6px;
    text-align: right;
  }
}
</style>
