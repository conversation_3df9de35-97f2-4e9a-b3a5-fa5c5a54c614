<template>
  <VbDemo>
    <VbCard title="price">
      <DeSupplyDemandChartSvg
        :rounds="rounds"
        :width="200"
        :height="300"
        :quantity_axis="quantityAxis"
      />
    </VbCard>
    <VbCard title="dense">
      <DeSupplyDemandChartSvg
        :rounds="rounds"
        :width="200"
        :height="300"
        :quantity_axis="quantityAxis"
        dense
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeSupplyDemandChartSvg from './DeSupplyDemandChartSvg.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__DeRoundElement} from '../../../../../demo-helpers/DeRoundTable.helper';
import {DeRoundElement} from '@au21-frontend/client-connector';

import {DeRoundChartQuantityAxis} from '../DeRoundChart/DeRoundChart-types';

@Component({
  name: 'DeSupplyDemandChartDemo',
  components: {DeSupplyDemandChartSvg},
})
export default class DeSupplyDemandChartDemo extends Vue {
  rounds: DeRoundElement[] = []

  quantityAxis = DeRoundChartQuantityAxis.Y

  created() {
    const numberOfRounds = 20
    const basePrice = 100
    const priceIncrement = 1
    const priceDecrement = 0.25
    const firstDecrementRound = 18
    const rounds = createMultipleByClosure(createDemo__DeRoundElement, numberOfRounds, true)
    rounds.forEach(round => {
      const isDecrementRound = round.round_number >= firstDecrementRound
      const increment = isDecrementRound ? priceIncrement * (firstDecrementRound) : priceIncrement * round.round_number
      const decrement = (isDecrementRound ? 1 : 0) * (round.round_number - firstDecrementRound) * priceDecrement
      round.round_price = basePrice + increment - decrement
      round.buy_quantity = round.round_number * 2.7
      round.sell_quantity = 100 - round.round_number * 2.7
    })
    this.rounds = rounds
  }
}
</script>
