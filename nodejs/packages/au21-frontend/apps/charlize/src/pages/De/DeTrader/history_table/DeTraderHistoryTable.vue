<template>
  <div class="DeTraderHistoryTable">
    <TableHeading>
      Round History
    </TableHeading>
    <AuAgGrid
      ref="auAgGrid"
      :height="height"
      :width="width"
      :rowData="historyRows"
      :gridOptions="gridOptions"
      :column-defs="column_defs"
    />
  </div>
</template>

<script lang="ts">
import {ColDef, GridOptions} from 'ag-grid-community';
import Vue from 'vue';
import {Component, Prop, Ref, Watch} from 'vue-property-decorator';
import DeTraderHistoryCell from './DeTraderHistoryCell.vue';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import AuSectionHeader from '../../../../ui-components/AuSectionHeader.vue';
import {DeSettingsValue, DeTraderHistoryRowElement} from '@au21-frontend/client-connector';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import DeTraderHistoryTableHeader from './DeTraderHistoryTableHeader.vue';
import {sleep} from '@au21-frontend/utils';
import TableHeading from '../../../common/components/TableHeading/TableHeading.vue';

@Component({
  name: 'DeTraderHistoryTable',
  components: { TableHeading, Blinker, AuSectionHeader, AuAgGrid}
})
export default class DeTraderHistoryTable extends Vue {
  @Prop({required: true}) height: number;
  @Prop({required: true, type: Array}) historyRows: DeTraderHistoryRowElement[];
  @Prop({required: true, type: Object}) settings: DeSettingsValue;

  @Ref() readonly auAgGrid: AuAgGrid;

  width = 590;

  gridOptions: GridOptions = null;

  // TODO: unclear why default col defs not working?

  get column_defs(): ColDef[] {
    return [
      {
        headerName: 'Round',
        width: 45,
        headerComponentFramework: DeTraderHistoryTableHeader,
        cellRendererFramework: DeTraderHistoryCell,
      },
      {
        headerName: 'Price',
        width: 75,
        headerComponentFramework: DeTraderHistoryTableHeader,
        cellRendererFramework: DeTraderHistoryCell,
      },
      {
        headerName: 'Constraints',
        width: 140,
        headerComponentFramework: DeTraderHistoryTableHeader,
        cellRendererFramework: DeTraderHistoryCell,
      },
      {
        headerName: 'Order',
        width: 70,
        headerComponentFramework: DeTraderHistoryTableHeader,
        cellRendererFramework: DeTraderHistoryCell,
      },
      {
        headerName: 'Value',
        width: 84,
        headerComponentFramework: DeTraderHistoryTableHeader,
        cellRendererFramework: DeTraderHistoryCell,
      },
      {
        headerName: 'Submitted by',
        width: 85,
        headerComponentFramework: DeTraderHistoryTableHeader,
        cellRendererFramework: DeTraderHistoryCell,
      },
      {
        headerName: 'Excess',
        width: 70,
        headerComponentFramework: DeTraderHistoryTableHeader,
        cellRendererFramework: DeTraderHistoryCell,
      }
    ] as ColDef[]
  }

  mounted() {

    this.gridOptions = {
      defaultColDef: {
        suppressMenu: true,
        // headerComponentFramework: DeTraderHistoryTableHeader,
        // cellRendererFramework: DeTraderHistoryCell,
        cellRendererParams: {
          price_label: this.settings?.price_label,
          quantity_label: this.settings?.quantity_label
        },
        headerComponentParams: {
          price_label: this.settings?.price_label,
          quantity_label: this.settings?.quantity_label
        }
      },
      suppressHorizontalScroll: true,
    };

    this.auAgGrid.scrollToBottom();
  };

  @Watch('historyRows', {deep: true})
  async onHistoryRowsChange() {
    await this.auAgGrid.scrollToBottom();
    await sleep(100);
    // update blinker bottom to correspond to last row.
    const lastRowCells = this.$el.querySelectorAll('[role="row"]:last-child [role="gridcell"]');

    // noinspection ES6MissingAwait
    await Array.from(lastRowCells).forEach(async cell => {
      // Not the greatest approach, but low risk potential.
      cell.classList.add('Blinker');
      cell.classList.add('Blinker--active');
      cell.classList.add('Blinker--background');
      await sleep(400);
      cell.classList.remove('Blinker');
      cell.classList.remove('Blinker--active');
      cell.classList.remove('Blinker--background');
    });
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeTraderHistoryTable {
  position: relative;

  &__blinker {
    position: absolute;
    height: 26px;
    bottom: 0;
    z-index: 1000;
  }

  &__heading {
    background-color: #333;
    border-radius: 4px;
    text-align: center;
  }
}
</style>
