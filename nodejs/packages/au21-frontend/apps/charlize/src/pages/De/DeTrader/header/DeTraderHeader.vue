<template>
  <!--  <a-row class="DeTraderHeader">-->

  <a-row :style="{width: (screen.app_width -60) + 'px'}"
         type="flex"
         justify="space-between"
         class="DeTraderHeader">

    <a-col>
      <div class="_round">
        <div class="_heading au-label">Round</div>
        <div class="_round-number" v-test:round_number>{{ common_status.round_number || "---" }}</div>
      </div>
    </a-col>

    <a-col>
      <div class="_vertical_line"></div>
    </a-col>

    <a-col>
      <!--    ROUND STATUS-->
      <div>
        <div class="heading au-label" style="text-align: center">
          Status
        </div>
        <DeCommonStatusPanel
          style="margin-top: 7px"
          :common_state="common_status.common_state"
          :common_state_text="common_status.common_state_text"
        />
      </div>
    </a-col>

    <a-col>
      <div class="_vertical_line"></div>
    </a-col>

    <a-col style="margin-left: 0">
      <!-- ROUND PRICE -->
      <div class="au-label"
           style="text-align: center">
        Round Price
      </div>
      <InfoPanel
        style="margin-top: 10px; height: 42px;"
        :color="price_color"
        :heading="settings.price_label"
        :contents="roundPrice"
        :width="100"
      >
        <!--      :units="`${settings.price_label}/${settings.quantity_label}`"-->
        <!--        <slot>-->
        <!--          &lt;!&ndash;          <PriceArrow :price_direction="common_status.price_direction" />&ndash;&gt;-->
        <!--          {{ common_status.round_price || "-&#45;&#45;" }}-->
        <!--        </slot>-->
      </InfoPanel>
    </a-col>

    <a-col>
      <div class="_vertical_line"></div>
    </a-col>

    <a-col
      v-if="!auctionClosed"
    >
      <!--           style="border: 1px solid #832633;float: left; width: 120px; margin-left: 3px"-->
      <!--           class="group-box"-->
      <div class="au-label" style="text-align: center;">
        <!--        Your round {{ common_status.round_number || "-&#45;&#45;" }} order-->
        Current Order
      </div>
      <DeCurrentOrder style="margin-top: 6px"
                      :common_status="common_status"
                      :trader_info="trader_info"
                      :settings="settings"

      />
      <!--      <div class="au-label">Current round</div>-->
    </a-col>

    <a-col v-if="auctionClosed">
      <div class="_vertical_line"></div>
    </a-col>

    <a-col
      v-if="auctionClosed"
    >
      <!--           class="group-box"-->
      <!--           style="float: left; margin-left: 3px"-->
      <div class="au-label">
        Your Award: move to status line: ie: Auction Closed. You were awarded x
      </div>

      <!--      TODO: this is the auctioneer award modal?-->
      <!--      <DeTraderAwardModal-->
      <!--        :de_auction=""/>-->
    </a-col>

    <a-col>
      <div class="_vertical_line"></div>
    </a-col>

    <a-col
      v-if="!auctionClosed"
      style="width: 150px; float: left; margin-left: 2px;"
    >
      <!--           class="group-box"-->
      <!--      style="width: 170px; float: left; margin-left: 2px; border: 1px solid pink"-->

      <DeOrderEntry
        :de_trader_info_value="trader_info"
        :common_status="common_status"
        :price_direction="common_status.price_direction"
        :price_has_reversed="common_status.price_has_reversed"
      />
    </a-col>

    <a-col>
      <div class="_vertical_line"></div>
    </a-col>

    <a-col

      v-if="!auctionClosed"
    >
      <!--      style="border: 1px solid yellow; width: 268px; padding-left: 7px; margin-left: 3px"-->
      <!--      class="group-box"-->
      <!--      style="border: 1px solid yellow; float: left; width: 268px; padding-left: 7px; margin-left: 3px"-->

      <div class="au-label" style="text-align: center">
        <!--        Your round {{ common_status.round_number || "-&#45;&#45;" }} order constraints-->
        Round Constraints
      </div>
      <DeOrderConstraintsWithRange
        :order_quantity="trader_info.order_quantity"
        :order_type="trader_info.order_type"
        :constraints="trader_info.bid_constraints"
        :quantity_label="settings.quantity_label"
      />
    </a-col>

  </a-row>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';
import AuSectionHeader from '../../../../ui-components/AuSectionHeader.vue';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import DeCurrentOrder from './children/DeCurrentOrder.vue';
import DeOrderEntry from './children/DeOrderEntry.vue';
import DeTraderAwardModal from './children/DeTraderAwardModal.vue';
import {
  DeAuctionValue,
  DeBidConstraints,
  DeCommonStatusValue,
  DeSettingsValue,
  DeTraderInfoValue
} from '@au21-frontend/client-connector';
import DeOrderConstraintsBar from '../constraints/DeOrderConstraintsBar.vue';
import {Container} from 'typescript-ioc';
import DeOrderConstraintsWithRange from '../constraints/DeOrderConstraintsWithRange.vue';
import {AuColors} from '../../../../au-styles/AuColors';
import InfoPanel from "../../../common/components/InfoPanel/InfoPanel.vue";
import {AuScreen} from "../../../../plugins/screen-plugin/AuScreen";
import DeCommonStatusPanel from "../../components/common-status/DeCommonStatusPanel.vue";
import {CharlizeStore} from "../../../../services/connector/CharlizeStore";

@Component({
  components: {
    AuSectionBorder,
    AuSectionHeader,
    DeCommonStatusPanel,
    DeCurrentOrder,
    DeOrderConstraintsBar,
    DeOrderConstraintsWithRange,
    DeOrderEntry,
    DeTraderAwardModal,
    InfoPanel,
  }
})
export default class DeTraderHeader extends Vue {
  @Prop({required: true}) store:CharlizeStore;

  colors = Container.get(AuColors);
  screen = new AuScreen(false);

  get de_auction():DeAuctionValue | null{
    return this.store?.live_store?.de_auction
  }

  get settings():DeSettingsValue | null{
    return this.de_auction?.settings
  }

  get common_status():DeCommonStatusValue{
    return this.de_auction?.common_status
  }

  // TODO: format the round price as in auctioneer page
  get roundPrice(): string | null {
    return this.de_auction?.common_status?.round_price;
  }

  get price_color(): string {
    return this.colors.getPriceColor(this.common_status?.price_direction)
  }

  get trader_info():DeTraderInfoValue | null{
    return this.de_auction?.trader_info
  }

  get bid_constraints():DeBidConstraints{
    return this.trader_info?.bid_constraints
  }

  get min_buy_vol(): number {
    return this.bid_constraints?.min_buy_quantity;
  }

  get max_buy_vol(): number {
    return this.bid_constraints?.max_buy_quantity;
  }

  get min_sell_vol(): number {
    return this.bid_constraints?.min_sell_quantity;
  }

  get max_sell_vol(): number {
    return this.bid_constraints?.max_sell_quantity;
  }

  // NB: move to: DeOrderConstraintsWith Range
  get buy_range(): string {
    if (this.min_sell_vol > 0)
      return '---';
    else if (this.min_buy_vol == this.max_buy_vol)
      return this.max_buy_vol + '';
    else
      return this.min_buy_vol + ' to ' + this.max_buy_vol;
  }

  get sell_range(): string {
    if (this.min_buy_vol > 0)
      return '---';
    else if (this.min_sell_vol == this.max_sell_vol)
      return this.max_sell_vol + '';
    else
      return this.min_sell_vol + ' to ' + this.max_sell_vol;
  }

  get auctionClosed(): boolean {
    return this.de_auction?.common_status?.isClosed === true;
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";


.DeTraderHeader {

  background-color: @au-background !important;
  width: 100% !important;

  /deep/ ._vertical_line {

    background-color: hsl(220, 10%, 40%);
    //float: left;
    height: 66px;
    position: relative;
    top: 7px;
    width: 0.5px;
  }

  ._round {
    margin-left: 10px;
    width: 60px;
  }

  ._heading {
    font-size: 12px;
    font-weight: bold;
    text-align: center;
  }

  ._round-number {
    color: white;
    font-size: 32px;
    margin-top: 10px;
    position: relative;
    text-align: center;
    top: -5px;
    width: 100%;
  }
}


</style>
