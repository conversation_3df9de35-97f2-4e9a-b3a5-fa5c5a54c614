<template>
  <div class="AuctionSettingsEdit">
    <div class="_legend">Auction Name</div>
    <a-textarea
      class="au-input"
      :rows="2"
      v-test:auction_name
      v-model="settings.auction_name"
      style="height: 40px; width: 550px"
      :disabled="!editable"
    />

    <hr>

    <div class="_legend">Round Timer</div>

    <div class="_form-item">
      <label class="_label">Starting time:&nbsp;</label>
      <AuDatePickerOld
        :crud="crud"
        class="_input"
        style="height:30px !important; overflow: hidden"
        :starting_time="settings.starting_time"
        @starting_time="starting_time"
        :disabled="!editable"
      />
    </div>

    <div class="_form-item">
      <label class="_label">Mins before start to announce price:&nbsp;</label>
      <NumberInput
        class="_input"
        v-model="settings.starting_price_announcement_mins"
        :disabled="!editable"
      />
      <div class="_right_column_label">(minutes)</div>
    </div>

    <!--    <div class="_form-item">-->
    <!--      <label class="_label">Minimum round closed time:&nbsp;</label>-->
    <!--      <NumberInput-->
    <!--        class="_input"-->
    <!--        v-model="settings.round_closed_min_secs"-->
    <!--        :disabled="!editable"-->
    <!--      />-->
    <!--      <span>&nbsp;(seconds)</span>-->
    <!--    </div>-->

    <!--    <div class="_form-item">-->
    <!--      <label class="_label">Minimal round open time:&nbsp;</label>-->
    <!--      <NumberInput-->
    <!--        class="_input"-->
    <!--        v-model="settings.round_open_min_secs"-->
    <!--        :disabled="!editable"-->
    <!--      />-->
    <!--      <span>&nbsp;(seconds)</span>-->
    <!--    </div>-->

    <div class="_form-item">
      <label class="_label">Show orange status after:&nbsp;</label>
      <NumberInput
        class="_input"
        v-model="settings.round_orange_secs"
        :disabled="!editable"
      />
      <div class="_right_column_label">(seconds)</div>
    </div>

    <div class="_form-item">
      <label class="_label">Show red status after:&nbsp;</label>
      <NumberInput
        class="_input"
        v-model="settings.round_red_secs"
        :disabled="!editable"
      />
      <div class="_right_column_label">(seconds)</div>
    </div>


    <hr>
    <div class="_legend">Price settings</div>

    <!--    REMOVING min round open and closed times until we implement autopilot -->
    <!--    <div class="_form-item">-->
    <!--      <label class="_label">Initial price direction:&nbsp;</label>-->
    <!--      <AuSelect-->
    <!--        class="_input"-->
    <!--        v-model="settings.initial_price_direction"-->
    <!--        :disabled="!editable"-->
    <!--        :options="priceDirectionOptions"-->
    <!--        :textBy="item => item.text"-->
    <!--        :valueBy="item => item.key"-->
    <!--      />-->
    <!--    </div>-->

    <div class="_form-item">
      <label class="_label">Initial price change:</label>
      <NumberInput
        class="_input"
        :disabled="!editable"
        v-model="settings.price_change_initial"
        :decimalPlaces="decimalPlacesComputed"
      />
      <div class="_right_column_label">({{ settings.price_label }})</div>
    </div>

    <div class="_form-item">
      <label class="_label">Post reversal price change:</label>
      <NumberInput
        class="_input"
        :disabled="!editable"
        v-model="settings.price_change_post_reversal"
        :decimalPlaces="decimalPlacesComputed"
      />
      <div class="_right_column_label">({{ settings.price_label }})</div>
    </div>

    <div class="_form-item">
      <label class="_label">Price label:&nbsp;</label>
      <au-input
        left
        class="_input au-input"
        v-model="settings.price_label"
        :disabled="!editable"
      />
    </div>

    <div class="_form-item">
      <label class="_label">Decimal places:&nbsp;</label>
      <NumberInput
        class="_input"
        v-model="decimalPlacesComputed"
        :disabled="!editable"
      />
      <div class="_right_column_label">e.g. enter 3 for 0.000</div>
    </div>

    <hr>

    <div class="_legend">Quantity Settings</div>

    <div class="_form-item">
      <label class="_label">Category:&nbsp;</label>
      <AuSelect
        class="_input"
        style="width: 195px"
        dense
        :options="auction_categories"
        :text-by="category => category.name"
        v-model="settings.category_id"
      />
    </div>

    <div class="_form-item">
      <label class="_label">Quantity label:</label>
      <au-input
        left
        class="_input au-input"
        v-model="settings.quantity_label"
        :disabled="!editable"
      />
    </div>

    <div class="_form-item">
      <label class="_label">Quantity decrement:</label>
      <NumberInput
        class="_input"
        :disabled="!editable"
        v-model="settings.quantity_step"
      />
      <div class="_right_column_label">({{ settings.quantity_label }})</div>
    </div>

    <div class="_form-item">
      <label class="_label">Minimum Quantity:</label>
      <NumberInput
        class="_input"
        :disabled="!editable"
        v-model="settings.quantity_minimum"
      />
      <div class="_right_column_label">({{ settings.quantity_label }})</div>
    </div>

    <hr>

    <div class="_legend">Default Initial Trader Limits</div>

    <div class="_form-item">
      <label class="_label">Default seller quantity limit:</label>
      <NumberInput
        class="_input"
        :disabled="!editable"
        v-model="settings.default_seller_quantity_limit"
      />
      <div class="_right_column_label">({{ settings.quantity_label }})</div>
    </div>

    <div class="_form-item">
      <label class="_label">Default buyer credit limit</label>
      <NumberInput
        class="_input"
        :disabled="!editable"
        v-model="settings.default_buyer_credit_limit"
      />
      <div class="_right_column_label">
        ($) (determines max buy)
      </div>
    </div>

    <hr>
    <div class="_legend">Excess ({{ settings.quantity_label }})
      -> Label
    </div>

    <table class="_excess-demand-table">
      <thead>
      <tr style="text-align: center">
        <th>Excess ({{ settings.quantity_label }})</th>
        <th>Excess label</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="n in [4,3,2,1]">
        <td :key="n" style="text-align: right;">
          <label class="_label">
            >
            <NumberInput
              class="_input"
              :decimalPlaces="0"
              :disabled="!editable"
              v-model="settings[`excess_level_${n}_quantity`]"
            />
          </label>
        </td>
        <td>
          <div class="_form-item">
            <AuInput
              class="_input"
              :disabled="!editable"
              v-model="settings[`excess_level_${n}_label`]"
            />
          </div>
        </td>
      </tr>
      <tr>
        <td style="text-align: right;">
          <label class="_label">
            <AuInput
              class="_input"
              disabled
              :value="`0 - ${minExcessValue}`"
            />
          </label>
        </td>
        <td>
          <div class="_form-item">
            <AuInput
              class="_input"
              v-model="settings.excess_level_0_label"
            />
          </div>
        </td>
      </tr>

      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';
import {isCrudEditable} from '../../../../entity-helpers/Crud';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';
import {createMultipleByClosure, forceDecimals} from '@au21-frontend/utils';
import {Crud, DateTimeValue, DeSettingsValue, PriceDirection} from '@au21-frontend/client-connector';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import AuDatePickerOld from '../../../../ui-components/AuDatePicker/AuDatePickerOld.vue';
import AuInput from '../../../../ui-components/AuInput/AuInput.vue';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {createDemo__AuctionCategory} from '../../../../demo-helpers/AuctionCategory.helper';

@Component({
  name: 'AuctionSettingsEdit',
  components: {AuInput, AuDatePickerOld, AuSelect, NumberInput}
})
export default class AuctionSettingsEdit extends Vue {
  @Prop({required: true}) store: CharlizeStore;
  @Prop({required: true}) settings: DeSettingsValue;
  @Prop({required: true}) crud: Crud;

  starting_time(st: DateTimeValue | null) {
    this.settings.starting_time = st;
  }

  get editable(): boolean {
    return isCrudEditable(this.crud);
  }

  get decimalPlacesComputed() {
    return this.settings.price_decimal_places;
  }

  get auction_categories () {
    // TODO Use categories from store
    return createMultipleByClosure(createDemo__AuctionCategory, 10)
  }

  set decimalPlacesComputed(value) {
    this.settings.price_change_initial = forceDecimals(this.settings.price_change_initial, value);
    this.settings.price_change_post_reversal = forceDecimals(this.settings.price_change_post_reversal, value);
    this.settings.price_decimal_places = value;
  }

  get priceDirectionOptions(): { key: PriceDirection, text: string }[] {
    return [
      {key: PriceDirection.UP, text: 'Up'},
      {key: PriceDirection.DOWN, text: 'Down'}
    ];
  }

  get minExcessValue(): string {
    return this.settings.excess_level_1_quantity;
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.AuctionSettingsEdit {
  background-color: #111; //  hsl(186, 12%, 30%);
  border-radius: 5px;
  border: 1px solid #111;
  font-size: 12px;
  margin: 2px;
  padding: 2px;
  width: 555px;

  ._right_column_label {
    color: @au-label-color !important;
    font-weight: bold;
  }

  ._input {
    font-size: 12px;
    height: 23px !important;
    margin: 2px 10px !important;
    width: 100px;
  }

  ._form-item {
    display: flex;
    align-items: baseline;
  }

  ._legend {
    border-bottom-width: 0;
    color: @au-label-color !important;
    font-size: 13px;
    font-weight: bold;
    margin-left: 4px;
    margin-right: 3px;
    width: 250px;
  }

  ._label {
    color: @au-label-color !important;
    font-weight: bold;
    min-width: 250px;
    text-align: right;
    width: 250px;
  }

  ._excess-demand-table {
    margin-left: 120px;
  }
}
</style>
