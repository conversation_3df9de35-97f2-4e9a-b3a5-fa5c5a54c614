<template>
  <div class="DeAwardRow" :style="{color}">
    <!--    <div>-->
    <!--      <div class="_cell">{{ trader.shortname }}</div>-->
    <!--      <div class="_cell">&nbsp;{{ side_label }}</div>-->
    <!--    </div>-->

    <!--    <div class="_row">-->

    <div v-if="params.colDef.headerName==='Trader'" class="_trader">
      {{ row.trader_name }}
    </div>

    <div v-if="params.colDef.headerName==='Side'" class="_side">
      {{ row.side_str }}
    </div>

    <div v-if="params.colDef.headerName==='Quantity'" class="_quantity">
      <div style="width: 40px; margin-left:10px; text-align: right;">
        {{ row.quantity }}
      </div>
    </div>

    <div v-if="params.colDef.headerName==='Counterparty'" class="_trader">
      <div v-if="row.counterparty_name">
        <div class="_counterparty"
             v-if="row.side === OrderType.BUY && row.counterparty_name">
          from:
        </div>
        <div class="_counterparty"
             v-if="row.side === OrderType.SELL && row.counterparty_name">
          to:
        </div>
        {{ row.counterparty_name }}
      </div>
    </div>

    <div v-if="params.colDef.headerName==='Value'" class="_value">
      <div style="width: 98px; text-align: right;">
        {{ row.value }}
      </div>
    </div>

    <div v-if="params.colDef.headerName==='Credit limit'" class="_credit">
      <div style="width: 98px; text-align: right;">
        {{ row.credit_limit }}
      </div>
    </div>

    <!--  </div>-->

  </div>

</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeAwardTable from './DeAwardTable.vue';
import {OrderType} from '@au21-frontend/client-connector';
import {DeAwardRowModel} from './DeAwardRowModel';
import {AuColors} from '../../../../../au-styles/AuColors';
import {Container} from 'typescript-ioc';

const getColId = (round: number) => `round-${round}`;

@Component({
  name: 'DeAwardRow',
  components: {}
})
export default class DeAwardRow extends Vue {

  params = null;

  colors = Container.get(AuColors);

  OrderType = OrderType;

  get color(): string {
    return this.colors.order_quantity_text_color(this.row.side)

  }

  get award_table(): DeAwardTable {
    return this.$parent.$parent.$parent as DeAwardTable;
  }

  get row(): DeAwardRowModel {
    return this.params.data;
  }

}

</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables";

.DeAwardRow {
  //border: 1px solid red;
  overflow: hidden;

  //position: relative;

  ._cell {
    display: inline-block;
  }

  ._counterparty {
    display: inline-block;
    margin-right: 5px;
    text-align: right;
    width: 25px;
  }

  //._counterparty_heading {
  //  //    border: 1px solid yellow;
  //  color: hsl(200, 10%, 60%);
  //  font-size: 10px;
  //  font-weight: bold;
  //  line-height: 1em;
  //  margin: 0;
  //  padding: 0;
  //}

  //._row {
  //  border: 1px solid yellow;
  //  color: hsl(0, 0%, 80%);
  //  font-size: 11px;
  //  height: 15px;
  //  line-height: 1.5em;
  //  overflow: hidden;
  //  padding: 0;
  //  position: relative;
  //  top: 0;
  //
  //  &:nth-child(odd) {
  //    background-color: hsl(0, 0%, 18%);
  //  }
  //
  //  &:nth-child(even) {
  //    background-color: hsl(0, 0%, 14%);
  //  }
  //}

  ._trader {
    //    border: 1px solid red;
    display: inline-block;
  }

  ._side {
    //  border: 1px solid yellow;
    display: inline-block;
    text-align: center;
    width: 75px;
  }

  ._quantity {
    display: inline-block;
    //border: 1px solid green;
    text-align: center;
  }

  ._value {
    display: inline-block;
    //border: 1px solid blue;
    text-align: center;
  }

  ._credit {
    //border: 1px solid pink;
    display: inline-block;
    text-align: center;

  }

}
</style>
