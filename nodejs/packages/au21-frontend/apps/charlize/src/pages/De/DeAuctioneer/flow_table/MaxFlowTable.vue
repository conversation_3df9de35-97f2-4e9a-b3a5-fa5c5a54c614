<template>
  <AuAgGrid
    ref="auAgGrid"
    :height="height"
    :width="width"
    :rowData="flowCalcResult.scenarios"
    :columnDefs="columnDefs"
    :getRowHeight="() => 90"
  />
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {FlowCalcResult, FlowCalcTrader} from '../../../../services/flow-algos/domain/model';
import {ColDef} from 'ag-grid-community';
import MaxFlowTableCell from './MaxFlowTableCell.vue';

@Component({
  components: {MaxFlowTableCell, AuAgGrid},
})
export default class MaxFlowTable extends Vue {
  @Prop({required: true, type: Number}) height: number
  @Prop({required: true, type: Number}) width: number
  @Prop({required: true}) flowCalcResult: FlowCalcResult

  get columnDefs() {
    const cols: ColDef[] = []

    cols.push({
      headerName: 'Max flow',
      pinned: true,
      width: 140,
      suppressMenu: true,
      cellStyle: {padding: 0},
    })

    this.flowCalcResult.ordered_traders.forEach((trader: FlowCalcTrader) => {
      cols.push({
        headerName: trader.cid,
        suppressMenu: true,
        width: 70,
        cellStyle: {padding: 0, margin: 0},
        cellRendererFramework: MaxFlowTableCell
      })
    })

    return cols
  }

}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";


</style>
