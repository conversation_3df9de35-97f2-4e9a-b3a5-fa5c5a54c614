<template>
  <a-modal
    class="DeAuctionRulesModal"
    title="Auction Rules"
    @cancel="cancel()"
    visible
    closable
    centered
    width="930px"
  >
    <div
      :style="{maxHeight: modalHeight ? modalHeight + 'px' : undefined}"
      class="DeAuctionRulesModal__content"
    >
      <DeAuctionRules
        :price_change_initial="settings.price_change_initial"
        :price_change_post_reversal="settings.price_change_post_reversal"
        :price_units="settings.price_label"
        :quantity_units="settings.quantity_label"
        :de_settings_value="settings"
      />
    </div>
    <a-button
      slot="footer"
      type="primary"
      @click="cancel()"
    >
      Cancel
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuSectionBorder from '../../../../../../ui-components/AuSectionBorder.vue';
import DeAuctionRules from './DeAuctionRules.vue';
import {AuScreen} from '../../../../../../plugins/screen-plugin/AuScreen';
import {CharlizeStore} from "../../../../../../services/connector/CharlizeStore";
import {DeSettingsValue} from "@au21-frontend/client-connector";

@Component({
  name: 'DeAuctionRulesModal',
  components: {DeAuctionRules, AuSectionBorder},
})
export default class DeAuctionRulesModal extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  screen = new AuScreen() // same for auctioneer or trader

  get settings(): DeSettingsValue {
    return this.store.live_store?.de_auction?.settings
  }

  get modalHeight() {
    return this.screen.modal_height
  }

  cancel() {
    this.$emit('close')
  }
}
</script>

<style lang="less" scoped>
.DeAuctionRulesModal {
  &__content {
    white-space: normal;
    overflow-y: auto;
  }
}
</style>
