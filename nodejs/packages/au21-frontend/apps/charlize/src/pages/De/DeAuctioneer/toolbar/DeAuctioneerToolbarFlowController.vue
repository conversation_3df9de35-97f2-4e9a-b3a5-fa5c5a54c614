<template>
  <div class="DeAuctioneerToolbarFlowController">

    <div style="display: inline-block" class="au-label">Controls:</div>

    <a-button-group>

      <ToolbarButton
        @click="emitWithDebounce(DeFlowControlType.START_AUCTION)"
        :disabled="is_disabled(DeFlowControlType.START_AUCTION)"
      >
        Start
      </ToolbarButton>
      <ToolbarButton
        @click="emitWithDebounce(DeFlowControlType.CLOSE_ROUND)"
        :disabled="is_disabled(DeFlowControlType.CLOSE_ROUND)"
      >
        Close
      </ToolbarButton>
      <a-popconfirm
        title="Reset this Round?"
        placement="bottom"
        @confirm="emitOnControl(DeFlowControlType.REOPEN_ROUND)"
        okText="Yes"
        cancelText="No"
        :disabled="is_disabled(DeFlowControlType.REOPEN_ROUND)"
      >
        <ToolbarButton :disabled="is_disabled(DeFlowControlType.REOPEN_ROUND)">
          Reset
        </ToolbarButton>
      </a-popconfirm>
      <ToolbarButton
        @click="emitWithDebounce(DeFlowControlType.NEXT_ROUND)"
        :disabled="is_disabled(DeFlowControlType.NEXT_ROUND)"
      >
        Next
      </ToolbarButton>

      <ToolbarButton @click="$emit('showAward')">
        Award
      </ToolbarButton>

      <!--      <ToolbarButton-->
      <!--        @click="emitWithDebounce(DeFlowControlType.AWARD_AUCTION)"-->
      <!--        :disabled="is_disabled(DeFlowControlType.AWARD_AUCTION)"-->
      <!--      >-->
      <!--        Award-->
      <!--      </ToolbarButton>-->
    </a-button-group>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import ToolbarButton from './ToolbarButton.vue';
import {DeAuctionValue, DeFlowControlType} from '@au21-frontend/client-connector';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';

@Component({
  components: {AuSectionBorder, ToolbarButton}
})
export default class DeAuctioneerToolbarFlowController extends Vue {
  @Prop({required: true}) auction: DeAuctionValue | null;

  DeFlowControlType = DeFlowControlType;

  is_disabled(c: DeFlowControlType): boolean {
    return this.auction.auctioneer_status.controls[c] === false;
  }

  // To prevent double-click.
  disableEvents = false;

  // emitWithDebounce(eventType: keyof typeof DeFlowControlType): void {
  emitWithDebounce(eventType: DeFlowControlType): void {
    if (this.disableEvents) {
      return;
    }

    this.disableEvents = true;
    setTimeout(() => {
      this.disableEvents = false;
    });

    this.emitOnControl(eventType);
  }

  emitOnControl(eventType: DeFlowControlType) {
    this.$emit('onControl', eventType);
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.DeAuctioneerToolbarFlowController {

}
</style>
