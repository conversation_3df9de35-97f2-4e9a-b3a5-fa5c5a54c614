<template>
  <VbDemo>
    <VbCard>
      <AButton @click="() => awardable = true">engage</AButton>
      <AButton @click="() => awardable = false">disengage</AButton>
      <!--      <AButton @click="set_level('NORMAL')">normal</AButton>-->
      <!--      <AButton @click="set_level('WARNING')">warning</AButton>-->
      <!--      <AButton @click="set_level('ERROR')">error</AButton>-->
    </VbCard>
    <VbCard>
      <div
        v-for="status in statusOptions"
        :key="status"
      >
        <br>
        {{ status }}
        <br>
        <DeAwardableDisplay :awardable="awardable"/>
      </div>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createDemo__DeCommonStatusValue} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import {DeAuctioneerState} from '@au21-frontend/client-connector';
import DeAwardableDisplay from './DeAwardableDisplay.vue';

@Component({
  components: {AuSelect, DeAwardableDisplay}
})
export default class DeAwardableDisplayDemo extends Vue {
  commonStatus = createDemo__DeCommonStatusValue();
  statusOptions = Object.values(DeAuctioneerState);
  auctioneerStatus = {};

  awardable = false;

}
</script>
