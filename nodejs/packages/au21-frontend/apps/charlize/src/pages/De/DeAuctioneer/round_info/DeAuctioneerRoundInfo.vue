<template>

  <div class='DeAuctioneerRoundInfo'>

    <!--    ROUND-->

    <div class='_round'>
      <div class='heading au-label'>Round</div>
      <div class='round-data' style='position: relative;'>{{ roundNumber || '---' }}</div>
    </div>

    <!--    PRICE-->

    <div class='_vertical-line'></div>

    <div style='float:left; width: 215px;'>

      <div class='heading au-label'>
        Price ({{ priceLabel }})
      </div>

      <!-- prices -->

      <div style='margin-left: 20px'>

        <!--    Starting Price    -->
        <InfoPanel
          heading='Starting'
          :contents="auctioneer_status ? auctioneer_status.starting_price : ''"
          :height='info_panel_height'
          :width='77'
          color='#ccc'
        />

        <!--    Round Price    -->
        <InfoPanel style='display: inline-block'
                   :heading='`Round ${roundNumber}`'
                   :heading_margin_left='10'
                   :height='info_panel_height'
                   :contents='roundPrice'
                   :width='box_width + 30'
                   :color='price_color'
        >
          <slot>
            <!--            <PriceArrow-->
            <!--              :price_direction="commonStatus.price_direction"-->
            <!--              :big="true"-->
            <!--            />-->
            {{ common_status ? common_status.round_price : '---' }}
          </slot>
          <!--      <template v-if="commonStatus.price_direction" #after>-->
          <!--        <PriceArrow :up="commonStatus.price_direction === PriceDirection.UP"/>-->
          <!--      </template>-->
        </InfoPanel>
      </div>
    </div>

    <!--  QUANTITY  -->

    <div class='_vertical-line'></div>

    <div style='float:left;'>

      <div class='heading au-label'>Quantity ({{ quantityLabel }})</div>
      <!--                    Round {{ roundNumber }}-->

      <InfoPanel
        style='margin-left: 10px'
        :color='colors.au_buy()'
        :contents='last_total_buy'
        :heading='`Buy`'
        :height='info_panel_height'
        :width='box_width'
      />

      <InfoPanel
        :color='colors.au_sell()'
        :contents='last_total_sell'
        :heading='`Sell`'
        :height='info_panel_height'
        :width='box_width'
      />

      <InfoPanel
        :color='colors.au_match()'
        :contents='last_match'
        :heading='`Match`'
        :height='info_panel_height'
        :width='box_width'
      />

      <InfoPanel
        :heading='`Excess`'
        :contents='last_excess'
        :width='box_width'
        :height='info_panel_height'
      />

      <InfoPanel
        :contents='potential'
        :heading='`Potential`'
        :height='info_panel_height'
        :width='box_width'
      />

    </div>

    <!--    <div class="_vertical_line"></div>-->

    <!--    <div style="float:left;">-->

    <!--      <div class="heading au-label">Round</div>-->
    <!--      <DeRoundController-->
    <!--        v-model="selectedRoundProxy"-->
    <!--        :lastRound="round_number"-->
    <!--      />-->
    <!--    </div>-->

  </div>
</template>

<script lang='ts'>
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import {
  DeAuctioneerInfoValue,
  DeAuctioneerStatusValue,
  DeAuctionValue,
  DeCommonStatusValue,
  DeSettingsValue,
  PriceDirection
} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import InfoPanel from '../../../common/components/InfoPanel/InfoPanel.vue';
import DeClock from '../../components/de-clock/DeClock.vue';
import {LEVEL_STATUS} from '../../components/de-starting-price/DeStartingPrice.vue';
import DeAuctioneerInfoPanel from './DeAuctioneerInfoPanel/DeAuctioneerInfoPanel.vue';
import {AuColors} from '../../../../au-styles/AuColors';
import DeAuctioneerClock from '../clock/DeAuctioneerClock.vue';
import DeRoundController from '../round_controller/DeRoundController.vue';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';

@Component({
  components: {
    AuSectionBorder,
    DeAuctioneerClock,
    DeAuctioneerInfoPanel,
    DeClock,
    DeRoundController,
    InfoPanel
  }
})
export default class DeAuctioneerRoundInfo extends Vue {
  @Prop({ required: true }) store: CharlizeStore;
  @Prop({ required: true }) selected_round: number;

  round_number = this.common_status?.round_number || 0; // initial set

  get de_auction(): DeAuctionValue | null {
    return this.store?.live_store?.de_auction;
  }

  get common_status(): DeCommonStatusValue | null {
    return this.de_auction?.common_status;
  }


  get auctioneer_info(): DeAuctioneerInfoValue {
    return this.de_auction?.auctioneer_info;
  }

  get auctioneer_status(): DeAuctioneerStatusValue | null {
    return this.de_auction?.auctioneer_status;
  }

  get auction_settings(): DeSettingsValue {
    return this.de_auction?.settings;
  }


  colors = Container.get(AuColors);
  disabled_color = '#777';

  box_width = 75;
  info_panel_height = 50;

  PriceDirection = PriceDirection;

  get selectedRoundProxy() {
    return this.selected_round;
  }

  set selectedRoundProxy(round: number) {
    this.$emit('update:selectedRound', round);
  }

  get price_color(): string {
    if (!this.common_status) {
      return '';
    }
    return this.colors.getPriceColor(this.common_status.price_direction);
  }

  get priceLabel(): string {
    return this.auction_settings?.price_label || '';
  }

  get quantityLabel(): string {
    return this.auction_settings?.quantity_label || '';
  }

  get roundPrice(): string {
    return this.common_status?.round_price || '';
  }

  get last_total_buy(): string {
    return this.auctioneer_info?.last_total_buy || '';
  }

  get last_total_sell(): string {
    return this.auctioneer_info?.last_total_sell || '';
  }

  get last_match(): string {
    return this.auctioneer_info?.last_match || '';
  }

  get last_excess(): string {
    return this.auctioneer_info?.last_excess || '';
  }

  get potential(): string {
    return this.auctioneer_info?.potential || '';
  }

  // MOVED FROM OLD DeRoundStatusDisplay, might not be needed:


  thresholds = {
    alert: 5,
    warning: 10
  };

  // state_colors: { [e in LEVEL_STATUS]: string } = {
  //   NOT_STARTED: '#fff',
  //   NORMAL: 'rgb(65, 184, 131)',
  //   WARNING: 'orange',
  //   ALERT: 'red'
  // };
  //
  // get color() {
  //   return this.state_colors[this.level_status];
  // }

  get roundNumber(): number | null {
    return this.common_status?.round_number;
  }

  get roundSeconds(): number | null {
    return 1; // this.commonStatus?.initial_time
  }

  get remainingTime(): number | null {
    return 2; // this.commonStatus?.remaining_time
  }

  get level_status(): LEVEL_STATUS | null {
    return 'NORMAL'; // TODO !
    // if(this.commonStatus == null)
    //   return null
    //
    // const time = this.commonStatus.remaining_time
    // if ([
    //   DeAuctioneerState.ROUND_RUNNING,
    //   DeAuctioneerState.ROUND_PAUSED,
    // ].includes(this.commonStatus.auctioneer_state))
    //   return time <= this.thresholds.alert ? 'ALERT' :
    //     time <= this.thresholds.warning ? 'WARNING' : 'NORMAL'
    // else
    //   return 'NOT_STARTED'
  }
}
</script>
<style lang='less' scoped>
@import (reference) '../../../../au-styles/variables.less';

.DeAuctioneerRoundInfo {
  background-color: @au-background !important;
  //border: 1px solid red;
  display: flex;
  float: left;
  height: 66px !important;
  overflow: hidden;
  //  position: relative;
  width: 700px;

  .heading {
    margin-top: 2px;
    padding: 0;
    text-align: center;
    width: 100%;
  }

  ._round {
    //height: 60px;
    display: inline-block;
    float: left;
    font-size: 11px;
    height: 60px;
    padding: 0;
    position: relative;
    text-align: center;
    width: 90px;

    .round-data {
      color: @au_round;
      font-size: 36px;
      font-weight: bold;
      height: 45px;
      top: -6px;
      text-align: center;
      width: 100%;
    }
  }

  ._vertical-line {
    background-color: hsl(220, 10%, 50%); // @au_beige; // !important;
    float: left;
    height: 50px;
    position: relative;
    top: 7px;
    width: 0.5px;
  }

}
</style>
