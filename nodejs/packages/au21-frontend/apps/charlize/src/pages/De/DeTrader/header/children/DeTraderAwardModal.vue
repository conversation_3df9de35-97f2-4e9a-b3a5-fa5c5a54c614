<template>
  <a-modal
    class="DeTraderAwardModal"
    title="Set Auction Award"
    @cancel="cancel()"
    visible
    closable
    centered
    width="1520px"
  >

    <!-- TODO: For now the system determines the award round, so probably we just need: -->
    <!-- TODO: 1) order book  -->
    <!-- TODO: 2) sankey / 3) match table master, detail by buyer or seller -->
    <!-- TODO: 4) matrix with summary rows & cols / 5) supply demand chart -->

    <div style="display: flex">
      <RoundTotalTable
        style="width: 600px; height: 700px"
        :roundResults="roundResults"
        :selectedRound.sync="selectedRoundNumber"
      />

      <AwardMatchesTable
        style="width: 510px"
        :height="700"
        :matches="matches"
      />

      <div style="width: 400px; position: relative;" class="text-color-white">
        <a-spin size="large" style="position: absolute; top: 240px; left: 180px"/>
        <a-radio-group class="ml-1 mb-1" style="color: white" v-model="nonQuantityAxis">
          Non quantity axis:
          <a-radio :value="DeRoundChartNonQuantityAxis.PRICE">
            Price
          </a-radio>
          <a-radio :value="DeRoundChartNonQuantityAxis.ROUND">
            Round
          </a-radio>
        </a-radio-group>
        <br>
        <a-radio-group class="ml-1 mb-1" style="color: white" v-model="quantityAxis">
          Quantity axis:
          <a-radio :value="DeRoundChartQuantityAxis.Y">
            Y
          </a-radio>
          <a-radio :value="DeRoundChartQuantityAxis.X">
            X
          </a-radio>
        </a-radio-group>

        <!--        <DeRoundChart-->
        <!--          :rounds="rounds"-->
        <!--          :traders="traders"-->
        <!--          :cells="roundTraderCells"-->
        <!--          :width="400"-->
        <!--          :height="300"-->
        <!--          :non_quantity_axis="nonQuantityAxis"-->
        <!--          :quantity_axis="quantityAxis"-->
        <!--        />-->

        <DeSupplyDemandChartSvg
          :rounds="rounds"
          :width="400"
          :height="340"
          quantity_axis='quantityAxis'
        />
      </div>
    </div>

    <a-button
      slot="footer"
      type="primary"
      @click="cancel()"
    >
      Cancel
    </a-button>

    <a-button
      slot="footer"
      :disabled="!selectedRoundNumber"
      type="primary"
      @click="awardAuction()"
    >
      Award
    </a-button>
  </a-modal>
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import AuSectionBorder from '../../../../../ui-components/AuSectionBorder.vue';
import {Container} from 'typescript-ioc';
import {
  CompanyElement,
  de_auction_award_command,
  DeAuctionValue,
  DeMatrixEdgeElement,
  DeRoundElement,
  DeRoundResultVM,
  DeRoundTraderElement,
  DeScenarioMatchVM,
  DeTraderElement,
  SocketConnector
} from '@au21-frontend/client-connector';
import AuSelect from '../../../../../ui-components/AuSelect/AuSelect.vue';
import RoundTotalTable from '../../../DeAuctioneer/award/AwardRoundTotalTable/RoundTotalTable.vue';
import DeAwardTraderMatrix from '../../../DeAuctioneer/award/DeAwardTraderMatrix.vue';
import AwardMatchesTableDemo from '../../../DeAuctioneer/award/AwardMatchesTable.demo.vue';
import AwardMatchesTable from '../../../DeAuctioneer/award/AwardMatchesTable.vue';
import {
  DeRoundChartNonQuantityAxis,
  DeRoundChartQuantityAxis
} from '../../../DeAuctioneer/award/DeRoundChart/DeRoundChart-types';
import DeSupplyDemandChartSvg from '../../../DeAuctioneer/award/DeSupplyDemandChart/DeSupplyDemandChartSvg.vue';

@Component({
  components: {
    DeSupplyDemandChartSvg,
    AwardMatchesTable,
    AwardMatchesTableDemo,
    DeAwardTraderMatrix,
    RoundTotalTable,
    AuSelect,
    AuSectionBorder
  }
})
export default class DeTraderAwardModal extends Vue {
  @Prop({required: true}) de_auction: DeAuctionValue;
  @Prop({required: true}) companies: CompanyElement[];

  connector = Container.get(SocketConnector);
  selectedRoundNumber: number | null = null;
  isChartFlipped = false

  DeRoundChartQuantityAxis = DeRoundChartQuantityAxis
  DeRoundChartNonQuantityAxis = DeRoundChartNonQuantityAxis

  quantityAxis = DeRoundChartQuantityAxis.Y
  nonQuantityAxis = DeRoundChartNonQuantityAxis.ROUND

  awardAuction() {
    this.connector.publish(de_auction_award_command({
      //allocations: {},
      auction_id: this.de_auction.auction_id,
      round_number: this.selectedRoundNumber + ''
    }));
    this.$emit('close');
  }

  get roundTraderCells(): DeRoundTraderElement[] {
    return this.de_auction?.blotter?.round_traders || [];
  }

  get rounds(): DeRoundElement[] {
    return this.de_auction?.blotter.rounds;
  }

  get traders(): DeTraderElement[] {
    return this.de_auction?.blotter.traders;
  }

  cancel() {
    this.$emit('close');
    this.selectedRoundNumber = null;
  }

  get roundResults(): DeRoundResultVM[] {
    return this.de_auction?.award_value?.round_results?.reverse() || []
  }

  get matches(): DeScenarioMatchVM[] {
    if (this.selectedRoundNumber === null) return [];
    const result = this.roundResults.find(result => result.round_number === this.selectedRoundNumber) as DeRoundResultVM;
    return result.matches;
  }

  get currentRoundEdges(): DeMatrixEdgeElement[] {
    return this.de_auction?.matrix_last_round?.edges?.filter(edge => edge['r'] === this.selectedRoundNumber) || [];
  }

  get relevantRounds() {
    return this.de_auction?.blotter?.rounds?.slice(-3) || [];
  }

  get relevantRoundNumbers() {
    return this.relevantRounds.map(round => round.round_number);
  }
}
</script>

<style lang="less" scoped>

@import (reference) "../../../../../au-styles/variables.less";

.DeTraderAwardModal {
  background-color: #333; //  hsl(186, 12%, 30%);
  border-radius: 5px;
  border: 1px solid #111;
  color: #ccc;
  overflow: auto;
  font-size: 12px;
  margin: 2px;
  padding: 2px;
  width: 555px;
  display: flex;
  flex-direction: row;

  &__input {
    width: 100px;
    font-size: 12px;
    margin: 2px 10px !important;
  }

  &__form-item {
    display: flex;
    align-items: baseline;
  }

  &__label {
    width: 180px;
    min-width: 180px;
    text-align: right;
  }
}
</style>
