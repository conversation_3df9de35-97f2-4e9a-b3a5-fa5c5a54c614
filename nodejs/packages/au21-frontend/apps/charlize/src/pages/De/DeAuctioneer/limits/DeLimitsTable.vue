<template>
  <AuAgGrid
    class='DeLimitsTable'
    :height='height'
    :width='width'
    :rowData='rows'
    :gridOptions='gridOptions'
  />
</template>

<script lang='ts'>
import {Component, Prop, Ref, Vue} from 'vue-property-decorator';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {DeRoundTraderElement, StoreElement} from '@au21-frontend/client-connector';

import {ColDef, GridOptions} from 'ag-grid-community';
import {
  DeLimitsTableActionsParams,
  DeLimitsTableCellParams,
  DeLimitsTableRow,
  LimitsField
} from './DeLimitsTable.types';
import DeLimitsTableInput from './DeLimitsTableInput.vue';
import DeLimitsTableActions from './DeLimitsTableActions.vue';
import DeLimitsTableHeader from './DeLimitsTableHeader.vue';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';

@Component({
  name: 'DeLimitsTable',
  components: { AuAgGrid }
})
export default class DeLimitsTable extends Vue {
  @Prop({ required: true }) height: number;
  @Prop({required:true}) quantity_label: string;
  @Prop({ required: true }) round_trader_elements: DeRoundTraderElement[];
  @Prop({ type: Boolean }) readonly: boolean;

  get rows(): DeLimitsTableRow[] {
    return this.round_trader_elements.map((rte:DeRoundTraderElement) => ({
      id: rte.id,
      company_id: rte.cid,
      company_name: rte.company_shortname,
      buyer_credit_limit: rte.buyer_credit_limit,
      buyer_credit_limit_str: rte.buyer_credit_limit_str,
      max_buy_quantity: rte.constraints?.max_buy_quantity || 0,
      min_buy_quantity: rte.constraints?.min_buy_quantity || 0,
      min_sell_quantity: rte.constraints?.min_sell_quantity || 0,
      max_sell_quantity: rte.constraints?.max_sell_quantity || 0,
    }));
  }

  @Ref() readonly auAgGrid: AuAgGrid;
  width = 725;
  gridOptions: GridOptions = {
    getRowNodeId: (data: StoreElement) => data.id,
    defaultColDef: {
      suppressMenu: true
    },
    suppressHorizontalScroll: true,
    columnDefs: <ColDef[]>[
      {
        headerName: 'Company',
        headerComponentFramework: AuAgGridCenteredHeader,
        width: 120,
        valueGetter: (value) => {
          const row = value.node.data as DeLimitsTableRow;
          return row.company_name; // company_id;
        },
        field: 'company_name'
        // cellRendererParams: {
        //   field: 'company_name'
        // }
      },
      {
        headerName: 'Buyer Limits @ current price',
        headerComponentFramework: AuAgGridCenteredHeader,
        children: [
          {
            headerName: 'Credit Limit',
            headerComponentFramework: DeLimitsTableHeader,
            width: 110,
            cellRendererParams: this.getCellRendererParamsForColumn('buyer_credit_limit_str'),
            cellRendererFramework: DeLimitsTableInput
          },
          {
            headerName: 'Buy Max (MMlb)',
            headerComponentFramework: DeLimitsTableHeader,
            width: 80,
            cellRendererParams: this.getCellRendererParamsForColumn('max_buy_quantity'),
            cellRendererFramework: DeLimitsTableInput
          },
          {
            headerName: 'Buy Min (MMlb)',
            headerComponentFramework: DeLimitsTableHeader,
            width: 80,
            cellRendererParams: this.getCellRendererParamsForColumn('min_buy_quantity'),
            cellRendererFramework: DeLimitsTableInput
          }
        ]
      },
      {
        headerName: 'Seller Limits @ current price',
        headerComponentFramework: AuAgGridCenteredHeader,
        children: [
          {
            headerName: 'Sell Min',
            headerComponentFramework: DeLimitsTableHeader,
            width: 80,
            cellRendererParams: this.getCellRendererParamsForColumn('min_sell_quantity'),
            cellRendererFramework: DeLimitsTableInput
          },
          {
            headerName: 'Sell Max',
            headerComponentFramework: DeLimitsTableHeader,
            width: 80,
            cellRendererParams: this.getCellRendererParamsForColumn('max_sell_quantity'),
            cellRendererFramework: DeLimitsTableInput
          },
        ]
      },
      {
        headerName: 'Actions',
        headerComponentFramework: DeLimitsTableHeader,
        width: 80,
        cellRendererParams: {
          isRowAllowable: () => {
            // TODO Show only if current round missed, and round closed.
            return true;
          },
          allow: (row: DeLimitsTableRow) => {
            this.$emit('allow', row.company_id);
          }
        } as DeLimitsTableActionsParams,
        cellRendererFramework: DeLimitsTableActions
      }
    ]
  };

  getCellRendererParamsForColumn(field: LimitsField): DeLimitsTableCellParams {
    return {
      field,
      getReadonly: () => this.readonly,
      saveValue(value, field) {
        // TODO Should save value properly.
        // console.log('save', value, field);
      }
    };
  }
}
</script>

<style lang='less' scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeLimitsTable {
  ///deep/ .ag-header-group-cell-label{
  // // border: 1px solid red;
  // // text-align: center !important;
  // // padding: 0 50px;
  //}
}
</style>
