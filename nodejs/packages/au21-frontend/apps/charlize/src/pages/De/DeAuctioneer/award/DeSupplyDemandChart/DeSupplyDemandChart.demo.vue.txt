<template>
  <VbDemo>
    <VbCard title="price">
      <DeSupplyDemandChart
        :rounds="rounds"
        :width="500"
        :height="500"
        :non_quantity_axis="DeRoundChartNonQuantityAxis.PRICE"
        :quantity_axis="DeRoundChartQuantityAxis.Y"
      />
    </VbCard>
    <VbCard title="round">
      <DeSupplyDemandChart
        :rounds="rounds"
        :width="500"
        :height="500"
        :non_quantity_axis="DeRoundChartNonQuantityAxis.ROUND"
        :quantity_axis="DeRoundChartQuantityAxis.Y"
      />
    </VbCard>
    <VbCard title="price + X">
      <DeSupplyDemandChart
        :rounds="rounds"
        :width="500"
        :height="500"
        :non_quantity_axis="DeRoundChartNonQuantityAxis.PRICE"
        :quantity_axis="DeRoundChartQuantityAxis.X"
      />
    </VbCard>
    <VbCard title="round + X">
      <DeSupplyDemandChart
        :rounds="rounds"
        :width="500"
        :height="500"
        :non_quantity_axis="DeRoundChartNonQuantityAxis.ROUND"
        :quantity_axis="DeRoundChartQuantityAxis.X"
      />
    </VbCard>
    <VbCard title="is reactive">
      <button
        @click="nonQuantityAxis = nonQuantityAxis === DeRoundChartNonQuantityAxis.PRICE ? DeRoundChartNonQuantityAxis.ROUND : DeRoundChartNonQuantityAxis.PRICE"
      >
        nonQuantityAxis: {{ nonQuantityAxis }}
      </button>
      <button
        @click="quantityAxis = quantityAxis === DeRoundChartQuantityAxis.Y ? DeRoundChartQuantityAxis.X : DeRoundChartQuantityAxis.Y"
      >
        quantityAxis: {{ quantityAxis }}
      </button>
      <DeSupplyDemandChart
        :rounds="rounds"
        :width="500"
        :height="500"
        :non_quantity_axis="nonQuantityAxis"
        :quantity_axis="quantityAxis"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeSupplyDemandChart from './DeSupplyDemandChart.vue.txt';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__DeRoundElement} from '../../../../../demo-helpers/DeRoundTable.helper';
import {DeRoundElement} from '@au21-frontend/client-connector';

import {DeRoundChartNonQuantityAxis, DeRoundChartQuantityAxis} from '../DeRoundChart/DeRoundChart-types';

@Component({
  name: 'DeSupplyDemandChartDemo',
  components: {DeSupplyDemandChart}
})
export default class DeSupplyDemandChartDemo extends Vue {
  rounds: DeRoundElement[] = [];

  nonQuantityAxis = DeRoundChartNonQuantityAxis.ROUND;
  quantityAxis = DeRoundChartQuantityAxis.Y;

  DeRoundChartNonQuantityAxis = DeRoundChartNonQuantityAxis;
  DeRoundChartQuantityAxis = DeRoundChartQuantityAxis;

  created() {
    const numberOfRounds = 20;
    const basePrice = 100;
    const priceIncrement = 1;
    const priceDecrement = 0.25;
    const firstDecrementRound = 18;
    const rounds = createMultipleByClosure(createDemo__DeRoundElement, numberOfRounds, true);
    rounds.forEach(round => {
      const isDecrementRound = round.round_number >= firstDecrementRound;
      const increment = isDecrementRound ? priceIncrement * (firstDecrementRound) : priceIncrement * round.round_number;
      const decrement = (isDecrementRound ? 1 : 0) * (round.round_number - firstDecrementRound) * priceDecrement;
      const price = basePrice + increment - decrement;
      round.round_price = price;
      round.round_price_str = price.toString();
      round.buy_quantity = round.round_number * 2.7;
      round.sell_quantity = 100 - round.round_number * 2.7;
    });
    this.rounds = rounds;
  }
}
</script>
