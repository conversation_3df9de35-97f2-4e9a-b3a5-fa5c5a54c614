<template>
  <div class="DeOrderEntry">
    <div class="au-label" style="text-align: center">
      New Order
    </div>

    <div :class="[round_is_open ? 'flash' : '']" style="height: 58px; padding-top: 5px; border-radius: 7px;">

      <div style="display: inline-block; position: relative; top: -2px; margin-left: 6px">
        <div>
          <NumberInput
            v-test:order_quantity
            class="DeOrderEntry__input"
            v-model="bid_quantity"
            :font_size="16"
          />
        </div>
        <div class="au-label" style="text-align: center; top: 3px; position:relative;">
          {{ de_trader_info_value.quantity_label }}
        </div>
      </div>

      <div style="margin-top: 5px; display: inline-block">
        <div>
          <AButton
            v-test:buy_order
            class="DeOrderEntry__button"
            type="primary"
            @click="buy()"
          >
            Buy
          </AButton>
        </div>
        <div>
          <AButton
            v-test:sell_order
            class="DeOrderEntry__button"
            type="primary"
            @click="sell()"
          >
            Sell
          </AButton>
        </div>
      </div>
    </div>
    <DeOrderConfirmModal
      v-if="showConfirmBidModal"
      @close="showConfirmBidModal = false"
      :order_quantity="order_quantity"
      :order_type="order_type"
      :de_trader_info_value="de_trader_info_value"
      :price_direction="price_direction"
      :price_has_reversed="price_has_reversed"
      @confirm="on_confirm()"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import AuSectionBorder from '../../../../../ui-components/AuSectionBorder.vue';
import {Container} from 'typescript-ioc';
import {
  de_order_submit_command,
  DeBidConstraints,
  DeCommonState,
  DeCommonStatusValue,
  DeTraderInfoValue,
  OrderType,
  PriceDirection,
  SocketConnector
} from '@au21-frontend/client-connector';
import AuInput from '../../../../../ui-components/AuInput/AuInput.vue';
import DeOrderConfirmModal from './order-confirm/DeOrderConfirmModal.vue';
import NumberInput from '../../../../../ui-components/NumberInput/NumberInput.vue';

@Component({
  components: {
    NumberInput,
    DeOrderConfirmModal,
    AuInput,
    AuSectionBorder
  }
})
export default class DeOrderEntry extends Vue {
  @Prop({required: true}) de_trader_info_value: DeTraderInfoValue;
  @Prop({required: true}) common_status: DeCommonStatusValue;
  @Prop({required: true}) price_direction:PriceDirection;
  @Prop({required: true}) price_has_reversed:boolean;

  /**
   * TODO: the DeTraderBidConfirmModal will require an number
   * so: we need to parse the bid quantity somewhere,
   * probably before we get to the modal
   */
  showConfirmBidModal = false;

  bid_quantity = '';
  order_type = OrderType.BUY;

  connector = Container.get(SocketConnector);

  get round_is_open(): Boolean {
    return this.common_status.common_state == DeCommonState.ROUND_OPEN;
  }

  @Watch('de_trader_info_value.round_number')
  onRoundChange() {
    this.showConfirmBidModal = false;
  }

  get order_quantity(): number {
    return parseInt(this.bid_quantity.replace(',', ''));
  }

  round() {
    return this.de_trader_info_value.round_number;
  }

  get constraints(): DeBidConstraints {
    return this.de_trader_info_value.bid_constraints;
  }

  /**
   * now that we're showing constraints BEFORE we submit, we need to do basic validation
   */
  validate(): boolean {

    const constraint_alert = (buy_sell: string, more_less: string, max_min: string, vol: number) =>
      this.notify(`You cannot submit a ${buy_sell} order for ${more_less} than your ${max_min} ${buy_sell} quantity of ${vol} ${this.de_trader_info_value.quantity_label}`);

    // quantity is a valid number:
    if (this.bid_quantity == '') {
      this.notify('no quantity entered');
      return false;
    } else if (isNaN(this.order_quantity)) {
      this.notify(this.bid_quantity + ' is not a valid number');
      return false;
    }

    // max / min quantity constraints:
    if (this.order_type == OrderType.BUY) {
      if (this.order_quantity > this.constraints.max_buy_quantity) {
        constraint_alert('buy', 'more', 'maximum', this.constraints.max_buy_quantity);
        return false;
      } else if (this.order_quantity < this.constraints.min_buy_quantity) {
        constraint_alert('buy', 'less', 'minimum', this.constraints.min_buy_quantity);
        return false;
      }
    } else if (this.order_type == OrderType.SELL) {
      if (this.order_quantity > this.constraints.max_sell_quantity) {
        constraint_alert('sell', 'more', 'maximum', this.constraints.max_sell_quantity);
        return false;
      } else if (this.order_quantity < this.constraints.min_sell_quantity) {
        constraint_alert('sell', 'less', 'minimum', this.constraints.min_sell_quantity);
        return false;
      }
    } else {
      this.notify('Order can only be Buy or Sell, not: ' + this.order_type);
      return false;
    }

    return true;
  }

  notify(message: string) {
    this.$notification.open({
      message: '',
      description: message,
      duration: 5,
      placement: 'topRight'
    });
  }

  buy() {
    this.order_type = OrderType.BUY;
    this.showConfirmBidModal = this.validate();
  }

  sell() {
    this.order_type = OrderType.SELL;
    this.showConfirmBidModal = this.validate();
  }

  on_confirm() {
    const quantity = this.order_quantity;
    if (!quantity) {
      alert('order quantity is not a valid number');
      return;
    }
    this.submit_order();
    this.showConfirmBidModal = false;
    this.bid_quantity = '';
  }

  submit_order() {
    this.connector.publish(de_order_submit_command({
      auction_id: this.de_trader_info_value.auction_id,
      company_id: this.de_trader_info_value.company_id,
      round: this.de_trader_info_value.round_number?.toString(),
      order_type: this.order_type,
      quantity: this.bid_quantity
    }));
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.DeOrderEntry {
  //border: 1px solid blue;
  overflow: hidden;
  position: relative;
  left: -5px;
  width: 154px;

  &__button {
    height: 20px !important;
    margin: 2px !important;
    top: -3px;

    /deep/ span {
      position: relative;
      top: -2px;
    }
  }

  &__input {
    color: black;
    margin: 0 2px !important;
    top: 1px;
    width: 77px !important;
    text-align: right;
    font-weight: bold;
  }

  &__heading {
    font-size: 11px;
    font-weight: bold;
    margin: 3px 6px 6px 6px;
    position: relative;
    text-align: center;
    top: 4px;
  }

}


@keyframes bgFlash {
  from {
//    background-color: hsl(0%, 90%, 30%);
    background-color: hsl(20, 25%, 35%);
  }
  to {
    background-color: transparent; // #111; // #333;
  }
}

/* The element to apply the animation to */
.flash {
  animation-name: bgFlash;
  animation-duration: 0.66s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-in-out;//ease-out;
}

</style>
