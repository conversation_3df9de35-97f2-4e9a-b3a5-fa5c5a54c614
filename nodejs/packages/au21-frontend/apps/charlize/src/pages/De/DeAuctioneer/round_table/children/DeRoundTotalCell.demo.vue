<template>
  <VbDemo>
    <VbCard title="default">
      <DeRoundTotalCell
        :height="60"
        :width="60"
        :round="round"
        :maxValue="100"
      />
    </VbCard>
    <VbCard title="set values">
      <DeRoundTotalCell
        :height="60"
        :width="60"
        :round="round"
        :maxValue="maxValue"
      />
      <br>
      buy_quantity: <input type="number" v-model="buy_quantity.buy_quantity">
      <br>
      sell_quantity: <input type="number" v-model="sell_quantity.sell_quantity">
      <br>
      matched: <input type="number" v-model="matched">
      <br>
      <br>
      Max value: <input type="number" v-model="maxValue">
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {createDemo__DeRoundElement} from '../../../../../demo-helpers/DeRoundTable.helper';
import DeRoundTotalCell from "./DeRoundTotalCell.vue";

@Component({
  components: {
    DeRoundTotalCell,
  },
})
export default class DeRoundTotalChartDemo extends Vue {
  round = createDemo__DeRoundElement()
  buy_quantity = 50
  matched = 20
  sell_quantity = 30
  maxValue = 50
}
</script>

<style lang="less" scoped>

</style>
