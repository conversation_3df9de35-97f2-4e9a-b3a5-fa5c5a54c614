<template>
  <VbDemo>
    <VbCard title="color">
      <DeClock
        :value="10"
        :warningSecs="warningSecs"
        :dangerSecs="dangerSecs"
      />
      <DeClock
        :value="20"
        :warningSecs="warningSecs"
        :dangerSecs="dangerSecs"
      />
      <DeClock
        :value="35"
        :warningSecs="warningSecs"
        :dangerSecs="dangerSecs"
      />
    </VbCard>
    <VbCard title="play with slider">
      <DeClock
        :value="value"
        :warningSecs="warningSecs"
        :dangerSecs="dangerSecs"
      />
      <br>
      <ASlider
        v-model="value"
        :min="0"
        :max="60"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeClock from './DeClock.vue';

@Component({
  components: {DeClock},
})
export default class DeClockDemo extends Vue {
  value = 10
  warningSecs = 15
  dangerSecs = 30
}
</script>
