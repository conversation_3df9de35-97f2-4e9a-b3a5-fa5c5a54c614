<template>
  <div class="DeOrderConfirm">
    <div style="text-align: center">
      <div style="display:inline-block; font-size: 16px; font-weight: bold">Your quantity constraints</div>
    </div>
    <div style="height: 5px">&nbsp;</div>

    <table class="_table">

      <tr>
        <td>
          <div class="bar-title">
            This round:
          </div>
        </td>
        <td class="spacer">
        </td>
        <td>
          <DeOrderConstraintsWithRange
            :order_quantity='order_quantity'
            :order_type="order_type"
            :constraints="current_constraints"
            :quantity_label='de_trader_info_value.quantity_label'
          />
        </td>
      </tr>

      <tr>
        <td>
          <div class="bar-title">
            Next round if price increases:
          </div>
        </td>
        <td class="spacer">
        </td>
        <td>
          <DeOrderConstraintsWithRange
            :order_quantity='order_quantity'
            :order_type="order_type"
            :constraints="price_increase_constraints"
            :quantity_label='de_trader_info_value.quantity_label'
          />
        </td>
      </tr>


      <tr>
        <td>
          <div class="bar-title">
            Next round if price decreases:
          </div>
        </td>
        <td class="spacer">
        </td>
        <td>
          <DeOrderConstraintsWithRange
            :order_quantity='order_quantity'
            :order_type="order_type"
            :constraints="price_decrease_constraints"
            :quantity_label='de_trader_info_value.quantity_label'
          />
        </td>
      </tr>

    </table>

    <div style="font-size: 15px; width: 500px; margin: 10px 35px">
      Check your order carefully before submitting because
      orders are final when the round ends, and the round ends when all bids are in.
    </div>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeBidConstraints, DeTraderInfoValue, OrderType, PriceDirection} from '@au21-frontend/client-connector';
import DeOrderConstraintsWithRange from '../../../constraints/DeOrderConstraintsWithRange.vue';
import {calculate_constraints} from '../../../../../../helpers/de-constraint-calculator';
import {AuColors} from '../../../../../../au-styles/AuColors';
import {Container} from 'typescript-ioc';

@Component({
  components: {DeOrderConstraintsWithRange}
})
export default class DeOrderConfirm extends Vue {
  @Prop({required: true}) order_quantity: number;
  @Prop({required: true}) order_type: OrderType;
  @Prop({required: true}) de_trader_info_value: DeTraderInfoValue;

  colors = Container.get(AuColors);

  get current_constraints(): DeBidConstraints {
    return this.de_trader_info_value.bid_constraints;
  }

  get price_increase_constraints(): DeBidConstraints {
    return calculate_constraints({
      prev_constraints: this.current_constraints,
      order_type: this.order_type,
      order_quantity: this.order_quantity,
      next_round_direction: PriceDirection.UP
    });
  }

  get price_decrease_constraints(): DeBidConstraints {
    return calculate_constraints({
      prev_constraints: this.current_constraints,
      order_type: this.order_type,
      order_quantity: this.order_quantity,
      next_round_direction: PriceDirection.DOWN
    });
  }

}
</script>
<style lang="less" scoped>
@import (reference) "../../../../../../au-styles/variables.less";

.DeOrderConfirm {

  background-color: @au-background;
  color: hsl(0, 0%, 70%);
  font-size: 14px;
  padding: 4px 14px;
  text-align: center;
  white-space: normal;
  width: 100%;

  .title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    width: 100%;
  }

  ._table {

    margin: 10px 30px 10px 30px;

    tr {
      border: 1px solid hsl(0, 0%, 50%);
    }

  }

  .bar-title {
    font-size: 15px;
    position: relative;
    text-align: right;
    top: 20px;
    width: 230px;
  }

  .spacer {
    width: 20px;
  }
}
</style>
