<template>
  <div class="DeRoundTotalCell">
    <!--    <Blinker :value="totals" background style="position:absolute; left: 0;">-->
    <!--    </Blinker>-->

    <div class="_chart_container">
      <div class="_chart" :style="{
             'background-color': buy_color,
             top: this.buy_top + 'px',
             height: this.buy_height +'px'
          }"
      />
      <div class="_chart" :style="{
             'background-color': sell_color,
             height: chart_height + 'px',
             top: this.sell_top + 'px',
             height: this.sell_height +'px'
          }"
      />
      <div class="_chart" :style="{
             'background-color': match_color,
             height: chart_height + 'px',
             top: this.match_top + 'px',
             height: this.match_height +'px'
          }"
      />
    </div>

    <div style="position: absolute; top: 40px">

      <div class="_column">
        <div class="_small_text" :style="{color: buy_sell_top_row_color}">
          {{ buy_sell_top_row_quantity }}
        </div>
        <div :style="{color: buy_sell_bottom_row_color}">
          {{ buy_sell_bottom_row_quantity }}
        </div>
        <div :style="{color: colors.au_excess()}">
          {{ round.excess_quantity }}
        </div>
      </div>

      <div class="_column">
        <div class="_small_text" :style="{color: excess_color}">
          {{ round.excess_indicator }}
        </div>
        <div :style="{color: this.colors.au_match()}">
          {{ round.matched }}
        </div>
        <div>
          &nbsp;&nbsp;
        </div>
      </div>

    </div>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeRoundElement, OrderType} from '@au21-frontend/client-connector';
import Blinker from '../../../../../ui-components/Blinker/Blinker.vue';
import {AuColors} from '../../../../../au-styles/AuColors';
import {Container} from 'typescript-ioc';


@Component({
  components: {Blinker}
})
export default class DeRoundTotalCell extends Vue {
  @Prop({required: true}) round: DeRoundElement | null;
  // @Prop({required: true}) height: number
  // @Prop({required: true}) width: number
  // Max for all three values.
  @Prop({required: true}) maxValue: number;

  chart_height = 40;
  colors = Container.get(AuColors);

  get pixels_per_vol(): number {
    return this.chart_height / this.maxValue;
  }

  // BUY:

  get buy_color(): string {
    return this.colors.au_buy_dimmed();
  }

  get buy_height(): number {
    return this.pixels_per_vol * this.round.buy_quantity;
  }

  get buy_top(): number {
    return this.chart_height - this.buy_height;
  }

  // SELL:

  get sell_color(): string {
    return this.colors.au_sell_dim();
  }

  get sell_height(): number {
    return this.pixels_per_vol * this.round.sell_quantity;
  }

  get sell_top(): number {
    return this.chart_height - this.sell_height;
  }

  // rows:

  get buy_sell_top_row_color(): string {
    return this.colors.order_quantity_text_color(this.round.excess_side)
  }

  get buy_sell_bottom_row_color(): string {
    return this.colors.order_quantity_opposite_text_color(this.round.excess_side)
  }


  get buy_sell_top_row_quantity(): number {
    return this.round.excess_side === OrderType.BUY ?
      this.round.buy_quantity :
      this.round.sell_quantity;
  }

  get buy_sell_bottom_row_quantity(): number {
    return this.round.excess_side === OrderType.SELL ?
      this.round.buy_quantity :
      this.round.sell_quantity;
  }

  // MATCH:

  get match_color(): string {
    return 'hsl(163, 19%, 28%)'; // chroma(this.colors.au_match()).alpha(0.6).hex()
  }

  get match_height(): number {
    return this.pixels_per_vol * this.round.matched;
  }

  get match_top(): number {
    return this.chart_height - this.match_height;
  }

  // EXCESS

  get excess_color(): string {
    return this.colors.order_quantity_text_color(this.round.excess_side)
  }

  // get totals() {
  //   const buy: number = +this.round?.buy_quantity || 0
  //   const sell: number = +this.round?.sell_quantity || 0
  //   const no_vol: boolean = buy + sell == 0
  //   const buy_greater: boolean = buy > sell
  //   const sell_greater: boolean = sell > buy
  //
  //   const buy_ratio: number = sell * buy == 0 || sell_greater ? 0 : buy / sell
  //   const sell_ratio: number = sell * buy == 0 || buy_greater || sell ? 0 : sell / buy
  //
  //   return {
  //     buy,
  //     sell,
  //     no_vol,
  //     buy_greater,
  //     sell_greater,
  //     buy_ratio: buy_ratio > 0 ? buy_ratio.toFixed(1) + 'x' : '---',
  //     sell_ratio: sell_ratio > 0 ? sell_ratio.toFixed(1) + 'x' : '---',
  //     buy_height: sell == 0 && buy > 0 ? this.half_height : buy_ratio * this.half_height,
  //     sell_height: buy == 0 && sell > 0 ? this.half_height : sell_ratio * this.half_height
  //   }
  // }

  // getRectangleHeight(column: Column): number {
  //   if (this.maxValue === 0) {
  //     return 0
  //   }
  //   return (column.value / this.maxValue) * (this.height - this.VALUE_HEIGHT)
  // }

}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.DeRoundTotalCell {
  margin: 0;
  padding: 0;
  font-size: 13px;
  line-height: 1.2em;

  ._chart_container {
    position: absolute;
    top: 0;

    ._chart {
      position: absolute;
      top: 0;
      width: 70px;
    }

  }

  ._column {
    //border: 1px solid blue;
    display: inline-block;
    font-weight: bold;
    padding-right: 5px;
    text-align: right;
    width: 35px;
  }

  //._excess {
  //  height: 12px;
  //  position: absolute;
  //  top: 43px;
  //}

}
</style>
