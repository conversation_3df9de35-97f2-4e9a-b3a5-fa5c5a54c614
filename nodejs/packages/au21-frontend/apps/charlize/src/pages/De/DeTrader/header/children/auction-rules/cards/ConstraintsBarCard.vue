<template>
  <div class="BuySellSwitchingCard pl-3"
       @click="click">
    <div style="height: 40px; width: 400px; background-color: white">
    </div>
    <br>
    <ul>
      <VueTextTransition tag="li" name="fade" :show="count > 0" :interval="1">
        The Price Quantity Constraints and Buy Sell Switching Constraints
        are presented in the form of a Constraints Bar.
      </VueTextTransition>
      <VueTextTransition tag="li" name="fade" :show="count > 1" :interval="1">
        Buy Qty is presented from the center to the left.
      </VueTextTransition>
      <VueTextTransition tag="li" name="fade" :show="count > 2" :interval="1">
        Sell Qty is presented from the center to the right.
      </VueTextTransition>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import VueTextTransition from 'vue-text-transition'

Vue.component('VueTextTransition', VueTextTransition)


@Component({
  name: 'ConstraintsBarCard',
})
export default class ConstraintsBarCard extends Vue {
  count = 1

  click() {
    this.count++
    console.log({count: this.count})
  }
}
</script>

<style lang="less">
.ConstraintsBarCard {

}

.v--vtt-fade {
  will-change: opacity;
  transition: opacity 0.1s ease-in-out;
}

.v--vtt-fade_visible {
  opacity: 1;
}

.v--vtt-fade_hidden {
  opacity: 0.38;
}
</style>
