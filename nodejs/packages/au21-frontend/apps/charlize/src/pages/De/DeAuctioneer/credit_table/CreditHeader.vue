<template>
  <div style="text-align:center; width: 100%">
    <div class="mb-05">{{ company.company_shortname }}</div>
    <a-button
      v-if="editable"
      type="primary"
      size="small"
      @click="show_credit_window()"
    >
      edit
    </a-button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {CompanyElement} from '@au21-frontend/client-connector';
import type CreditTable from './CreditTable.vue';

@Component({components: {}})
export default class CreditTableHeader extends Vue {
  params = null

  get company(): CompanyElement {
    return this.params.column.colDef.cellRendererParams.company
  }

  get editable(): boolean {
    return this.params.column.colDef.cellRendererParams.editable
  }

  show_credit_window() {
    ;(this.$parent.$parent.$parent as CreditTable).onCompanySelect(this.company)
  }
}
</script>


<style lang="less" scoped>
.CreditTableHeader {

}
</style>
