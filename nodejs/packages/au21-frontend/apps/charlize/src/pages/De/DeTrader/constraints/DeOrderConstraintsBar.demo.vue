<template>
  <VbDemo>
    <VbCard dark style="width: 300px; height: 100px">
      <DeOrderConstraintsBar
        :width="width"
        :height="height"
        :constraints="constraints"
        :tick_font_size="tick_font_size"
        :order_type="order_type"
        :order_quantity='order_quantity'
        :show_labels="show_labels"
      />
    </VbCard>

    <VbCard>

      <div class="block">
        <div class="label">width:</div>
        <input class="control" type="text" v-model="width">
        <a-slider class="slider" v-model="width" :min="60" :max="280"/>
      </div>

      <div class="block">
        <div class="label">height:</div>
        <input class="control" type="text" v-model="height">
        <a-slider class="slider" v-model="height" :min="5" :max="50"/>
      </div>

      <div class="block">
        <div class="label">max quantity:</div>
        <input class="control" type="text" v-model="max_quantity">
        <a-slider class="slider" v-model="max_quantity" :min="20" :max="100"/>
      </div>

      <div class="block">
        <div class="label">tick quantity:</div>
        <input class="control" type="text" v-model="tick_quantity">
        <a-slider class="slider" v-model="tick_quantity" :min="1" :max="50"/>
      </div>

      <div class="block">
        <div class="label">max buy:</div>
        <input class="control" type="text" v-model="constraints.max_buy_quantity">
        <a-slider class="slider" v-model="constraints.max_buy_quantity" :min="0" :max="max_quantity"/>
      </div>

      <div class="block">
        <div class="label">min buy:</div>
        <input class="control" type="text" v-model="constraints.min_buy_quantity">
        <a-slider class="slider" v-model="constraints.min_buy_quantity" :min="0" :max="max_quantity"/>
      </div>

      <div class="block">
        <div class="label">max sell:</div>
        <input class="control" type="text" v-model="constraints.max_sell_quantity">
        <a-slider class="slider" v-model="constraints.max_sell_quantity" :min="0" :max="max_quantity"/>
      </div>

      <div class="block">
        <div class="label">min sell:</div>
        <input class="control" type="text" v-model="constraints.min_sell_quantity">
        <a-slider class="slider" v-model="constraints.min_sell_quantity" :min="0" :max="max_quantity"/>
      </div>

      <div class="block">
        <div class="label">tick font size:</div>
        <input class="control" type="text" v-model="tick_font_size">
        <a-slider class="slider" v-model="tick_font_size" :min="0" :max="15"/>
      </div>

      <div class="block">
        <div class="label">show labels:</div>
        <input class="control" type="checkbox" v-model="show_labels">
      </div>

      <div class="block" style="top:20px">
        <div class="label">order side:</div>
        <AButton style="width: 50px" class="control" size="small" @click="order_type = OrderType.NONE">
          None
        </AButton>
        <AButton style="width: 50px" class="control" size="small" @click="order_type = OrderType.BUY">
          Buy
        </AButton>
        <AButton style="width: 50px" class="control" size="small" @click="order_type = OrderType.SELL">
          Sell
        </AButton>
      </div>

      <div class="block">
        <div class="label">order quantity:</div>
        <input class="control" type="text" v-model="order_quantity">
        <a-slider class="slider" v-model="order_quantity" :min="0" :max="max_quantity"/>
      </div>

    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeOrderConstraintsBar from './DeOrderConstraintsBar.vue';
import {DeBidConstraints, OrderType} from '@au21-frontend/client-connector';

@Component({
  components: {DeOrderConstraintsBar}
})
export default class DeOrderConstraintsBarDemo extends Vue {
  width = 200;
  height = 18;
  max_quantity = 50;
  tick_quantity = 10;
  tick_font_size = 8;
  order_type: OrderType = OrderType.BUY;
  order_quantity = 10;
  show_labels = true

  OrderType = OrderType;

  constraints = {
    max_buy_quantity: 40,
    max_sell_quantity: 20,
    min_buy_quantity: 30,
    min_sell_quantity: 15
  } as DeBidConstraints;

}
</script>
<style lang="less" scoped>
.block {
  height: 30px;
  position: relative;
  width: 300px;
}

.label {
  display: inline-block;
  margin-right: 5px;
  position: relative;
  text-align: right;
  top: -15px;
  width: 120px;
}

.control {
  display: inline-block;
  position: relative;
  top: -15px;
  width: 30px;
}

.slider {
  display: inline-block;
  top: -5px;
  width: 100px;
}
</style>
