import {DeMatrixEdgeElement, DeMatrixRoundElement, OrderType} from "@au21-frontend/client-connector";
import {matched_nodes, sort_by_shortname} from "../../../../../services/helpers/de-helpers";

export interface DeAwardRowModel {
  id: string,
  is_summary_row: boolean,
  trader_name: string,
  side: OrderType,
  side_str: string,
  counterparty_name: string,
  value: string,
  quantity: string,
  credit_limit: string
}

export class DeTraderCounterparties {
  readonly id: string

  constructor(
    public trader_name: string,
    public side: OrderType,
    public total_quantity_str: string,
    public total_cost_str: string,
    public counterparties: DeCounterparty[],
  ) {
    if (counterparties.length === 0) {
      throw new Error("counterparties cannot be empty if there were matches!")
    }
    this.id = `${trader_name}.${counterparties[0].name}`
  }
}

export interface DeCounterparty {
  name: string,
  value: number,
  value_str: string,
  quantity: string,
  credit_limit: string
}


export function matrix_to_trader_counterparties(matrix: DeMatrixRoundElement): DeTraderCounterparties[] {

  if (matrix === null || matrix.nodes === null || matrix.edges === null)
    return []

  const matched_edges: DeMatrixEdgeElement[] =
    matrix.edges.filter(m => m.match > 0)

  return sort_by_shortname(matched_nodes(matrix.nodes))

    .map(node => {

      const side: OrderType =
        node.buy_match > 0 ? OrderType.BUY
          : node.sell_match > 0 ? OrderType.SELL
            : OrderType.NONE

      return new DeTraderCounterparties(
        node.shortname,
        side,
        Math.max(node.buy_match, node.sell_match) + '',
        node.cost_str,
        matched_edges
          .filter(edge => {
            switch (side) {
              case OrderType.BUY:
                return edge.buyer_shortname === node.shortname
              case OrderType.SELL:
                return edge.seller_shortname === node.shortname
              default:
                return false
            }
          })
          .map(e => ({
            name: ({
              [OrderType.BUY]: e.seller_shortname,
              [OrderType.SELL]: e.buyer_shortname,
            })[side],
            value: e.value,
            value_str: e.value_str,
            quantity: '' + e.match,
            credit_limit: e.credit_str
          })),
      )
    })
}

export function counterparties_to_rows(trader_counterparties: Array<DeTraderCounterparties>): DeAwardRowModel[] {

  const to_side_total_str = (side: OrderType): string => {
    switch (side) {
      case OrderType.BUY:
        return 'bought from'
      case OrderType.SELL:
        return 'sold to'
      default:
        return ''
    }
  }

  const to_side_str = (side: OrderType): string => {
    switch (side) {
      case OrderType.BUY:
        return 'bought'
      case OrderType.SELL:
        return 'sold'
      default:
        return ''
    }
  }

  const to_counterparty_side_str = (side: OrderType): string => {
    switch (side) {
      case OrderType.BUY:
        return 'from' // 'seller: '
      case OrderType.SELL:
        return 'to' // 'buyer: '
      default:
        return ''
    }
  }

  const rows: DeAwardRowModel[] = []

  // could flatten this:
  trader_counterparties.forEach((t: DeTraderCounterparties) => {

      let count = 0

      if (t.counterparties.length === 1) {
        const counterparty: DeCounterparty = t.counterparties[0]
        rows.push({
          id: t.id + '.' + (++count),
          is_summary_row: true,
          trader_name: t.trader_name,
          side: t.side,
          side_str: to_side_str(t.side), //to_side_total_str(t.side),
          counterparty_name: counterparty.name,
          value: counterparty.value_str,
          quantity: t.total_quantity_str,
          credit_limit: counterparty.credit_limit
        })

      } else if (t.counterparties.length > 1) {
        t.counterparties.forEach((c: DeCounterparty, index: number) => {
            if (index === 0) {
              rows.push({
                id: t.trader_name + '.TOTAL.' + (++count),
                is_summary_row: true,
                trader_name: t.trader_name,
                side: t.side,
                side_str: to_side_str(t.side),
                counterparty_name: '',
                value: t.total_cost_str,
                quantity: t.total_quantity_str, // this total should come from backend
                credit_limit: ''
              })
            }
            rows.push({
              id: t.id + '.' + (++count),
              is_summary_row: false,
              trader_name: '',
              side: t.side,
              side_str: to_side_str(t.side), // to_counterparty_side_str(t.side), // to_side_str(t.side),
              counterparty_name: c.name,
              value: c.value_str,
              quantity: c.quantity,
              credit_limit: c.credit_limit
            })
          }
        )
      }
    }
  )

  return rows;
}
