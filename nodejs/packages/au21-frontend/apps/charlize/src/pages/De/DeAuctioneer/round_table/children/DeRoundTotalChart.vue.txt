<template>
  <Blinker :value="totals" background style="position:absolute; left: 0;">
    <svg :viewBox="`0 0 ${width} ${height}`"
         :width="width"
         :height="height" style="position:absolute; left: 0;">
      <!--      <g-->
      <!--        v-for="(column, index) in columns"-->
      <!--        :transform="`translate(${index * columnSize})`"-->
      <!--      >-->
      <!--        <text-->
      <!--          :x="columnSize / 2" :y="(height - getRectangleHeight(column)) - 2"-->
      <!--          text-anchor="middle" font-size="12" fill="white"-->
      <!--        >{{ column.text }}-->
      <!--        </text>-->
      <!--        <rect-->
      <!--          :y="height - getRectangleHeight(column)"-->
      <!--          :x="8"-->
      <!--          :width="columnSize / 2"-->
      <!--          :height="getRectangleHeight(column)"-->
      <!--          :class="column.class"-->
      <!--        />-->
      <!--      </g>-->


      <!--    SELL   -->
      <g>

        <text
          :y="21"
          :x="text_left"
          :fill="colors.au_sell()"
          text-anchor="middle"
        >{{ 5 }} + &nbsp; {{ totals.sell_ratio }}
        </text>

        <text
          :x="text_left"
          :y="45"
          :fill="colors.au_sell()"
          text-anchor="middle"
        >{{ totals.sell }}
        </text>

        <rect
          :y="50 - totals.sell"
          :x="-10"
          :width="width"
          :height="totals.sell_height"
          :fill="colors.au_sell_dimmedX2()"
        />
      </g>

      <!--    BUY   -->
      <g>

        <text
          :y="65"
          :x="text_left"
          :fill="colors.au_buy()"
          text-anchor="middle"
        >{{ totals.buy }}
        </text>

        <text
          :y="90"
          :x="text_left"
          :fill="colors.au_buy()"
          text-anchor="middle"
        >{{ totals.buy_ratio }} &nbsp; 5+
        </text>

        <rect
          :y="height / 2"
          :x="-10"
          :width="width"
          :height="totals.buy_height"
          :fill="colors.au_buy_dimmedX2()"
        />
      </g>

    </svg>
  </Blinker>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeRoundElement} from '@au21-frontend/client-connector';
import Blinker from '../../../../../ui-components/Blinker/Blinker.vue';
import {AuColors} from "../../../../../au-styles/AuColors";
import {Container} from "typescript-ioc";

type Column = {
  value: number
  text: number
  class: string
}

@Component({
  components: {Blinker},
})
export default class DeRoundTotalChart extends Vue {
  @Prop({required: true}) round: DeRoundElement | null
  // @Prop({required: true}) height: number
  // @Prop({required: true}) width: number
  // Max for all three values.
  @Prop({required: true}) maxValue: number

  height = 100
  half_height = 50
  width = 84
  text_left = 12

  colors = Container.get(AuColors)

  VALUE_HEIGHT = 16

  get totals() {
    const buy: number = +this.round?.buy_quantity || 0
    const sell: number = +this.round?.sell_quantity || 0
    const no_vol: boolean = buy + sell == 0
    const buy_greater: boolean = buy > sell
    const sell_greater: boolean = sell > buy

    const buy_ratio: number = sell * buy == 0 || sell_greater ? 0 : buy / sell
    const sell_ratio: number = sell * buy == 0 || buy_greater || sell ? 0 : sell / buy

    return {
      buy,
      sell,
      no_vol,
      buy_greater,
      sell_greater,
      buy_ratio: buy_ratio > 0 ? buy_ratio.toFixed(1) + 'x' : '---',
      sell_ratio: sell_ratio > 0 ? sell_ratio.toFixed(1) + 'x' : '---',
      buy_height: sell == 0 && buy > 0 ? this.half_height : buy_ratio * this.half_height,
      sell_height: buy == 0 && sell > 0 ? this.half_height : sell_ratio * this.half_height
    }
  }


// get columns(): Column[] {
//   return [
//     {
//       value: +this.round?.buy_quantity,
//       text: +this.round?.buy_quantity,
//       class: 'color-buy',
//     },
//     {
//       value: +this.round?.matched,
//       text: +this.round?.matched,
//       class: 'color-match',
//     },
//     {
//       value: +this.round?.sell_quantity,
//       text: +this.round?.sell_quantity,
//       class: 'color-sell',
//     },
//   ]
// }

  getRectangleHeight(column: Column): number {
    if (this.maxValue === 0) {
      return 0
    }
    return (column.value / this.maxValue) * (this.height - this.VALUE_HEIGHT)
  }

}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

</style>
