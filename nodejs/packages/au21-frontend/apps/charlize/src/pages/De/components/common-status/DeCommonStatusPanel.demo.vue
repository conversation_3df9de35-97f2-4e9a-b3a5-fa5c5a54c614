<template>
  <VbDemo>
    <VbCard>
      <DeCommonStatusPanel
        :common_state="commonStatus.common_state"
        :common_state_text="commonStatus.common_state_text"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createDemo__DeCommonStatusValue} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import {DeAuctioneerState} from '@au21-frontend/client-connector';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';
import DeCommonStatusPanel from './DeCommonStatusPanel.vue';

@Component({
  components: {AuSelect, DeCommonStatusPanel}
})
export default class DeCommonStatusPanelDemo extends Vue {
  commonStatus = createDemo__DeCommonStatusValue();
  settings = createDefault__DeSettingsValue()
  statusOptions = Object.values(DeAuctioneerState);


}
</script>
