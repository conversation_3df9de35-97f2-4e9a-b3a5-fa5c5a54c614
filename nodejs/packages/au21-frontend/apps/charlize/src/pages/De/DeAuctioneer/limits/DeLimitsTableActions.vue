<template>
  <div class="DeLimitsTableActions">
    <a-button
      class="au-btn"
      style="height: 24px"
      @click="allow"
    >
      edit
    </a-button>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';
import {DeLimitsTableActionsParams, DeLimitsTableRow} from './DeLimitsTable.types';

@Component({
  name: 'DeLimitsTableActions',
  components: {NumberInput},
})
export default class DeLimitsTableActions extends Vue {
  params = null

  get row(): DeLimitsTableRow {
    return this.params.data
  }

  get isAllowable(): boolean {
    return this.cellParams.isRowAllowable(this.row)
  }

  async allow() {
    this.cellParams.allow(this.row)
  }

  get cellParams(): DeLimitsTableActionsParams {
    return this.params.column.colDef.cellRendererParams;
  }
}
</script>

<style lang="less" scoped>
.DeLimitsTableActions {
  text-align: center;
}
</style>
