<template>
  <a-modal
    class="CompanyCreditModal"
    :title="`Buyer credit for: ${seller_longname} (${seller_shortname})`"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="570px"
  >
    <ClientCreditTable
      :rows="rows"
    />

    <a-button
      slot="footer"
      type="primary"
      @click="$emit('close')"
    >
      Close
    </a-button>

    <a-button
      slot="footer"
      @click="saveCompanyCredit()"
    >
      Save
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {de_credit_set_command, SocketConnector} from '@au21-frontend/client-connector';
import ClientCreditTable from './ClientCreditTable.vue';
import {ClientCreditTableRow} from './ClientCreditTable.types';
import {CharlizeClient} from '../../../../services/connector/CharlizeClient';
import {sortBy} from 'lodash';

@Component({
  name: 'CompanyCreditModal',
  components: {ClientCreditTable},
})
export default class CompanyCreditModal extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  @Prop({required: true}) seller_id: string
  @Prop({required: true}) seller_shortname: string
  @Prop({required: true}) seller_longname: string

  connector = Container.get(SocketConnector)
  charlizeClient = Container.get(CharlizeClient)

  rows: ClientCreditTableRow[] = []

  calculateRows(): void {
    let credits = this.store.live_store.counterparty_credits
    if (this.seller_id) {
      credits = credits.filter(credit => credit.seller_id === this.seller_id)
    }
    credits = sortBy(credits, 'buyer_shortname')

    this.rows = credits.map(credit => ({
      companyId: credit.buyer_id,
      companyName: credit.buyer_longname,
      currentCreditStr: credit.limit_str,
      newCreditStr: '',
    }))
  }

  created() {
    this.calculateRows()
    this.charlizeClient.subscribe('CommandSucceeded', this.cancelAuctionEdit)
  }

  beforeDestroy() {
    this.charlizeClient.unsubscribe('CommandSucceeded', this.cancelAuctionEdit)
  }

  cancelAuctionEdit(command): void {
    this.$emit('close')
  }

  saveCompanyCredit() {
    this.rows.forEach(row => {
      if (!row.newCreditStr) return

      this.connector.publish(de_credit_set_command({
        auction_id: '', // ie: not set from inside an auction.
        borrower_id: row.companyId,
        lender_id: this.seller_id,
        credit_limit: row.newCreditStr,
      }))
    })
  }
}
</script>

<style lang="less" scoped>
.CompanyCreditModal {
}
</style>
