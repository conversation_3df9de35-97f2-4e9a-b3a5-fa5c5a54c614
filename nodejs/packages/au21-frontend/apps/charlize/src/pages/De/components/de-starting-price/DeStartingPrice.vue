<template>

  <div>

    <!-- 1) Round Number -->

    <div class="output-box">
      <!--      :class="{-->
      <!--     // 'output-text': true,-->
      <!--      //'blinking' :true-->
      <!--    }"-->
      <!--      :color="{color}"-->
      <div v-if="starting_price_set" class="box-data-starting-price">999.999</div>
      <div v-else class="box-data-not-set">Not set</div>
      <div class="units-label">cents/lb</div>
    </div>

  </div>

</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import {DeAuctioneerState, DeAuctioneerStatusValue, DeCommonStatusValue} from '@au21-frontend/client-connector';
import ToolbarButton from '../../DeAuctioneer/toolbar/ToolbarButton.vue';
import {find} from 'lodash';

export type CLOCK_STATUS = 'NOT_STARTED' | 'RUNNING' | 'PAUSED' | 'ENDED' // TODO: add pause / running
export type LEVEL_STATUS = 'NOT_STARTED' | 'NORMAL' | 'WARNING' | 'ALERT'

@Component({
  components: {ToolbarButton, AuSectionBorder}
})
export default class DeStartingPrice extends Vue {

  @Prop({required: true}) commonStatus: DeCommonStatusValue;
  @Prop({required: true}) auctioneerStatus: DeAuctioneerStatusValue;

  //colors = Container.get(AuColors);

  get starting_price(): string {
    return this.auctioneerStatus?.starting_price || '';
  }

  // get price_color() {
  //   return this.colors.auPrice;
  // }

  get starting_price_set(): boolean {
    return find([
        DeAuctioneerState.STARTING_PRICE_SET,
        DeAuctioneerState.STARTING_PRICE_ANNOUNCED
      ], s => s == this.auctioneerStatus.auctioneer_state
    ) != null;
  }

  colors: { [e in LEVEL_STATUS]: string } = {
    NOT_STARTED: '#fff',
    NORMAL: 'rgb(65, 184, 131)',
    WARNING: 'orange',
    ALERT: 'red'
  };

  thresholds = {
    alert: 5,
    warning: 10
  };


  get color() {
    return this.colors[this.level_status];
  }

  get roundNumber(): number | null {
    return this.commonStatus?.round_number;
  }

  get roundSeconds(): number | null {
    return 1; // this.commonStatus?.initial_time
  }

  get remainingTime(): number | null {
    return 2; // this.commonStatus?.remaining_time
  }

  get level_status(): LEVEL_STATUS | null {
    return null;
    // if(this.commonStatus == null)
    //   return null
    //
    // const time = this.commonStatus.remaining_time
    // if ([
    //   DeAuctioneerState.ROUND_RUNNING,
    //   DeAuctioneerState.ROUND_PAUSED,
    // ].includes(this.commonStatus.auctioneer_state))
    //   return time <= this.thresholds.alert ? 'ALERT' :
    //     time <= this.thresholds.warning ? 'WARNING' : 'NORMAL'
    // else
    //   return 'NOT_STARTED'
  }
}

</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

//.blinking {
//  animation: pulse 2s infinite;
//}
//
//@keyframes pulse {
//  0% {
//    background-color: red;
//  }
//
//  70% {
//    background-color: blue;
//  }
//
//  100% {
//    background-color:yellow;
//  }
//}

.output-box {
  width: 80px;
}

.box-data-starting-price {
  font-size: 19px;
  position: relative;
  text-align: center;
}

.box-data-not-set {
  font-size: 18.5px;
  text-align: center;
}

.units-label {
  font-size: 9px;
  font-weight: bold;
  text-align: center;
}

.box-heading {
  border: 0;
  font-size: 11px;
  font-weight: bold;
  margin: 0;
  padding: 0;
  text-align: center;
}
</style>
