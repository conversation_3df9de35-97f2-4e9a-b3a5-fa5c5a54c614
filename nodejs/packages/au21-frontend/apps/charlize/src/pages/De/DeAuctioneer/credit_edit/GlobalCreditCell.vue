<template>
  <div class="GlobalCreditCell">
    <div
      v-if="row_name === 'buyer'"
      class="_buyer"
    >
      {{ row.company_name }}
    </div>
    <div
      v-if="row_name === 'current_max_buy'"
      class="_current-credit"
    >
      {{ row.current_max_buy ? '' + row.current_max_buy : '' }}
    </div>
    <CreditSelector
      v-if="row_name === 'new_max_buy'"
      :width="115"
      v-model="row.new_max_buy"
      :decimal-places="2"
    />
    <div
      v-if="row_name === 'current_max_sell'"
      class="_current-credit"
    >
      {{ row.current_max_sell ? '' + row.current_max_sell : '' }}
    </div>
    <CreditSelector
      v-if="row_name === 'new_max_sell'"
      :width="80"
      v-model="row.new_max_sell"
      :decimal-places="0"
    />
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';
import CreditSelector from "../../../common/components/CreditSelector/CreditSelector.vue";
import {GlobalCreditTableColumnParams, GlobalCreditTableRow,} from './GlobalCreditTable.types';

@Component({
  components: {CreditSelector, NumberInput},
})
export default class GlobalCreditCell extends Vue {
  params = null

  get row(): GlobalCreditTableRow | null {
    return this.params.data
  }

  get row_name() {
    return this.cellParams.name
  }

  get cellParams (): GlobalCreditTableColumnParams {
    return this.params.column.colDef.cellRendererParams
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.GlobalCreditCell {
  text-align: center;
  ._buyer {
    padding-left: 5px;
    padding-top: 6px
  }
  ._current-credit {
    padding-right: 5px;
    //padding-top: 6px;
    text-align: right;
  }
}
</style>
