<template>
  <AuAgGrid
    class="DeMatrix"
    :height="height"
    :width="width"
    :columnDefs="columnDefs"
    :rowData="trader_rows"
    :gridOptions="gridOptions"
    :getRowHeight="() => row_height"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import AuSectionHeader from '../../../../ui-components/AuSectionHeader.vue';
import {DeMatrixEdgeElement, DeMatrixNodeElement} from '@au21-frontend/client-connector';

import DeMatrixHeader from './DeMatrixHeader.vue';
import DeMatrixBodyCell from './DeMatrixBodyCell.vue';
import {TraderMatrixRow} from './DeMatrixRows';
import DeMatrixLeftCell, {DeMatrixLeftCellParams} from './DeMatrixLeftCell.vue';
import {AuColors} from '../../../../au-styles/AuColors';
import {Container} from 'typescript-ioc';
import DeMatrixDebugModal from './DeMatrixDebugModal.vue';
import {range} from 'lodash';
import {sort_by_shortname} from "../../../../services/helpers/de-helpers";


@Component({
  components: {AuSectionHeader, AuAgGrid, DeMatrixDebugModal}
})
export default class DeMatrix extends Vue {
  @Prop({required: true, type: Array}) nodes: DeMatrixNodeElement[];
  @Prop({required: true, type: Array}) edges: DeMatrixEdgeElement[];
  @Prop({type: Number, default: 404}) height: number;
  @Prop({type: String, default: null}) selectedCompanyId: string;

  row_height = 70;
  first_col_width = 60;
  body_col_width = 60;

  // get nodes(): DeMatrixNodeElement[] {
  //   return this.store.de_auction?.matrix_last_round?.nodes || []
  // }
  //
  // get edges(): DeMatrixEdgeElement[] {
  //   return this.store.de_auction?.matrix_last_round?.edges || []
  // }

  // colors = Container.get(AuColors);


  colors = Container.get(AuColors);
  width = 414;

  gridOptions: GridOptions = null

  beforeMount() {

    this.gridOptions = {
      headerHeight: 70,
      defaultColDef: {
        headerComponentFramework: DeMatrixHeader,
        headerComponentParams: {
          nodes: this.nodes,
          edges: this.edges
          // edgesSorted: this.edgesSorted
        },
        cellStyle: () => ({padding: '0', border: '0', 'border-right': '1px solid #666'})
      },
      suppressHorizontalScroll: false
    };

  }

  get empty_row_count(): number {
    // TODO: allow option for removal of empty buyer nodes: (and seller nodes)
    const body_height = this.height - 70 - 14; // ie: - header_height - scroll height
    const rows_height = this.nodes.length * this.row_height;
    const empty_height = body_height - rows_height;
    const empty_rows = empty_height > 0 ?
      (empty_height / this.row_height)
      : 0;
    // console.log({body_height, rows_height, empty_height, empty_rows});
    return empty_rows;
  }

  get empty_col_count(): number {
    // TODO: allow option for removal of empty buyer nodes: (and seller nodes)
    const body_width = this.width - this.first_col_width - 14; // slider
    const cols_width = this.bodyColumns.length * this.body_col_width;
    const empty_width = body_width - cols_width;
    const empty_cols = empty_width > 0 ?
      (empty_width / this.body_col_width)
      : 0;
    //  console.log({body_width, cols_width, empty_width, empty_cols});
    return empty_cols;
  }

  get bodyColumns() {
    return this.tradersSorted.map((trader: DeMatrixNodeElement) => ({
      headerName: trader.shortname,

      // We need 3 widths set for create_scenario_result_rows to stay consistent.
      width: this.body_col_width,
      minWidth: this.body_col_width,
      maxWidth: this.body_col_width,

      cellRendererFramework: DeMatrixBodyCell,
      cellRendererParams: {
        trader
      }
    }));
  }

  get columnDefs(): ColDef[] {

    const leftFixedColumn = {
      //headerName: 'Buyers vs Sellers',
      pinned: true,
      // We need 3 widths set for create_scenario_result_rows to stay consistent.
      width: this.first_col_width,
      minWidth: this.first_col_width,
      maxWidth: this.first_col_width,
      headerComponentFramework: DeMatrixDebugModal, // AuAgGridCenteredHeader,
      cellRendererFramework: DeMatrixLeftCell,
      cellRendererParams: {
        getBuyer: (edges: DeMatrixEdgeElement[]) => this.getBuyer(edges)
      } as DeMatrixLeftCellParams
    };

    return [
      leftFixedColumn,
      ...this.bodyColumns,
      ...range(this.empty_col_count).map(() => ({
        headerComponentFramework: null,
        width: this.body_col_width,
        minWidth: this.body_col_width,
        maxWidth: this.body_col_width
      }))
    ];
  }

  get tradersSorted(): DeMatrixNodeElement[] {
    return sort_by_shortname(this.nodes)
  }

  getTrader(company_id: string): DeMatrixNodeElement | null {
    return this.nodes.find(node => node.cid === company_id) || null;
  }

  onSellerClick(companyId: string) {
    this.$emit('update:selectedCompanyId', companyId);
  }

  get trader_rows(): TraderMatrixRow[] {

    // TODO: need an option to remove empty rows (and cols):
    const buyerToNodes: Record<string, DeMatrixEdgeElement[]> = {};
    this.edges.forEach(node => {
      const id = node.buyer_cid;
      if (!buyerToNodes[id]) {
        buyerToNodes[id] = [];
      }
      buyerToNodes[id].push(node);
    });

    // Sort by company name in 2 directions.
    for (const id in buyerToNodes) {
      // Sort rows
      buyerToNodes[id].sort((a, b) => this.getTrader(a.seller_cid).shortname > this.getTrader(b.seller_cid).shortname ? 1 : -1);
    }
    // sort columns
    const edgesSorted = Object.values(buyerToNodes)
      .sort((a, b) => this.getTrader(a[0].buyer_cid).shortname > this.getTrader(b[0].buyer_cid).shortname ? 1 : -1);

    const result: TraderMatrixRow[] = [];
    edgesSorted.forEach((edgeList, index) => {
      result.push({
        id: index,
        edges: edgeList
      });
    });

    range(this.empty_row_count).forEach(row_index => {
      result.push({
        // not: because ids are numbers, and there can't be duplicates, so we start at col length
        id: this.columnDefs.length + row_index,
        edges: range(4).map(col_index => ({
            id: `empty-cell.row.${row_index}.cell.${col_index}`
          } as DeMatrixEdgeElement)
        )
      } as TraderMatrixRow);
    });

    //debugger
    return result;
  }


  getBuyer(edges: DeMatrixEdgeElement[]): DeMatrixNodeElement | null {
    if (edges.length == 0) {
      return null;
    } else {
      const buyer_cid = edges[0].buyer_cid;
      return this.nodes.find(node => node.cid == buyer_cid);
    }
  }


}
</script>

<style lang="less" scoped>

@import (reference) '../../../../au-styles/variables.less';

.DeMatrix {

  .ag-cell-value {
    width: 100%;
  }

  .ag-header-cell {
    padding-left: 2px !important;
    padding-right: 2px !important;
  }


}
</style>
