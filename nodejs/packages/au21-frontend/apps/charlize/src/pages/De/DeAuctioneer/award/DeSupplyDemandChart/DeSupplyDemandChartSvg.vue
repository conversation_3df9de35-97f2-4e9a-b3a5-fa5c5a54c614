<template>
  <div class="DeSupplyDemandChartSvg"
       :style="{width: width + 'px', height: height + 'px'}">
    <svg
      :viewBox="`0 0 ${chartCoordinatesPx.xMax + MARGIN} ${height}`"
      :style="{
        height: '100%',
      }"
      fill="transparent"
      stroke-width="2"
      xmlns="http://www.w3.org/2000/svg"
    >
      <!-- Charts -->
      <path
        v-for="(chart, index) in compiledCharts"
        :key="index"
        :d="chart.coordinates"
        :stroke="chart.color"
      />
      <!--      <path :d="getFieldCoordinates('sell_quantity')" :stroke="colors.auSell" />-->

      <template v-if="!dense">
        <!-- Axes -->
        <path
          :d="`M ${chartCoordinatesPx.xMin} ${chartCoordinatesPx.yMax} L ${chartCoordinatesPx.xMin} ${chartCoordinatesPx.yMin}`"
          stroke="white"/>
        <path
          :d="`M ${chartCoordinatesPx.xMin} ${chartCoordinatesPx.yMax} L ${chartCoordinatesPx.xMax} ${chartCoordinatesPx.yMax}`"
          stroke="white"/>

        <!-- Ticks -->
        <path :d="getTicksCoordinates(xAxisTicks, 'x')" stroke="white"/>
        <path :d="getTicksCoordinates(yAxisTicks, 'y')" stroke="white"/>

        <!-- Tick labels -->
        <text
          v-for="tick in xAxisTicks.filter((n, i) => !(i % 5))"
          :x="tick.s"
          :y="chartCoordinatesPx.yMax + 16"
          style="fill: white; text-anchor: middle; font-size: 10px"
        >
          {{ tick.o }}
        </text>
        <text
          v-for="tick in yAxisTicks"
          :x="chartCoordinatesPx.xMin - 16"
          :y="tick.s"
          style="fill: white; alignment-baseline: middle; font-size: 10px"
        >
          {{ tick.o }}
        </text>

        <!-- Labels -->
        <text
          :x="(chartCoordinatesPx.xMax - chartCoordinatesPx.xMin)/2 + chartCoordinatesPx.xMin"
          :y="chartCoordinatesPx.yMax + 32" style="fill: white"
          text-anchor="middle">Price
        </text>
        <text
          :x="(chartCoordinatesPx.yMax - chartCoordinatesPx.yMin)/2 + chartCoordinatesPx.yMin"
          :y="chartCoordinatesPx.xMax - 64"
          style="fill: white; transform: rotate(-90deg); transform-origin: bottom left;">
          Quantity
        </text>
      </template>
    </svg>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeRoundElement} from '@au21-frontend/client-connector';
import {DeRoundChartQuantityAxis} from '../DeRoundChart/DeRoundChart-types';
import {sortBy} from 'lodash';
import {Container} from 'typescript-ioc';
import {rangeFromLimits} from '@au21-frontend/utils';
// TODO Use utils
import {AuColors} from '../../../../../au-styles/AuColors';

type QuantitySequenceField = 'buy_quantity' | 'sell_quantity';
const X_MULTIPLIER = 20;
const MARGIN = 10;
const LABEL_MARGIN = 30;

type Chart = {
  xAxis: number[],
  yAxis: number[],
  color: string,
}

const scaleOffset = (sequence: number[], scale, offset): number[] => {
  return sequence.map(item => item * scale + offset);
};

@Component({
  name: 'DeSupplyDemandChartSvg'
})
export default class DeSupplyDemandChartSvg extends Vue {
  @Prop({type: Array, required: true}) rounds: DeRoundElement[];
  @Prop({type: Number, required: true}) height: number;
  @Prop({type: Number, required: true}) width: number;
  @Prop({type: String, required: true}) quantity_axis: DeRoundChartQuantityAxis;
  @Prop({type: Boolean}) dense: boolean;

  colors = Container.get(AuColors);

  created() {
    // console.log('chartCoordinates', this.chartCoordinates);
    // console.log('chartCoordinatesPx', this.chartCoordinatesPx);
    // console.log('compiledCharts', this.compiledCharts);
  }

  // ************** Pure charts logic (knows nothing about domain) ************

  get charts(): Chart[] {
    return [
      {
        xAxis: this.priceList,
        yAxis: this.buyQuantityList,
        color: this.colors.au_buy()
      },
      {
        xAxis: this.priceList,
        yAxis: this.sellQuantityList,
        color: this.colors.au_sell()
      }
    ];
  }

  get chartCoordinates() {
    return {
      xMin: Math.min(...this.charts[0].xAxis, ...this.charts[1].xAxis),
      xMax: Math.max(...this.charts[0].xAxis, ...this.charts[1].xAxis),
      yMin: Math.min(...this.charts[0].yAxis, ...this.charts[1].yAxis),
      yMax: Math.max(...this.charts[0].yAxis, ...this.charts[1].yAxis)
    };
  }

  MARGIN = MARGIN;

  get chartCoordinatesPx() {
    const marginComputed = this.dense ? 1 : MARGIN
    const labelMarginComputed = this.dense ? 0 : LABEL_MARGIN

    return {
      xMin: marginComputed + labelMarginComputed,
      xMax: marginComputed + labelMarginComputed + (this.chartCoordinates.xMax - this.chartCoordinates.xMin) * X_MULTIPLIER,
      yMin: marginComputed,
      yMax: this.height - marginComputed - labelMarginComputed
    };
  }

  get compiledCharts(): (Chart & { coordinates: string, scaled: Chart })[] {
    return this.charts.map(chart => {
      const scaled = this.getScaledChart(chart);
      return {
        coordinates: this.getChartCoordinates(scaled),
        scaled,
        ...chart,
      };
    });
  }

  scaleToSize(sequence: number[], axis: 'x' | 'y'): number[] {
    if (axis === 'x') {
      const scaleX = (this.chartCoordinatesPx.xMax - this.chartCoordinatesPx.xMin) / (this.chartCoordinates.xMax - this.chartCoordinates.xMin);
      const offsetX = this.chartCoordinatesPx.xMin - this.chartCoordinates.xMin * scaleX;
      return scaleOffset(sequence, scaleX, offsetX);
    } else {
      const scaleY = (this.chartCoordinatesPx.yMax - this.chartCoordinatesPx.yMin) / (this.chartCoordinates.yMax - this.chartCoordinates.yMin);
      const offsetY = this.chartCoordinatesPx.yMin - this.chartCoordinates.yMin * scaleY;
      return scaleOffset(sequence, scaleY, offsetY);
    }
  }

  getScaledChart(chart: Chart): Chart {
    return {
      ...chart,
      xAxis: this.scaleToSize(chart.xAxis, 'x'),
      yAxis: this.scaleToSize(chart.yAxis, 'y'),
    };
  }

  getChartCoordinates(chart: Chart): string {
    const {xAxis, yAxis} = chart;

    // console.log('chart', chart);
    return yAxis.map((y, index) => {
      const x = xAxis[index];
      if (index === 0) {
        // Use first round to determine initial coordinate
        return `M ${x},${y}`;
      } else {
        return `L ${xAxis[index - 1]},${y} L ${x},${y}`;
      }
    }).join(' ');
  }

  // *************************** TICKS ***********************************

  get xAxisTicks() {
    // xAsisTicks are based on exact values.
    const xAxis = this.charts[0].xAxis;
    const xAxisScaled = this.compiledCharts[0].scaled.xAxis;

    // o - original, s - scaled
    const ticks: { o: number, s: number }[] = [];

    let previousTick = 0;
    xAxis.forEach((tick, index) => {
      if (previousTick >= tick) {
        return;
      }
      previousTick = tick;
      ticks.push({
        o: xAxis[index],
        s: xAxisScaled[index]
      });
    });
    // console.log('ticks', ticks);
    return ticks;
  }

  get yAxisTicks(): { o: number, s: number }[] {
    // While y axis ticks are dependent only on range.
    // So they behave a bit differently.
    const ticks = rangeFromLimits(this.chartCoordinates.yMin, this.chartCoordinates.yMax);
    const scaledTicks = this.scaleToSize(ticks, 'y').reverse(); // reverse works because step is consistent for y
    return ticks.map((_, i) => ({
      o: ticks[i],
      s: scaledTicks[i]
    })).reverse()
  }

  getTicksCoordinates(ticks: number[], axis: 'x' | 'y'): string {
    const TICK_SIZE = 6;
    if (axis === 'y') {
      // console.log('ticks', ticks);
    }
    const closure = axis === 'x'
      ? tick => `M ${tick.s} ${this.chartCoordinatesPx.yMax - TICK_SIZE / 2} L ${tick.s} ${this.chartCoordinatesPx.yMax + TICK_SIZE / 2}`
      : tick => `M ${this.chartCoordinatesPx.xMin - TICK_SIZE / 2} ${tick.s} L ${this.chartCoordinatesPx.xMin + TICK_SIZE / 2} ${tick.s}`
    return ticks.map(closure).join(' ');
  }

  // ************************** domain logic ***********************

  get sortedRounds() {
    return sortBy(this.rounds, 'price');
  }

  getQuantityList(field: QuantitySequenceField): number[] {
    return this.sortedRounds.map(round => Number(round[field])) // .replace(',', '' /* gives NaN if we don't remove commas */)));
  }

  get buyQuantityList(): number[] {
    return this.getQuantityList('buy_quantity');
  }

  get sellQuantityList(): number[] {
    return this.getQuantityList('sell_quantity');
  }

  get priceList(): number[] {
    return this.sortedRounds.map(round => round.round_price);
  }
}
</script>

<style lang="less" scoped>
.DeSupplyDemandChartSvg {
  position: relative;
  overflow-y: hidden;
  overflow-x: auto;
  font-size: 16px;
}
</style>
