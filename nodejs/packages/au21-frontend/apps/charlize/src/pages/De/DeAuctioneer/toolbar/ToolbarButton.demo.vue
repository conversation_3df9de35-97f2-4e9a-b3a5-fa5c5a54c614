<template>
  <VbDemo>
    <VbCard title="default">
      <ToolbarButton icon="fast-backward">
        Action
      </ToolbarButton>
    </VbCard>
    <VbCard title="disabled">
      <ToolbarButton icon="fast-backward" disabled>
        Action
      </ToolbarButton>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import ToolbarButton from './ToolbarButton.vue';

@Component({
  components: {ToolbarButton},
})
export default class ToolbarButtonDemo extends Vue {

}
</script>
