<template>
  <VbDemo>
    <VbCard style="width: 600px">
      deprecated
      <!--      <AwardScenarioTable-->
      <!--        :flowScenarioRows="flowScenarioRows"-->
      <!--        :companies="companies"-->
      <!--        :selectedCompanyId.sync="selectedCompanyId"-->
      <!--      />-->
      <!--      <pre>{{selectedCompanyId}}</pre>-->
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
// import AwardScenarioTable from './AwardScenarioTable.vue'
// import { createDemo__FlowScenarioRow } from '../../../../../demo-helpers/FlowScenarioRow.helper'
// import { createMultipleByClosure } from '@au21-frontend/utils'
// import { createDemo__CompanyElement } from '../../../../../demo-helpers/CompanyElement.helper'

@Component({
  // components: { AwardScenarioTable },
})
export default class AwardScenarioTableDemo extends Vue {
  // companies = createMultipleByClosure(createDemo__CompanyElement, 20)
  // flowScenarioRows = this.companies.map(company => createDemo__FlowScenarioRow(company.company_id))
  // selectedCompanyId = null
}
</script>
