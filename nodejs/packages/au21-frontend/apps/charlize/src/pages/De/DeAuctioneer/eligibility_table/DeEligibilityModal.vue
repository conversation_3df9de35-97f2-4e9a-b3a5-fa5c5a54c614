<template>
  <a-modal
    class="DeEligibilityModal"
    title="Trader Eligibility"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="725px"
  >
    <DeEligibilityTable
      :height="modalHeight"
      :round_trader_elements="round_trader_elements"
      :round_number="round_number"
      :readonly="readonly"
    />
    <a-button
      slot="footer"
      type="primary"
      class="au-btn"
      @click="$emit('close')"
    >
      Cancel
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import DeEligibilityTable from './DeEligibilityTable.vue';
import {CompanyElement, DeAuctionValue, DeRoundTraderElement} from '@au21-frontend/client-connector';
import {round_trader_elements_for_round} from '../../../../services/helpers/de-helpers';

@Component({
  components: {DeEligibilityTable}
})
export default class DeEligibilityModal extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  screen = new AuScreen(true);

  get modalHeight() {
    return this.screen.modal_height > 700 ? 700 : this.screen.modal_height;
  }

  get companies(): CompanyElement[] {
    return this.store.live_store.companies
  }

  get auction(): DeAuctionValue {
    return this.store.live_store.de_auction;
  }

  // get historyRows () {
  // //  console.log('this.store.live_store.de_auction', this.store.live_store.de_auction);
  // //  return this.store.live_store.de_auction.blotter.traders;
  // }

  get round_number(): number {
    return this.auction.common_status.round_number; // for now using last round
  }

  get round_trader_elements(): DeRoundTraderElement[] {
    return round_trader_elements_for_round(
      this.auction.blotter.round_traders,
      this.round_number);
  }

  get readonly() {
    return true;
    // return this.store.live_store.de_auction.trader_history_rows;
  }
}
</script>

<style lang="less" scoped>
.DeEligibilityModal {
}
</style>
