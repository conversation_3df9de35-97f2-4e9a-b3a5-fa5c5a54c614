<template>
  <VbDemo>
    <VbCard>
      <!--      <button @click="show = true">Buy</button>-->
      <!--      <button @click="show = true">Sell</button>-->
      <!--      <button @click="show = true">Buy-Buy</button>-->
      <!--      <button @click="show = true">Sell-Sell</button>-->
      <!--      <button @click="show = true">Buy-Sell</button>-->
      <DeOrderConfirm
        :order_quantity="order_quantity"
        :order_type="order_type"
        :de_trader_info_value="de_trader_info_value"
        :price_direction="PriceDirection.UP"
        :price_has_reversed="false"
      />
      <hr>
      <DeOrderConfirm
        :order_quantity="order_quantity"
        :order_type="order_type"
        :de_trader_info_value="de_trader_info_value"
        :price_direction="PriceDirection.UP"
        :price_has_reversed="true"
      />
      <hr>
      <DeOrderConfirm
        :order_quantity="order_quantity"
        :order_type="order_type"
        :de_trader_info_value="de_trader_info_value"
        :price_direction="PriceDirection.DOWN"
        :price_has_reversed="true"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {DeTraderInfoValue, OrderType, PriceDirection} from '@au21-frontend/client-connector';
import {createDemo__DeTraderInfoValue} from '../../../../../../demo-helpers/DeTrader.helper';
import DeOrderConfirm from './DeOrderConfirm.vue';

@Component({
  components: {DeOrderConfirm}
})
export default class DeOrderConfirmDemo extends Vue {
  PriceDirection = PriceDirection
  show = false;
  order_quantity: number = 10;
  order_type: OrderType = OrderType.BUY;
  de_trader_info_value: DeTraderInfoValue = createDemo__DeTraderInfoValue();
}
</script>
