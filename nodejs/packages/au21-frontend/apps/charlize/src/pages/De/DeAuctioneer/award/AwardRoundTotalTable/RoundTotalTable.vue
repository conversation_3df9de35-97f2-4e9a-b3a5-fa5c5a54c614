<template>
  <AuAgGrid
    :columnDefs="columnDefs"
    :rowData="rowData"
    :height="height"
    turnOffAutoColSize
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeRoundResultVM, DeTraderFlowVM} from '@au21-frontend/client-connector';
import {DeRoundTotalTableCellParams, DeRoundTotalTableRow} from './RoundTotalTable.types';
import {ColDef} from 'ag-grid-community';
import RoundTotalTableHeaderCell from './RoundTotalTableHeaderCell.vue';
import RoundTotalTableAgBodyCell from './RoundTotalTableAgBodyCell.vue';
import AuAgGrid from '../../../../../ui-components/AuAgGrid.vue';
import AuSectionHeader from '../../../../../ui-components/AuSectionHeader.vue';

@Component({
  name: 'RoundTotalTable',
  components: {
    AuSectionHeader,
    AuAgGrid,
    RoundTotalTableAgBodyCell,
  },
})
export default class RoundTotalTable extends Vue {
  @Prop({required: true}) roundResults: DeRoundResultVM[]
  @Prop({type: Number}) selectedRound: number
  @Prop({type: Number, default: 300}) height: number

  get columnDefs() {
    const cols: ColDef[] = [
      {
        width: 140,
        valueFormatter: (value) => {
          const row: DeRoundTotalTableRow = value.data
          return row.text
        },
        cellStyle: {paddingTop: '3px'},
        cellClass: 'align-right',
        headerClass: 'align-center',
      },
    ]

    this.roundResults.forEach((roundResult: DeRoundResultVM) => {
      cols.push({
        headerName: `Round ${roundResult.round_number}`,
        width: 100,
        cellClass: 'align-center',
        suppressMenu: true,
        cellRendererFramework: RoundTotalTableAgBodyCell,
        headerComponentFramework: RoundTotalTableHeaderCell,
        cellRendererParams: {roundResult} as DeRoundTotalTableCellParams,
      })
    })

    return cols
  }

  get rowData(): DeRoundTotalTableRow[] {
    return [
      {
        id: 'PRICE',
        text: 'Price:',
      },
      {
        id: 'BUY_TOTAL',
        text: 'Buy total:',
      },
      {
        id: 'SELL_TOTAL',
        text: 'Sell total:',
      },
      {
        id: 'MATCH',
        text: 'Max match:',
      },
      {
        id: 'TRADERS_ROW',
        text: 'Traders',
      },
      ...this.roundResults[0]?.trader_flows.map((flow: DeTraderFlowVM) => ({
        id: flow.company_id,
        text: flow.company_shortname,
      }))
    ]
  }

  setSelectedRound(round: number) {
    this.$emit('update:selectedRound', round)
  }
}
</script>

<style lang="less" scoped>

</style>
