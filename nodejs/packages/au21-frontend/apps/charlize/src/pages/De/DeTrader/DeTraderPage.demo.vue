<template>
  <VbDemo>
    <VbCard>

      <div>
        <label>Use counterparty credit limits:&nbsp;</label>
<!-- TODO Should use global settings       -->
        <a-radio-group
          style="margin-left: 7px"
          v-model:value="store.live_store.de_auction.settings.use_counterparty_credits"
          :default-value="false"
        >
          <a-radio :value="true">yes</a-radio>
          <a-radio :value="false">no</a-radio>
        </a-radio-group>
      </div>

      <DeTraderPage :store="store"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeTraderPage from "./DeTraderPage.vue";
import {CharlizeStore} from "../../../services/connector/CharlizeStore";
import {createDemo__store_for_trader} from '../../../demo-helpers/CharlizeStore.helper';

@Component({
  components: {DeTraderPage},
})
export default class DeTraderHeaderDemo extends Vue {
  store: CharlizeStore = createDemo__store_for_trader()
}
</script>
