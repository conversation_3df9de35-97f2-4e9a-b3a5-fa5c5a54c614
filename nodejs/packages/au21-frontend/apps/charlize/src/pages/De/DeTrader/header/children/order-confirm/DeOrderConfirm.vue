<template>
  <div class="DeOrderConfirm">
    <div style="text-align: center">
      <div class="au-label"
           style="font-size: 16px; margin-top:7px;">Your quantity constraints
      </div>
    </div>
    <div style="height: 5px">&nbsp;</div>

    <table class="_table au-label">
      <tr>
        <td colspan="2">&nbsp;</td>
        <td class="_quantity_header_span" colspan="2">
          <div>Buy</div>
          <div>
            <div class="_quantity_header_cell">Max</div>
            <div class="_quantity_header_cell">Min</div>
          </div>
        </td>
        <td class="_quantity_header_span" colspan="2">
          <div>Sell</div>
          <div>
            <div class="_quantity_header_cell">Min</div>
            <div class="_quantity_header_cell">Max</div>
          </div>
        </td>
      </tr>

      <tr>
        <td>
          <div class="_bar_title">
            This round:
          </div>
        </td>
        <td>
          <DeOrderConstraintsBar
            class="_bar"
            :width="200"
            :height="12"
            :tick_font_size="10"
            :constraints="current_constraints"
            :order_quantity='order_quantity'
            :order_type="order_type"
          />
        </td>
        <td class="_quantity_cell">{{ current_constraints.max_buy_quantity }}</td>
        <td class="_quantity_cell">{{ current_constraints.min_buy_quantity }}</td>
        <td class="_quantity_cell">{{ current_constraints.min_sell_quantity }}</td>
        <td class="_quantity_cell">{{ current_constraints.max_sell_quantity }}</td>
      </tr>

      <tr>
        <td>
          <div class="_bar_title">
            Next round if price increases:
          </div>
        </td>
        <td v-if="price_can_increase">
          <DeOrderConstraintsBar
            class="_bar"
            :width="200"
            :height="12"
            :tick_font_size="10"
            :constraints="price_increase_constraints"
            :order_quantity='order_quantity'
            :order_type="order_type"
          />
        </td>
        <td v-if="price_can_increase"
            class="_quantity_cell">{{ price_increase_constraints.max_buy_quantity }}
        </td>
        <td v-if="price_can_increase"
            class="_quantity_cell">{{ price_increase_constraints.min_buy_quantity }}
        </td>
        <td v-if="price_can_increase"
            class="_quantity_cell">{{ price_increase_constraints.min_sell_quantity }}
        </td>
        <td v-if="price_can_increase"
            class="_quantity_cell">{{ price_increase_constraints.max_sell_quantity }}
        </td>

        <td v-if="!price_can_increase"
            colspan="5">
          <div class="_has_reversed">
            price has reversed and cannot increase in subsequent rounds
          </div>
        </td>

      </tr>

      <tr>
        <td>
          <div class="_bar_title">
            Next round if price decreases:
          </div>
        </td>
        <td v-if="price_can_decrease">
          <DeOrderConstraintsBar
            class="_bar"
            :width="200"
            :height="12"
            :tick_font_size="10"
            :constraints="price_decrease_constraints"
            :order_quantity='order_quantity'
            :order_type="order_type"
          />
        </td>
        <td v-if="price_can_decrease"
            class="_quantity_cell">{{ price_decrease_constraints.max_buy_quantity }}
        </td>
        <td v-if="price_can_decrease"
            class="_quantity_cell">{{ price_decrease_constraints.min_buy_quantity }}
        </td>
        <td v-if="price_can_decrease"
            class="_quantity_cell">{{ price_decrease_constraints.min_sell_quantity }}
        </td>
        <td v-if="price_can_decrease"
            class="_quantity_cell">{{ price_decrease_constraints.max_sell_quantity }}
        </td>

        <td v-if="!price_can_decrease"
            colspan="6">
          <div class="_has_reversed">
            price has reversed and cannot decrease in subsequent rounds
          </div>
        </td>
      </tr>

    </table>

    <div  class="au-label _check_message">
      <div>
        Check your order carefully before submitting because:
      </div>
      <div>
        orders are final when the round ends,
        and the round ends when all bids are in.
      </div>
    </div>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeBidConstraints, DeTraderInfoValue, OrderType, PriceDirection} from '@au21-frontend/client-connector';
import DeOrderConstraintsWithRange from '../../../constraints/DeOrderConstraintsWithRange.vue';
import {calculate_constraints} from '../../../../../../helpers/de-constraint-calculator';
import {AuColors} from '../../../../../../au-styles/AuColors';
import {Container} from 'typescript-ioc';
import DeOrderConstraintsBar from "../../../constraints/DeOrderConstraintsBar.vue";

@Component({
  components: {
    DeOrderConstraintsBar,
    DeOrderConstraintsWithRange
  }
})
export default class DeOrderConfirm extends Vue {
  @Prop({required: true}) order_quantity: number;
  @Prop({required: true}) order_type: OrderType;
  @Prop({required: true}) de_trader_info_value: DeTraderInfoValue;
  @Prop({required: true}) price_has_reversed: boolean;
  @Prop({required: true}) price_direction: PriceDirection;

  colors = Container.get(AuColors);

  get current_constraints(): DeBidConstraints {
    return this.de_trader_info_value.bid_constraints;
  }

  get price_can_increase(): boolean {
    return this.price_direction == PriceDirection.UP || !this.price_has_reversed
  }

  get price_can_decrease(): boolean {
    return this.price_direction == PriceDirection.DOWN || !this.price_has_reversed
  }


  get price_increase_constraints(): DeBidConstraints {
    return calculate_constraints({
      prev_constraints: this.current_constraints,
      order_type: this.order_type,
      order_quantity: this.order_quantity,
      next_round_direction: PriceDirection.UP
    });
  }

  get price_decrease_constraints(): DeBidConstraints {
    return calculate_constraints({
      prev_constraints: this.current_constraints,
      order_type: this.order_type,
      order_quantity: this.order_quantity,
      next_round_direction: PriceDirection.DOWN
    });
  }

}
</script>
<style lang="less" scoped>
@import (reference) "../../../../../../au-styles/variables.less";

.DeOrderConfirm {

  background-color: @au-background;
  color: hsl(0, 0%, 70%);
  font-size: 14px;
  padding: 4px 14px;
  text-align: center;
  white-space: normal;
  width: 100%;

  .title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    width: 100%;
  }

  ._table {
    margin: 10px 30px 10px 30px;
    width: 610px;

    tr {
      border: 1px solid hsl(0, 0%, 50%);
      height: 35px;
    }

  }

  ._bar {
    height: 37px !important;
    margin-top: 10px;
    margin-right: 10px;
  }

  ._bar_title {
    //font-size: 14px;
    margin-right: 10px;
    position: relative;
    text-align: right;
    top: 7px;
    width: 180px;
  }

  ._has_reversed {
    color: lighten(@au-label-color, 8);
    font-style: italic;
    position: relative;
    top: 7px;

  }

  ._quantity_header_span {
    text-align: center;
  }

  ._quantity_header_cell {
    display: inline-block;
    width: 50%;
  }

  ._quantity_cell {
    padding-right: 12px;
    vertical-align: middle;
    text-align: right;
    width: 50px;
  }

  ._check_message {
    color: lighten(@au-label-color, 8);
    font-size: 11px;
    line-height: 1.8em;
    margin: 10px 55px;
  }

}
</style>
