<template>

  <div class="DeMatrixDataPanel" :style="{
                height: height + 'px',
                width: width + 'px',
                color: this.au_text_color,
                overflow: 'hidden'
            }">

    <div class="_company">{{ shortname || '&nbsp;' }}</div>

    <div class="_data">
      <div>{{ vol_max }}</div>
      <div>{{ vol_hatched }}</div>
      <div style="font-weight: bold;">{{ vol_solid }}</div>
    </div>


    <VolumeBar
      :style="{
         position:'absolute',
         left:'25px',
         bottom: '0',
         height,
         width
      }"
      :height="height"
      :width="30"
      :vol_max="vol_max"
      :vol_hatched="vol_hatched"
      :vol_solid="vol_solid"
      :color="bar_color"
    />
    {{vol_hatched}}

  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';
import VolumeBar from '../../../../ui-components/svg/VolumeBar.vue';

@Component({
  name: 'DeMatrixDataPanel',
  components: {VolumeBar}
})
export default class DeMatrixDataPanel extends Vue {

  width = 60;
  height = 68;

  @Prop({default: ''}) shortname: string;
  @Prop({required: true}) label_max: string;
  @Prop({required: true}) label_hatched: string;
  @Prop({required: true}) label_solid: string;

  @Prop({required: true}) vol_max: number;
  @Prop({required: true}) vol_hatched: number;
  @Prop({required: true}) vol_solid: number;

  @Prop({required: true}) au_text_color: string;
  @Prop({required: true}) bar_color: string;

}
</script>


<style lang="less" scoped>

.DeMatrixDataPanel {
  // border: 1px solid red;
  margin: 0;
  padding: 0;
  font-size: 12px;

  ._company {
    font-weight: bold;
    text-align: center;
    width: 55px;
  }

  ._data {
    bottom: 0;
    display: inline-block;
    font-size: 11px;
    line-height: 1.4em;
    margin-right: 40px;
    position: absolute;
    text-align: right;
    width: 19px;
  }

  //._row {
  //  //height: 13px;
  //  //width: 65px;
  //}


  //._node-label {
  //  font-size: 11px;
  //  font-weight: normal;
  //  //display: inline-block;
  //  height: 13px;
  //  //  left: 2px;
  //  overflow: hidden;
  //  //  position: relative;
  //  text-align: right;
  //  //  top: 0;
  //  width: 36px;
  //}

  //._node-data {
  //  font-weight: bold;
  //  display: inline-block;
  //  height: 13px;
  //  left: 40px;
  //  overflow: hidden;
  //  text-align: right;
  //  width: 23px;
  //}

}
</style>
