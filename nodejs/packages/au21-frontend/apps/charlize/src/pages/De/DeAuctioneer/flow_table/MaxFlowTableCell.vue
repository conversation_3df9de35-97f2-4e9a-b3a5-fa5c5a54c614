<template>
  <BuySellVBar
    :width="65"
    :height="20"
    :buyMax="50"
    :sellMax="50"
    :order_quantity_int='0'
    :match="50"
    order_quantity_str='0'
    :order_type="OrderType.NONE"
    :order_submission_type="OrderSubmissionType.MANUAL"
  />
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {FlowScenario} from '../../../../services/flow-algos/domain/model';
import BuySellVBar from '../../../common/components/BuySellBars/BuySellVBar.vue';
import {OrderSubmissionType, OrderType} from '@au21-frontend/client-connector';

/**
 * TODO: not currently used
 */

@Component({
  name: 'MaxFlowTableCell',
  components: {BuySellVBar},
})
export default class MaxFlowTableCell extends Vue {
  params = null

  OrderType = OrderType;
  OrderSubmissionType = OrderSubmissionType;

  get row(): FlowScenario {
    return this.params.data
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

</style>
