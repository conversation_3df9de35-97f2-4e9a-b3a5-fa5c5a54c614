<template>

  <div class="DeOrderConstraintsBar"
       :style="{
            width: width +'px',
            height: height +'px'
        }"
  >

    <!-- TICKS and TICK LABELS -->

    <!--    <div :style="`position:absolute; font-size:${tick_font_size}px; top:${height + 16 - tick_font_size}px`">-->
    <div
      v-if="show_labels"
      :style="`position:absolute; font-size:${tick_font_size}px; top:${height - 2 + (tick_font_size / 2)}px`"
    >
      <!-- zero tick -->
      <div
        class="tick-label"
        :style="`color: white; left:${mid_x - 0.035 * this.width}px; width:${2*tick_width}px; text-align:center`"
      >
        0
      </div>

      <!-- buy tick labels -->
      <div
        v-for="i in buy_ticks"
        class="tick-label"
        :style="`left: ${i.x}px; color:${colors.au_buy()}`"
      >
        {{ i.vol }}
      </div>

      <!-- sell tick labels -->
      <div
        v-for="i in sell_ticks"
        class="tick-label"
        :style="`left: ${i.x}px; color:${colors.au_sell()}; width:${tick_width}px; text-align:right`"
      >
        {{ i.vol }}
      </div>

    </div>

    <svg
      :width="bar_width" :height="height"
      :viewBox="`0 0 ${bar_width} ${height}`"
      xmlns="http://www.w3.org/2000/svg"
    >

      <!-- buy dimmed bar -->
      <rect
        :x="0"
        :y='3'
        :width="bar_width / 2"
        :height="height - 6"

        :fill="buy_dimmed"
      />

      <!-- buy constraints -->
      <rect
        :x="mid_x - to_pixels(max_buy_quantity)"
        :y='3'
        :width="Math.max(0, to_pixels(max_buy_quantity - min_buy_quantity))"
        :height="height - 6"
        :fill="colors.au_buy_dimmed()"
      />

      <!--      <rect-->
      <!--        :x="width - to_pixels(max_quantity)"-->
      <!--        :width="to_pixels(max_quantity)"-->
      <!--        :height="height"-->
      <!--        :fill="colors.au_buy()"-->
      <!--      />-->

      <!-- 2) right (sell) bar -->

      <!-- sell dimmed bar -->
      <rect
        :x="bar_width/2"
        :y='3'
        :width="bar_width"
        :height="height - 6"
        :fill="sell_dimmed"
      />

      <!-- sell constraints -->
      <rect
        :x="mid_x + to_pixels(min_sell_quantity)"
        :y='3'
        :width="Math.max(0, to_pixels(max_sell_quantity  - min_sell_quantity))"
        :height="height - 6"
        :fill="colors.au_sell_dim()"
      />

      <!-- 3) middle -->
        <!--        <svg :x="(width/2) - 2">-->
      <!--          &lt;!&ndash;        <polygon points='20,15 0,15 10,0' fill='#f00' />&ndash;&gt;-->
      <!--          <polygon :points="`0,${height} 4,${height}, 2,0`" fill="#ddd"/>-->
      <!--        </svg>-->

      <!--  center (not needed)  -->
      <!--            <rect :x="mid_x" :height="height" width="2" fill="yellow"/>-->

      <!-- tick_x_arr -->
      <rect :width="0.25" :height="height" v-for="x in tick_x_arr" fill="white" :x="x"/>

      <!-- 4) order -->
      <rect
        v-if="order_type"
        :x="order_x"
        :width="order_width"
        :height="height"
        :fill="order_color"
      />

    </svg>


  </div>
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import chroma from 'chroma-js';
import {range} from 'lodash';
import {DeBidConstraints, OrderType} from '@au21-frontend/client-connector';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: {}
})
export default class DeOrderConstraintsBar extends Vue {
  @Prop({required: true}) width: number;
  @Prop({required: true}) height: number;
  @Prop({default: 50}) max_quantity: number;
  @Prop({default: 10}) tick_quantity: number;
  @Prop({type: Boolean, default: true}) show_labels: boolean;
  @Prop({required: true}) tick_font_size: number;
  @Prop({required: true}) constraints: DeBidConstraints;
  @Prop({required: true}) order_type: OrderType | null;
  @Prop({required: true}) order_quantity: number | null;

  colors = Container.get(AuColors);

  get bar_width(): number {
    return this.width * 0.9
  }

  get buy_dimmed() {
    return chroma(this.colors.au_buy()).alpha(0.1);
  }

  get sell_dimmed() {
    return chroma(this.colors.au_sell()).alpha(0.1);
  }

  // get order_color(): string {
  //   switch (this.order_type) {
  //     case OrderType.SELL:
  //       return this.colors.au_sell_bright();
  //     case OrderType.BUY:
  //       return this.colors.au_buy_bright()
  //     default:
  //       return 'white'
  //   }
  // }

  get order_color():string{
    return this.colors.order_bright(this.order_type);
  }

  get max_buy_quantity(): number {
    return this.constraints?.max_buy_quantity || 0;
  }

  get min_buy_quantity(): number {
    return this.constraints?.min_buy_quantity;
  }

  get max_sell_quantity(): number {
    return this.constraints?.max_sell_quantity;
  }

  get min_sell_quantity(): number {
    return this.constraints?.min_sell_quantity;
  }

  get mid_x(): number {
    return this.bar_width / 2;
  }

  get pixels_per_vol() {
    return 0.9 * this.width / (this.max_quantity * 2);
  }

  to_pixels(vol: number): number {
    return vol * this.pixels_per_vol;
  }

  get tick_count(): number {
    return (this.max_quantity * 2 / this.tick_quantity) + 1;
  }

  get tick_width() {
    return this.tick_quantity * this.pixels_per_vol;
  }

  get tick_x_arr() {
    return range(0, this.tick_count).map(i =>
      i == 0 ? 1 : i * this.tick_width - 1);
  }

  get buy_ticks() {
    return range(0, this.max_quantity + 1, this.tick_quantity)
      .slice(1)
      .reverse()
      .map(vol => ({
        vol,
        x: this.mid_x - (vol * this.pixels_per_vol) + 0.02 * this.bar_width
      }));
  }

  get sell_ticks() {
    return range(0, this.max_quantity + 1, this.tick_quantity)
      .slice(1)
      .map(vol => ({
        vol,
        x: this.mid_x + (vol * this.pixels_per_vol) - this.tick_width + 0.08 * this.bar_width
      }));
  }

  get order_width(): number {
    return 3;
  }

  get order_x(): string {
    switch (this.order_type) {
      case OrderType.BUY:
        return (this.mid_x - (this.order_width / 2) - this.to_pixels(Math.min(this.max_quantity, this.order_quantity || 0))) + 'px';
      case OrderType.SELL:
        return (this.mid_x - (this.order_width / 2) + this.to_pixels(Math.min(this.max_quantity, this.order_quantity || 0))) + 'px';
      default: // ie: NONE or null:
        return (this.mid_x - (this.order_width / 2)) + 'px';
    }
  }

}
</script>

<style
  lang="less"
  scoped
>

.DeOrderConstraintsBar {
  position: relative;
  margin: 0;
  padding: 0;
  text-align: center;
}

.tick-label {
  color: white;
  position: absolute;
}


// TODO: do we need these?

.flash {
  animation-name: example;
  animation-duration: 1.0s;
  animation-direction: alternate-reverse;
  animation-iteration-count: 15
}

@keyframes example {
  0% {
    background-color: #1a1a1a;
  }
  38% {
    background-color: #3d080f;
  }
}
</style>
