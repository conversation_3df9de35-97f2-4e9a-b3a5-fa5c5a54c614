<template>
  <div class="DeCommonStatusPanel">
    <div
      class="_inner"
      :class="bgColorClass"
    >
      {{ common_state_text }}
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeCommonState} from '@au21-frontend/client-connector';
import chroma from 'chroma-js';

@Component({})
export default class DeCommonStatusPanel extends Vue {
  // TODO: add color change based on time

  @Prop({required: true}) common_state: DeCommonState;
  @Prop({required: true}) common_state_text: string;

  DeCommonState = DeCommonState;

  // colors = Container.get(AuColors);
  auRBGScale = chroma.scale(['#0f0', '#F00']).mode('lrgb');
  disabled_color = '#444';


  // colors: { [key in DeCommonState]: any } = {
  //   SETUP: chroma(this.colors.ERROR).alpha(0.5),
  //   STARTING_PRICE_ANNOUNCED: chroma(this.colors.WARNING).alpha(0.5),
  //   ROUND_OPEN: chroma(this.colors.NORMAL).alpha(0.5),
  //   ROUND_CLOSED: chroma(this.colors.WARNING).alpha(0.5),
  //   AUCTION_CLOSED: chroma(this.colors.ERROR).alpha(0.5)
  // };

  get bgColorClass(): string {
    return ({
      SETUP: '',
      STARTING_PRICE_ANNOUNCED: '', // chroma(this.colors.WARNING).alpha(0.5),
      ROUND_OPEN: '', // chroma(this.colors.NORMAL).alpha(0.5),
      ROUND_CLOSED: '', //  chroma(this.colors.WARNING).alpha(0.5),
      AUCTION_CLOSED: '' //  chroma(this.colors.ERROR).alpha(0.5)
    } as { [key in DeCommonState]: any })[this.common_state];
  }

  get auction_not_started(): boolean {
    return this.common_state != DeCommonState.SETUP &&
      this.common_state != DeCommonState.STARTING_PRICE_ANNOUNCED;
  }
}
</script>

<style lang="less" scoped>

.DeCommonStatusPanel {
  //  display: table;
  //  border: 1px solid red;
  font-size: 12px;
  font-weight: bold;
  //  height: 45px;
  margin: 0 7px 0 7px;
  position: relative;
  text-align: center;
  width: 110px;

  ._inner {
    background-color: hsl(220, 10%, 65%);
    border-radius: 5px;
    border: 1px solid #666;
    color: black;
    display: table-cell;
    // line-height: 1.3em;
    height: 40px;
    //  margin: 3px;
    padding: 3px;
    vertical-align: middle;
    white-space: normal;
    width: 115px;
  }
}
</style>
