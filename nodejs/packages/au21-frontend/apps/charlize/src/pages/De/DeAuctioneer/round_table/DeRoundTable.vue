<template>
  <AuAgGrid
    ref='auAgGrid'
    :columnDefs='columnDefs'
    :pinnedBottomRowData='pinnedBottomRowData'
    :pinnedTopRowData='pinnedTopRowData'
    :rowData='rowData'
    :width='table_width'
    :height='height'
    :getRowHeight='getRowHeight'
    turnOffAutoColSize
    :auto_refresh='false'
  />
</template>

<script lang='ts'>
import {ColDef} from 'ag-grid-community';
import {Component, Prop, PropSync, Ref, Vue, Watch} from 'vue-property-decorator';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';
import DeRoundTableAgBodyCell from './children/DeRoundTableAgBodyCell.vue';
import DeRoundTableAgColumnCell from './children/DeRoundTableAgColumnCell.vue';
import DeRoundTableAgHeaderCell from './children/DeRoundTableAgHeaderCell.vue';
import {
  DeBlotterRowAg,
  DeRoundTableAgRowType,
  DeRoundTableCellParams,
  DeRoundTableConstraintsParams,
  DeRoundTableSortBy,
} from './de-round-table-ag-helpers';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {DeBlotter, DeRoundElement, DeTraderElement, UserElement} from '@au21-frontend/client-connector';
import {sleep} from '@au21-frontend/utils';

import {max, range, round} from 'lodash';
import {round_trader_elements_for_company_sorted_by_round} from '../../../../services/helpers/de-helpers';
import DeRoundTableAgConstraintsCell from './children/DeRoundTableAgConstraintCell.vue';
import TableHeading from '../../../common/components/TableHeading/TableHeading.vue';

const getColId = (round: number) => `round-${round}`;

const sort_by_options: DeRoundTableSortBy[] = [
  { title: 'Name', field: 'shortname', sort_direction: 'asc' },
  { title: 'Qty', field: 'quantity_str', sort_direction: 'desc' },
]

@Component({
  name: 'DeRoundTable',
  components: {
    TableHeading,
    AuAgGrid,
    DeRoundTableAgBodyCell,
    DeRoundTableAgColumnCell,
    DeRoundTableAgHeaderCell
  }
})
export default class DeRoundTable extends Vue {
  @Prop({ required: true }) auction_id: string;
  @Prop({ required: true }) blotter: DeBlotter;
  @Prop({ type: Array, required: true }) online_users: UserElement[];
  @Prop({ type: Array, required: true }) users_that_have_seen_auction: UserElement[];
  @Prop({ type: Number, required: true }) height: number;
  @Prop({ type: Number, required: true }) buyMax: number;
  @Prop({ type: Number, required: true }) sellMax: number;

  @PropSync('selected_round', { type: Number }) selected_round_synced: number;

  @Ref() readonly auAgGrid: AuAgGrid;

  table_width = 650;
  body_cell_width = 70;
  row_height = 25;
  footer_height = 90;
  sort_by: DeRoundTableSortBy = sort_by_options[0]

  async mounted() {
    await sleep();
    this.onSelectedRoundChange();
  }

  // ========================= CONFIG ===========================

  // gridApi: GridApi = null;
  // gridOptions: GridOptions = {} // the below are all defaults of AuAgGrid
  // // gridOptions: GridOptions = {
  // //   getRowNodeId: (data: DeBlotterRowAg) => data.id,
  // //   onGridReady: params => this.onGridReady(params.api)
  // // };
  // onGridReady(gridApi: GridApi) {
  //   this.gridApi = gridApi;
  // }

  getRowHeight(params): number {
    return (params.data as DeBlotterRowAg).rowType === 'FOOTER' ?
      this.footer_height :
      this.row_height;
  }

  get maxValue(): number {
    let maxValue = 0;
    this.blotter.rounds.forEach(round => {
      const roundMaxValue = +max([
        round.buy_quantity,
        round.sell_quantity,
        round.matched
      ]);
      if (roundMaxValue > maxValue) {
        maxValue = roundMaxValue;
      }
    });
    return maxValue;
  }

  get columnDefs() {
    const cols: ColDef[] = [];

    cols.push({
      headerName: 'Traders',
      pinned: 'left',
      width: 140,
      suppressMenu: true,
      cellStyle: { padding: 0 },
      headerComponentFramework: DeRoundTableAgHeaderCell,
      cellRendererFramework: DeRoundTableAgColumnCell,
      cellRendererParams: {
        onRoundClick: () => {},
        onTradersClick: () => {
          // Toggle between 2 options.
          this.sort_by = sort_by_options.find(option => this.sort_by !== option)
        },
        getSortBy: () => this.sort_by
      } as DeRoundTableCellParams
    });

    cols.push({
      headerName: 'Constraints',
      pinned: 'left',
      width: 140,
      suppressMenu: true,
      cellStyle: { padding: 0 },
      headerComponentFramework: AuAgGridCenteredHeader,
      cellRendererFramework: DeRoundTableAgConstraintsCell,
      cellRendererParams: {
        rounds: this.blotter.rounds,
      } as DeRoundTableConstraintsParams
    });

    this.blotter.rounds.forEach((roundElement: DeRoundElement) => {
      cols.push({
        colId: getColId(roundElement.round_number),
        headerName: roundElement.round_number + '',
        suppressMenu: true,
        cellStyle: { padding: 0, margin: 0 },
        width: this.body_cell_width,
        headerClass: (this.selected_round_synced === roundElement.round_number ? 'DeRoundTable__col--selected' : ''),
        headerComponentFramework: DeRoundTableAgHeaderCell,
        cellRendererFramework: DeRoundTableAgBodyCell,
        cellRendererParams: {
          onRoundClick: () => this.onRoundClick(roundElement.round_number),
          onTradersClick: () => {},
          getSortBy: () => this.sort_by
        } as DeRoundTableCellParams
      });
    });

    const empty_col_count = 6 - this.blotter.rounds.length;
    if (empty_col_count > 0) {
      range(empty_col_count).forEach(index => {
        cols.push({
          colId: 'empty-col.' + index,
          headerName: '',
          suppressMenu: true,
          cellStyle: { padding: 0, margin: 0 },
          width: this.body_cell_width
        });
      });
    }

    return cols;
  }

  // ========================= DATA ===========================
  get pinnedTopRowData(): DeBlotterRowAg[] {
    return [
      new DeBlotterRowAg({
        id: 'PRICE',
        rowType: 'PRICE' as DeRoundTableAgRowType,
        trader: null,
        cells: null,
        selected_round: null
      })
    ];
  }

  get pinnedBottomRowData(): DeBlotterRowAg[] {
    return [
      new DeBlotterRowAg({
        id: 'FOOTER',
        rowType: 'FOOTER' as DeRoundTableAgRowType,
        trader: null,
        cells: null,
        selected_round: null
      })
    ];
  }

  get extra_row_count(): number {
    const pinned_body_height =
      this.row_height * 2 // header, price // removed excess
      + this.footer_height
      + 14; // horiz scrollbar

    const non_pinned_body_height =
      this.blotter.traders.length * this.row_height;

    const body_height = pinned_body_height + non_pinned_body_height;

    if (body_height > this.height) {
      return 0;
    }

    return max([0, round((this.height - body_height) / this.row_height) + 2]);
  }

  get rowData(): DeBlotterRowAg[] {
    let rows = this.blotter.traders
      .map(
        (t: DeTraderElement) => new DeBlotterRowAg({
          id: 'TRADER.' + t.company_id,
          rowType: 'TRADER' as DeRoundTableAgRowType,
          trader: t,
          cells: round_trader_elements_for_company_sorted_by_round(
            this.blotter.round_traders, t.company_id),
          selected_round: this.selected_round_synced
        })
      )
    ;

    const comparator = (row: DeBlotterRowAg): string => {
      if (this.sort_by.field === 'shortname') {
        return row.trader.shortname
      }

      if (this.sort_by.field === 'quantity_str') {
        const firstRound = this.blotter.rounds[0]
        const firstRoundTraderElement = this.blotter.round_traders.find(
          round_trader =>
            round_trader.round === firstRound.round_number
            && round_trader.cid === row.trader.company_id
        )

        // Default to shortname if no data.
        if (!firstRoundTraderElement) {
          return row.trader.shortname
        }
        return firstRoundTraderElement.quantity_str
      }
    }

    rows = [...rows].sort((a,b) => comparator(a) > comparator(b) ? 1 : -1)

    if (this.sort_by.sort_direction === 'desc') {
      rows = rows.reverse()
    }

    if (this.extra_row_count > 0) {
      range(1, this.extra_row_count)
        .forEach(
          i => rows.push(new DeBlotterRowAg({
            id: 'BLANK.' + i,
            rowType: 'BLANK' as DeRoundTableAgRowType,
            trader: null,
            cells: [],
            selected_round: null
          }))
        );
    }

    return rows;
  }


  // getOnlineUsersForCompany(companyId: string): UserElement[] {
  //   const result = this.users.filter(user => {
  //     if (user.company_id !== companyId) {
  //       return
  //     }
  //     return user.isOnline
  //   })
  //   return result
  // }

  @Watch('columnDefs')
  @Watch('selectedRoundNumberSynced')
  @Watch('rowData', { deep: true })
  onSelectedRoundChange() {
    // note we set auto_refresh property to false
    // - so that we don't trigger refreshes twice!
    this.auAgGrid.ensureColumnVisible(getColId(this.selected_round_synced));
    this.auAgGrid.refresh(); // For some reason ag-grid doesn't refresh header class on change.
  }

  onCompanyClick(companyId: string) {
    this.$emit('onCompanyClick', companyId);
  }

  onRoundClick(roundNumber: number): void {
    this.selected_round_synced = roundNumber;
  }
}
</script>

<style lang='less' scoped>
@import (reference) "../../../../au-styles/variables.less";

/deep/ .ag-header-cell::after {
  background-color: transparent !important;
}

/deep/ .DeRoundTable__col--selected {
  background-color: @au-pseudo-input-color;
  color: black;
}
</style>
