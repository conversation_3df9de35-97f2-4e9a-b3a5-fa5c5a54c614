<template>
  <div class="BidderDeSettings form-block">
    <table style="width: 100%; table-layout: fixed">
      <tr>
        <td style="width: 120px">Auction name:</td>
        <td>
          <AuInput class="BidderDeSettings__input" :value="deSettings.auction_name" output left/>
        </td>
      </tr>
      <tr>
        <td>Round duration:</td>
        <td>
          <AuInput class="BidderDeSettings__input" :value="roundDuration" output left/>
        </td>
      </tr>
      <tr>
        <td>Eligibility:</td>
        <td>
          <AuInput class="BidderDeSettings__input" :value="trader.current_buy_max" output left/>
        </td>
      </tr>
      <tr>
        <td>Activity table:</td>
        <td>
          <table class="BidderDeSettings__limit-table">
            <tr>
              <th>Buy-sell excess</th>
              <th>Label</th>
              <th>Next round price</th>
            </tr>

            <div class="DeSettingsEdit__form-item">
              <label class="DeSettingsEdit__label">More than 50 {{ form.volume_label
                }}:</label>
              <NumberInput
                class="DeSettingsEdit__input"
                :decimalPlaces="decimalPlacesComputed"
                :disabled="!editable"
                v-model="form.price_level_5"
              />
              <div>({{ form.price_label }})</div>
            </div>

            <div class="DeSettingsEdit__form-item">
              <label class="DeSettingsEdit__label">50 {{ form.volume_label }} or
                less:</label>
              <NumberInput
                class="DeSettingsEdit__input"
                :decimalPlaces="decimalPlacesComputed"
                :disabled="!editable"
                v-model="form.price_level_4"
              />
              <div>({{ form.price_label }})</div>
            </div>


            <div class="DeSettingsEdit__form-item">
              <label class="DeSettingsEdit__label">40 {{ form.volume_label }} or
                less:</label>
              <NumberInput
                class="DeSettingsEdit__input"
                :decimalPlaces="decimalPlacesComputed"
                :disabled="!editable"
                v-model="form.price_level_3"
              />
              <div>({{ form.price_label }})</div>
            </div>


            <div class="DeSettingsEdit__form-item">
              <label class="DeSettingsEdit__label">30 {{ form.volume_label }} or
                less:</label>
              <NumberInput
                class="DeSettingsEdit__input"
                :decimalPlaces="decimalPlacesComputed"
                :disabled="!editable"
                v-model="form.price_level_2"
              />
              <div>({{ form.price_label }})</div>
            </div>

            <div class="DeSettingsEdit__form-item">
              <label class="DeSettingsEdit__label">20 {{ form.volume_label }} or
                less:</label>
              <NumberInput
                class="DeSettingsEdit__input"
                :decimalPlaces="decimalPlacesComputed"
                :disabled="!editable"
                v-model="form.price_level_1"
              />
              <div>({{ form.price_label }})</div>
            </div>


            <tr>
              <td>
                <AuInput class="BidderDeSettings__input" :value="trader.current_buy_max" output left/>
              </td>
              <td>
                <AuInput class="BidderDeSettings__input" :value="trader.current_buy_max" output left/>
              </td>
              <td>
                <AuInput class="BidderDeSettings__input" :value="trader.current_buy_max" output left/>
              </td>
            </tr>
            <tr>
              <td>
                <div class="pseudo-input">{{ deSettings.MED_LIMIT }}
                </div>
              </td>
              <td>
                <div class="pseudo-input">{{ deSettings.MED_LABEL }}
                </div>
              </td>
              <td>
                <div class="pseudo-input">{{ deSettings.MED_CHANGE }}
                </div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="pseudo-input">{{ deSettings.LOW_LIMIT }}
                </div>
              </td>
              <td>
                <div class="pseudo-input">{{ deSettings.LOW_LABEL }}
                </div>
              </td>
              <td>
                <div class="pseudo-input">{{ deSettings.LOW_CHANGE }}
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import {
  DeSettingsValue,
  DeTraderElement,
} from '../../../../../../../../libs/client-connector/src'
import AuInput from '../../../../../ui-components/AuInput/AuInput.vue'

@Component({
  name: 'TraderDeSettings',
  components: { AuInput },
})
export default class TraderDeSettings extends Vue {
  @Prop({ required: true }) deSettings: DeSettingsValue
  @Prop({ required: true }) trader: DeTraderElement

  get roundDuration () {
    const first = this.deSettings.first_round_duration
    const following = this.deSettings.following_round_duration

    return `First round: ${first} seconds, remaining rounds: ${following} seconds`
  }

  get activityTableRows () {
    const getRow = (volumeString, price) => ({
      volumeString,
      price: this.getIncrementString(price),
    })

    return [
      {
        excess: ,
        price
      }
    ]
  }

  getIncrementString (value) {
    `increases by ${value} ${this.deSettings.price_label}`
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../assets/variables.less";

.BidderDeSettings {
  color: @au-text-color;

  > table > tr > td:first-child {
    padding-top: 2px;
  }

  &__input {
    width: 100%;
  }

  && {
    padding: 16px;
  }

  th:first-child {
    font-weight: 700;
  }

  td {
    vertical-align: top;
  }

  &__limit-table {
    width: 100%;

    tr {
      text-align: center;
    }

    td > .pseudo-input {
      justify-content: center;
    }
  }
}
</style>
