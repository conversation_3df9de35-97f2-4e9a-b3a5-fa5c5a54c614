<template>
  <div class="DeTraderHistoryTableHeader">
    <div style="height: 15px">{{ top }}</div>
    <div style="height: 15px; font-size: 10px">{{ bottom }}</div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import DeTraderHistoryTable from "./DeTraderHistoryTable.vue";

@Component({components: {}})
export default class DeTraderHistoryTableHeader extends Vue {
  params = null;

  get settings() {
    return (this.$parent.$parent.$parent as DeTraderHistoryTable).settings;
  }

  get top(): string {
    return this.params?.displayName || '';
  }

  get price_label() {
    return this.settings.price_label;
  }

  get quantity_label() {
    return this.settings.quantity_label;
  }

  get bottom(): string {
    const display_name = this.params?.displayName;
    if (display_name === 'Round')
      return '';
    if (display_name === 'Price')
      return `(${this.price_label})`;
    if (display_name === 'Order')
      return `(${this.quantity_label})`;
    if (display_name === 'Constraints')
      return `(${this.quantity_label})`;
    if (display_name === 'Excess')
      return '(indicator)';
    if (display_name === 'Submitted by')
      return '(bidder)';

    else return '';
  }

  refresh(params) {
    return true;
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeTraderHistoryTableHeader {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  text-align: center;
  width: 100%;
  white-space: normal;
}
</style>
