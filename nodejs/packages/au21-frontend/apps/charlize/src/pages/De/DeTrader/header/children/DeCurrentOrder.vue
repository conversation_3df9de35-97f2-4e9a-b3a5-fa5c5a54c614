<template>
  <Blinker
    class="DeCurrentOrder"
    :value="`${trader_info.order_type}-${currentQuantity}`"
    background
  >
    <!-- SIDE -->
    <InfoPanel
      style="margin: 1px 3px 1px 8px"
      heading="Side"
      :contents="order_type_label"
      :width="panel_width"
      :color="side_info.color"
    />

    <!-- CURRENT QUANTITY -->
    <InfoPanel
      style="margin: 1px 5px"
      :heading="settings.quantity_label"
      :contents="currentQuantity"
      :width="panel_width"
      :color="side_info.color"
    />
  </Blinker>
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {DeCommonStatusValue, DeSettingsValue, DeTraderInfoValue, OrderType} from '@au21-frontend/client-connector';
import AuSectionBorder from '../../../../../ui-components/AuSectionBorder.vue';
import InfoPanel from '../../../../common/components/InfoPanel/InfoPanel.vue';
import Blinker from '../../../../../ui-components/Blinker/Blinker.vue';
import {AuColors} from '../../../../../au-styles/AuColors';

@Component({
  components: {Blinker, InfoPanel, AuSectionBorder}
})
export default class DeCurrentOrder extends Vue {
  @Prop({required: true}) trader_info: DeTraderInfoValue;
  @Prop({required: true}) common_status: DeCommonStatusValue;
  @Prop({required: true}) settings: DeSettingsValue;

  panel_width = 68;
  colors = Container.get(AuColors);

  get roundPrice(): string | null {
    return this.common_status?.round_price;
  }

  get currentQuantity(): string | null {
    return this.trader_info?.order_quantity?.toString();
  }

  get order_type_label(): string {
    return this.trader_info.order_type === OrderType.BUY ? "Buy" :
      this.trader_info.order_type === OrderType.SELL ? "Sell" :
        "---"
  }

  get side_info() {
    return {
      color:
        this.trader_info?.order_type === OrderType.BUY ? this.colors.au_buy() :
          this.trader_info?.order_type === OrderType.SELL ? this.colors.au_sell() :
            '#999',
      label:
        this.trader_info?.order_type === OrderType.BUY ? 'Buy' :
          this.trader_info?.order_type === OrderType.SELL ? 'Sell' :
            '---'
    };
  }

}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.DeCurrentOrder {
  display: flex;
  height: 45px;
  overflow: hidden;
  padding: 2px;
  text-align: center;
  width: 100px;
}
</style>
