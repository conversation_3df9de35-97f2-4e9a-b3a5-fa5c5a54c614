<template>
  <VbDemo>
    <VbCard>
      <div v-for="state in states">
        <AButton @click="setState(state)">{{ state }}</AButton>
      </div>
    </VbCard>
    <VbCard>
      <DeAuctioneerInfoPanel
        :auctioneer_state="auctioneerStatus.auctioneer_state"
        :auction_trader_state="traderState"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {DeAuctioneerState, DeAuctioneerStatusValue, DeCommonState} from '@au21-frontend/client-connector';
import {createDemo__DeAuctioneerStatusValue} from '../../../../../demo-helpers/DeAuctioneerStatusValue.helper';
import DeAuctioneerInfoPanel from './DeAuctioneerInfoPanel.vue';
import {auctioner_state_to_trader_state} from '../../../components/de-utils';

@Component({
  components: {DeAuctioneerInfoPanel}
})
export default class DeAuctioneerInfoPanelDemo extends Vue {
  auctioneerStatus: DeAuctioneerStatusValue = createDemo__DeAuctioneerStatusValue();

  // for some reason need to add this:
  states = DeAuctioneerState;

  get traderState(): DeCommonState {
    return auctioner_state_to_trader_state[this.auctioneerStatus.auctioneer_state];
  }

  setState(s: DeAuctioneerState) {
    this.auctioneerStatus.auctioneer_state = s;
  }

}
</script>
