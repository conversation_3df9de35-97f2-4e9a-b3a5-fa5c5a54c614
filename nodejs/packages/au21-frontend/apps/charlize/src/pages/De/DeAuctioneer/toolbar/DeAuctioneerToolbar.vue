<template>
  <AuSectionBorder>
    <div class="DeAuctioneerToolbar">

      <div class="spacer"/>

      <!--      <div class="au-label _label"-->
      <!--      style="text-align: center">-->
      <!--        <div>Only show</div>-->
      <!--        <div>active traders</div>-->
      <!--      </div>-->
      <!--      -->
      <!--      <a-checkbox-->
      <!--        style="margin: 8px"-->
      <!--        v-model="showOnlySeenProxy" class="DeAuctioneerToolbar__checkbox">-->
      <!--      </a-checkbox>-->

      <div class='au-label _label'>Show traders:</div>
      <a-radio-group value='ALL'
                     style='position:relative; top: -2px'>
        <a-radio class='au-label _label' value='ALL'>all</a-radio>
        <a-radio class='au-label _label' value='ACTIVE'>active</a-radio>
      </a-radio-group>

      <div class="spacer"/>

      <DeAuctioneerToolbarStartingPrice
        :auction="auction"
        @onControl="onControl"
        @showAward="showAward()"
      />

      <div class="spacer"/>

      <DeAuctioneerToolbarFlowController
        :auction="auction"
        @onControl="onControl"
        @showAward="showAward()"
      />

      <div class="spacer"/>

      <div class="au-label _label">
        Traders:
      </div>

      <a-button-group>
        <ToolbarButton
          v-test:add_traders
          @click="addTraders(true)"
        >
          Add
        </ToolbarButton>

        <ToolbarButton
          v-test:remove_traders
          @click="addTraders(false)"
        >
          Remove
        </ToolbarButton>
      </a-button-group>

      <div class="spacer"/>

      <div class="au-label _label">
        Windows:
      </div>

      <a-button-group>
        <ToolbarButton>
          Matches
        </ToolbarButton>
        <ToolbarButton @click="showTransfers()">
          Credit History
        </ToolbarButton>
        <ToolbarButton @click="$emit('showRules')">
          Max Flow
        </ToolbarButton>

        <!--        <AuctionSettingsButton-->
        <!--          label="Settings"-->
        <!--          :crud="Crud.UPDATE"-->
        <!--          :store="store"-->
        <!--          @click="showSettings()"-->
        <!--        />-->

        <ToolbarButton @click="showRules()">
          Activity
        </ToolbarButton>
        <ToolbarButton
          icon="exception"
          @click="showRules()"
        >
          Rules
        </ToolbarButton>

        <AuctionSettingsButton
          :store="store"
          :crud="Crud.READ"
          label="Settings"
          :use_toolbar_button="true"
        />

        <!--    TODO:    Notice-->
        <!-- AuctionNoticeButton -->
        <!--        icon="setting"-->
        <!--    showNotice()-->

      </a-button-group>

      <!--      <div class="spacer"/>-->

    </div>

    <!-- Modals -->

    <TraderAssignModal
      v-if="showTraderAssignModal"
      :is_add_traders="is_add_traders"
      :store="store"
      @close="showTraderAssignModal = false"
    />
    <DePriceModal
      v-if="showPriceModal"
      @close="showPriceModal = false"
      :store="store"
    />
    <DeAwardModal
      v-if="showAwardModal"
      @close="showAwardModal = false"
      :store="store"
    />
    <DeAuctionRulesModal
      v-if="showRulesModal"
      @close="showRulesModal = false"
      :store="store"
    />
    <DeEligibilityModal
      v-if="showEligibilityModal"
      @close="showEligibilityModal = false"
      :store="store"
    />
    <TransfersModal
      v-if="show_transfers_modal"
      @close="show_transfers_modal = false"
      :store="store"
    />
  </AuSectionBorder>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import DeAuctioneerToolbarFlowController from './DeAuctioneerToolbarFlowController.vue';
import DeRoundController from '../round_controller/DeRoundController.vue';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {Container} from 'typescript-ioc';
import {
  Crud,
  de_flow_control_command,
  DeAuctionValue,
  DeFlowControlType,
  SocketConnector
} from '@au21-frontend/client-connector';
import TraderAssignModal from '../../../common/components/TraderSelectTable/TraderAssignModal.vue';
import DePriceModal from '../price/DePriceModal.vue';
import ToolbarButton from './ToolbarButton.vue';
import DeAuctionRulesModal from '../../DeTrader/header/children/auction-rules/DeAuctionRulesModal.vue';
import DeEligibilityModal from '../eligibility_table/DeEligibilityModal.vue';
import DeAuctioneerToolbarStartingPrice from './DeAuctioneerToolbarStartingPrice.vue';
import DeAwardModal from '../award/DeAwardModal.vue';
import AuctionSettingsButton from "../auction_settings/AuctionSettingsButton.vue";
import TransfersTable from '../../../common/components/TransactionsTable/TransfersTable.vue';
import TransfersModal from '../../../common/components/TransactionsTable/TransfersModal.vue';

@Component({
  components: {
    TransfersModal,
    TransfersTable,
    AuSectionBorder,
    DeAuctionRulesModal,
    DeAuctioneerToolbarFlowController,
    DeAuctioneerToolbarStartingPrice,
    DeAwardModal,
    DeEligibilityModal,
    DePriceModal,
    DeRoundController,
    AuctionSettingsButton,
    ToolbarButton,
    TraderAssignModal
  }
})
export default class DeAuctioneerToolbar extends Vue {
  @Prop({required: true}) store: CharlizeStore;
  @Prop({required: true}) show_only_seen: boolean;

  connector = Container.get(SocketConnector);

  Crud = Crud
  showAwardModal = false;
  showEligibilityModal = false;
  showPriceModal = false;
  showRulesModal = false;
  showSettingsModal = false;
  showTraderAssignModal = false;
  show_transfers_modal = false;

  is_add_traders = true;

  get showOnlySeenProxy() {
    return this.show_only_seen;
  }

  set showOnlySeenProxy(round) {
    this.$emit('update:showOnlySeen', round);
  }

  get auction(): DeAuctionValue | null {
    return this.store.live_store.de_auction;
  }


  onControl(control: DeFlowControlType) {
    if (control === DeFlowControlType.SET_STARTING_PRICE) {
      this.showPriceModal = true;
    } else {
      this.connector.publish(de_flow_control_command({
        auction_id: this.store.live_store.de_auction.auction_id,
        control: control,
        starting_price: null
      }));
    }
  }

  addTraders(is_add_traders) {
    this.is_add_traders = is_add_traders;
    this.showTraderAssignModal = true;
  }

  showNotice() {
    throw new Error('Not done!');
  }

  showAward() {
    this.showAwardModal = true;
  }

  showEligibility() {
    this.showEligibilityModal = true;
  }

  showSettings() {
    this.showSettingsModal = true
  }

  showRules() {
    this.showRulesModal = true;
  }

  showTransfers() {
    this.show_transfers_modal = true;
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.DeAuctioneerToolbar {
  background-color: @au-background !important;
  border-radius: 5px;
  display: flex;
  margin: 0;
  padding: 0;
  top: 2px;
  width: 1611px;


  ._label {
    color: @au-label-color !important;
    display: inline-block;
    padding: 6px 4px;
    position: relative;
    top: 2px !important;
  }

}

</style>
