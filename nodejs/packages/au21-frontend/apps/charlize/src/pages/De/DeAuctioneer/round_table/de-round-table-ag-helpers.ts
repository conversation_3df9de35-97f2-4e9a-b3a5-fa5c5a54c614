import {DeRoundElement, DeRoundTraderElement, DeTraderElement,} from '@au21-frontend/client-connector';

export type DeRoundTableSortBy = {
  title: string,
  field: 'quantity_str' | 'shortname',
  sort_direction: 'asc' | 'desc',
}

export type DeRoundTableCellParams = {
  onRoundClick: () => void
  onTradersClick: () => void
  getSortBy: () => DeRoundTableSortBy
}

export type DeRoundTableConstraintsParams = {
  rounds: DeRoundElement[]
}

export type DeRoundTableAgRowType =
  | 'PRICE'
  | 'TRADER'
  | 'FOOTER'
  | 'EXCESS'
  | 'BLANK'

export class DeBlotterRowAg {
  readonly id: string;
  readonly rowType: DeRoundTableAgRowType;
  readonly trader: DeTraderElement;
  readonly cells: DeRoundTraderElement[];
  readonly selected_round_trader: DeRoundTraderElement | null;

  constructor(o: {
    id: string;
    rowType: DeRoundTableAgRowType;
    trader: DeTraderElement | null;
    cells: DeRoundTraderElement[];
    selected_round: number | null;
  }) {
    this.id = o.id;
    this.trader = o.trader;
    this.rowType = o.rowType;
    this.cells = o.cells;
    if (o.selected_round > 0 && o.selected_round <= o.cells.length) {
      this.selected_round_trader = o.cells[o.selected_round - 1];
    }
  }
}

export function exists(value: any): boolean {
  return !(value === null || value === undefined || value === '');
}

export function addOrRemoveCssClass(
  element: HTMLElement,
  className: string,
  addOrRemove: boolean
) {
  if (addOrRemove) {
    addCssClass(element, className);
  } else {
    removeCssClass(element, className);
  }
}

export function removeCssClass(element: HTMLElement, className: string) {
  if (element.classList) {
    element.classList.remove(className);
  } else {
    if (element.className && element.className.length > 0) {
      const cssClasses = element.className.split(' ');
      if (cssClasses.indexOf(className) >= 0) {
        // remove all instances of the item, not just the first, in case it's in more than once
        while (cssClasses.indexOf(className) >= 0) {
          cssClasses.splice(cssClasses.indexOf(className), 1);
        }
        element.className = cssClasses.join(' ');
      }
    }
  }
}

export function addCssClass(element: HTMLElement, className: string) {
  if (!className || className.length === 0) {
    return;
  }
  if (className.indexOf(' ') >= 0) {
    className.split(' ').forEach(value => addCssClass(element, value));
    return;
  }
  if (element.classList) {
    element.classList.add(className);
  } else {
    if (element.className && element.className.length > 0) {
      const cssClasses = element.className.split(' ');
      if (cssClasses.indexOf(className) < 0) {
        cssClasses.push(className);
        element.className = cssClasses.join(' ');
      }
    } else {
      element.className = className;
    }
  }
}
