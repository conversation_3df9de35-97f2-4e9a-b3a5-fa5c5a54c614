const Path = (instructions = []) => {
  const push = (arr, el) => {
    const copy = arr.slice(0, arr.length)
    copy.push(el)
    return copy
  }

  const areEqualPoints = ([a1, b1], [a2, b2]) => a1 === a2 && b1 === b2

  const trimZeros = (string, char = null) => {
    let l = string.length
    while (string.charAt(l - 1) === '0') {
      l = l - 1
    }
    if (string.charAt(l - 1) === '.') {
      l = l - 1
    }
    return string.substr(0, l)
  }

  const round = (number, digits) => {
    const str = number.toFixed(digits)
    return trimZeros(str)
  }

  const printInstrunction = ({command, params}) => {
    const numbers = params.map(param => round(param, 6))
    return `${command} ${numbers.join(' ')}`
  }

  const point = ({command, params}, prev) => {
    const [prevX, prevY] = prev
    switch (command) {
      case 'M':
        return [params[0], params[1]]
      case 'L':
        return [params[0], params[1]]
      case 'H':
        return [params[0], prevY]
      case 'V':
        return [prevX, params[0]]
      case 'Z':
        return null
      case 'C':
        return [params[4], params[5]]
      case 'S':
        return [params[2], params[3]]
      case 'Q':
        return [params[2], params[3]]
      case 'T':
        return [params[0], params[1]]
      case 'A':
        return [params[5], params[6]]
    }
  }

  const plus = instruction => Path(push(instructions, instruction))

  return {
    moveto: (x, y) =>
      plus({
        command: 'M',
        params: [x, y],
      }),
    lineto: (x, y) =>
      plus({
        command: 'L',
        params: [x, y],
      }),
    hlineto: x =>
      plus({
        command: 'H',
        params: [x],
      }),
    vlineto: y =>
      plus({
        command: 'V',
        params: [y],
      }),
    closepath: () =>
      plus({
        command: 'Z',
        params: [],
      }),
    curveto: (x1, y1, x2, y2, x, y) =>
      plus({
        command: 'C',
        params: [x1, y1, x2, y2, x, y],
      }),
    smoothcurveto: (x2, y2, x, y) =>
      plus({
        command: 'S',
        params: [x2, y2, x, y],
      }),
    qcurveto: (x1, y1, x, y) =>
      plus({
        command: 'Q',
        params: [x1, y1, x, y],
      }),
    smoothqcurveto: (x, y) =>
      plus({
        command: 'T',
        params: [x, y],
      }),
    arc: (rx, ry, xrot, largeArcFlag, sweepFlag, x, y) =>
      plus({
        command: 'A',
        params: [rx, ry, xrot, largeArcFlag, sweepFlag, x, y],
      }),
    print: () => instructions.map(printInstrunction).join(' '),
    toString: function () {
      this.print()
    },
    points: () => {
      const ps = []
      let prev = [0, 0]
      for (const instruction of instructions) {
        const p = point(instruction, prev)
        prev = p
        if (p) {
          ps.push(p)
        }
      }
      return ps
    },
    instructions: () => instructions.slice(0, instructions.length),
    connect: function (path) {
      const ps = this.points()
      const last = ps[ps.length - 1]
      const first = path.points()[0]
      const newInstructions = path.instructions().slice(1)
      if (!areEqualPoints(last, first)) {
        newInstructions.unshift({
          command: 'L',
          params: first,
        })
      }
      return Path(this.instructions().concat(newInstructions))
    },
  }
}

export default function (arg = []) {
  return Path(arg)
}
