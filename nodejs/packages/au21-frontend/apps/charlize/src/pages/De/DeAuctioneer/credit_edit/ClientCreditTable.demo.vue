<template>
  <VbDemo>
    <VbCard>
      <ClientCreditTable
        :rows="rows"
        :width="500"
      />
      <pre style="color: white">{{ rows }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import ClientCreditTable from './ClientCreditTable.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__ClientCreditTableRow} from '../../../../demo-helpers/ClientCreditTable.helper';

@Component({
  components: {ClientCreditTable},
})
export default class ClientCreditTableDemo extends Vue {
  rows = createMultipleByClosure(createDemo__ClientCreditTableRow, 10)
}
</script>
