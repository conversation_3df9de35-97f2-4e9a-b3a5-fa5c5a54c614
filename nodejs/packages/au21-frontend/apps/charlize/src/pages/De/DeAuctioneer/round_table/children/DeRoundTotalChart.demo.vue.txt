<template>
  <VbDemo>
    <VbCard title="default">
      <DeRoundTotalChart
        :height="60"
        :width="60"
        :round="round"
        :maxValue="100"
      />
    </VbCard>
    <VbCard title="set values">
      <DeRoundTotalChart
        :height="60"
        :width="60"
        :round="roundSet"
        :maxValue="maxValue"
      />
      <br>
      buy_quantity: <input type="number" v-model="roundSet.buy_quantity">
      <br>
      sell_quantity: <input type="number" v-model="roundSet.sell_quantity">
      <br>
      matched: <input type="number" v-model="roundSet.matched">
      <br>
      <br>
      Max value: <input type="number" v-model="maxValue">
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import DeRoundTotalChart from './DeRoundTotalChart.vue';
import {createDemo__DeRoundElement} from '../../../../../demo-helpers/DeRoundTable.helper';

@Component({
  components: {
    DeRoundTotalChart,
  },
})
export default class DeRoundTotalChartDemo extends Vue {
  round = createDemo__DeRoundElement()
  roundSet = {
    ...createDemo__DeRoundElement(),
    buy_quantity: 50,
    matched: 20,
    sell_quantity: 30,
  }
  maxValue = 50
}
</script>

<style lang="less" scoped>

</style>
