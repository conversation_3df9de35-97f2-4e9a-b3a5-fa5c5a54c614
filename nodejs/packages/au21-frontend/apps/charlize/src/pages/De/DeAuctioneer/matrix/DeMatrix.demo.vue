<template>
  <VbDemo>
    <VbCard>
      <DeMatrix
        :nodes="nodes"
        :edges="edges"
        :selectedCompanyId.sync="selectedCompanyId"
      />
      selectedCompanyId: {{ selectedCompanyId }}
      <button @click="refreshAllData()">Refresh all data</button>
      <button @click="partialRefresh()">Refresh partial data</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue, Watch} from 'vue-property-decorator';
import DeMatrix from './DeMatrix.vue';
import {createMultipleByClosure, random_from_array} from '@au21-frontend/utils';
import {createDemo__DeMatrixEdgeElement} from '../../../../demo-helpers/DeMatrixEdgeElement.helper';
import {DeMatrixEdgeElement, DeMatrixNodeElement} from '@au21-frontend/client-connector';
import {range} from 'lodash';
import {createDemo__DeMatrixNodeElement} from "../../../../demo-helpers/DeMatrixNodeElement.helper";

@Component({
  name: 'DeMatrixDemo',
  components: {DeMatrix}
})
export default class DeMatrixDemo extends Vue {
  selectedCompanyId = null;
  edges: DeMatrixEdgeElement[] = [];
  nodes: DeMatrixNodeElement[] = [];

  companies: { id: string, shortname: string }[] = range(1, 4).map(cid => ({
    id: cid + '',
    shortname: 'c-' + cid
  }));

  get path() {
    return this.$route.path
  }

  get is_selected() {
    return this.path.includes('DeMatrix.demo')
  }

  @Watch('path')
  on_url(selected, old) {
    // console.log({selected})
    // debugger
  }

  created() {
    this.refreshAllData();
  }

  refreshAllData() {

    this.nodes = this.companies.map(c =>
      createDemo__DeMatrixNodeElement(1, c.shortname, c.id)
    );
    //)createMultipleByClosure(() => createDemo__DeMatrixNodeElement(1), 10);

    this.edges = (() => {
      const edges = [];
      this.nodes.forEach(nodeA => {
        this.nodes.forEach(nodeB => {
          if (nodeA === nodeB) {
            return;
          }
          edges.push(createDemo__DeMatrixEdgeElement(0, nodeA.cid, nodeB.cid));
        });
      });
      return edges;
    })();
  }

  partialRefresh() {
    createMultipleByClosure(() => {
      const edge = random_from_array(this.edges);
      const edgeData = createDemo__DeMatrixEdgeElement(0, edge.buyer_cid, edge.seller_cid);
      edge.match = edgeData.match;
      edge.capacity = edgeData.capacity;
    }, 20);

    // createMultipleByClosure(() => {
    //   const node = random_from_array(this.nodes);
    //   const nodeData = createDemo__DeMatrixNodeElement(0);
    //   node.buy_vol = nodeData.buy_vol;
    //   node.buy_match = nodeData.buy_match;
    //   node.buy_max = nodeData.buy_max;
    // }, 3);
  }
}
</script>
