<template>
  <div
    class="DeMatrixHeader"
    :class="{'DeMatrixHeader--selected-seller': isSelectedSeller}"
    @click="selectSeller()"
  >
    <DeMatrixDataPanel
      :shortname="companyName"
      label_max="max"
      label_hatched="order"
      label_solid="match"
      :vol_max="trader_node.sell_max"
      :vol_hatched="trader_node.sell_quantity"
      :vol_solid="trader_node.sell_match"
      :au_text_color="colors.au_sell()"
      :bar_color="colors.au_sell()"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import type DeMatrixTable from './DeMatrix.vue';
import {DeMatrixNodeElement} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import {AuColors} from 'apps/charlize/src/au-styles/AuColors';
import DeMatrixDataPanel from './DeMatrixDataPanel.vue';

@Component({
  components: {DeMatrixDataPanel}
})
export default class DeMatrixHeader extends Vue {
  params = null;
  colors = Container.get(AuColors);
  bar_width = 9;

  get companyName(): string {
    return this.params.column.colDef.headerName;
  }

  get tableRef() {
    return this.$parent.$parent.$parent as DeMatrixTable;
  }

  get trader_node(): DeMatrixNodeElement {
    return this.tableRef.nodes
      .find(c => c.shortname === this.companyName);
  }

  get colId(): string {
    return this.params.column.colId;
  }

  selectSeller() {
    this.tableRef.onSellerClick(this.colId);
  }

  get isSelectedSeller() {
    return this.tableRef.selectedCompanyId === this.colId;
  }

  get sellRange(): string {
    return 'TODO';
  }
}
</script>


<style lang="less" scoped>

@import (reference) '../../../../au-styles/variables.less';

.DeMatrixHeader {
  margin: 0 !important;
  padding: 0 !important;
  border: 0 !important;
  font-weight: normal;
  position: absolute;
  left: 0;

  &--selected-seller {
    background-color: black;
  }
}


</style>
