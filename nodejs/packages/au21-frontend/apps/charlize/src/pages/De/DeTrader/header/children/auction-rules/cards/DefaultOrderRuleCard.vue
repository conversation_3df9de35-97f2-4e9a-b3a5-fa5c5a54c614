<template>
  <div class="DefaultOrderRuleCard pl-3">
    <ul>
      <li>
        A consequence of the <b>Quantity Constraints Rule</b>
        is that it is always possible to create a default order,
        as follows:
      </li>
    </ul>
    <div class="pl-3">
      If there is a minimum buy or sell quantity constraint:
      <div class="pl-3">
        <ul>
          <li>
            Then the default order is that minimum quantity at the current round price.
          </li>
        </ul>
      </div>
    </div>

    <div class="pl-3">
      Otherwise the default quantity is zero
      <div class="pl-3">
        <ul>
          <li>
            ie: no quantity at the current round price.
          </li>
        </ul>
      </div>
    </div>

    <ul>
      <li>
        At the start of each round the system creates this
        default order.
      </li>
      <li>
        This order is submitted if the bidder fails to change it during the round,
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({
  name: 'DefaultOrderRuleCard',
})
export default class DefaultOrderRuleCard extends Vue {

}
</script>

<style lang="less" scoped>
.DefaultOrderRuleCard {

}
</style>
