<template>
  <VbDemo>
    <VbCard>
      <AButton @click="() => this.auctioneerStatus.announced = true">announce</AButton>
      <AButton @click="() => this.auctioneerStatus.announced = false">unannounce</AButton>
    </VbCard>
    <VbCard>
      <DeAnnounceDisplay :auctioneerStatus="auctioneerStatus"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuSelect from '../../../../../ui-components/AuSelect/AuSelect.vue';
import {createDemo__DeAuctioneerStatusValue} from '../../../../../demo-helpers/DeAuctioneerStatusValue.helper';
import DeAnnounceDisplay from './DeAnnounceDisplay.vue';

@Component({
  components: {AuSelect, DeAnnounceDisplay}
})
export default class DeAnnounceDisplayDemo extends Vue {
  auctioneerStatus = createDemo__DeAuctioneerStatusValue()

  announce() {
    this.auctioneerStatus.announced = true
  }

  unannounce() {
    this.auctioneerStatus.announced = false
  }
}
</script>
