<template>
  <TwoStateDisplay
    :width="55"
    first_label="NO"
    second_label="YES"
    :first_color="first_color"
    :second_color="second_color"
  />
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import TwoStateDisplay from '../../../common/components/TwoStateDisplay/TwoStateDisplay.vue';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: {TwoStateDisplay}
})
export default class DeAwardableDisplay extends Vue {

  @Prop({required: true}) awardable: boolean;

  colors = Container.get(AuColors);

  disabled_color = '#777';

  // get color() {
  //   return {
  //     NORMAL: this.colors.NORMAL,
  //     WARNING: this.colors.WARNING,
  //     ERROR: this.colors.ERROR
  //   }[this.level];
  // }

  get first_color() {
    return this.awardable ?
      'red' : // this.colors.NORMAL :
      this.disabled_color;
  }

  get second_color() {
    return this.awardable ?
      this.disabled_color :
      'red' // this.colors.ERROR;
  }

  //
  // auRBGScale = chroma.scale(['#0f0', '#F00']).mode('lrgb');
  //
  // state_colors: { [state in DeAuctioneerState]: string } = {
  //   STARTING_PRICE_NOT_SET: this.auRBGScale(0.5).hex(),
  //   STARTING_PRICE_SET: this.auRBGScale(0.9).hex(), //this.auRBGScale(0.50).hex(), //'#ddad00',
  //   STARTING_PRICE_ANNOUNCED: this.auRBGScale(0.5).hex(), //  this.auRBGScale(0.50).hex(), // '#ddad00',
  //   ROUND_OPEN: this.auRBGScale(0.25).hex(), // this.auRBGScale(0.50).hex(),  // 'rgba(52, 168, 83, 1.000)', //'#68c675',
  //   ROUND_CLOSED: this.auRBGScale(0.90).hex(),
  //   AUCTION_CLOSED: '#999'
  // };

  // round_closed_color(): string {
  //   return this.auctioneerStatus.isAwardable ?
  //     this.auRBGScale(0.90).hex() //  this.auRBGScale(0.50).hex(),
  //     : this.auRBGScale(0.75).hex() //// this.auRBGScale(0.50).hex(),//'rgba(251, 188, 5, 1.000)',
  // }

  // PROPS:

  // @Prop({ required: true, default: DeAuctioneerState.AUCTION_INIT }) state: DeAuctioneerState;
  //
  // get state(): DeAuctioneerState | null {
  //   return this.commonStatus?.auctioneer_state;
  // }
  //
  // get isSetup(): boolean {
  //   return this.state === DeAuctioneerState.STARTING_PRICE_NOT_SET;
  // }
  //
  // get paused(): boolean {
  //   return false; // this.commonStatus?.auctioneer_state == DeAuctioneerState.ROUND_PAUSED
  // }
  //
  // state_color(states: DeAuctioneerState[]): string {
  //   return states.includes(this.state) ?
  //     this.state_colors[this.state] : '#999';
  // }


}
</script>

<style scoped>
</style>
