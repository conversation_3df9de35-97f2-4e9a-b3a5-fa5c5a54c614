<template>
  <a-modal
    class="DeOrderConfirmModal"
    @cancel="cancel()"
    visible
    closable
    centered
    width="700px"
  >
    <div slot="title" class="_title">
      Confirm Order: <span :style='{color: order_color}'>{{ title }}</span>
    </div>

    <DeOrderConfirm
      :order_quantity="order_quantity"
      :order_type="order_type"
      :de_trader_info_value="de_trader_info_value"
      :price_direction="PriceDirection.UP"
      :price_has_reversed="false"
    />

    <a-button
      slot="footer"
      type="primary"
      class="au-btn"
      @click="cancel()"
    >
      Cancel
    </a-button>

    <a-button
      slot="footer"
      class="au-btn"
      type="primary"
      v-test:confirm_order
      @click="on_confirm()"
    >
      Confirm
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import DeOrderConfirm from './DeOrderConfirm.vue';
import {DeTraderInfoValue, OrderType, PriceDirection} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import {AuColors} from '../../../../../../au-styles/AuColors';

/**
 * NB !!
 * The next round constraints calculation needs to be kep in sync with the kotlin file:
 * au21/engine/domain/de/services/constraints/constraint-calculator.kt
 */


@Component({
  components: {DeOrderConfirm}
})
export default class DeOrderConfirmModal extends Vue {
  @Prop({required: true}) order_quantity: number;
  @Prop({required: true}) order_type: OrderType;
  @Prop({required: true}) de_trader_info_value: DeTraderInfoValue;
  @Prop({required: true}) price_direction:PriceDirection;
  @Prop({required: true}) price_has_reversed:boolean

  colors = Container.get(AuColors);
  PriceDirection = PriceDirection

  on_confirm() {
    this.$emit('confirm');
  }

  cancel() {
    this.$emit('close');
  }

  get order_color(): string {
    return this.colors.order_quantity_text_color(this.order_type);
  }

  get title(): string {
    return (
      this.order_quantity == 0 ?
        'No quantity' :
        `${this.order_type} ${this.order_quantity} ${this.de_trader_info_value.quantity_label}`
    ) + ` @${this.de_trader_info_value.round_price} ${this.de_trader_info_value.price_label}`;
  }
}
</script>
<style lang="less" scoped>
.DeOrderConfirmModal {
  ._title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    width: 100%;
  }
}
</style>
