<template>
  <VbDemo>
    <VbCard>
      <button @click="buy()">Buy</button>
      <button @click="sell()">Sell</button>
      <button @click="buy_buy()">Buy-Buy</button>
      <button @click="sell_sell()">Sell-Sell</button>
      <button @click="buy_sell()">Buy-Sell</button>
      <DeOrderConstraintsWithRange
        :order_type="order_type"
        :order_quantity='order_quantity'
        :constraints="constraints"
        quantity_label='quantity_label'
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {DeBidConstraints, OrderType} from '@au21-frontend/client-connector';
import {createDemo__DeBidConstraints} from '../../../../demo-helpers/DeBidConstraints.helper';
import DeOrderConstraintsWithRange from './DeOrderConstraintsWithRange.vue';

@Component({
  components: {DeOrderConstraintsWithRange},
})
export default class DeOrderConstraintsWithRangeDemo extends Vue {
  order_quantity: number = 10
  order_type: OrderType = OrderType.BUY
  quantity_label = "MMlb"
  constraints: DeBidConstraints = createDemo__DeBidConstraints()

  mounted(){
    this.buy()
  }

  buy(){
    this.order_quantity = 10
    this.constraints = {
      max_buy_quantity: 40,
      min_buy_quantity: 0,
      min_sell_quantity: 0,
      max_sell_quantity: 30,
    }
  }

  sell(){}
  buy_buy(){}
  sell_sell(){}
  buy_sell(){}

}
</script>
