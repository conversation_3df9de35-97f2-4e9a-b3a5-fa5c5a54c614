<template>
  <div class="DeOrderConstraintsWithRange">
    <div
      class="_heading"
      :style="`left: 10px; color:${colors.au_buy_dimmed()}`"
    >Buy: {{ buy_range }}
    </div>
    <div
      class="_heading"
      :style="`left: 10px; color:${colors.au_sell_dim()}`"
    >Sell: {{ sell_range }}
    </div>
    <DeOrderConstraintsBar
      style="margin-top: 4px"
      :width="250"
      :height="12"
      :tick_font_size="10"
      :constraints="constraints"
      :order_quantity='order_quantity'
      :order_type="order_type"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import DeOrderConstraintsBar from './DeOrderConstraintsBar.vue';
import {DeBidConstraints, OrderType} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: {
    DeOrderConstraintsBar
  }
})
export default class DeOrderConstraintsWithRange extends Vue {
  @Prop({required: true}) order_quantity: number;
  @Prop({required: true}) order_type: OrderType;
  @Prop({required: true}) constraints: DeBidConstraints;
  @Prop({required: true}) quantity_label: string;

  colors = Container.get(AuColors);

  constraints_bar = {
    height: 10,
    width: 300,
    tick_quantity: 10,
    tick_font_size: 11
  };

  get buy_range(): string {
    if (this.constraints.min_buy_quantity == 0 && this.constraints.max_buy_quantity == 0)
      return 'none';
    else if (this.constraints.min_buy_quantity == this.constraints.max_buy_quantity)
      return this.constraints.max_buy_quantity + '';
    else
      return `${this.constraints.min_buy_quantity} to ${this.constraints.max_buy_quantity} ${this.quantity_label}`;
  }

  get sell_range(): string {
    if (this.constraints.min_sell_quantity == 0 && this.constraints.max_sell_quantity == 0)
      return 'none';
    else if (this.constraints.min_sell_quantity == this.constraints.max_sell_quantity)
      return this.constraints.max_sell_quantity + '';
    else
      return `${this.constraints.min_sell_quantity} to ${this.constraints.max_sell_quantity} ${this.quantity_label}`;
  }

  // from DeTraderHeader
  // get buy_range(): string {
  //   if (this.constraints.min_sell_quantity > 0)
  //     return '---';
  //   else if (this.constraints.min_buy_quantity == this.constraints.max_buy_quantity)
  //     return this.constraints.max_buy_quantity + '';
  //   else
  // }
  //
  // get sell_range(): string {
  //   if (this.constraints.min_buy_quantity > 0)
  //     return '---';
  //   else if (this.constraints.min_sell_quantity == this.constraints.max_sell_quantity)
  //     return this.constraints.max_sell_quantity + '';
  //   else
  //     return this.constraints.min_sell_quantity + ' to ' + this.constraints.max_sell_quantity;
  // }


  // original attempt:
  // get buy_range(): string {
  //   if (this.constraints.min_buy_quantity == 0 && this.constraints.max_buy_quantity == 0)
  //     return 'none';
  //   else
  //     return `${this.constraints.min_buy_quantity} - ${this.constraints.max_buy_quantity} ${this.quantity_label}`;
  // }
  //
  // get sell_range(): string {
  //   if (this.constraints.min_sell_quantity == 0 && this.constraints.max_sell_quantity == 0)
  //     return 'none';
  //   else
  //     return `${this.constraints.min_sell_quantity} - ${this.constraints.max_sell_quantity} ${this.quantity_label}`;
  // }

}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeOrderConstraintsWithRange {
  font-size: 12px;
  height: 63px;
  overflow: hidden;
  padding: 2px;
  width: 260px;

  ._heading {
    display: inline-block;
    font-size: 12px;
    font-weight: bold;
    margin-left: 8px;
    text-align: left;
    width: 120px;
  }
}
</style>
