<template>
  <VbDemo >
    <VbCard >
      <DeAuctioneerSummaryRow
        :commonStatus="commonStatus"
        :auctioneerInfo="auctioneerInfo"
        :auctioneerStatus="auctioneerStatus"
        :deSettings="deSettings"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import {
  DeAuctioneerStatusValue,
  DeAuctionState,
  DeCommonStatusValue,
  PriceDirection
} from '@au21-frontend/client-connector';
import { createDemo__DeSettingsValue } from '../../../../demo-helpers/DeSettingsValue.helper';
import { createDemo__DeAuctioneerStatusValue } from '../../../../demo-helpers/DeAuctioneerStatusValue.helper';
import DeAuctioneerSummaryRow from './DeAuctioneerSummaryRow.vue';
import { createDemo__DeAuctioneerInfoValue } from '../../../../demo-helpers/DeAuctioneerInfoValue.helper';

@Component({
  components: { DeAuctioneerSummaryRow }
})
export default class DeAuctioneerSummaryRowDemo extends Vue {
  commonStatus: DeCommonStatusValue = {
    auction_state: DeAuctionState.ROUND_OPEN,
    common_status_label: 'auction status label',
    current_price_direction: PriceDirection.UP,
    isClosed: false,
    round_number: 99,
    round_price: '67.875',
    round_seconds: 10
  };

  auctioneerStatus: DeAuctioneerStatusValue = createDemo__DeAuctioneerStatusValue()
  deSettings = createDemo__DeSettingsValue();
  auctioneerInfo = createDemo__DeAuctioneerInfoValue();
}
</script>
