export type DeEligibilityTableRow = {
  company_id: string,
  company_name: string,
  id: string,
  max_buy_quantity: string,
  min_buy_quantity: string,
  min_sell_quantity: string,
  max_sell_quantity: string,
}

export type EligibilityField = 'max_buy_quantity' | 'min_buy_quantity' | 'min_sell_quantity' | 'max_sell_quantity'

export type DeEligibilityTableCellParams = {
  field: EligibilityField,
  getReadonly: () => boolean,
  saveValue: (value: string, field: EligibilityField) => void,
}

export type DeEligibilityTableActionsParams = {
  isRowAllowable: (row: DeEligibilityTableRow) => boolean,
  allow: (row: DeEligibilityTableRow) => void,
}
