<template>
  <VbDemo>
    <VbCard>
      <DeSankeyDiagram
        :height="500"
        :width="400"
        :matrix_edges="edges"
        :round_trader_elements="round_traders"
      />
    </VbCard>
    <VbCard title="empty">
      <DeSankeyDiagram
        :height="500"
        :width="400"
        :matrix_edges="[]"
        :round_trader_elements="[]"
      />
    </VbCard>
    <VbCard title="one to one">
      <DeSankeyDiagram
        :height="500"
        :width="400"
        :matrix_edges="oneToOne.edges"
        :round_trader_elements="oneToOne.cells"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import DeSankeyDiagram from './DeSankeyDiagram.vue';
import {createDemo__DeMatrixEdgeElement} from '../../../../demo-helpers/DeMatrixEdgeElement.helper';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {DeMatrixEdgeElement} from '@au21-frontend/client-connector';
import {createDemo__DeRoundTraderElement} from '../../../../demo-helpers/DeRoundTable.helper';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';

@Component({
  components: {
    DeSankeyDiagram,
  },
})
export default class DeSankeyDiagramDemo extends Vue {
  companies = createMultipleByClosure(createDemo__CompanyElement, 6)
  round_traders = this.companies.map(company => createDemo__DeRoundTraderElement(1, company))
  edges: DeMatrixEdgeElement[] = (() => {
    const edges: DeMatrixEdgeElement[] = []
    this.companies.forEach(companyA => {
      this.companies.forEach(companyB => {
        if (companyA === companyB) {
          return
        }
        edges.push(
          createDemo__DeMatrixEdgeElement(1, companyA.company_id, companyB.company_id),
        )
      })
    })
    return edges
  })()

  oneToOne = {
    cells: [{
      "cid": "215",
      "username": "b1",
      "isBuy": true,
      "isSell": false,
      "round": 4,
      "adjusted": false,
      "implied": false,
      "quantity": "9",
      "timestamp_formatted": "09:51:26",
      "id": "ROUND.4.COMPANY.215"
    }, {
      "cid": "216",
      "username": "b2",
      "isBuy": false,
      "isSell": true,
      "round": 4,
      "adjusted": false,
      "implied": false,
      "quantity": "9",
      "timestamp_formatted": "09:51:32",
      "id": "ROUND.4.COMPANY.216"
    }],
    edges: [{
      "r": 4,
      "buyer_cid": "215",
      "seller_cid": "215",
      "match": 0,
      "capacity": 0,
      "id": "R_4_B_215_S_215"
    }, {"r": 4, "buyer_cid": "215", "seller_cid": "216", "match": 9, "capacity": 10, "id": "R_4_B_215_S_216"}, {
      "r": 4,
      "buyer_cid": "216",
      "seller_cid": "215",
      "match": 0,
      "capacity": 0,
      "id": "R_4_B_216_S_215"
    }, {"r": 4, "buyer_cid": "216", "seller_cid": "216", "match": 0, "capacity": 0, "id": "R_4_B_216_S_216"}]
  }

  emptyExample = {
    "cells": [
      {
        "id": "R_2_T_2423572",
        "round": 2,
        "cid": "2423572",
        "shortname": "c-1",
        "buy_match": 0,
        "buy_max": 42,
        "buy_min": 0,
        "buy_vol": 33,
        "sell_match": 0,
        "sell_max": 50,
        "sell_min": 0,
        "sell_vol": 0
      },
      {
        "id": "R_2_T_2423574",
        "round": 2,
        "cid": "2423574",
        "shortname": "c-2",
        "buy_match": 0,
        "buy_max": 43,
        "buy_min": 0,
        "buy_vol": 33,
        "sell_match": 0,
        "sell_max": 50,
        "sell_min": 0,
        "sell_vol": 0
      },
      {
        "id": "R_2_T_2423576",
        "round": 2,
        "cid": "2423576",
        "shortname": "c-3",
        "buy_match": 0,
        "buy_max": 0,
        "buy_min": 0,
        "buy_vol": 0,
        "sell_match": 0,
        "sell_max": 50,
        "sell_min": 0,
        "sell_vol": 0
      },
      {
        "id": "R_2_T_2423578",
        "round": 2,
        "cid": "2423578",
        "shortname": "c-4",
        "buy_match": 0,
        "buy_max": 50,
        "buy_min": 0,
        "buy_vol": 42,
        "sell_match": 0,
        "sell_max": 50,
        "sell_min": 0,
        "sell_vol": 0
      }
    ],
    "edges": [
      {
        "id": "R_2_B_2423574_S_2423572",
        "r": 2,
        "buyer_cid": "2423574",
        "seller_cid": "2423572",
        "match": 0,
        "capacity": 9,
        "credit_str": "$9,800,000.00"
      },
      {
        "id": "R_2_B_2423576_S_2423572",
        "r": 2,
        "buyer_cid": "2423576",
        "seller_cid": "2423572",
        "match": 0,
        "capacity": 0,
        "credit_str": "$2,000,000.00"
      },
      {
        "id": "R_2_B_2423578_S_2423572",
        "r": 2,
        "buyer_cid": "2423578",
        "seller_cid": "2423572",
        "match": 0,
        "capacity": 1,
        "credit_str": "$1,200,000.00"
      },
      {
        "id": "R_2_B_2423572_S_2423574",
        "r": 2,
        "buyer_cid": "2423572",
        "seller_cid": "2423574",
        "match": 0,
        "capacity": 2,
        "credit_str": "$2,700,000.00"
      },
      {
        "id": "R_2_B_2423576_S_2423574",
        "r": 2,
        "buyer_cid": "2423576",
        "seller_cid": "2423574",
        "match": 0,
        "capacity": 0,
        "credit_str": "$1,200,000.00"
      },
      {
        "id": "R_2_B_2423578_S_2423574",
        "r": 2,
        "buyer_cid": "2423578",
        "seller_cid": "2423574",
        "match": 0,
        "capacity": 0,
        "credit_str": "$900,000.00"
      },
      {
        "id": "R_2_B_2423572_S_2423576",
        "r": 2,
        "buyer_cid": "2423572",
        "seller_cid": "2423576",
        "match": 0,
        "capacity": 6,
        "credit_str": "$6,400,000.00"
      },
      {
        "id": "R_2_B_2423574_S_2423576",
        "r": 2,
        "buyer_cid": "2423574",
        "seller_cid": "2423576",
        "match": 0,
        "capacity": 7,
        "credit_str": "$7,600,000.00"
      },
      {
        "id": "R_2_B_2423578_S_2423576",
        "r": 2,
        "buyer_cid": "2423578",
        "seller_cid": "2423576",
        "match": 0,
        "capacity": 6,
        "credit_str": "$6,300,000.00"
      },
      {
        "id": "R_2_B_2423572_S_2423578",
        "r": 2,
        "buyer_cid": "2423572",
        "seller_cid": "2423578",
        "match": 0,
        "capacity": 4,
        "credit_str": "$4,100,000.00"
      },
      {
        "id": "R_2_B_2423574_S_2423578",
        "r": 2,
        "buyer_cid": "2423574",
        "seller_cid": "2423578",
        "match": 0,
        "capacity": 9,
        "credit_str": "$9,900,000.00"
      },
      {
        "id": "R_2_B_2423576_S_2423578",
        "r": 2,
        "buyer_cid": "2423576",
        "seller_cid": "2423578",
        "match": 0,
        "capacity": 0,
        "credit_str": "$2,400,000.00"
      }
    ]
  }
}
</script>

<style lang="less" scoped>

</style>
