<template>
  <a-modal
    class="DeAwardModal"
    title="Award Auction"
    @cancel="cancel()"
    visible
    closable
    centered
    width="1392px"
  >

    <div :style="`height:${height}px;`">

      <DeSankeyDiagram
        style="float:left"
        :width="400"
        :height="this.height - 25"
        :matrix_edges="[]"
        :round_trader_elements="round_trader_elements"
      />

      <DeOrderBook
        style="float:left"
        :auction-settings="settings"
        :round_trader_elements="round_trader_elements"
        :height="this.height - 25"
        :companies="companies"
        :quantity_label="settings.quantity_label"
      />

      <DeAwardTable
        style="float: left"
        :height="this.height - 50"
        :matrix="matrix"/>


    </div>

    <a-button
      slot="footer"
      type="primary"
      @click="cancel()"
    >
      Cancel
    </a-button>

    <a-button
      slot="footer"
      :disabled="!selectedRoundNumber"
      type="primary"
      @click="awardAuction()"
    >
      Award
    </a-button>
  </a-modal>
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {CharlizeStore} from "../../../../services/connector/CharlizeStore";
import {
  CompanyElement,
  de_auction_award_command,
  DeAuctionValue,
  DeMatrixEdgeElement,
  DeMatrixRoundElement,
  DeRoundElement,
  DeRoundResultVM,
  DeRoundTraderElement,
  DeScenarioMatchVM,
  DeSettingsValue,
  DeTraderElement,
  SocketConnector
} from '@au21-frontend/client-connector';
import {AuScreen} from "../../../../plugins/screen-plugin/AuScreen";
import DeOrderBook from "../order_book/DeOrderBook.vue";
import {round_trader_elements_for_round} from "../../../../services/helpers/de-helpers";
import DeSankeyDiagram from "../sankey/DeSankeyDiagram.vue";
import DeAwardTable from "./award_table/DeAwardTable.vue";

@Component({
  components: {DeAwardTable, DeSankeyDiagram, DeOrderBook}
})
export default class DeAwardModal extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  // @Prop({required: true}) selected_round_number: number

  connector = Container.get(SocketConnector);
  screen = new AuScreen(true)

  selectedRoundNumber: number | null = null;

  // isChartFlipped = false
  // DeRoundChartQuantityAxis = DeRoundChartQuantityAxis
  // DeRoundChartNonQuantityAxis = DeRoundChartNonQuantityAxis
  // quantityAxis = DeRoundChartQuantityAxis.Y
  // nonQuantityAxis = DeRoundChartNonQuantityAxis.ROUND

  get height(): number {
    return this.screen.modal_height
  }

  get auction(): DeAuctionValue {
    return this.store.live_store.de_auction
  }

  get settings(): DeSettingsValue {
    return this.auction.settings
  }

  get matrix(): DeMatrixRoundElement | null {
    return this.auction.matrix_last_round
  }

  get awardable_round(): number | null {
    // for now just taking the last round, but this will come from backend eventually
    return this.matrix.round_number
  }

  get companies(): CompanyElement[] {
    return this.store.live_store.companies
  }

  awardAuction() {
    this.connector.publish(de_auction_award_command({
      //allocations: {},
      auction_id: this.auction.auction_id,
      round_number: this.selectedRoundNumber + ''
    }));
    this.$emit('close');
  }

  // get roundTraderCells(): DeRoundTraderElement[] {
  //   return this.store.live_store.de_auction.blotter?.round_traders || [];
  // }

  get rounds(): DeRoundElement[] {
    return this.auction.blotter.rounds;
  }

  get traders(): DeTraderElement[] {
    return this.auction.blotter.traders;
  }

  get round_trader_elements(): DeRoundTraderElement[] {
    return round_trader_elements_for_round(
      this.auction.blotter.round_traders,
      this.awardable_round
    )
  }

  cancel() {
    this.$emit('close');
    this.selectedRoundNumber = null;
  }

  get roundResults(): DeRoundResultVM[] {
    return this.auction.award_value.round_results.reverse()
  }

  get matches(): DeScenarioMatchVM[] {
    if (this.selectedRoundNumber === null) return [];
    const result = this.roundResults.find(result => result.round_number === this.selectedRoundNumber) as DeRoundResultVM;
    return result.matches;
  }

  get currentRoundEdges(): DeMatrixEdgeElement[] {
    return this.store.live_store.de_auction
      .matrix_last_round.edges.filter(edge => edge['r'] === this.selectedRoundNumber) || [];
  }

  // get relevantRounds() {
  //   return this.store.live_store.de_auction.blotter.rounds.slice(-3) || [];
  // }
  //
  // get relevantRoundNumbers() {
  //   return this.relevantRounds.map(round => round.round_number);
  // }
}
</script>

<style lang="less" scoped>

@import (reference) "../../../../au-styles/variables.less";

.DeAwardModal {
}
</style>


<!-- TODO: For now the system determines the award round, so probably we just need: -->
<!-- TODO: 1) order book  -->
<!-- TODO: 2) sankey / 3) match table master, detail by buyer or seller -->
<!-- TODO: 4) matrix with summary rows & cols / 5) supply demand chart -->

<!--    <div style="display: flex">-->
<!--      <RoundTotalTable-->
<!--        style="width: 600px; height: 700px"-->
<!--        :roundResults="roundResults"-->
<!--        :selectedRound.sync="selectedRoundNumber"-->
<!--      />-->

<!--      <AwardMatchesTable-->
<!--        style="width: 510px"-->
<!--        :height="700"-->
<!--        :matches="matches"-->
<!--      />-->

<!--      <div style="width: 400px; position: relative;" class="text-color-white">-->
<!--        <a-spin size="large" style="position: absolute; top: 240px; left: 180px"/>-->
<!--        <a-radio-group class="ml-1 mb-1" style="color: white" v-model="nonQuantityAxis">-->
<!--          Non quantity axis:-->
<!--          <a-radio :value="DeRoundChartNonQuantityAxis.PRICE">-->
<!--            Price-->
<!--          </a-radio>-->
<!--          <a-radio :value="DeRoundChartNonQuantityAxis.ROUND">-->
<!--            Round-->
<!--          </a-radio>-->
<!--        </a-radio-group>-->
<!--        <br>-->
<!--        <a-radio-group class="ml-1 mb-1" style="color: white" v-model="quantityAxis">-->
<!--          Quantity axis:-->
<!--          <a-radio :value="DeRoundChartQuantityAxis.Y">-->
<!--            Y-->
<!--          </a-radio>-->
<!--          <a-radio :value="DeRoundChartQuantityAxis.X">-->
<!--            X-->
<!--          </a-radio>-->
<!--        </a-radio-group>-->

<!--        &lt;!&ndash;        <DeRoundChart&ndash;&gt;-->
<!--        &lt;!&ndash;          :rounds="rounds"&ndash;&gt;-->
<!--        &lt;!&ndash;          :traders="traders"&ndash;&gt;-->
<!--        &lt;!&ndash;          :cells="roundTraderCells"&ndash;&gt;-->
<!--        &lt;!&ndash;          :width="400"&ndash;&gt;-->
<!--        &lt;!&ndash;          :height="300"&ndash;&gt;-->
<!--        &lt;!&ndash;          :non_quantity_axis="nonQuantityAxis"&ndash;&gt;-->
<!--        &lt;!&ndash;          :quantity_axis="quantityAxis"&ndash;&gt;-->
<!--        &lt;!&ndash;        />&ndash;&gt;-->

<!--        <DeSupplyDemandChartSvg-->
<!--          :rounds="rounds"-->
<!--          :width="400"-->
<!--          :height="340"-->
<!--          :quantity_axis="quantityAxis"-->
<!--        />-->
<!--      </div>-->
<!--    </div>-->
