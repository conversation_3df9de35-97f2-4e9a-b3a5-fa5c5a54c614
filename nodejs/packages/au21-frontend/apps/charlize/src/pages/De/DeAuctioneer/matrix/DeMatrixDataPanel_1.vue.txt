<template>
  <div class="DeMatrxDataPanel_1" :style="{
                height: height + 'px',
                width: width + 'px',
                color: this.au_text_color,
                overflow: 'hidden'
            }">

    <div class="_company">{{ shortname || '&nbsp;' }}</div>

    <div class="_row">
      <div class="_node-label">{{ label_max }}:</div>
      <div class="_node-data">{{ vol_max }}</div>
    </div>

    <div class="_row">
      <div class="_node-label">{{label_hatched}}:</div>
      <div class="_node-data">{{ vol_hatched }}</div>
    </div>

    <div class="_row">
      <div class="_node-label">{{label_solid}}:</div>
      <div class="_node-data">{{vol_solid}}</div>
    </div>

    <VolumeBar
      :style="{
         position:'absolute',
         left:'65px',
         top: '10px',
         height,
         width
      }"
      :height="height"
      :width="8"
      :vol_max="vol_max"
      :vol_hatched="vol_hatched"
      :vol_solid="vol_solid"
      :color="bar_color"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';
import VolumeBar from '../../../../ui-components/svg/VolumeBar.vue';

@Component({
  name: 'DeMatrxDataPanel_1',
  components: { VolumeBar }
})
export default class DeMatrxDataPanel_1 extends Vue {

  width = 80;
  height = 66;

  @Prop({ default: '' }) shortname: string;
  @Prop({ required: true }) label_max: string;
  @Prop({ required: true }) label_hatched: string;
  @Prop({ required: true }) label_solid: string;

  @Prop({ required: true }) vol_max: number;
  @Prop({ required: true }) vol_hatched: number;
  @Prop({ required: true }) vol_solid: number;

  @Prop({ required: true }) au_text_color: string;
  @Prop({ required: true }) bar_color: string;

}
</script>


<style lang="less" scoped>

.DeMatrxDataPanel_1 {
  font-size: 12px;
  font-weight: bold;
  line-height: normal;
  margin: 0;
  padding: 0;
  position: relative;

  ._company{
    font-size: 13px;
    height: 13px;
    margin-left: 2px;
  }

  ._row{
    //height: 13px;
    //width: 65px;
  }

  ._node-label {
    font-size: 11px;
    font-weight: normal;
    display: inline-block;
    height: 13px;
    left: 2px;
    overflow: hidden;
    position: relative;
    text-align: right;
    top: 0;
    width: 36px;
  }

  ._node-data {
    font-weight: bold;
    display: inline-block;
    height: 13px;
    left: 40px;
    overflow: hidden;
    text-align: right;
    width: 23px;
  }

}
</style>
