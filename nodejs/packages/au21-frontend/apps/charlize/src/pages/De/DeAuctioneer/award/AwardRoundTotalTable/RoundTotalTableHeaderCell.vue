<template>
  <div
    class="RoundTotalTableHeaderCell"
    :class="{'RoundTotalTableHeaderCell--selected': isRoundSelected}"
  >
    <a-button
      class="RoundTotalTableHeaderCell__button"
      @click="selectRound()"
      type="primary"
      size="small"
    >
      {{ `Round ${roundResult.round_number}` }}
    </a-button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeRoundResultVM} from '@au21-frontend/client-connector';
import {DeRoundTotalTableCellParams, DeRoundTotalTableRow} from './RoundTotalTable.types';
import RoundTotalTable from './RoundTotalTable.vue';

@Component({
  name: 'RoundTotalTableHeaderCell',
})
export default class RoundTotalTableHeaderCell extends Vue {
  params = null

  get roundTable(): RoundTotalTable {
    return this.$parent.$parent.$parent as RoundTotalTable
  }

  get isRoundSelected(): boolean {
    return this.roundTable.selectedRound === this.roundResult.round_number
  }

  selectRound() {
    this.roundTable.setSelectedRound(this.roundResult.round_number)
  }

  get row(): DeRoundTotalTableRow {
    return this.params.data
  }

  get column(): DeRoundTotalTableCellParams {
    return this.params.column.colDef.cellRendererParams
  }

  get roundResult(): DeRoundResultVM {
    return this.column!.roundResult
  }
}
</script>


<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.RoundTotalTableHeaderCell {
  font-size: 16px;
  padding: 0 !important;
  margin-right: auto;
  margin-left: auto;

  &--selected {
    .RoundTotalTableHeaderCell__button {
      background-color: red;
      color: white;
    }
  }
}
</style>
