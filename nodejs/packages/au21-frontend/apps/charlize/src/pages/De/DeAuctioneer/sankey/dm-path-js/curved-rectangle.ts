import Connector from './connector';
import {average} from './ops';

export default function (o: {
  topleft: number[];
  topright: number[];
  bottomleft: number[];
  bottomright: number[];
}) {
  const topCurve = Connector({start: o.topleft, end: o.topright}).path
  const bottomCurve = Connector({
    start: o.bottomright,
    end: o.bottomleft,
  }).path
  const path = topCurve.connect(bottomCurve).closepath()
  const centroid = average([o.topleft, o.topright, o.bottomleft, o.bottomright])

  return {
    path: path,
    centroid: centroid,
    coordinates: {
      topleft: o.topleft,
      topright: o.topright,
      bottomleft: o.bottomleft,
      bottomright: o.bottomright,
    }
  }
}
