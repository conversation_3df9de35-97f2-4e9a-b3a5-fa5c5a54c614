<template>
  <div class="DeAuctioneerToolbarFlowController">
    <ToolbarButton
      @click="emitWithDebounce('reset')"
      icon="left-circle"
    >
      Reset
    </ToolbarButton>
    <ToolbarButton
      icon="dollar"
      @click="emitWithDebounce('showPrice')"
      :disabled="!priceEnabled"
    >
      Price
    </ToolbarButton>
    <ToolbarButton
      icon="play-circle"
      @click="emitWithDebounce('play')"
      :disabled="!playEnabled"
    >
      Go
    </ToolbarButton>
    <ToolbarButton
      icon="pause-circle"
      @click="emitWithDebounce('pause')"
      :disabled="!pauseEnabled"
    >
      Pause
    </ToolbarButton>
    <ToolbarButton
      icon="close-circle"
      @click="emitWithDebounce('stop')"
      :disabled="!stopEnabled"
    >
      End
    </ToolbarButton>
    <ToolbarButton
      icon="right-circle"
      @click="emitWithDebounce('next')"
      :disabled="!nextEnabled"
    >
      Next
    </ToolbarButton>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import ToolbarButton from './ToolbarButton.vue';
import { DeAuctionState } from '@au21-frontend/client-connector';


@Component({
  components: { ToolbarButton },
})
export default class DeAuctioneerToolbarFlowController extends Vue {
  @Prop({ required: true }) auctionState: DeAuctionState

  // To prevent double-click.
  disableEvents = false

  emitWithDebounce(eventType: string): void {
    if (this.disableEvents) {
      return
    }

    this.disableEvents = true
    setTimeout(() => {
      this.disableEvents = false
    })

    this.$emit(eventType)
  }

  get resetEnabled(): boolean {
    return false
  }

  get priceEnabled(): boolean {
    return ['AUCTION_INIT', 'ROUND_SETUP', 'ROUND_READY'].includes(this.commonStatus.auctionState)
  }

  get playEnabled(): boolean {
    return (
      this.commonStatus.auctionState === 'ROUND_READY' ||
      this.commonStatus.auctionState === 'ROUND_PAUSED'
    )
  }

  get pauseEnabled(): boolean {
    return this.commonStatus.auctionState === 'ROUND_RUNNING'
  }

  get stopEnabled(): boolean {
    return this.commonStatus.auctionState === 'ROUND_PAUSED'
  }

  get nextEnabled(): boolean {
    return this.commonStatus.auctionState === 'ROUND_CLOSED'
  }
}
</script>

<style lang="less" scoped>
.DeAuctioneerToolbarFlowController {

}
</style>
