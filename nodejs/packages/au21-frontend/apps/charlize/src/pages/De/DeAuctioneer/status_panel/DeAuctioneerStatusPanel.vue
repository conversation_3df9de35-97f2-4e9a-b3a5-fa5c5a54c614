<template>
  <div class="DeAuctioneerStatusPanel">

    <div class="_common_state">
      <div class="_heading au-label">
        State
      </div>
      <div class="_common_state_panel">
        <DeCommonStatusPanel
          :common_state="common_status.common_state"
          :common_state_text="common_status.common_state_text"
        />
      </div>
    </div>

<!--    <div class="_vertical-line"></div>-->

    <div class="_auctioneer_info">
      <div class="_heading au-label _auctioneer_info_header">
        Auctioneer Info
      </div>
      <div class="_auctioneer_info_state">
        <DeAuctioneerInfoPanel
          style="margin: 0 10px;"
          :auctioneer_state="auctioneer_status.auctioneer_state"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeAuctioneerStatusValue, DeCommonStatusValue} from "@au21-frontend/client-connector";
import DeAuctioneerInfoPanel from "../round_info/DeAuctioneerInfoPanel/DeAuctioneerInfoPanel.vue";
import DeCommonStatusPanel from "../../components/common-status/DeCommonStatusPanel.vue";

@Component({
  name: 'DeAuctioneerStatusPanel',
  components: {DeAuctioneerInfoPanel, DeCommonStatusPanel}
})
export default class DeAuctioneerStatusPanel extends Vue {
  @Prop({required: true}) common_status: DeCommonStatusValue;
  @Prop({required: true}) auctioneer_status: DeAuctioneerStatusValue;

}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.DeAuctioneerStatusPanel {
  background-color: @au-background !important;
  //border: 1px solid red;
  display: flex;
  float: left;
  height: 66px !important;
  overflow: hidden;
  //  position: relative;
  width: 300px;


  ._auctioneer_info {
    float: left;
    //   height: 81px;
    margin: 0;
    padding: 0;
  }


  ._auctioneer_info_header {
    position: relative;
    top: -2px;
  }

  ._auctioneer_info_state {
    height: 40px;
    margin-left: 5px;
    //   overflow: hidden;
    position: relative;
    text-align: center;
    top: -2px;
    word-wrap: break-word;
    width: 125px;
  }

  ._common_state {
    //border: 1px solid red;
    float: left;
    margin: 0 5px;
    padding: 0;
    //   overflow: hidden;
    position: relative;
    top: -3px;
  }

  ._common_state_panel {
    //border: 1px solid green;
    position: relative;
    text-align: center;
  }

  ._heading {
    margin: 0;
    padding: 0;
    text-align: center;
    width: 100%;
  }

  ._vertical-line {
    background-color: hsl(220, 10%, 50%); // @au_beige; // !important;
    float: left;
    height: 50px;
    position: relative;
    top: 7px;
    width: 0.5px;
  }

}
</style>
