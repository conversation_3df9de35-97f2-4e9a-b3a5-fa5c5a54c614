<template>
  <div class="DeRoundC<PERSON>"/>
</template>

<script lang="ts">
import type {Data, Layout, newPlot} from 'plotly.js';
import Plotly from 'plotly.js-dist-min';

import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import {DeRoundElement, DeRoundTraderElement, DeTraderElement, OrderType} from '@au21-frontend/client-connector';
import {sortBy} from 'lodash';
import {DeRoundChartNonQuantityAxis, DeRoundChartQuantityAxis} from './DeRoundChart-types';

const getColId = (round: number) => `round-${round}`;

/**
 * two toggles:
 * - 1) quantity is X or Y axis
 * - 2) other axis is Price or Round
 */

const colorScale = [
  ['0', '#a5ca56'],
  ['1', '#73c9e1']
];

// @archieved Used plotly, which we disposed of, so was archieved
@Component({
  name: 'DeRound<PERSON><PERSON>'
})
export default class DeRound<PERSON>hart extends Vue {
  @Prop({type: Array, required: true}) cells: DeRoundTraderElement[];
  @Prop({type: Array, required: true}) rounds: DeRoundElement[];
  @Prop({type: Array, required: true}) traders: DeTraderElement[];
  @Prop({type: Number, required: true}) width: number;
  @Prop({type: Number, required: true}) height: number;
  @Prop({required: true}) quantity_axis: DeRoundChartQuantityAxis;
  @Prop({required: true}) non_quantity_axis: DeRoundChartNonQuantityAxis;

  generateXYZForCells(cells: DeRoundTraderElement[], index: number): {
    x: [number, number],
    y: [number, number][],
    z: [number, number][],
    name: string,
  } {
    const rtes = cells[0];

    const nonQuantityValues = cells.map(cell => this.non_quantity_axis === DeRoundChartNonQuantityAxis.ROUND ? [cell.round, cell.round] : [this.roundsByNumber[cell.round].round_price, this.roundsByNumber[cell.round].round_price])
    const quantityValues = cells.map(cell => [cell.quantity_int * (cell.order_type == OrderType.BUY ? 1 : -1), cell.quantity_int * (cell.order_type == OrderType.BUY ? 1 : -1)])

    return {
      x: [index * 3, index * 3 + 2],
      y: (this.quantity_axis === DeRoundChartQuantityAxis.Y ? quantityValues : nonQuantityValues) as any,
      z: (this.quantity_axis === DeRoundChartQuantityAxis.Y ? nonQuantityValues : quantityValues) as any,
      name: rtes.order_submitted_by
    };
  }


  get traderCells(): Record<string, DeRoundTraderElement[]> {
    const result = {};
    this.cells.forEach((cell: DeRoundTraderElement) => {
      if (!result[cell.cid]) {
        result[cell.cid] = [];
      }
      result[cell.cid].push(cell);
    });
    for (const index in result) {
      result[index] = sortBy(result[index], 'round');
    }
    return result;
  }

  getRoundTrace(traderElement: DeTraderElement, index: number) {
    const traderCells = this.traderCells[traderElement.company_id];

    return {
      ...this.generateXYZForCells(traderCells, index),
      colorscale: colorScale,
      type: 'surface',
      showscale: false
    };
  }

  get roundsByNumber(): Record<string, DeRoundElement> {
    const result = {};
    this.rounds.forEach(round => result[round.round_number] = round);
    return result;
  }

  mounted() {
    this.redrawPlot();
  }

  @Watch('quantity_axis')
  @Watch('non_quantity_axis')
  redrawPlot() {
    const data = this.traders.map((trader, index) => this.getRoundTrace(trader, index));

    const layout: Partial<Layout> = {
      paper_bgcolor: 'rgb(45, 52, 54)',
      plot_bgcolor: 'rgba(255,255,255,0)',
      showlegend: false,
      autosize: true,
      width: this.width,
      height: this.height,
      margin: {
        t: 1,
        b: 1,
        l: 1,
        r: 1,
        pad: 0
      },
      scene: {
        xaxis: {title: 'Trader', color: 'white'},
        yaxis: {title: this.non_quantity_axis === DeRoundChartNonQuantityAxis.ROUND ? 'Round' : 'Price', color: 'white'},
        zaxis: {title: 'Quantity', color: 'white'}
      }
    };

    (Plotly.newPlot as typeof newPlot)(this.$el as HTMLElement, data as Data[], layout);
  }
}
</script>

<style lang="less" scoped>

</style>
