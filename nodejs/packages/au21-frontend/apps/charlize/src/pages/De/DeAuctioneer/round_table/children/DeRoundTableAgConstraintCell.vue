<template>
  <div class="DeRoundTableAgConstraintCell">
    <DeOrderConstraintsBar
      v-if="rowType === 'TRADER'"
      style="left: -3px; top: -3px"
      :width="140"
      :height="12"
      :tick_font_size="9"
      :constraints="constraints"
      :order_type="order_type"
      :order_quantity='order_quantity'
    />
    <DeSupplyDemandChartSvg
      v-if="rowType === 'FOOTER'"
      :rounds="rounds"
      :width="130"
      :height="85"
      dense
      :quantity_axis="DeRoundChartQuantityAxis.Y"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeBlotterRowAg, DeRoundTableAgRowType, DeRoundTableConstraintsParams,} from '../de-round-table-ag-helpers';
import TraderOnlineIcon from '../../../../../ui-components/OnlineIcon/TraderOnlineIcon.vue';
import type DeRoundTable from '../DeRoundTable.vue';
import DeOrderConstraintsBar from '../../../DeTrader/constraints/DeOrderConstraintsBar.vue';
import {DeBidConstraints, DeRoundTraderElement, OrderType} from '@au21-frontend/client-connector';
import DeSupplyDemandChartSvg from '../../award/DeSupplyDemandChart/DeSupplyDemandChartSvg.vue';
import {DeRoundChartQuantityAxis} from '../../award/DeRoundChart/DeRoundChart-types';

@Component({
  components: { DeSupplyDemandChartSvg, DeOrderConstraintsBar, TraderOnlineIcon},
})
export default class DeRoundTableAgConstraintCell extends Vue {
  params = null

  DeRoundChartQuantityAxis = DeRoundChartQuantityAxis

  get table(): DeRoundTable {
    return (this.$parent.$parent.$parent as DeRoundTable)
  }

  get row(): DeBlotterRowAg {
    return this.params?.data
  }

  get rowType(): DeRoundTableAgRowType {
    return this.row.rowType;
  }

  get cellParams (): DeRoundTableConstraintsParams {
    return this.params.column.colDef.cellRendererParams
  }

  get rounds () {
    return this.cellParams.rounds
  }

  get selected_round_trader():DeRoundTraderElement | null{
    return this.row?.selected_round_trader
  }
  get constraints():DeBidConstraints | null{
    return this.selected_round_trader?.constraints
  }

  get order_quantity():number{
    return this.selected_round_trader?.quantity_int
  }

  get order_type():OrderType{
    return this.selected_round_trader?.order_type
  }
}
</script>


<style lang="less" scoped>
.DeRoundTableAgConstraintCell {
  overflow: hidden;
  padding: 3px;
  position: relative;
  width: 100%;
}
</style>
