<template>
  <ToolbarButton
    v-if="use_toolbar_button"
    @click="onButtonClick()"
  >
    {{ label }}
    <AuctionSettingsModal
      v-if="showAuctionSettingsModal"
      :settings.sync="deSettings"
      @close="cancelAuctionEdit()"
      :crud="crud"
      @saveAuction="saveAuction()"
    />
  </ToolbarButton>
  <a-button
    v-else
    :is="component"
    :icon="icon"
    type="primary"
    size="small"
    @click="onButtonClick()"
  >
    {{ label }}
    <AuctionSettingsModal
      v-if="showAuctionSettingsModal"
      :settings.sync="deSettings"
      @close="cancelAuctionEdit()"
      :crud="crud"
      @saveAuction="saveAuction()"
      :store="store"
    />
  </a-button>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuctionSettingsModal from './AuctionSettingsModal.vue';
import {Container} from 'typescript-ioc';
import {Crud, de_auction_save_command, DeSettingsValue, SocketConnector} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {CharlizeClient} from '../../../../services/connector/CharlizeClient';
import {cloneDeep} from 'lodash';
import {
  convert_DeSettingsValue_to_DeAuctionSaveCommand,
  createDefault__DeSettingsValue
} from '../../../../demo-helpers/DeSettingsValue.helper';
import {dateTimeValue_to_moment, moment_to_DateTimeValue} from '../../../../entity-helpers/DateTimeValue';
import ToolbarButton from "../toolbar/ToolbarButton.vue";

@Component({
  components: {
    AuctionSettingsModal,
    ToolbarButton
  }
})
export default class AuctionSettingsButton extends Vue {
  @Prop({required: true}) store: CharlizeStore;
  @Prop({default: 'a-button'}) component: any;
  @Prop({default: false}) use_toolbar_button:boolean;
  @Prop({type: String}) icon: string;
  @Prop({required: true}) crud: Crud;
  @Prop({required: true}) label: string;

  charlizeClient = Container.get(CharlizeClient);
  connector = Container.get(SocketConnector);

  showAuctionSettingsModal = false;
  deSettings: DeSettingsValue | null = null; // Note this is not the same as the DeAuctionSaveCommand ! (and it shouldn't be)
  auction_id: string | null;

  created() {
    this.charlizeClient.subscribe('CommandSucceeded', this.cancelAuctionEdit);
  }

  beforeDestroy() {
    this.charlizeClient.unsubscribe('CommandSucceeded', this.cancelAuctionEdit);
  }

  onButtonClick(): void {
    this.crud === Crud.CREATE ?
      this.show_modal_for_create_auction() :
      this.show_modal_for_edit_auction();
  }

  show_modal_for_create_auction() {
    this.auction_id = null;
    this.deSettings = {
      ...createDefault__DeSettingsValue(),
      starting_time: moment_to_DateTimeValue(
        dateTimeValue_to_moment(this.store.live_store.time.date_time)
          .add(2, 'minutes'),
        true
      )
    };
    this.showAuctionSettingsModal = true;
  }

  show_modal_for_edit_auction() {
    this.auction_id = this.store.live_store.de_auction.auction_id;
    this.deSettings = cloneDeep(this.store.live_store.de_auction.settings);
    this.showAuctionSettingsModal = true;
  }

  cancelAuctionEdit() {
    this.showAuctionSettingsModal = false;
    this.deSettings = null;
  }

  saveAuction() {
    this.connector.publish(de_auction_save_command(
      convert_DeSettingsValue_to_DeAuctionSaveCommand(
        this.auction_id,
        this.deSettings
      )
    ));
  }

}
</script>

<style lang="less" scoped>
.AuctionSettingsButton {

}
</style>
