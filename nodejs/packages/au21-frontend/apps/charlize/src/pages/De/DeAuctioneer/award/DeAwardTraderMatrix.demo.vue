<template>
  <VbDemo>
    <VbCard style="width: 500px">
      <DeAwardTraderMatrix
        :companies="companies"
        :edges="edges"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeAwardTraderMatrix from './DeAwardTraderMatrix.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__DeMatrixEdgeElement} from '../../../../demo-helpers/DeMatrixEdgeElement.helper';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';

@Component({
  components: {DeAwardTraderMatrix},
})
export default class DeAwardTraderMatrixDemo extends Vue {
  companies = createMultipleByClosure(createDemo__CompanyElement, 20)
  selectedCompanyId = null
  edges = (() => {
    const nodes = []
    this.companies.forEach(companyA => {
      this.companies.forEach(companyB => {
        if (companyA === companyB) {
          return
        }
        nodes.push(createDemo__DeMatrixEdgeElement(0, companyA.id, companyB.id))
      })
    })
    return nodes
  })()
}
</script>
