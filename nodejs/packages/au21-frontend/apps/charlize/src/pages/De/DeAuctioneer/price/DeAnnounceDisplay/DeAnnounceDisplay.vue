<template>

  <TwoStateDisplay
    :width="55"
    first_label="NO"
    second_label="YES"
    :first_color="first_color"
    :second_color="second_color"
  />
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeAuctioneerStatusValue} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import TwoStateDisplay from '../../../../common/components/TwoStateDisplay/TwoStateDisplay.vue';
import {AuColors} from '../../../../../au-styles/AuColors';

@Component({
  components: {TwoStateDisplay}
})
export default class DeAnnounceDisplay extends Vue {

  @Prop({required: true}) auctioneerStatus: DeAuctioneerStatusValue;

  colors = Container.get(AuColors);

  // unAnnouncedColor: { [e in DeTimeState]: () => string } = {
  //   'BEFORE_ANNOUNCE_TIME': () =>
  //     this.isEngaged ? this.colors.NORMAL : this.colors.WARNING,
  //   'BEFORE_START_TIME': () =>
  //     this.isEngaged ? this.colors.NORMAL : this.colors.ERROR, // TODO: ? warn ?
  //   'AFTER_START_TIME': () =>
  //     this.isEngaged ? this.colors.NORMAL : this.colors.ERROR
  // };
  //
  // get isEngaged(): boolean {
  //   return this.auctioneerStatus.autopilot == AutopilotMode.ENGAGED;
  // }

  get first_color() {
    if (!this.auctioneerStatus.announced) {
      return 'orange' // this.colors.logo_orange; // this.colors.prime_color // this.colors.WARNING // this.unAnnouncedColor[this.auctioneerStatus.time_state]();
    }
    return 'white' // this.colors.disabled;
  }

  get second_color() {
    if (this.auctioneerStatus.announced) {
      //   return this.colors.NORMAL;
      return 'green' // this.colors.auHighlight
    }
    //return this.colors.DISABLED;
    return 'white' // this.colors.disabled;
  }


}
</script>

<style scoped>
</style>
