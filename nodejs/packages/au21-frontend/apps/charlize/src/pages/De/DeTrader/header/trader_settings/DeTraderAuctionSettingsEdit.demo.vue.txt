<template>
  <VbDemo>
    <VbCard>
      <DeTraderDeSettings
        :deSettings="deSettings"
        :trader="trader"
      />
      <pre>{{ JSON.stringify(deSettings, null, 2) }}</pre>
      <pre>{{ JSON.stringify(trader, null, 2) }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script>
import DeTraderDeSettings from './DeTraderDeSettingsEdit.vue.txt'
import { createDemo__DeSettingsValue } from '../../../../../demo-helpers/DeSettingsValue.helper'
import { createDemo__DeTraderElement } from '../../../../../demo-helpers/DeRoundTable.helper'


export default {
  components: {
    DeTraderDeSettings,
  },
  data () {
    return {
      deSettings: createDemo__DeSettingsValue(),
      trader: createDemo__DeTraderElement(),
    }
  },
}
</script>
