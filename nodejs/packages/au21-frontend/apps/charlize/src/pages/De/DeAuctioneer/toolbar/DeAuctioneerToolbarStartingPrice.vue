<template>
  <div class="DeAuctioneerToolbarStartingPrice">
    <div
      class="au-label"
      style="position: relative; left: 3px; display: inline-block">Starting price:
    </div>

    <a-button-group style="left: 3px">
      <ToolbarButton
        v-test:set_starting_price
        @click="emitWithDebounce(DeFlowControlType.SET_STARTING_PRICE)"
        :disabled="is_disabled(DeFlowControlType.SET_STARTING_PRICE)"
      >
        Set
      </ToolbarButton>
      <ToolbarButton
        v-test:announce_starting_price
        @click="emitWithDebounce(DeFlowControlType.ANNOUNCE_STARTING_PRICE)"
        :disabled="is_disabled(DeFlowControlType.ANNOUNCE_STARTING_PRICE)"
      >
        Announce
      </ToolbarButton>
    </a-button-group>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import ToolbarButton from "./ToolbarButton.vue";
import AuSectionBorder from "../../../../ui-components/AuSectionBorder.vue";
import {DeAuctionValue, DeFlowControlType} from '@au21-frontend/client-connector';

@Component({
  components: {
    AuSectionBorder,
    ToolbarButton
  }
})
export default class DeAuctioneerToolbarStartingPrice extends Vue {

  @Prop({required: true}) auction: DeAuctionValue | null;

  DeFlowControlType = DeFlowControlType;

  is_disabled(c: DeFlowControlType): boolean {
    return this.auction.auctioneer_status.controls[c] === false;
  }

  // To prevent double-click.
  disableEvents = false;

  // emitWithDebounce(eventType: keyof typeof DeFlowControlType): void {
  emitWithDebounce(eventType: DeFlowControlType): void {
    if (this.disableEvents) {
      return;
    }

    this.disableEvents = true;
    setTimeout(() => {
      this.disableEvents = false;
    });

    this.emitOnControl(eventType);
  }

  emitOnControl(eventType: DeFlowControlType) {
    this.$emit('onControl', eventType);
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.DeAuctioneerToolbarStartingPrice {
  //display: flex;
  //flex-wrap: nowrap;
  //
  &__section {
    display: flex;
    //flex-wrap: nowrap;
    //align-items: center;
    //border: 0;
    //margin: 0;
    //padding: 0;
  }
}

</style>
