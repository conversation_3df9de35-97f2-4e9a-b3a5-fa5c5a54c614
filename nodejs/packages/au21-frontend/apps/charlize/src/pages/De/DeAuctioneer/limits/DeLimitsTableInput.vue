<template>
  <div class="DeLimitsTableInput">
    <div
      class="_default"
      :class="{'-readonly': readonly}"
      v-if="editedValue === null"
      @click="edit"
    >
      {{ value }}
    </div>
    <NumberInput
      class="_input"
      ref="numberInput"
      v-else
      style="width: 100%"
      v-model="editedValue"
      :decimalPlaces="2"
      :height="20"
      @blur="saveValue"
      @enter="saveValue"
      @escape="cancel"
    />
  </div>
</template>

<script lang="ts">
import {Component, Ref, Vue} from 'vue-property-decorator';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';
import {DeLimitsTableCellParams, DeLimitsTableRow} from './DeLimitsTable.types';
import {sleep} from '@au21-frontend/utils';

@Component({
  name: 'DeLimitsTableInput',
  components: {NumberInput},
})
export default class DeLimitsTableInput extends Vue {
  params = null
  editedValue = null
  @Ref() readonly numberInput: NumberInput

  get row(): DeLimitsTableRow {
    return this.params.data
  }

  get value() {
    return this.row[this.cellParams.field]
  }

  async edit() {
    if (this.readonly) {
      return
    }
    this.editedValue = this.value
    await sleep()
    this.numberInput?.select()
  }

  async saveValue() {
    if (this.editedValue !== null && this.value !== this.editedValue) {
      this.cellParams.saveValue(this.editedValue, this.cellParams.field)
    }
    await sleep()
    this.editedValue = null
  }

  async cancel() {
    this.editedValue = null
    await sleep()
    window.blur()
  }

  get readonly() {
    return this.cellParams.getReadonly()
  }

  get field() {
    return this.cellParams.field
  }

  get cellParams(): DeLimitsTableCellParams {
    return this.params.column.colDef.cellRendererParams;
  }
}
</script>

<style lang="less" scoped>
.DeLimitsTableInput {

  padding: 0 50px;

  ._default {
    cursor: pointer;
    padding: 0 3px;
    text-align: right;
  }

  .-readonly {
    cursor: inherit;
  }
}
</style>
