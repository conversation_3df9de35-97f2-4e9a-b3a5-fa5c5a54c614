<template>
  <VbDemo>
    <VbCard>
      <CrudToggle v-model="crud"/>
      <AuctionSettingsEdit
        :settings="settings"
        :store="store"
        :crud="crud"
      />
      <pre>{{ JSON.stringify(settings, null, 2) }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import AuctionSettingsEdit from './AuctionSettingsEdit.vue';
import CrudToggle from '../../../../demo-components/CrudToggle.vue';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';
import {Crud} from '@au21-frontend/client-connector';
import {createDemo__store_for_auctioneer} from '../../../../demo-helpers/CharlizeStore.helper';

@Component({
  components: {
    CrudToggle,
    AuctionSettingsEdit,
  },
})
export default class AuctionSettingsEditDemo extends Vue {
  settings = createDefault__DeSettingsValue()
  store = createDemo__store_for_auctioneer()
  crud = Crud.CREATE
}
</script>


<style lang="less" scoped>

</style>
