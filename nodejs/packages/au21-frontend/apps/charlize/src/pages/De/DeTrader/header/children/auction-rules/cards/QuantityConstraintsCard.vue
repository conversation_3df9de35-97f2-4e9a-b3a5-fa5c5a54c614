<template>
  <div class="QuantityConstraintsBarCard pl-3"
       @click="click">
    <ul>
      <b>Buy Quantity:</b>
      <VueTextTransition tag="li" name="fade" :show="count > 0" :interval="1">
        You can't buy more at a higher price,
      </VueTextTransition>
      <ul>
      <VueTextTransition tag="li" name="fade" :show="count > 1" :interval="1">
        or buy less at a lower price.
      </VueTextTransition>
      </ul>
      <br>
      <b>Sell Quantity:</b>
      <VueTextTransition tag="li" name="fade" :show="count > 2" :interval="1">
        You can't sell more at a lower price,
      </VueTextTransition>
      <ul>
        <VueTextTransition tag="li" name="fade" :show="count > 3" :interval="1">
          or sell less at a higher price.
        </VueTextTransition>
      </ul>
    </ul>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import VueTextTransition from 'vue-text-transition'

Vue.component('VueTextTransition', VueTextTransition)


@Component({
  name: 'QuantityConstraintsCard',
})
export default class QuantityConstraintsCard extends Vue {
  count = 0

  click() {
    this.count++
    console.log({count: this.count})
  }
}
</script>

<style lang="less">
.QuantityConstraintsCard {

}

.v--vtt-fade {
  will-change: opacity;
  transition: opacity 0.1s ease-in-out;
}

.v--vtt-fade_visible {
  opacity: 1;
}

.v--vtt-fade_hidden {
  opacity: 0.38;
}
</style>
