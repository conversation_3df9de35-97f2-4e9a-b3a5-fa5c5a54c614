<template>
  <div class="EndingRuleCard pl-3">
    <ul>
      <li>The auction ends if demand = supply in any round.</li>
      <li>If the auction proceeds through the price increament and decrement
        phase without reaching equilibrium
        then the auction will be awarded at the round with the lowest
        supply-demand difference.
      </li>
      <li>If more than one round has the same supply-demand difference, then
        the auction will be awarded at the
        round
        which yields the greatest match, based on trader credit constraints.
      </li>
      <li>If more than one round meets that criteria then the auction will
        be awarded at the earliest round.
      </li>
    </ul>
  </div>

</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({
  name: 'EndingRuleCard',
})
export default class EndingRuleCard extends Vue {

}
</script>

<style lang="less" scoped>
.EndingRuleCard {

}
</style>
