<template>
  <div class="DeAuctionRules" :style="`height:${height}px`">
    <div class="_navigation">
      <div
        class="_navigation_item"
        v-for="page in pages"
        :key="page.name"
        :class="{'_navigation_item-active': currentPage === page}"
        @click="selectPage(page.name)"
      >
        {{ page.title }}
      </div>
    </div>

    <div class="_content">
      <div class="_content_header">
        <div style="display: inline-block">
          {{ currentPage.title }}
        </div>
        <div style="float: right">
          <a-button-group>
            <a-button @click="selectPage(navigationPages.previous)" :disabled="!navigationPages.previous"> <</a-button>
            <a-button @click="selectPage(navigationPages.next)" :disabled="!navigationPages.next"> ></a-button>
          </a-button-group>
        </div>
      </div>
      <component
        class="_content_card"
        :is="currentPage.component"
        v-bind="currentPage.props"
        @navigate="navigate"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AuScreen} from '../../../../../../plugins/screen-plugin/AuScreen';
import ActivityFeedbackRuleCard from './cards/ActivityFeedbackRuleCard.vue';
import DefaultOrderRuleCard from './cards/DefaultOrderRuleCard.vue';
import EndingRuleCard from './cards/EndingRuleCard.vue';
import PriceDirectionRuleCard from './cards/PriceDirectionRuleCard.vue';
import RulesOverviewCard from './cards/RulesOverviewCard.vue';
import ConstraintsBarCard from './cards/ConstraintsBarCard.vue';
import QuantityConstraintsCard from './cards/QuantityConstraintsCard.vue';
import MandatoryOrderRuleCard from './cards/MandatoryOrderRuleCard.vue';
import {DeAuctionRulesPageName} from './DeAuctionRules.types';
import {DeSettingsValue} from '@au21-frontend/client-connector';
import BuySellSwitchingCard from "./cards/BuySellSwitchingCard.vue";

type DeAuctionRulesPage = {
  name: DeAuctionRulesPageName,
  component: any,
  title: string,
  props?: Record<string, any>
}

@Component({
  name: 'DeAuctionRules',
})
export default class DeAuctionRules extends Vue {
  @Prop({required: true}) de_settings_value: DeSettingsValue;

  screen = new AuScreen() // same for auctioneer or trader
  selectedPageName = DeAuctionRulesPageName.OVERVIEW;

  selectPage(name: DeAuctionRulesPageName) {
    this.selectedPageName = name
  }

  get currentPage(): DeAuctionRulesPage {
    return this.pages.find(page => page.name === this.selectedPageName);
  }

  get navigationPages(): { previous?: DeAuctionRulesPageName, next?: DeAuctionRulesPageName } {
    const index = this.pages.indexOf(this.currentPage);

    return {
      previous: this.pages[index - 1]?.name,
      next: this.pages[index + 1]?.name,
    };
  }

  get pages(): DeAuctionRulesPage[] {
    return [
      {
        name: DeAuctionRulesPageName.OVERVIEW,
        title: 'Overview',
        component: RulesOverviewCard,
      },
      {
        name: DeAuctionRulesPageName.PRICE_DIRECTION,
        component: PriceDirectionRuleCard,
        title: 'Price Direction Rule',
        props: {
          price_change_initial: this.de_settings_value.price_change_initial,
          price_change_post_reversal: this.de_settings_value.price_change_post_reversal,
          price_units: this.de_settings_value.price_label,
        },
      },
      {
        name: DeAuctionRulesPageName.QUANTITY_CONSTRAINTS,
        component: QuantityConstraintsCard,
        title: 'Price Quantity Constraints',
      },
      {
        name: DeAuctionRulesPageName.BUY_SELL_SWITCHING,
        component: BuySellSwitchingCard,
        title: 'Buy Sell Switching',
      },
      // {
      //   name: DeAuctionRulesPageName.QUANTITY_CONSTRAINTS,
      //   component: QuantityConstraintsRuleCard,
      //   title: 'Quantity Constraints Rules',
      // },
      {
        name: DeAuctionRulesPageName.QUANTITY_CONSTRAINTS_BAR,
        title: 'Constraints Bar',
        component: ConstraintsBarCard,
      },
      {
        name: DeAuctionRulesPageName.DEFAULT_ORDERS,
        title: 'Default Orders',
        component: DefaultOrderRuleCard,
      },
      {
        name: DeAuctionRulesPageName.MANDATORY_ORDERS,
        title: 'Mandatory Orders',
        component: MandatoryOrderRuleCard,
      },
      {
        name: DeAuctionRulesPageName.ACTIVITY_FEEDBACK,
        title: 'Activity Feedback',
        component: ActivityFeedbackRuleCard,
        props: {
          de_settings_value: this.de_settings_value
        }
      },
      {
        name: DeAuctionRulesPageName.ENDING_RULE,
        title: 'Ending Rule',
        component: EndingRuleCard,
      },
    ];
  }

  get height(): number {
    return this.screen.page_height - 100;
  }

  navigate(pageName: DeAuctionRulesPageName): void {
    this.selectedPageName = pageName
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../../au-styles/variables.less";



.DeAuctionRules {
  background-color: @au-background;
  color: @au-label-color;
  display: flex;
  overflow-y: auto;
  padding: 4px;
  white-space: normal;
  width: 100%;

  /deep/ div{
    color: @au-label-color;
  }

  /deep/ p{
    color: @au-label-color;
  }

  /deep/ ul{
    color: @au-label-color;
  }

  /deep/ li{
    color: @au-label-color;
  }


  ._navigation {
    flex: 200px 0 0;
    border-right: @au-background-light solid 1px;
    padding-right: 5px;

    &_item {
      cursor: pointer;
      display: block;
      font-size: 15px;
      padding: 2px;
      color: @au-label-color;

      &-active {
        background-color: @au-background-light;
      }
    }
  }

  ._content {
    position: relative;
    background-color: @au-background;
    flex-grow: 1;
    height: 100%;
    overflow-y: scroll;
    width: 730px;

    &_header {
      position: sticky;
      top: 0;
      font-size: 20px !important;
      font-weight: bold;
      padding: 10px 20px;
      width: 100%;
      background-color: @au-background;
    }

    &_card {
      color: hsl(0, 0%, 69%);
      padding: 4px 14px;
      line-height: 1.5em;
      font-size: 15px;

      /deep/ ul {
        list-style-type: square;
        margin-left: 5px;
      }
    }
  }
}
</style>
