<template>
  <a-modal
    class="DePriceModal"
    title="Set Starting Price"
    @cancel="cancel()"
    visible
    closable
    centered
    width="320px"
  >
    <!--    <div class="DePriceModal__form-item">-->
    <!--      <label class="DePriceModal__label">Round number:&nbsp;</label>-->
    <!--      <NumberInput-->
    <!--        class="DePriceModal__input"-->
    <!--        :value="round_number"-->
    <!--        output-->
    <!--      />-->
    <!--    </div>-->

    <div class="DePriceModal__form-item">
      <label class="DePriceModal__label">Current starting price
        ({{ price_label }}):&nbsp;</label>
      <NumberInput
        class="DePriceModal__input"
        :value="current_starting_price"
        :decimalPlaces="decimalPlaces"
        output
      />
    </div>

    <div class="DePriceModal__form-item">
      <label class="DePriceModal__label">New starting price
        ({{ price_label }}):&nbsp;</label>
      <NumberInput
        v-test:starting_price
        class="DePriceModal__input"
        v-model="new_starting_price"
        :decimalPlaces="decimalPlaces"
      />
    </div>

    <a-button
      slot="footer"
      type="primary"
      class="au-btn"
      @click="cancel()"
    >
      Cancel
    </a-button>

    <a-button
      slot="footer"
      class="au-btn"
      type="primary"
      v-test:save_price
      @click="savePrice()"
    >
      Save
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component, Prop} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {de_flow_control_command, DeFlowControlType, SocketConnector} from '@au21-frontend/client-connector';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';

@Component({
  name: 'DePriceModal',
  components: {
    NumberInput,
  },
})
export default class DePriceModal extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  connector = Container.get(SocketConnector)
  new_starting_price = ''

  // get round_number():number | null {
  //   return this.store.live_store.de_auction.common_status?.round_number
  // }

  get current_starting_price(): string {
    // Dec 29: Note that there is always at least one round!
    return this.store.live_store?.de_auction?.blotter.rounds[0]?.round_price_str
  }

  get price_label(): string {
    return this.store.live_store.de_auction.settings?.price_label || ''
  }

  savePrice() {
    this.connector.publish(de_flow_control_command({
      auction_id: this.store.live_store.de_auction.auction_id,
      control: DeFlowControlType.SET_STARTING_PRICE,
      starting_price: this.new_starting_price,
    }))
    this.$emit('close')
  }

  get decimalPlaces() {
    return this.store.live_store.de_auction.settings?.price_decimal_places || 0
  }

  cancel() {
    this.$emit('close')
    this.new_starting_price = ''
  }
}
</script>

<style lang="less" scoped>
.DePriceModal {
  background-color: #333; //  hsl(186, 12%, 30%);
  border-radius: 5px;
  border: 1px solid #111;
  color: #ccc;
  overflow: auto;
  font-size: 12px;
  margin: 2px;
  padding: 2px;
  width: 555px;

  &__input {
    width: 100px;
    font-size: 12px;
    margin: 2px 10px !important;
  }

  &__form-item {
    display: flex;
    align-items: baseline;
  }

  &__label {
    width: 180px;
    min-width: 180px;
    text-align: right;
  }
}
</style>
