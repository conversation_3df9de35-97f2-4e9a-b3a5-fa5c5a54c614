<template>
  <div class="DeAwardTable">
    <a-radio-group class="_radio" v-model="filter">
      <a-radio-button value="all">
        All
      </a-radio-button>
      <a-radio-button value="sellers">
        Sellers
      </a-radio-button>
      <a-radio-button value="buyers">
        Buyers
      </a-radio-button>
    </a-radio-group>
    <AuAgGrid
      :height="height"
      :width="width"
      :grid-options="grid_options"
      :columnDefs="column_defs"
      :get-row-height="null"
      :rowData="row_data"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGrid from '../../../../../ui-components/AuAgGrid.vue';
import {ColDef, GridOptions} from 'ag-grid-community';
import DeAwardRow from './DeAwardRow.vue';
import {counterparties_to_rows, DeAwardRowModel, matrix_to_trader_counterparties} from './DeAwardRowModel';
import {DeMatrixRoundElement, OrderType} from '@au21-frontend/client-connector';
import AuAgGridCenteredHeader from "../../../../../ui-components/AuAgGridCenteredHeader.vue";

const getColId = (round: number) => `round-${round}`;

@Component({
  name: 'DeAwardTable',
  components: {AuAgGrid}
})
export default class DeAwardTable extends Vue {
  @Prop({required: true}) matrix: DeMatrixRoundElement;
  @Prop({type: Number, required: true}) height: number;

  width = 660;
  filter: 'all' | 'sellers' | 'buyers' = 'all'

  grid_options: GridOptions = {
    defaultColDef: {
      cellRendererFramework: DeAwardRow,
      headerComponentFramework: AuAgGridCenteredHeader,

    },
    animateRows: false
  };

  get column_defs(): ColDef[] {
    return [
      {
        headerName: 'Trader',
        width: 100
      },
      {
        headerName: 'Side',
        width: 80
      },
      {
        headerName: 'Counterparty',
        width: 160
      },
      {
        headerName: 'Quantity',
        width: 80
      },
      {
        headerName: 'Value',
        width: 120
      },
      {
        headerName: 'Credit limit',
        width: 120
      }
    ];
  }

  get row_data(): DeAwardRowModel[] {
    const counterparties = matrix_to_trader_counterparties(this.matrix)
    const counterpartiesFiltered = counterparties.filter(counterparty => {
      if (this.filter === 'all') {
        return true
      }
      if (this.filter === 'sellers') {
        return counterparty.side === OrderType.SELL
      }
      if (this.filter === 'buyers') {
        return counterparty.side === OrderType.BUY
      }
    })
    return counterparties_to_rows(counterpartiesFiltered);
  }

}

</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables";

.DeAwardTable {
  ._radio {
    // TODO Move to component.
    /deep/ .ant-radio-button-wrapper {
      border-color: @au-primary-color !important;
      color: @au-text-color;
      background-color: @au-background-light;

      &.ant-radio-button-wrapper-checked {
        color: @outputColor;
        background-color: @au-primary-color;
      }
    }

    /deep/ .ag-cell-wrapper {
      padding-right: 0;
    }

    margin-bottom: 1rem;
  }
}
</style>
