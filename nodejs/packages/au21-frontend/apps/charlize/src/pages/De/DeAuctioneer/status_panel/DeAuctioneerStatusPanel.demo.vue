<template>
    <VbDemo>
        <VbCard>
            <DeAuctioneerStatusPanel
              :common_status="common_status"
              :auctioneer_status="auctioneer_status"
            />
        </VbCard>
    </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import DeAuctioneerStatusPanel from './DeAuctioneerStatusPanel.vue';
import {createDemo__DeCommonStatusValue} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import {createDemo__DeAuctioneerStatusValue} from '../../../../demo-helpers/DeAuctioneerStatusValue.helper';

@Component({
  components: { DeAuctioneerStatusPanel },
})
export default class DeAuctioneerStatusPanelDemo extends Vue {
  common_status = createDemo__DeCommonStatusValue()
  auctioneer_status = createDemo__DeAuctioneerStatusValue()
}
</script>
