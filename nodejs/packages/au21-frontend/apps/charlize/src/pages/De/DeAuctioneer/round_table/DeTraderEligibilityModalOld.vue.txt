<template>
  <a-modal
    class="DeTraderEligibilityModalOld"
    :title="title"
    @cancel="cancel()"
    visible
    closable
    centered
    width="400px"
  >
    TODO: need to decide to show max buy and max sell?
    <br>
    TODO: also: do we need to edit the minimuns?

    <!--    <div class="DeTraderEligibilityModalOld__form-item">-->
<!--      <label class="DeTraderEligibilityModalOld__label">Current {{ price_direction === "DOWN" ? "Sell" : "Buy"}} quantity-->
<!--        ({{ quantity_label }}):&nbsp;</label>-->
<!--      <NumberInput-->
<!--        class="DeTraderEligibilityModalOld__input"-->
<!--        :value="currentEligibility"-->
<!--        output-->
<!--      />-->
<!--    </div>-->

<!--    <div class="DeTraderEligibilityModalOld__form-item">-->
<!--      <label class="DeTraderEligibilityModalOld__label">New {{ price_direction === "DOWN" ? "Sell" : "Buy"}} quantity-->
<!--        ({{ quantity_label }}):&nbsp;</label>-->
<!--      <NumberInput-->
<!--        class="DeTraderEligibilityModalOld__input"-->
<!--        v-model="newEligibility"-->
<!--      />-->
<!--    </div>-->

<!--    <div class="DeTraderEligibilityModalOld__form-item">-->
<!--      <label class="DeTraderEligibilityModalOld__label">Current {{ price_direction === "DOWN" ? "Sell" : "Buy"}} quantity-->
<!--        ({{ quantity_label }}):&nbsp;</label>-->
<!--      <NumberInput-->
<!--        class="DeTraderEligibilityModalOld__input"-->
<!--        :value="currentEligibility"-->
<!--        output-->
<!--      />-->
<!--    </div>-->

<!--    <div class="DeTraderEligibilityModalOld__form-item">-->
<!--      <label class="DeTraderEligibilityModalOld__label">New {{ price_direction === "DOWN" ? "Sell" : "Buy"}} quantity-->
<!--        ({{ quantity_label }}):&nbsp;</label>-->
<!--      <NumberInput-->
<!--        class="DeTraderEligibilityModalOld__input"-->
<!--        v-model="newEligibility"-->
<!--      />-->
<!--    </div>-->

    <a-button
      slot="footer"
      type="primary"
      @click="cancel()"
    >
      Cancel
    </a-button>

    <a-button
      slot="footer"
      type="primary"
      @click="savePrice()"
    >
      Save
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import Vue from 'vue';
import { Component, Prop } from 'vue-property-decorator';
import { Container } from 'typescript-ioc';
import { CharlizeStore } from '../../../../services/connector/CharlizeStore';
import {
  de_eligibility_set_command,
  DeTraderElement,
  PriceDirection,
  SocketConnector
} from '@au21-frontend/client-connector';
import NumberInput from '../../../../ui-components/NumberInput/NumberInput.vue';

// @deprecated replaced with new DeTraderEligibilityModal
@Component({
  components: {
    NumberInput,
  },
})
export default class DeTraderEligibilityModalOld extends Vue {
  store = Container.get(CharlizeStore)
  connector = Container.get(SocketConnector)
  @Prop({ type: String, required: true }) companyId: string
  newEligibility = ''

  get price_direction():PriceDirection | null {
    return this.store.live_store.de_auction.common_status.price_direction // settings?.initial_price_direction
  }

  get title():string {
    const c = this.store.live_store.companies.find(c => c.company_id === this.companyId)
    const name = c.company_longname || c.company_shortname
    return 'Set Max ' + (this.price_direction === PriceDirection.DOWN ? 'Sell' : 'Buy') + ' quantity for ' + name
  }

  get quantity_label(){
    return this.store.live_store.de_auction.settings?.quantity_label || ""
  }
  savePrice() {
    this.connector.publish(de_eligibility_set_command({
      auction_id: this.store.live_store.de_auction.auction_id,
      company_id: this.companyId,
      max_buy: '50', // TODO
      max_sell: '50' // TODO
    }))
    this.$emit('close')
  }

  get trader(): DeTraderElement | null{
    return this.store.live_store.de_auction?.blotter?.traders?.find(trader => trader.company_id === this.companyId)
  }

  // IS THIS BUY / SELL, MAX / MIN ?
  // get currentEligibility (): string {
  //   return last(this.store.live_store.de_auction?.trader_history_rows || [])
  // }

  cancel() {
    this.$emit('close')
    this.newEligibility = ''
  }
}
</script>

<style lang="less" scoped>
.DeTraderEligibilityModalOld {
  background-color: #333; //  hsl(186, 12%, 30%);
  border-radius: 5px;
  border: 1px solid #111;
  color: #ccc;
  overflow: auto;
  font-size: 12px;
  margin: 2px;
  padding: 2px;
  width: 555px;

  &__input {
    width: 100px;
    font-size: 12px;
    margin: 2px 10px !important;
  }

  &__form-item {
    display: flex;
    align-items: baseline;
  }

  &__label {
    width: 250px;
    min-width: 250px;
    text-align: right;
  }
}
</style>
