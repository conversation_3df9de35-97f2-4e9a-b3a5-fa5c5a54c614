<template>
  <div class="PriceDirectionRuleCard pl-3">
    <p><b>1) First Round Demand > Supply</b></p>
    <div class="pl-3">
      <p>
        Price <b>increases</b> each round by
        {{ price_change_initial }} {{ price_units }}
        until either:
      </p>
      <p>a) Demand = Supply</p>
      <div class="pl-3">
        <ul>
          <li>Auction ends, see: <a @click="navigateToEnding()" href="javascript:void(0);"><b>Auction Ending Rule</b></a>, or</li>
        </ul>
      </div>
      <p>b) Demand < Supply (price overshoots)</p>
      <div class="pl-3">
        <ul>
          <li>
            At which point the round price will reverse direction
          </li>
          <li>
            and <u>decrease</u> by
            {{ price_change_post_reversal }}
            until either:
            <div class="pl-3">i) demand = supply
              <div class="pl-3">
                <ul>
                  <li>Auction ends, see: <a @click="navigateToEnding()" href="javascript:void(0);"><b>Auction Ending Rule</b></a>, or</li>
                </ul>
              </div>
            </div>
            <div class="pl-3">ii) the round price reaches
              {{ price_change_post_reversal }} <b>above</b> the previous
              round at which the price increased.
              <div class="pl-3">
                <ul>
                  <li>At which poin the auction ends, see: <a @click="navigateToEnding()" href="javascript:void(0);"><b>Auction Ending Rule</b></a></li>
                </ul>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <p><b>2) First Round Supply > Demand</b></p>
    <div class="pl-3">
      <p>(Same principle as above, though in the opposite direction)</p>
      <p>
        Price <b>decreases</b> each round by
        {{ price_change_initial }} {{ price_units }}
        until either:
      </p>
      <p>a) Supply = Demand</p>
      <div class="pl-3">
        <ul>
          <li>Auction ends, see: <a @click="navigateToEnding()" href="javascript:void(0);"><b>Auction Ending Rule</b></a>, or</li>
        </ul>
      </div>
      <p>b) Supply < Demand (price overshoots)</p>
      <div class="pl-3">
        <ul>
          <li>
            At which point the round price will reverse direction
          </li>
          <li>
            and <u>increase</u> by
            {{ price_change_post_reversal }}
            until either:
            <div class="pl-3">i) supply = demand
              <div class="pl-3">
                <ul>
                  <li>Auction ends, see: <a @click="navigateToEnding()" href="javascript:void(0);"><b>Auction Ending Rule</b></a>, or</li>
                </ul>
              </div>
            </div>
            <div class="pl-3">ii) the round price reaches
              {{ price_change_post_reversal }} <b>below</b> the previous
              round at which the price increased.
              <div class="pl-3">
                <ul>
                  <li>At which point the auction ends, see: <a @click="navigateToEnding()" href="javascript:void(0);"><b>Auction Ending Rule</b></a></li>
                </ul>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <p><b>3) In Summary</b>:</p>
    <div class="pl-3">
      <table style="width: 650px">
        <tr>
          <th>&nbsp;</th>
          <th class="_header">
            First Round
          </th>
          <th>&nbsp;</th>
          <th class="_header">
            Before Price Overshoot
          </th>
          <th class="_header">
            After Price Overshoot
          </th>
        </tr>
        <tr>
          <td>a)&nbsp;</td>
          <td>Demand > Supply</td>
          <td>&nbsp;</td>
          <td>price increases by {{ price_change_initial }} {{ price_units }}</td>
          <td>price decreases by {{ price_change_post_reversal }} {{ price_units }}</td>
        </tr>
        <tr>
          <td>b)&nbsp;</td>
          <td>Supply > Demand</td>
          <td>&nbsp;</td>
          <td>price decreases by {{ price_change_initial }} {{ price_units }}</td>
          <td>price decreases by {{ price_change_post_reversal }} {{ price_units }}</td>
        </tr>
      </table>
    </div>
    <br>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeAuctionRulesPageName} from '../DeAuctionRules.types';

@Component({
  name: 'PriceDirectionRuleCard',
})
export default class PriceDirectionRuleCard extends Vue {
  @Prop({required: true}) price_change_initial: string;
  @Prop({required: true}) price_change_post_reversal: string;
  @Prop({required: true}) price_units: string;

  navigateToEnding () {
    this.$emit('navigate', DeAuctionRulesPageName.ENDING_RULE)
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../../../au-styles/variables.less";

.PriceDirectionRuleCard {

  ._header {
    font-weight: bold;
  }
  a {
    color: @au-primary-color;
    text-decoration: underline;
  }
}
</style>
