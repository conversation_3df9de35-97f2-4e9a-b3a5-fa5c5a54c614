<template>
  <div
    class="DeRoundController"
    :style="{ 'background-color': bgColor }"
  >

    <div class="DeRoundController__current-round">
      {{ currentRoundProxy }}
    </div>

    <a-slider
      class="DeRoundController__slider"
      v-model="currentRoundProxy"
      :min="1"
      :max="lastRound"
      :tipFormatter="null"
    />

    <a-button-group style="left: -20px; top: 20px;">

      <a-button
        size="small"
        type="primary"
        icon="double-left"
        @click="currentRoundProxy = 1"
      />
      <a-button
        size="small"
        type="primary"
        icon="left"
        @click="currentRoundProxy--"
      />
      <a-button
        size="small"
        type="primary"
        icon="right"
        @click="currentRoundProxy++"
      />
      <a-button
        size="small"
        type="primary"
        icon="double-right"
        @click="currentRoundProxy = this.lastRound"
      />
    </a-button-group>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import {Container} from "typescript-ioc";
import {AuColors} from "../../../../au-styles/AuColors";

const DEBOUNCE_MS = 300;

@Component({
  name: 'DeRoundController'
})
export default class DeRoundController extends Vue {
  @Prop({type: Number}) value: number;
  @Prop({required: true, type: Number}) lastRound: number;

  colors = Container.get(AuColors)
  localValue = null;
  timeout = null;

  mounted() {
    // fixes issue: https://gitlab.com/auctionologies/frontend/au21-frontend/-/issues/106
    this.localValue = this.value;
  }

  @Watch('value')
  onValueChange(value: number) {
    this.localValue = value;
  }

  get currentRoundProxy(): number {
    return this.localValue || this.value || this.lastRound;
  }

  set currentRoundProxy(currentRound: number) {
    if (currentRound > this.lastRound) {
      return;
    }
    if (currentRound < 1) {
      return;
    }

    this.localValue = currentRound;
    clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      this.$emit('input', currentRound);
    }, DEBOUNCE_MS);
  }

  get isLastRoundSelected() {
    return this.currentRoundProxy === this.lastRound;
  }

  get bgColor(): string {
    return this.isLastRoundSelected ? // this.colors.au_background : '#af0505'
      'hsl(0,0%,20%)' : '#af0505'
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.DeRoundController {
  border: 1px solid red;
  //border-radius: 4px;
  display: flex;
  height: 60px;
  justify-content: center;
  margin: 0;
  width: 120px;

  &__slider {
    left: 60px;
    margin: 0 12px 0 5px;
    top: 4px;
    width: 80px;
  }

  &__current-round {
    //color: white;
    font-size: 18px;
    left: 90px;
    margin-right: 6px;
    padding: 0;
    position: relative;
    text-align: right;
    top: -25px;
    width: 20px;


  }

  a-button {
    //height: 30px !important;
  }

  button + button {
    //margin-left: 2px;
  }

  ///deep/ .ant-slider-track{
  //  background-color: red;
  //}

  /deep/ .ant-slider {
    background-color: transparent; // hsl(0, 0%, 20%);
  }
}
</style>
