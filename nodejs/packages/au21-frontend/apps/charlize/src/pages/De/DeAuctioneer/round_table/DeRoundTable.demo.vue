<template>
  <VbCard>
    <DeRoundTable
      auction_id="1"
      :blotter="blotter"
      :selected_round.sync="selectedRoundNumber"
      :users_that_have_seen_auction="users.map(u => u.user_id)"
      :online_users="users"
      :height="300"
      @onCompanyClick="$vb.log('onCompanyClick', $event)"
      :buyMax="50"
      :sellMax="50"
    />

    <br>
    <br>
    <button @click="randomizeSomeValues()">Randomize some values</button>

    <br>
    <br>
    <div>
      Selected round: {{ selectedRoundNumber }}
    </div>
    <button
      v-for="(round, index) in rounds"
      :key="round.round_number"
      @click="selectedRoundNumber = round.round_number"
    >
      {{ round.round_number }}
    </button>
  </VbCard>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import DeRoundTable from './DeRoundTable.vue';
import {createMultipleByClosure, random_bool, random_from_array, random_number} from '@au21-frontend/utils';
import {createDemo__DeBlotter, createDemo__excess_level} from '../../../../demo-helpers/DeRoundTable.helper';
import {range} from 'lodash';
import {createDemo__UserElement_for_trader} from '../../../../demo-helpers/UserElement.helper';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';
import {
  CompanyElement,
  DeBlotter,
  DeRoundElement,
  DeRoundTraderElement,
  DeTraderElement,
  OrderType,
  UserElement
} from '@au21-frontend/client-connector';

@Component({
  components: {
    DeRoundTable,
  },
})
export default class DeRoundTableDemo extends Vue {
  selectedRoundNumber = 8

  companies: CompanyElement[] = range(1, 5).map(i => createDemo__CompanyElement('trader ' + i))

  users: UserElement[] =
    this.companies.map(c =>
      createDemo__UserElement_for_trader(c.company_id, 'user-' + c.company_id))

  blotter: DeBlotter = createDemo__DeBlotter(this.companies)
  traders: DeTraderElement[] = this.blotter.traders
  rounds: DeRoundElement[] = this.blotter.rounds
  round_traders: DeRoundTraderElement[] = this.blotter.round_traders

  randomizeSomeValues() {
    const vol = random_number(50)
    const randomizeTraderElement = (() => {
      const trader = random_from_array(this.round_traders)
      trader.order_type = random_bool() ? OrderType.SELL : OrderType.BUY
      trader.quantity_int = vol
      trader.quantity_str = vol + ''
    })
    const randomizeRound = (() => {
      const round = random_from_array(this.rounds)
      round.buy_quantity = random_number()
      round.matched = random_number()
      round.excess_indicator = createDemo__excess_level()
      round.sell_quantity = random_number()
    })

    createMultipleByClosure(randomizeTraderElement, 50)
    createMultipleByClosure(randomizeRound, 3)
  }
}
</script>

<style lang="less" scoped>

</style>
