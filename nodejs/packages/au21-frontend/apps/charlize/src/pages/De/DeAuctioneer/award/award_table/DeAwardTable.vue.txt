<template>
  <div class="DeAwardResultsTable">
    <AuAgGrid
      :height="height"
      :width="width"
      :grid-options="grid_options"
      :columnDefs="column_defs"
      :rowData="matched_traders"/>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {DeMatrixEdgeElement, DeMatrixNodeElement} from '@au21-frontend/client-connector';
import AuAgGrid from "../../../../../ui-components/AuAgGrid.vue";
import {ColDef, GridOptions} from "ag-grid-community";
import DeAwardResultsCellRenderer from "./DeAwardResultsCellRenderer.vue";

const getColId = (round: number) => `round-${round}`;

@Component({
  name: 'DeAwardResultsTable',
  components: {AuAgGrid}
})
export default class DeAwardResultsTable extends Vue {
  @Prop({type: Array, required: true}) matrix_edges: DeMatrixEdgeElement[];
  @Prop({type: Array, required: true}) sorted_traders: DeMatrixNodeElement[];
  @Prop({type: Number, required: true}) width: number;
  @Prop({type: Number, required: true}) height: number;

  grid_options: GridOptions = {
    defaultColDef: {
      cellRendererFramework: DeAwardResultsCellRenderer
    }
  }

  get column_defs(): ColDef[] {
    return [
      {
        //headerName: 'Trader',
        field: 'shortname',
        headerComponentParams: {
          sortBy: 'shortname',
          template: `
              <div>
                <div class="border-red" style="display: inline-block; width: 120px;">Trader</div>
                <div class="centered border-red" style="display: inline-block; width: 60px; text-align: center">Side</div>
                <div class="border-red" style="display: inline-block; width: 90px; text-align: center"">Counterparty</div>
                <div class="border-red" style="display: inline-block; width: 90px; text-align: center"">Award</div>
                <div class="border-red" style="display: inline-block; width: 90px; text-align: center"">Value</div>
                <div class="border-red" style="display: inline-block; width: 90px; text-align: center"">Credit limit</div>
              </div>
          `
        },
      },
    ]
  }

  get matched_traders(): DeMatrixNodeElement[] {
    return this.sorted_traders.filter(t => t.buy_match > 0 || t.sell_match > 0)
  }

  get matched_edges(): DeMatrixEdgeElement[] {
    return this.matrix_edges.filter(m => m.match > 0)
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables";

.DeAwardResultsTable {

  .border_red {
    border: 1px solid red;
    text-align: center;
  }

}
</style>
