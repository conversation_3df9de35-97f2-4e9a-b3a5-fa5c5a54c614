<template>
  <div
    class="DeTraderHistoryCell"
  >
    <template v-if="headerName === 'Round'">
      <div class="_round">
        {{ row.round_number }}
      </div>
    </template>

    <template v-if="headerName === 'Price'">
      <div class="_price" :style="{color: price_color}">
        {{ row.round_price }}
      </div>
    </template>

    <template v-if="headerName === 'Constraints'">
      <div class="_constraints">
        <DeOrderConstraintsBar
          style="left: -1px; top: -6px"
          :width="140"
          :height="18"
          :tick_font_size="9"
          :show_labels="false"
          :constraints="row.bid_constraints"
          :order_type="row.order_type"
          :order_quantity='order_quantity'
        />
      </div>
    </template>

    <div
      v-if="headerName === 'Order'"
      :style="{ color: price_color}"
      class="_order"
    >
      <div class="_order_type">
        {{ order_type_label }}
      </div>
      <div class="_order_quantity">
        {{ row.quantity }}
      </div>
    </div>

    <template v-if="headerName === 'value'">
      <span class="_value" style="color:#ddd">
        {{ row.value }}
      </span>
    </template>

    <template v-if="headerName === 'Submitted by'">
      <span class="_username" style="color:#ddd">
        {{ row.order_submitted_by }}
      </span>
    </template>

    <template v-if="headerName === 'Excess'">
      <span
        class="_excess-level"
        :style="{color: this.excessLabelColor}"
      >
        {{ excess_level }}
      </span>
      <span
        class="_excess-direction"
        :style="{color: this.excessLabelColor}"
      >
        {{ excess_side }}
      </span>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {DeTraderHistoryRowElement, OrderType, PriceDirection} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import PriceArrow from '../../../../ui-components/PriceArrow.vue';
import DeOrderConstraintsBar from '../constraints/DeOrderConstraintsBar.vue';
import {toNumber} from 'lodash';
import {AuColors} from 'apps/charlize/src/au-styles/AuColors';

@Component({
  name: 'DeTraderHistoryCell',
  components: {DeOrderConstraintsBar, PriceArrow}
})
export default class DeTraderHistoryCell extends Vue {
  params = null;
  highlight = false;
  colors = Container.get(AuColors);

  refresh(params) {
    return true;
  }

  get headerName (): string {
    return this.params.column.colDef.headerName
  }

  get colId(): string {
    return this.params ? this.params.column.colId : '';
  }

  get row(): DeTraderHistoryRowElement {
    return this.params ? this.params.data : null;
  }

  get order_quantity(): number {
    return toNumber(this.row.quantity) || 0;
  }

  get order_type_label(): string {
    return this.row.order_type === OrderType.BUY ? 'Buy' :
      this.row.order_type === OrderType.SELL ? 'Sell' :
        '---';
  }

  get excessLabelText(): string {
    return this.row.price_direction === PriceDirection.UP ? 'Demand' : 'Supply';
  }

  get excess_level(): string {
    return this.row.excess_level;
  }

  get excess_side(): string {
    return this.row.excess_side;
  }

  get excessLabelColor(): string {
    return this.colors.order_quantity_text_color(this.row.excess_side)

  }

  get price_color(): string {
    return this.colors.getPriceColor(this.row.price_direction);
  }
}
</script>


<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.DeTraderHistoryCell {
  font-size: 13px;
  margin: 0;
  padding: 0;
  width: 100%;

  ._round {
    padding-left: 10px;
    padding-right: 5px;
    text-align: right;
    width: 30px;
  }

  ._price {
    display: inline-block;
    padding-right: 8px;
    text-align: right;
    width: 65px;
  }

  ._constraints {
    position: relative;
    text-align: center;
    top: 9px;
    left: -2px;
    width: 100%;
  }

  ._order {
    overflow: hidden;
    text-align: right;
    padding-right: 2px;
    width: 60px;

    ._order_type {
      display: inline-block;
      width: 35px;
    }

    ._order_quantity {
      display: inline-block;
      text-align: right;
      width: 25px;
    }

  }

  ._username {
    color: @white;
  }

  ._excess-level {
    display: inline-block;
    left: -5px;
    position: relative;
    text-align: right;
    width: 30px;
  }

  ._excess-direction {
    display: inline-block;
  }
}
</style>
