<template>
  <VbDemo>
    <VbCard>
      <div>
        <label>Use counterparty credit limits:&nbsp;</label>
        <a-radio-group
          style="margin-left: 7px"
          v-model:value="store.live_store.de_auction.settings.use_counterparty_credits"
          :default-value="false"
        >
          <a-radio :value="true">yes</a-radio>
          <a-radio :value="false">no</a-radio>
        </a-radio-group>
      </div>

      <TraderNavBar style="width: 920px" :store="store"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator'
import TraderNavBar from './TraderNavBar.vue'
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {createDemo__store_for_trader} from '../../../../demo-helpers/CharlizeStore.helper';
import {createDemo__SessionUserValue} from '../../../../demo-helpers/SessionUserValue.helper';

@Component({
  name: 'TraderNavBarDemo',
  components: { TraderNavBar },
})
export default class TraderNavBarDemo extends Vue {
  store: CharlizeStore = createDemo__store_for_trader()

  created () {
    this.store.live_store.session_user = createDemo__SessionUserValue()
  }
}
</script>
