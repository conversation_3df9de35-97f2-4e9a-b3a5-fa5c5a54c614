<template>
  <VbDemo>
    <VbCard>
      <UserTable
        :users="users"
        :height="500"
        :width="500"
        @selected="selectedUser = $event"
      />
      <pre>selected: {{ selectedUser && selectedUser.username }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import UserTable from './UserTable.vue';
import {createDemo__UserElement_for_trader} from '../../../../demo-helpers/UserElement.helper';
import _ from 'lodash';

@Component({
  components: {UserTable},
})
export default class UserTableDemo extends Vue {
  users = _.times(30, createDemo__UserElement_for_trader)
  selectedUser = null
}
</script>
