<template>
  <div class="AuctionCategoriesCrud">
    <AuctionCategoriesList
      style="height: 200px"
      :auction_categories="auction_categories"
    />
    <div class="_actions mt-2">
      <au-input class="_input mr-1" placeholder="Category name" v-model="category_name"/>
      <a-button class="_button" @click="onCreate()">Create</a-button>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__AuctionCategory} from '../../../../../demo-helpers/AuctionCategory.helper';
import AuctionCategoriesList from './AuctionCategoriesList.vue';
import AuInput from '../../../../../ui-components/AuInput/AuInput.vue';
import {Container} from 'typescript-ioc';
import {SocketConnector} from '@au21-frontend/client-connector';

@Component({
  name: 'AuctionCategoriesCrud',
  components: { AuInput, AuctionCategoriesList },
})
export default class AuctionCategoriesCrud extends Vue {
  // TODO Replace with store config
  auction_categories = createMultipleByClosure(createDemo__AuctionCategory, 15)
  connector = Container.get(SocketConnector)

  category_name = ''
  onCreate() {
    // TODO need publish into connector
    console.log(this.category_name)
    this.category_name = ''
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.AuctionCategoriesCrud {
  color: @white;
  ._actions {
    display: flex;
  }
  ._input {
    width: auto !important;
    flex-grow: 1;
    &::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
      color: @au-background-light;
      opacity: 1; /* Firefox */
    }
  }
  ._button {
    height: 30px;
  }
}
</style>
