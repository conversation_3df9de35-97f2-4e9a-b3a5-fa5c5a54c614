<template>
  <VbDemo>
    <VbCard dark>
      <div>
        <button @click="addMessage()">Add</button>
        <button @click="messages.pop()">Remove</button>
      </div>
      <br>
      <AuctionChat
        :messages="messages"
        :is_auctioneer="true"
        style="height: 300px"
        @submitMessage="onSubmit"
        :width="240"
        :outer_height="300"/>
    </VbCard>
    <VbCard dark title="highlightTraderMessage">
      <div>
        <button @click="addMessage()">Add</button>
        <button @click="messages.pop()">Remove</button>
      </div>
      <br>
      <AuctionChat
        :messages="messages"
        :height="300"
        :is_auctioneer="false"
        @submitMessage="onSubmit"
        :width="240"
        :outer_height="300"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuctionChat from './AuctionChat.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {
  createDemo__MessageElement_auctioneer,
  createDemo__MessageElement_trader
} from '../../../../demo-helpers/MessageElement.helper';

@Component({
  components: {AuctionChat},
})
export default class AuctionChatDemo extends Vue {
  messages = [
    ...createMultipleByClosure(createDemo__MessageElement_auctioneer, 5),
    ...createMultipleByClosure(createDemo__MessageElement_trader, 5),
  ]

  addMessage() {
    this.messages.push(createDemo__MessageElement_auctioneer())
  }

  onSubmit(message) {
    //console.log('submit', message)
  }
}
</script>

