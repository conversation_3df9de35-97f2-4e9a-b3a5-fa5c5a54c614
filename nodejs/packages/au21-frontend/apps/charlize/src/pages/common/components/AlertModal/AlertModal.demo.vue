<template>
  <VbDemo>
    <VbCard>
      <button @click="value = !value">show</button>
      <AlertModal v-if="value"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AlertModal from './AlertModal.vue';

@Component({
  name: 'AlertModalDemo',
  components: { AlertModal },
})
export default class AlertModalDemo extends Vue {
  // WHAT IS THIS? - it throws confict errors:
  // $forceUpdate() {
  //   super.$forceUpdate();
  // }
  value = false

}
</script>
