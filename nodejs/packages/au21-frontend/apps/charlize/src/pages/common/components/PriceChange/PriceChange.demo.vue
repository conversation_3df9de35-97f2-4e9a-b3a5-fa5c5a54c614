<template>
  <VbDemo>
    <VbCard>
      <AButton @click="increase">increase</AButton>
      <AButton @click="decrease">decrease</AButton>
      <AButton @click="overshoot">reverse</AButton>
    </VbCard>
    <VbCard>
        <PriceChange
          :price_direction="commonStatus.current_price_direction"
          :has_overshot="has_overshot"
          />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import {createDemo__DeCommonStatusValue} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import {PriceDirection} from '@au21-frontend/client-connector';
import PriceChange from './PriceChange.vue';

@Component({
  components: { AuSelect, PriceChange }
})
export default class PriceChangeDemo extends Vue {

  commonStatus = createDemo__DeCommonStatusValue()

  has_overshot = false

  increase() {
    this.commonStatus.price_direction = PriceDirection.UP
  }

  decrease() {
    this.commonStatus.price_direction = PriceDirection.DOWN
  }

  overshoot(){
    this.has_overshot = !this.has_overshot
  }
}
</script>
