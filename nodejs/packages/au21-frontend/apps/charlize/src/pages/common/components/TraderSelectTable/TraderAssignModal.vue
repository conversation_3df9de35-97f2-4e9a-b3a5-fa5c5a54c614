<template>
  <a-modal
    class="TraderAssignModal"
    :title="is_add_traders ? 'Add Traders' : 'Remove Traders'"
    @cancel="cancel()"
    visible
    closable
    centered
    width="700px"
  >
    <TraderSelectTable
      :style="{height: (modalHeight || 500) + 'px'}"
      :height="modalHeight || 500"
      :width="700"
      :companyIds.sync="selectedCompanyIds"
      :companies="companies"
    />

    <a-button
      slot="footer"
      type="primary"
      @click="cancel()"
    >
      Cancel
    </a-button>

    <a-button
      slot="footer"
      type="primary"
      @click="save()"
      v-test:save_traders
    >
      {{ is_add_traders ? "Add" : "Remove" }}
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import TraderSelectTable from './TraderSelectTable.vue';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import {
  CompanyElement,
  de_traders_add_command,
  de_traders_remove_command,
  SocketConnector
} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {companies_not_in_current_auction, current_auction_companies} from "../../../../services/helpers/de-helpers";

@Component({
  name: 'TraderAssignModal',
  components: {TraderSelectTable}
})
export default class TraderAssignModal extends Vue {
  @Prop({required: true}) is_add_traders: boolean;
  @Prop({required:true}) store:CharlizeStore;

  screen = new AuScreen(true);
  connector = Container.get(SocketConnector);

  selectedCompanyIds = [];

  get companies(): CompanyElement[] {
    return this.is_add_traders ?
      companies_not_in_current_auction(this.store) :
      current_auction_companies(this.store);
  }

  get modalHeight() {
    return this.screen.modal_height;
  }

  save() {
    if (this.is_add_traders) {
      this.connector.publish(de_traders_add_command({
        auction_id: this.store.live_store.de_auction.auction_id,
        company_ids: this.selectedCompanyIds
      }));
    } else {
      this.connector.publish(de_traders_remove_command({
        auction_id: this.store.live_store.de_auction.auction_id,
        company_ids: this.selectedCompanyIds
      }));
    }
    this.$emit('close');
  }

  cancel() {
    this.$emit('close');
    this.selectedCompanyIds = [];
  }

}
</script>

<style lang="less" scoped>
.TraderAssignModal {

}
</style>
