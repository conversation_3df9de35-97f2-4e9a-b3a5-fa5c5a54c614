<template>
  <VbDemo>
    <VbCard>
      <AuctionStatus
        name="Auction name"
        status="Auction status"
        style="color: white;"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuctionStatus from './AuctionStatus.vue';

@Component({
  components: {
    AuctionStatus,
  },
})
export default class AuctionStatusDemo extends Vue {
}
</script>
