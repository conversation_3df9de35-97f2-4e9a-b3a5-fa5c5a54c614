<template>
  <VbDemo>
    <VbCard>
      <CompanyTable
        :companies="companies"
        :height="500"
        :width="500"
        @selected="selectedCompany = $event"
      />
      <pre>selected: {{
          selectedCompany && selectedCompany.company_shortname
        }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import CompanyTable from './CompanyTable.vue';
import {times} from 'lodash';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';

@Component({
  components: {CompanyTable},
})
export default class CompanyTableDemo extends Vue {
  companies = times(30, createDemo__CompanyElement)
  selectedCompany = null
}
</script>
