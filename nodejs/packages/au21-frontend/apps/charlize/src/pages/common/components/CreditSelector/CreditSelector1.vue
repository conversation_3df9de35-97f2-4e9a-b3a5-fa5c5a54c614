<template>
  <ASelect class="CreditSelector1" default-value="no_limit">
    <ASelectOption value="NO_LIMIT">
      no limit
    </ASelectOption>
    <ASelectOption value="CREDIT_LIMIT">
      <NumberInput
        style="width: 100%"
        v-model="creditProxy"
        :decimalPlaces="2"
        :height="20"
      />
    </ASelectOption>
  </ASelect>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import NumberInput from "../../../../ui-components/NumberInput/NumberInput.vue";


// Experimental component
// @deprecated
@Component({
  name: 'CreditSelector1',
  components: {NumberInput}
})
export default class CreditSelector1 extends Vue {
  @Prop({default: 'no limit'}) credit_limit:string;

  get creditProxy() {
    return this.credit_limit;
  }

  set creditProxy(value) {
    this.$emit('credit_limit', value);
  }
}
</script>

<style lang="less" scoped>
.CreditSelector1 {

}
</style>
