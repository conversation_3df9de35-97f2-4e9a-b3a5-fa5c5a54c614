<template>
  <div class="CompanyEdit">
    <div class="mt-2 mb-1">Company short name:</div>
    <AuInput
      style="width: 100%" left :output="true"
      v-model="company.company_shortname"
    />
    <div class="mt-2 mb-1">Company long name:</div>
    <AuInput
      style="width: 100%" left
      v-model="company.company_longname"
    />
    <!--    <AuOutput-->
    <!--    v-else-->
    <!--    style="width: 100%" left-->
    <!--    v-model="company.company_longname"-->
    <!--  />-->
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuInput from '../../../../../ui-components/AuInput/AuInput.vue';
import {CompanyElement} from '@au21-frontend/client-connector';

@Component({
  components: {AuInput},
})
export default class CompanyEdit extends Vue {
  @Prop({required: true}) company: CompanyElement
  // @Prop({ type: Boolean }) output: boolean
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../../au-styles/variables.less';

.CompanyEdit {
  color: @au-text-color;
  background-color: @au-background-light;
  padding: 15px;
  overflow: auto;
}
</style>

