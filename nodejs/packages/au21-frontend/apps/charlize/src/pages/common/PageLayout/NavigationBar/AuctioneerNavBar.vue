<template>
  <a-row
    class="AuctioneerNavBar"
    type="flex"
    justify="space-between"
    align="middle"
  >
    <a-col :span="1">
      <LogoFramed class="_logo"
                  @click="goToHomePage()"
      />
    </a-col>

    <a-col :span="3">
      <template v-if="time">
        <div class="au-legend-small _time_legend">{{ dayOfWeekReadable }}</div>
        <span class="clock" style="font-size: x-large; color: #cecece">{{ timeReadable }}</span>
        <div class="au-legend-small _time_legend">
          {{ hourAmPm.amPm }} {{ time.city }}
        </div>
      </template>
    </a-col>

    <a-col :span="3">
      <div style="text-align: center;">
        <ConnectionMeter
          :lastPingLatency="lastPingLatency"
          style="display: inline-block"
        />
        <div style="display: inline-block">
          <div class="au-legend-small">
            Session
          </div>
          <div
            v-if="userSession"
            style="font-size: 12px !important; padding-top: 4px; display: inline-block"
            class="_text _time_legend"
          >
            {{
              store.live_store.session_user && store.live_store.session_user.session_id
            }}
          </div>
        </div>
      </div>
    </a-col>

    <a-col :span="3">
      <div
        class="au-legend-small"
        style="margin-bottom: 5px; text-align: center"
      >
        <div style="display: inline-block">
          Browser
        </div>
        <BrowserAgent class="_text"/>
      </div>
    </a-col>

    <a-col :span="1" v-if="userSession">
      <div style="text-align: center;">
        <div class="au-legend-small">
          Theme
        </div>
        <ASwitch
          size="small"
          @change="toggle_theme"
        />
      </div>
    </a-col>

    <a-col :span="2">
      <!--{{lastPingLatency}}-->
      <div
        class="au-legend-small"
        style="text-align: center"
      >
        <div>User</div>
        <span class="_text">
          {{ userSession.username }} ({{ roleLabel }})
        </span>
      </div>
      <span v-if="companyName">&nbsp;({{ companyName }})</span>
    </a-col>

    <a-col :span="6" push style="text-align: right; margin-right: 5px">
      <template
        v-if="store.live_store.session_user && store.live_store.session_user.username"
      >
        <a-button-group>
          <a-button class="au-btn" type="primary" @click="goToHomePage()">Home</a-button>
          <a-button class="au-btn" type="primary" @click="goToCreditPage()">Limits</a-button>
          <AdminSettingsButton :store="store"/>
          <a-button class="au-btn" type="primary" @click="goToUsersPage()">Users</a-button>
          <a-button class="au-btn" type="primary" @click="signOut()">Sign out</a-button>
        </a-button-group>
      </template>
    </a-col>
  </a-row>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import ConnectionMeter from '../ConnectionMeter/ConnectionMeter.vue';
import LogoFramed from './LogoFramed.vue';
import {Container} from 'typescript-ioc';
import {
  AuUserRole,
  page_set_command,
  PageName,
  session_terminate_command,
  SessionTerminationReason,
  SessionUserValue,
  SocketConnector
} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import {AuColors} from "../../../../au-styles/AuColors";
import BrowserAgent from "../../components/BrowserAgent/BrowserAgent.vue";
import AdminSettingsButton from './AdminSettings/AdminSettingsButton.vue';

@Component({
  components: {
    AdminSettingsButton,
    BrowserAgent,
    ConnectionMeter,
    LogoFramed
  }
})
export default class AuctioneerNavBar extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  connector = Container.get(SocketConnector);
  colors = Container.get(AuColors)

  toggle_theme() {
    this.colors.switch_theme()
  }

  get lastPingLatency() {
    return this.store.live_store.seconds_since_last_message_received || 0;
  }

  get userSession(): SessionUserValue {
    return this.store.live_store.session_user;
  }

  get companyName() {
    if (!this.store.live_store.session_user.company_id) {
      // auctioneer doesn't have company
      return '';
    }
    return ` (${this.store.live_store.session_user?.company_shortname})`;
  }

  // do we need this?
  get roleLabel(): string {
    if (this.userSession.role === AuUserRole.AUCTIONEER)
      return 'auctioneer';
    else if (this.userSession.role === AuUserRole.TRADER) {
      // TODO: remove this:
      alert("this is an auctioneer navbar")
      return 'trader';
    } else
      return this.userSession.role;
  }

  // do we need this?
  get traderUser(): SessionUserValue {
    return this.store.live_store.session_user;
  }

  gotoPage(page: PageName) {
    this.connector.publish(page_set_command({page}));
  }

  goToHomePage() {
    this.gotoPage(PageName.HOME_PAGE);
  }

  goToCreditPage() {
    this.gotoPage(PageName.CREDITOR_AUCTIONEER_PAGE);
  }

  goToUsersPage() {
    this.gotoPage(PageName.USER_PAGE);
  }

  signOut() {
    this.connector.publish(session_terminate_command({
      reason: SessionTerminationReason.SIGNED_OFF
    }));
  }

  get time() {
    return this.store.live_store.time;
  }

  get timeReadable() {
    const seconds = Number(this.time.date_time.seconds);
    let secondsString = String(seconds);
    if (seconds < 10) {
      secondsString = '0' + secondsString;
    }

    const minutes = Number(this.time.date_time.minutes);
    let minutesString = String(minutes);
    if (minutes < 10) {
      minutesString = '0' + minutesString;
    }

    const hours = this.hourAmPm.hour;

    return `${hours}:${minutesString}:${secondsString}`;
  }

  get hourAmPm(): { hour: number, amPm: 'am' | 'pm' } {
    const hour = this.time.date_time.hour;
    if (hour > 12) {
      return {
        hour: hour - 12,
        amPm: 'pm'
      };
    } else {
      return {
        hour,
        amPm: 'am'
      };
    }
  }


  get dayOfWeekReadable(): string {
    const dayOfWeek = this.time.date_time.day_of_week;

    switch (dayOfWeek) {
      case 0 :
        return 'Sunday';
      case 1 :
        return 'Monday';
      case 2 :
        return 'Tuesday';
      case 3 :
        return 'Wednesday';
      case 4 :
        return 'Thursday';
      case 5 :
        return 'Friday';
      case 6 :
        return 'Saturday';
      default:
        return '';
    }
  }

  get isAuctioneerOrAdmin(): boolean {
    return this.userSession.isAuctioneer;
  }

  showAdminSettingsModal () {

  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.AuctioneerNavBar {
  // background-color: @layout-color;

  /deep/ ._text {
    font-size: 12px !important;
    font-weight: bold;
    color: #cecece !important;
  }

  ._logo {
    margin-left: 30px;
    //position: relative;
    //top: -1px;
  }

  ._time_legend {
    //  border: 1px solid red;
    display: inline-block;
    margin-left: 5px;
    margin-right: 5px;
    position: relative;
    bottom: 4px;
  }
}
</style>
