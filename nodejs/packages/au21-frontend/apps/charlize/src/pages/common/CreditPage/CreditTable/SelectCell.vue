<template>
  <div class="SelectCell" @click="selected = !selected">
    <a-checkbox @click.prevent :checked="selected" />
  </div>
</template>

<script lang="ts">
import {Component, Inject, Vue} from 'vue-property-decorator';

@Component({})
export default class SelectCell extends Vue {
  params = null
  @Inject() readonly tableComponent: { onSelectAll: () => void, isValueSelected: (value: any) => boolean, isAnyValueSelected: boolean, selectValue: (any) => void }

  set selected(value: boolean) {
    this.tableComponent.selectValue(this.params.data)
  }

  get selected(): boolean {
    return this.tableComponent.isValueSelected(this.params.data)
  }
}
</script>

<style lang="less" scoped>
.SelectCell {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
