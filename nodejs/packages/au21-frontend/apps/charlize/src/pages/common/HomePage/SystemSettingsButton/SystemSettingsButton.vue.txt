<template>
  <a
    class="SystemSettingsButton text-link"
    @click="showSystemSettingsModal = true"
  >Settings<SystemSettingsModal
      v-if="showSystemSettingsModal"
      :site="localSite"
      @close="showSystemSettingsModal = false"
      @saveSystemSettings="saveSystemSettings()"
    />
  </a>
</template>

<script lang="ts">
// @ts-nocheck

import { Component, Vue, Watch } from "vue-property-decorator";
import SystemSettingsModal from "./SystemSettingsModal.vue";
import { Container } from "typescript-ioc";
import { SocketConnector } from "@au21-frontend/client-connector";
import { CharlizeStore } from "../../../../services/connector/CharlizeStore";

@Component({
  components: { SystemSettingsModal },
})
export default class SystemSettingsButton extends Vue {
  charlizeStore = Container.get(CharlizeStore)
  charlizeConnector = Container.get(CharlizeConnector)

  showSystemSettingsModal = false
  localSite: SiteValue = this.charlizeStore.site

  get site (): SiteValue {
    return this.charlizeStore.site
  }

  @Watch('site')
  on_site (site: SiteValue) {
    this.localSite = {...site}
  }

  created () {
    this.charlizeConnector.bus.$on('ModalCloseEvent', this.CommandSucceeded)
    this.localSite = this.charlizeStore.site
  }

  beforeDestroy () {
    this.charlizeConnector.bus.$off('ModalCloseEvent', this.CommandSucceeded)
  }

  CommandSucceeded () {
    this.showSystemSettingsModal = false
  }

  saveSystemSettings () {
    this.charlizeConnector.publish(site_save_request(this.localSite))
    this.showSystemSettingsModal = true
  }
}
</script>

<style lang="less" scoped>
.SystemSettingsButton {

}
</style>
