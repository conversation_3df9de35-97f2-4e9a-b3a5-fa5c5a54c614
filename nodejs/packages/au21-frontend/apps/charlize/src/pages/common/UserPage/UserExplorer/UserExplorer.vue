<template>
  <div class="UserExplorer">
    <AuSectionBorder>
      <div class="au-header"
           style="margin-left: 10px !important;">
        Users
      </div>
      <div style="flex: 1 1; display: flex; align-items: stretch;">
        <UserTable
          :height="bottomHeight"
          :width="tableWidth"
          :users="users"
          @selected="selectUser"
        />

        <div style="flex: 1 1; background-color: #666666">
          <div style="display: flex; justify-content: center; padding-top: 6px;">
            <div class="mr-1">
              User:
            </div>
            <a-button
              class="mr-1"
              :disabled="!selectedUser"
              type="primary"
              @click="startEditingUser()"
            >
              Edit
            </a-button>
            <a-button
              class="mr-1"
              type="primary"
              @click="startCreatingUser()"
            >
              New
            </a-button>
            <a-popconfirm
              v-if="selectedUser"
              placement="bottomRight"
              okText="Yes"
              cancelText="No"
              @confirm="deleteUser(selectedUser)"
              type="primary"
              :disabled="!selectedUser"
            >
              <template slot="title">
                <p>Delete "{{ selectedUser.username }}"?</p>
              </template>
              <a-button
                type="primary">
                Delete
              </a-button>
            </a-popconfirm>
            <a-button
              disabled v-else
              type="primary"
            >
              Delete
            </a-button>
          </div>

          <UserEdit
            v-if="selectedUser"
            :user="selectedUser"
            :companies="companies"
            output
          />
        </div>
      </div>
    </AuSectionBorder>

    <a-modal
      title="Create user"
      class="ant-modal--no-padding"
      v-if="userToCreate"
      :visible="true"
      @cancel="cancelUserEdit()"
      closable
      centered
      width="330px"
    >
      <UserEdit
        :user="userToCreate"
        :companies="companies"
        :style="{maxHeight: this.modalHeight + 'px'}"
      />

      <a-button
        slot="footer"
        type="primary"
        @click="cancelUserEdit()"
      >
        Cancel
      </a-button>

      <a-button
        slot="footer"
        type="primary"
        @click="saveUser(userToCreate)"
      >
        Create
      </a-button>
    </a-modal>

    <a-modal
      title="Edit user"
      class="ant-modal--no-padding"
      v-if="userToEdit"
      :visible="true"
      @cancel="cancelUserEdit()"
      closable
      centered
      width="330px"
    >
      <UserEdit
        v-if="userToEdit"
        :user="userToEdit"
        :companies="companies"
        :style="{maxHeight: this.modalHeight + 'px'}"
      />

      <a-button
        slot="footer"
        type="primary"
        @click="cancelUserEdit()"
      >
        Cancel
      </a-button>

      <a-button
        slot="footer"
        type="primary"
        @click="saveUser(userToEdit)"
      >
        Save
      </a-button>
    </a-modal>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import UserTable from './UserTable.vue';
import UserEdit from './UserEdit/UserEdit.vue';
import {create__clearUserElement} from './UserElement.factory';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import {
  CompanyElement,
  SocketConnector,
  user_delete_command,
  user_save_command,
  UserElement
} from '@au21-frontend/client-connector';
import {CharlizeClient} from '../../../../services/connector/CharlizeClient';
import AuSectionBorder from "../../../../ui-components/AuSectionBorder.vue";

@Component({
  components: {AuSectionBorder, UserEdit, UserTable}
})
export default class UserExplorer extends Vue {
  @Prop({required: true}) companies: CompanyElement[];
  @Prop({required: true}) users: UserElement[];

  screen = new AuScreen(true);
  connector = Container.get(SocketConnector);
  client = Container.get(CharlizeClient);

  selectedUserId: string | null = null;
  userToCreate: UserElement | null = null;
  userToEdit: UserElement | null = null;

  tableWidth = 600;

  get selectedUser() {
    return this.users.find(user => user.user_id === this.selectedUserId) || null;
  }

  created() {
    this.client.subscribe('CommandSucceeded', this.cancelUserEdit);
  }

  beforeDestroy() {
    this.client.unsubscribe('CommandSucceeded', this.cancelUserEdit);
  }

  get bottomHeight(): number {
    return this.screen.page_height - 55
  }

  get modalHeight() {
    return this.screen.modal_height;
  }

  cancelUserEdit() {
    //console.log('cancelUserEdit')
    this.userToCreate = null;
    this.userToEdit = null;
  }

  startCreatingUser() {
    this.userToCreate = create__clearUserElement();
  }

  startEditingUser() {
    this.userToEdit = {...this.selectedUser};
  }

  saveUser(user: UserElement) {
    this.connector.publish(user_save_command({
      company_id: user.company_id,
      email: '',
      password: user.password,
      phone: '',
      role: user.role,
      user_id: user.user_id, // Is null for create.
      username: user.username
    }));
  }

  selectUser(user: UserElement) {
    this.selectedUserId = user.user_id;
  }

  deleteUser(user: UserElement) {
    this.connector.publish(user_delete_command(
      {
        user_id: user.user_id
      }
    ));
  }
}
</script>

<style lang="less" scoped>
.UserExplorer {

}
</style>
