<template>
  <VbDemo>
    <VbCard>
      <button @click="show = true">
        Show
      </button>
      <AdminSettingsModal
        :adminSettings="ADMIN_SETTINGS_WIP"
        v-if="show"
        @save="$vb.log('save')"
        @close="show = false"
      />

      <pre>{{ADMIN_SETTINGS_WIP}}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator'
import AdminSettingsModal from './AdminSettingsModal.vue'

@Component({
  name: 'AdminSettingsModalDemo',
  components: { AdminSettingsModal },
})
export default class AdminSettingsModalDemo extends Vue {
  ADMIN_SETTINGS_WIP = {
    use_counterparty_credits: false,
  }

  show = false
}
</script>
