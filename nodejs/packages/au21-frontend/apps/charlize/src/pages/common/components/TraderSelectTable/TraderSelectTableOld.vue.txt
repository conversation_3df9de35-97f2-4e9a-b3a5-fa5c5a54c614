<template>
  <AuAgGrid
    class="TraderSelectTable"
    :columnDefs="columnDefs"
    :rowData="companies"
    :gridOptions="gridOptions"
    :height="height"
    :width="width"
  />
</template>

<script lang="ts">
import { Component, Prop, PropSync, Vue } from 'vue-property-decorator';
import SelectHeader from '../../CreditPage/CreditTable/SelectHeader.vue';
import SelectCell from '../../CreditPage/CreditTable/SelectCell.vue';
import { ColDef, GridOptions, ValueGetterParams } from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import { toggle } from '@au21-frontend/utils';
import { CompanyElement, UserElement } from '@au21-frontend/client-connector';

const makeComparatorForString = (valueGetter: (company: CompanyElement) => string) => {
  return (valueA, valueB, nodeA, nodeB, isInverted) => {
    const a: CompanyElement = nodeA.data
    const b: CompanyElement = nodeB.data
    return valueGetter(a).localeCompare(valueGetter(b))
  }
}

const makeComparatorForNumeric = (valueGetter: (company: CompanyElement) => string) => {
  return (valueA, valueB, nodeA, nodeB, isInverted) => {
    const a: CompanyElement = nodeA.data
    const b: CompanyElement = nodeB.data
    return Number(valueGetter(b)) - Number(valueGetter(b))
  }
}

@Component({
  components: { AuAgGrid },
  provide() {
    return {
      // @Provide doesn't work for `this` :/.
      tableComponent: this,
    }
  },
})
export default class TraderSelectTableOld extends Vue {
  @PropSync('companyIds', { type: Array }) companyIdsProxy: string[]
  @Prop() companies: CompanyElement[]
  @Prop() users: UserElement[]
  @Prop() height: number
  @Prop() width: number

  gridOptions: GridOptions = {
    headerHeight: 28,
    defaultColDef: {
      //
      // headerComponentFramework: AuAgGridCenteredHeader,
      cellStyle: () => ({ padding: '0', border: '0' }),
    },
    getRowNodeId: data => data.id,
    rowHeight: 24,
    suppressHorizontalScroll: true,
  }

  get columnDefs(): ColDef[] {
    return [
      {
        headerName: 'Select',
        field: null,
        width: 50,
        headerComponentFramework: SelectHeader,
        cellRendererFramework: SelectCell,
      },
      {
        headerName: 'Username',
        sortable: true,
        comparator: makeComparatorForString(company => company.company_longname),
        cellRenderer: (params: ValueGetterParams) => {
          const company: CompanyElement = params.data
          const user = this.users.find(user => user.company_id === company.company_id) || null
          return `<div style="padding: 3px 5px;">${user ? user.username : ''}</div>`
        },
      },
      {
        headerName: 'Company',
        sortable: true,
        comparator: makeComparatorForString(company => company.company_longname),
        cellRenderer: (params: ValueGetterParams) => {
          const company: CompanyElement = params.data
          return `<div style="padding: 3px 5px;">${company.company_longname}</div>`
        },
      },
      {
        headerName: 'Company Short',
        sortable: true,
        comparator: makeComparatorForString(company => company.company_shortname),
        cellRenderer: (params: ValueGetterParams) => {
          const company: CompanyElement = params.data
          return `<div style="padding: 3px 5px;">${company.company_shortname}</div>`
        },
      },
      // {
      //   headerName: 'Default Risk',
      //   sortable: true,
      //   field: 'default_risk',
      //   width: 110,
      //   comparator: makeComparatorForNumeric(company => company.default_risk),
      //   cellRenderer: (params: ValueGetterParams) => {
      //     const company: CompanyElement = params.data
      //     return `<div style="padding: 3px 5px; text-align: right">${company.default_risk}</div>`
      //   },
      // },
      // {
      //   headerName: 'Credit Limit',
      //   sortable: true,
      //   field: 'credit_limit',
      //   width: 110,
      //   comparator: makeComparatorForNumeric(company => company.credit_limit),
      //   cellRenderer: (params: ValueGetterParams) => {
      //     const company: CompanyElement = params.data
      //     return `<div style="padding: 3px 5px; text-align: right">${company.credit_limit}</div>`
      //   },
      // },
    ]
  }

  // This is required for selection demo-components to function.
  onSelectAll(value: boolean): void {
    this.companyIdsProxy = value ? this.companies.map(company => company.id) : []
  }

  isValueSelected(company: CompanyElement): boolean {
    return this.companyIdsProxy.includes(company.id)
  }

  get isLessThanAllSelected(): boolean {
    return !!this.companyIdsProxy.length && (this.companyIdsProxy.length < this.companies.map(company => company.id).length)
  }

  get isAnyValueSelected(): boolean {
    return !!this.companyIdsProxy.length
  }

  selectValue(company: CompanyElement) {
    this.companyIdsProxy = toggle(this.companyIdsProxy, company.id)
  }
}
</script>

<style lang="less" scoped>
.TraderSelectTable {
  .ag-header-cell {
    padding: 3px 5px !important;
  }
}
</style>
