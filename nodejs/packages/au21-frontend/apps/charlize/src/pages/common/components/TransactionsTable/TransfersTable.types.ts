// TODO Remove this type - should use generated
export enum TransactionElementCause {
  AUCTIONEER_MANUAL = 'AUCTIONEER_MANUAL',
  TRADER_MANUAL = 'TRADER_MANUAL',
  AUCTION_AWARDED = 'AUCTION_AWARDED',
  AUCTION_DELETED = 'AUCTION_DELETED',
  TRADER_ADDED = 'TRADER_ADDED',
  TRADER_REMOVED = 'TRADER_REMOVED',
}

export const TransactionElementCause_map = {
  [TransactionElementCause.AUCTIONEER_MANUAL]: 'Auctioneer manual',
  [TransactionElementCause.TRADER_MANUAL]: 'Trader manual',
  [TransactionElementCause.AUCTION_AWARDED]: 'Auction awarded',
  [TransactionElementCause.AUCTION_DELETED]: 'Auction deleted',
  [TransactionElementCause.TRADER_ADDED]: 'Auction added',
  [TransactionElementCause.TRADER_REMOVED]: 'Trader removed',
}

// TODO Remove this type - should use generated
export type CreditTransferElement = {
  id: string,
  date_timestamp: number,
  date_string: string,
  auction_id: string,
  auction_name: string,
  cause: TransactionElementCause,
  company_shortname: string,

  by_user: string,
  is_auctioneer: boolean,

  // Dealer mode only
  amount_delta: string,
  amount_balance: string,
  volume_delta: string,
  volume_balance: string,

  // Counterparty mode only
  buyer_company_shortname: string,
  seller_company_shortname: string,
  counterparty_amount_delta: string,
  counterparty_amount_balance: string,
}

export type DummyRow = {id: string, type: 'dummy'}

export const is_dummy_row = (row: any): row is DummyRow => row.type === 'dummy'

export type TransferTableCellParams = {
  getSort?: () => 'asc' | 'desc',
  setSort?: (direction: 'asc' | 'desc') => void,
  hide?: boolean,
  field: keyof CreditTransferElement | 'filler',
}
