<template>
  <a-button
    class="BidConstraintsButton au-btn"
    style="height: 25px;"
    type="primary"

    @click="showBidConstraintsModal = true"
  >
    Bid constraints
    <a-modal
      v-if="showBidConstraintsModal"
      title="Bid constraints"
      type="primary"
      @cancel="showBidConstraintsModal = false"
      visible
      closable
      centered
      width="620px"
    >
      <div style="white-space: initial" v-for="bidConstraint in bidConstraints">
        {{ bidConstraint }}
      </div>

      <a-button
        slot="footer"
        class="au-btn"
        type="primary"
        @click="showBidConstraintsModal = false"
      >
        Close
      </a-button>
    </a-modal>
  </a-button>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';

@Component({
  components: {},
})
export default class BidConstraintsButton extends Vue {
  showBidConstraintsModal = false

  get bidConstraints() {
    return null // TODO // this.charlizeStore.te_settings.bid_constraints
  }
}
</script>

<style lang="less" scoped>
.BidConstraintsButton {

}
</style>
