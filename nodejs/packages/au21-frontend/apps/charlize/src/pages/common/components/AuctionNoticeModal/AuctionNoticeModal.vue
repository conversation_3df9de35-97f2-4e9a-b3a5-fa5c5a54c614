<template>
  <a-modal
    class="AuctionNoticeModal"
    title="Auction notice"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="800px"
    :maskClosable="false"
  >
    <div
      :style="{
        maxHeight: modalHeight ? modalHeight + 'px' : undefined,
        height: modalHeight ? modalHeight + 'px' : undefined,
      }"
      v-if="editable"
    >
      <HtmlEditor v-model="noticeLocal" />
    </div>
    <div
      style="height: 500px"
      v-else
      ref="noticeModalContent"
      class="_html-container content ql-editor"
      v-html="noticeLocal"
    />

    <a-button
      slot="footer"
      class="au-btn"
      type="primary"
      @click="$emit('close')"
    >
      Close
    </a-button>

    <a-button
      v-if="editable"
      class="au-btn"
      type="primary"
      slot="footer"
      @click="saveUpdatingNotice()"
    >
      Save
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import HtmlEditor from '../../../../ui-components/HtmlEditor/HtmlEditor.vue';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';

@Component({
  components: { HtmlEditor },
})
export default class AuctionNoticeModal extends Vue {
  noticeLocal = ''
  screen = new AuScreen() // same for auctioneer or trader

  @Prop({ type: Boolean }) editable: boolean
  @Prop({ required: true, type: String }) notice: string

  @Watch('notice', { immediate: true })
  onValueChange(notice) {
    this.noticeLocal = notice
  }

  get modalHeight(): number {
    const modalHeightResponsive = this.screen.modal_height
    const maxModalHeight = 700
    return (modalHeightResponsive < maxModalHeight) ? modalHeightResponsive : maxModalHeight
  }

  @Watch('notice')
  async onNoticeUpdate() {
    await this.$nextTick()
    const fontElements: HTMLFontElement[] = Array.from((this.$refs.noticeModalContent as HTMLElement).querySelectorAll('font'))
    fontElements.forEach(fontElement => {
      // Fix font size
      const size = fontElement.getAttribute('size')
      if (size) {
        fontElement.setAttribute('style', `font-size: ${size}px`)
        fontElement.removeAttribute('size')
      }
      // Fix new lines
      if (!fontElement.innerText) {
        fontElement.innerHTML = '&nbsp;'
      }
    })
  }

  saveUpdatingNotice() {
    this.$emit('saveNotice', this.noticeLocal)
    this.$emit('close')
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.AuctionNoticeModal {
  ._html-container {
    height: 500px;

    p {
      margin-bottom: 0;
    }

    overflow: auto;
    padding: 12px 15px;
    background-color: @au-pseudo-input-color;
  }
}
</style>
