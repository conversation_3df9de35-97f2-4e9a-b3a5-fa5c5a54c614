<template>
  <VbDemo>
    <VbCard title="default">
      <TransactionsTable
        :transaction_elements="transaction_elements"
        :de_settings_value="de_settings_value"
        :height="300"
        :width="1100"
      />
    </VbCard>
    <VbCard title="counterparty mode">
      <TransactionsTable
        :transaction_elements="transaction_elements"
        :de_settings_value="de_settings_value"
        :height="300"
        :width="1100"
        :use_counterparty_mode="true"
      />
    </VbCard>
    <VbCard title="hide filters">
      <TransactionsTable
        :transaction_elements="transaction_elements"
        :de_settings_value="de_settings_value"
        :height="300"
        :width="1100"
        hide_filters
      />
    </VbCard>
    <VbCard title="scope by auction">
      <TransactionsTable
        :transaction_elements="transaction_elements"
        :de_settings_value="de_settings_value"
        :height="300"
        :width="1100"
        is_scoped_by_auction
        hide_filters
      />
    </VbCard>
    <VbCard title="scope by trader">
      <TransactionsTable
        :transaction_elements="transaction_elements"
        :de_settings_value="de_settings_value"
        :height="300"
        :width="1100"
        is_scoped_by_trader
        hide_filters
      />
    </VbCard>
    <VbCard title="dummy rows test">
      <TransactionsTable
        :transaction_elements="[transaction_elements[0], transaction_elements[1]]"
        :de_settings_value="de_settings_value"
        :height="300"
        :width="1100"
        hide_filters
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import TransactionsTable from './TransfersTable.vue';
import _ from 'lodash';

import {
  createMultipleByClosure,
  random_bool,
  random_date_time,
  random_enum,
  random_from_array,
  random_number,
  random_number_string,
  random_string,
  random_timestamp,
} from '@au21-frontend/utils';

import {CreditTransferElement, TransactionElementCause,} from './TransfersTable.types';
import {createDefault__DeSettingsValue} from '../../../../demo-helpers/DeSettingsValue.helper';

const auctions = createMultipleByClosure(index => ({id: `${index}` ,name: `auction_${index}`}), 3)
const companies = createMultipleByClosure(index => ({id: `${index}` ,name: `company_${index}`}), 4)

// TODO Move to separate file after getting proper types from Generated.
const createDemo__TransactionElement = (): CreditTransferElement => {
  const is_volume = random_bool()
  const is_auctioneer = random_bool()

  const auction = random_from_array(auctions)
  const company = random_from_array(companies)
  const buyer_company = random_from_array(companies)
  const seller_company = random_from_array(companies.filter(company => company !== buyer_company))

  return {
    id: random_string(8),
    date_timestamp: random_timestamp(),
    date_string: random_date_time(),
    auction_id: auction.id,
    auction_name: auction.name,
    cause: random_enum(TransactionElementCause),
    // This data doesn't really follow invariants,
    // which might or might not be a problem as that's mostly backend domain.
    company_shortname: is_auctioneer ? '' : company.name,
    buyer_company_shortname: buyer_company.name,
    seller_company_shortname: seller_company.name,
    by_user: `u_${random_string(3)}`,
    is_auctioneer,
    volume_delta: is_volume ? `${random_bool() ? '' : '-'}${random_number(10)}` : null,
    volume_balance: is_volume ? `${random_number(100)}` : null,
    amount_delta: is_volume ? null : `${random_bool() ? '' : '-'}$${random_number(10_000_000)}`,
    amount_balance: is_volume ? null : `$${random_number(100_000_000)}`,
    counterparty_amount_delta: random_number_string(100_000_000),
    counterparty_amount_balance: random_number_string(100_000_000),
  }
};

@Component({
  components: { TransactionsTable },
})
export default class TransactionsTableDemo extends Vue {
  transaction_elements = _.times(20, createDemo__TransactionElement);
  de_settings_value = createDefault__DeSettingsValue()
}
</script>
