<template>
  <a-radio-group
    :disabled="disabled"
    class="UserRoleSelect"
    v-model="valueProxy"
  >
    <a-radio
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      :style="radioStyle"
    >
      {{ option.name }}
    </a-radio>
  </a-radio-group>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {getAuUserRoleOptions} from '../../../../../entity-helpers/AuUserRole';
import {AuUserRole} from '@au21-frontend/client-connector';

@Component({})
export default class UserRoleSelect extends Vue {
  @Prop({type: String}) value: AuUserRole
  @Prop({type: Boolean}) disabled: boolean
  radioStyle = {
    display: 'block',
    height: '30px',
    lineHeight: '30px',
  }
  options = getAuUserRoleOptions()

  get valueProxy(): AuUserRole {
    return this.value
  }

  set valueProxy(value) {
    this.$emit('input', value)
  }
}
</script>

<style lang="less" scoped>
.UserRoleSelect {

}
</style>
