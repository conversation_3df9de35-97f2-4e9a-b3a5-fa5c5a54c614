<template>

  <TwoStateDisplay
    :width="55"
    first_label="UP"
    second_label="DOWN"
    :first_color="first_color"
    :second_color="second_color"
  />
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';
import {PriceDirection} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import TwoStateDisplay from '../TwoStateDisplay/TwoStateDisplay.vue';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: { TwoStateDisplay }
})
export default class PriceChange extends Vue {

  @Prop({ required: true }) price_direction: PriceDirection;
  @Prop({ required: true }) has_overshot: boolean; // note: unlike has_reversed, this occurs during a roun

  colors = Container.get(AuColors);

  // unAnnouncedColor: { [e in DeTimeState]: () => string } = {
  //   'BEFORE_ANNOUNCE_TIME': () =>
  //     this.isEngaged ? this.colors.NORMAL : this.colors.WARNING,
  //   'BEFORE_START_TIME': () =>
  //     this.isEngaged ? this.colors.NORMAL : this.colors.ERROR, // TODO: ? warn ?
  //   'AFTER_START_TIME': () =>
  //     this.isEngaged ? this.colors.NORMAL : this.colors.ERROR
  // };
  //
  // get isEngaged(): boolean {
  //   return this.auctioneerStatus.autopilot == AutopilotMode.ENGAGED;
  // }

  get first_color() {
    // if (this.price_direction === PriceDirection.UP) {
    //   return this.has_overshot ?
    //     this.colors.WARNING :
    //     this.colors.NORMAL; // this.unAnnouncedColor[this.auctioneerStatus.time_state]();
    // }
    // return this.colors.DISABLED;
    return 'red'
  }

  get second_color() {
    // if (this.price_direction === PriceDirection.DOWN) {
    //   return this.has_overshot ?
    //     this.colors.WARNING:
    //     this.colors.NORMAL
    // }
    // return this.colors.DISABLED;
    return 'green'
  }


}
</script>

<style scoped>
</style>
