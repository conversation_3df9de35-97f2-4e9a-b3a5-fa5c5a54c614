<template>
  <VbDemo>
    <VbCard>
      <TeCreditTable
        :companies="companies"
        :companyIds.sync="companyIds"
        :width="700"
        :height="400"
      />
      {{ companyIds }}
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import TeCreditTable from './TeCreditTable.vue';
import _ from 'lodash';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';

@Component({
  components: { TeCreditTable },
})
export default class TeCreditTableDemo extends Vue {
  companies = _.times(20, createDemo__CompanyElement)
  companyIds = [this.companies[0].id]
}
</script>
