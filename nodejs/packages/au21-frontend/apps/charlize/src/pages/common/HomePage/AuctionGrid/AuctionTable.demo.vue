<template>
  <VbDemo>
    <VbCard>
      <AuctionTable
        style="width: 1200px"
        :auction_rows="auctionRowElements"
        :is_auctioneer="isAuctioneer"
        @auction_selected="$vb.log('auction_selected', $event)"
        @auction_delete="$vb.log('auction_delete', $event)"
        @hide="$vb.log('hide', $event)"
        @unhide="$vb.log('unhide', $event)"
      />
    </VbCard>
    <VbCard>
      <label>
        <input type="checkbox" v-model="isAuctioneer">
        isAuctioneer
      </label>
      <br>
      <button @click="addAuction()">Add auction</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createMultipleByClosure} from '@au21-frontend/utils';
import AuctionTable from "./AuctionTable.vue";
import { createDemo__AuctionRowElement } from '../../../../demo-helpers/AuctionRowElement.helper';

@Component({
  components: {AuctionTable}
})
export default class AuctionListDemo extends Vue {
  isAuctioneer = true;
  auctionRowElements = createMultipleByClosure(
    (id) => createDemo__AuctionRowElement(this.isAuctioneer, id), 5
  );
  count = 5;

  created() {
    this.auctionRowElements[0].auction_name = 'loooooooooooooooooooooooooooongloooooooooooooooooooooooooooongloooooooooooooooooooooooooooongloooooooooooooooooooooooooooongloooooooooooooooooooooooooooongloooooooooooooooooooooooooooongloooooooooooooooooooooooooooong'
  }

  addAuction() {
    this.auctionRowElements = [
      createDemo__AuctionRowElement(this.isAuctioneer, this.count++),
      ...this.auctionRowElements,
    ]
  }
}
</script>
