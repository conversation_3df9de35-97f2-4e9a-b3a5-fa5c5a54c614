import { SocketConnector } from "@au21-frontend/client-connector";

export function saveAuction (settings: TeSettingsValue, connector: CharlizeConnector) {
  // TODO
  // connector.publish(te_settings_save_request({
  //   annual_discount_rate: settings.annual_discount_rate,
  //   auction_id: settings.auction_id,
  //   auction_name: settings.auction_name,
  //   bid_must_improve_highest: true, //settings.bid_must_improve_highest,
  //   bid_must_improve_own: true, // settings.bid_must_improve_own,
  //   bid_quiet_period: settings.bid_quiet_period && settings.bid_quiet_period.toString(),
  //   bid_ranking_rule:
  //   // fix to bad abstraction:
  //   settings.te_design === TeDesign.RATE_AND_TERM ?
  //     TeBidRankingRule.RATE_THEN_TERM :
  //     settings.bid_ranking_rule,
  //   contract_start_month: settings.contract_start_month && settings.contract_start_month.toString(),
  //   contract_start_year: settings.contract_start_year && settings.contract_start_year.toString(),
  //   has_second_window: settings.has_second_window,
  //   te_design: settings.te_design,
  //   demand_max: settings.demand_max,
  //   demand_min: '1', //settings.demand_min,
  //   rate_decimal_places: settings.rate_decimal_places.toString(),
  //   rate_min: settings.rate_min,
  //   rate_threshold_1: settings.rate_threshold_1,
  //   rate_threshold_2: settings.rate_threshold_2,
  //   rate_threshold_3: settings.rate_threshold_3,
  //   rate_threshold_4: settings.rate_threshold_4,
  //   term_max_at_max_rate: settings.term_max_at_max_rate && settings.term_max_at_max_rate.toString(),
  //   term_min: settings.term_min.toString(),
  //   term_threshold_1: settings.term_threshold_1 && settings.term_threshold_1.toString(),
  //   term_threshold_2: settings.term_threshold_2 && settings.term_threshold_2.toString(),
  //   term_threshold_3: settings.term_threshold_3 && settings.term_threshold_3.toString(),
  //   term_threshold_4: settings.term_threshold_4 && settings.term_threshold_4.toString(),
  //   threshold_count: settings.threshold_count.toString(),
  //   use_constant_days_per_month: settings.use_constant_days_per_month,
  //   window_one_closes: settings.window_one_closes,
  //   window_one_opens: settings.window_one_opens,
  //   window_two_closes: settings.window_two_closes,
  //   window_two_opens: settings.window_two_opens,
  //   withdrawals_allowed: settings.withdrawals_allowed && settings.withdrawals_allowed.toString(),
  // }))
}
