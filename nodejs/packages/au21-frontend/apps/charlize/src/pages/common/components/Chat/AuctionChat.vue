<template>
  <div class="AuctionChat" :style="{width: width + 'px'}">
    <a-list
      class="_messages"
      :style="{height: message_height +'px'}"
      ref="list"
      itemLayout="horizontal"
      :dataSource="messages"
      :locale="{emptyText: 'No Messages'}"
    >
      <a-list-item
        :locale="{emptyText: ''}"
        slot="renderItem"
        slot-scope="message, index"
        :key="message.id"
        :class="{
          '_item': true,
          '_item--of-trader': is_auctioneer && isTraderMessage(message),
        }"
      >
        <a-list-item-meta :description="message.message">
          <div slot="title">{{ message.timestamp_label }} &nbsp;> &nbsp;{{ message.from }} to {{ message.to }}</div>
        </a-list-item-meta>
      </a-list-item>
    </a-list>

    <div class="_info"
         v-if="!is_auctioneer">
      Type message to auctioneer
      <br>(message won't be seen by other traders)
    </div>

    <a-textarea
      v-model="message"
      class="au-input mt-1"
      style="resize: none; margin: 2px"
      :style="{width: (width - 4) + 'px'}"
      :autoSize="{ minRows: 1, maxRows: 12 }"
      @pressEnter="submit"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {scrollToBottom} from '@au21-frontend/utils';
import {AuMessageType, MessageElement} from '@au21-frontend/client-connector';

@Component({
  name: 'AuctionChat',
  components: {Blinker},
})
export default class AuctionChat extends Vue {
  @Prop({required: true}) is_auctioneer: boolean;
  @Prop({required: true, type: Array}) messages: MessageElement[]
  @Prop({required: true}) outer_height: number
  @Prop({required: true}) width: number

  message = ''

  get message_height(): number {
    return this.is_auctioneer ?
      this.outer_height - 58 :
      this.outer_height - 87
  }


  @Watch('messages')
  messages__change() {
    this.$nextTick(this.scrollToBottom)
  }

  mounted() {
    this.scrollToBottom()
  }

  scrollToBottom() {
    scrollToBottom((this.$refs.list as any).$el)
  }

  submit(event: KeyboardEvent) {
    if (event.ctrlKey || event.shiftKey) {
      return
    }
    this.$emit('submitMessage', this.message)
    this.message = ''
    event.preventDefault()
  }

  isTraderMessage(message: MessageElement): boolean {
    return message.message_type === AuMessageType.TRADER_TO_AUCTIONEER
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.AuctionChat {
  display: flex;
  justify-content: stretch;
  flex-direction: column;
  white-space: pre-wrap; // TODO: not sure that wrapping is working on long words!

  ._info {
    background-color: darken(@layout-color, 10);
    color: @au-label-color;
    height: 28px;
    line-height: 1.1em;
    padding: 0 3px;
  }

  ._messages {
    overflow: auto;
  }

  /deep/ .ant-list-empty-text {
    color: @au-label-color;
  }

  /deep/ .ant-list {
    background-color: hsl(193, 9%, 19%); // lighten(@dark-bg-color, 15);

    &.ant-list-split .ant-list-item {
      border-bottom-color: transparent;
    }
  }

  /deep/ .ant-list-empty-text {
    padding: 50px 30px;
    color: @au-primary-color;
  }

  /deep/ .ant-list-item {
    .ant-list-item-meta {
      overflow-wrap: break-word;
      width: 100%;

      .ant-list-item-meta-content {
        width: 100%;
      }
    }

    &:nth-child(2n) {
      background-color: hsl(195, 10%, 16%);
    }

    ._item--of-trader {
      background-color: darken(#832633, 10);
    }

    ._item--of-trader + ._item--of-trader {
      border-top: none;
    }

    padding: 3px 3px;
  }

  /deep/ .ant-list-item-meta-title {
    color: darken(@au-label-color, 10%);
    font-size: 12px;
    margin-bottom: 0;
    line-height: 16px;
  }

  /deep/ .ant-list-item-meta-description {
    font-size: 13px;
    font-weight: 550;
    line-height: 16px;
    color: lighten(@au-label-color, 5%);
  }
}
</style>
