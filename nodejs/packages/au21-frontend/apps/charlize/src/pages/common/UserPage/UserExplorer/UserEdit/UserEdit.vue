<template>
  <div class="UserEdit">
    <div class="mb-1">Username:</div>
    <AuInput
      style="width: 100%" left :output="output" v-model="user.username"
    />
    <div class="mt-2 mb-1">Password:</div>
    <AuInput
      style="width: 100%" left :output="output" v-model="user.password"
    />

    <div class="mt-2">Role:</div>
    <UserRoleSelect :disabled="output" :value="user.role" @input="onRoleSelect" v-model="user.role"/>

    <div class="mt-2 mb-1">Company:</div>
    <AuSelect
      style="width: 100%"
      :disabled="user.role !== AuUserRole.TRADER"
      v-model="user.company_id"
      :options="companies"
      :textBy="item => item.company_longname"
      :valueBy="item => item.id"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import UserRoleSelect from '../UserRoleSelect/UserRoleSelect.vue';
import AuInput from '../../../../../ui-components/AuInput/AuInput.vue';
import {AuUserRole, CompanyElement, UserElement} from '@au21-frontend/client-connector';
import AuSelect from '../../../../../ui-components/AuSelect/AuSelect.vue';

@Component({
  components: {AuSelect, AuInput, UserRoleSelect},
})
export default class UserEdit extends Vue {
  @Prop({required: true}) user: UserElement
  @Prop({required: true, type: Array}) companies: CompanyElement[]
  @Prop({type: Boolean}) output: boolean

  AuUserRole = AuUserRole

  onRoleSelect(role: AuUserRole) {
    if (this.user.role !== AuUserRole.TRADER) {
      this.user.company_id = null
    }
    this.user.role = role
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../../au-styles/variables.less';

.UserEdit {
  color: @au-text-color;
  background-color: @au-background-light;
  padding: 15px;
  overflow: auto;
}
</style>

