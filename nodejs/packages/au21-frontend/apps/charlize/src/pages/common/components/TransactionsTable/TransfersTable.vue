<template>
  <div class="TransfersTable">
    <div class="_filters" v-if="!hide_filters">
      <div class="_filters-selects">
        <div class="_filters-item">
          <label>
            <span class="_filters-label">Date: </span>
            <au-date-picker
              style="width: 200px"
              dense
              range
              v-model="filters.date_range"
              :options="auctions"
            />
          </label>
        </div>

        <div v-if="!is_scoped_by_auction" class="_filters-item">
          <label>
            <span class="_filters-label">Auction: </span>
            <au-select
              style="width: 200px"
              dense
              v-model="filters.auction_id"
              :value-by="auction => auction.id"
              :text-by="auction => auction.name"
              :options="auctions"
            />
          </label>
        </div>

        <div class="_filters-item">
          <label>
            <span class="_filters-label">Cause: </span>
            <au-select
              style="width: 200px"
              dense
              v-model="filters.cause"
              :value-by="item => item.key"
              :text-by="item => item.text"
              :options="causes"
            />
          </label>
        </div>

        <div class="_filters-item">
          <label>
            <span class="_filters-label">By User: </span>
            <au-select
              style="width: 200px"
              dense
              v-model="filters.by_user"
              :options="usernames_unique"
            />
          </label>
        </div>

        <div v-if="!use_counterparty_mode" class="_filters-item">
          <label>
            <span class="_filters-label">Company: </span>
            <au-select
              style="width: 200px"
              dense
              v-model="filters.company_shortname"
              :options="companies"
              :value-by="item => item.id"
              :text-by="item => item.name"
            />
          </label>
        </div>

        <div v-if="use_counterparty_mode" class="_filters-item">
          <label>
            <span class="_filters-label">Seller: </span>
            <au-select
              style="width: 200px"
              dense
              v-model="filters.seller_company_shortname"
              :options="seller_companies"
              :value-by="item => item.id"
              :text-by="item => item.name"
            />
          </label>
        </div>

        <div v-if="use_counterparty_mode" class="_filters-item">
          <label>
            <span class="_filters-label">Buyer: </span>
            <au-select
              style="width: 200px"
              dense
              v-model="filters.buyer_company_shortname"
              :options="buyer_companies"
              :value-by="item => item.id"
              :text-by="item => item.name"
            />
          </label>
        </div>
      </div>

      <div style="flex-basis: 10%; justify-items: center" class="ml-2">
        <a-button style="height: 24px; margin-top: 4px" size="small" @click="clearFilters()">Clear</a-button>
      </div>
    </div>

    <AuAgGrid
      :columnDefs="columnDefs"
      :rowData="rows"
      :gridOptions="gridOptions"
      :height="table_height"
      :width="width"
    />
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {ColDef, GridOptions} from 'ag-grid-community';
import {
  CreditTransferElement,
  DummyRow,
  TransactionElementCause,
  TransactionElementCause_map,
  TransferTableCellParams,
} from './TransfersTable.types';
import TransfersTableCell from './TransfersTableCell.vue';
import {DateTimeValue,} from '@au21-frontend/client-connector';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import {range, sortBy} from 'lodash';
import AuDatePicker from '../../../../ui-components/AuDatePicker/AuDatePicker.vue';
import TransfersTableSortableHeader from './TransfersTableSortableHeader.vue';
import AuAgGridCenteredHeader from '../../../../ui-components/AuAgGridCenteredHeader.vue';

type TransfersTableFilters = {
  date_range: null | [DateTimeValue, DateTimeValue],
  auction_id: null | string,
  cause: null | TransactionElementCause,
  seller_company_shortname: null | string,
  buyer_company_shortname: null | string,
  company_shortname: null | string,
  by_user: null | string,
}

const get_default_filters = (): TransfersTableFilters => ({
  date_range: null,
  auction_id: null,
  cause: null,
  seller_company_shortname: null,
  buyer_company_shortname: null,
  company_shortname: null,
  by_user: null,
});


@Component({
  name: 'TransfersTable',
  components: { AuDatePicker, AuSelect, AuAgGrid },
})
export default class TransfersTable extends Vue {
  @Prop({ required: true, type: Array }) transaction_elements: CreditTransferElement[];
  @Prop({ type: Boolean }) is_scoped_by_auction: boolean;
  @Prop({ type: Boolean }) is_scoped_by_trader: boolean;
  @Prop({ type: Boolean }) hide_filters: boolean;
  @Prop({ required: true, type: Number }) height: number;
  @Prop({ required: true, type: Number }) width: number;
  @Prop({ type: Boolean }) use_counterparty_mode: boolean;

  sortDirection = 'desc'

  get table_height () {
    return this.height -  (this.hide_filters ? 64: 0)
  }

  gridOptions: GridOptions = {
    headerHeight: 28,
    defaultColDef: {
      headerComponentFramework: AuAgGridCenteredHeader,
      cellRendererFramework: TransfersTableCell,
      suppressSizeToFit: true,
    },
    rowHeight: 24,
    suppressHorizontalScroll: true,
  };

  get columnDefs (): ColDef[] {
    const CURRENCY_FIELD_WIDTH = 110

    const columns = [
      {
        headerName: 'Date',
        width: 140,
        headerComponentFramework: TransfersTableSortableHeader,
        cellRendererParams: {
          field: 'date_string',
          getSort: () => this.sortDirection,
          setSort: (direction) => {
            this.sortDirection = direction
          },
        } as TransferTableCellParams,
      },
      {
        headerName: 'Auction',
        width: 280,
        cellRendererParams: {
          hide: this.is_scoped_by_auction,
          field: 'auction_name',
        } as TransferTableCellParams,
      },
      {
        headerName: 'Cause',
        width: 120,
        cellRendererParams: {
          field: 'cause',
        } as TransferTableCellParams,
      },
      {
        headerName: 'By User',
        width: 80,
        cellRendererParams: {
          field: 'by_user',
        } as TransferTableCellParams,
      },

      // Counterparty mode only
      {
        headerName: 'Buyer company',
        width: 80,
        cellRendererParams: {
          hide: !this.use_counterparty_mode || this.is_scoped_by_trader,
          field: 'buyer_company_shortname',
        } as TransferTableCellParams,
      },
      {
        headerName: 'Seller company',
        width: 80,
        cellRendererParams: {
          hide: !this.use_counterparty_mode,
          field: 'seller_company_shortname',
        } as TransferTableCellParams,
      },
      {
        headerName: 'Amount Δ',
        width: CURRENCY_FIELD_WIDTH,
        cellRendererParams: {
          hide: !this.use_counterparty_mode,
          field: 'counterparty_amount_delta',
        } as TransferTableCellParams,
      },
      {
        headerName: 'Amount balance',
        width: CURRENCY_FIELD_WIDTH,
        cellRendererParams: {
          hide: !this.use_counterparty_mode,
          field: 'counterparty_amount_balance',
        } as TransferTableCellParams,
      },

      // Dealer mode only
      {
        headerName: 'Company',
        width: 80,
        cellRendererParams: {
          hide: this.use_counterparty_mode,
          field: 'company_shortname',
        } as TransferTableCellParams,
      },
      {
        headerName: 'Amount Δ',
        width: CURRENCY_FIELD_WIDTH,
        cellRendererParams: {
          hide: this.use_counterparty_mode,
          field: 'amount_delta',
        } as TransferTableCellParams,
      },
      {
        headerName: 'Amount balance',
        width: CURRENCY_FIELD_WIDTH,
        cellRendererParams: {
          hide: this.use_counterparty_mode,
          field: 'amount_balance',
        } as TransferTableCellParams,
      },
      {
        headerName: `Volume Δ`,
        width: 80,
        cellRendererParams: {
          hide: this.use_counterparty_mode,
          field: 'volume_delta',
        } as TransferTableCellParams,
      },
      {
        headerName: 'Volume balance',
        width: 80,
        cellRendererParams: {
          hide: this.use_counterparty_mode,
          field: 'volume_balance',
        } as TransferTableCellParams,
      },
      // Filler column
      {
        headerName: '',
        suppressSizeToFit: false,
        cellRendererParams: {
          field: 'filler',
        } as TransferTableCellParams,
      },
    ]

    return columns.filter(column => !column.cellRendererParams.hide)
  }

  // ****** Filters handling ******
  filters = get_default_filters();

  clearFilters () {
    this.filters = get_default_filters();
  }

  get usernames_unique (): string[] {
    return [...new Set(this.transaction_elements.map(element => element.by_user))];
  }

  get companies () {
    return this.filter_by_id_and_name('company_shortname')
  }
  get seller_companies () {
    return this.filter_by_id_and_name('seller_company_shortname')
  }
  get buyer_companies () {
    return this.filter_by_id_and_name('buyer_company_shortname')
  }
  get auctions () {
    return this.filter_by_id_and_name('auction_name')
  }

  get causes (): { key: TransactionElementCause, text: string }[] {
    return (Object.keys(TransactionElementCause) as TransactionElementCause[]).map(cause => ({
      key: cause,
      text: TransactionElementCause_map[cause],
    }))
  }

  get transaction_elements_filtered (): CreditTransferElement[] {
    let result = this.transaction_elements.filter(transaction => {
      const field_off = (field: keyof TransfersTableFilters | keyof CreditTransferElement) =>
        this.filters[field] && transaction[field] !== this.filters[field];

      if (field_off('by_user')) return false;
      if (field_off('cause')) return false;
      if (field_off('auction_id')) return false;

      // Dealer mode
      if (field_off('company_shortname')) return false;

      // Counterparty mode
      if (field_off('buyer_company_shortname') && this.use_counterparty_mode) return false;
      if (field_off('seller_company_shortname') && this.use_counterparty_mode) return false;

      if (this.filters.date_range) {
        // TODO Implement date filter
        return false
      }

      return true;
    })

    result = sortBy(result, 'date_timestamp')
    if (this.sortDirection === 'desc') {
      result = result.reverse()
    }
    return result
  }

  get empty_row_count(): number {
    const header_height = 28;
    const row_height = 28;
    const empty_rows_height = this.table_height - header_height - (row_height * this.transaction_elements_filtered.length) + row_height // to force scroll;
    if (empty_rows_height <= 0) {
      return 0
    }
    return Math.ceil(empty_rows_height / row_height);
  }

  get rows () {
    const rows: (CreditTransferElement | DummyRow)[] = this.transaction_elements_filtered;

    range(this.empty_row_count).forEach(index => {
      rows.push({ id: 'empty-row.' + index, type: 'dummy' });
    });

    return rows
  }

  filter_by_id_and_name <ID extends keyof CreditTransferElement, NAME extends keyof CreditTransferElement>(name_field: NAME, id_field?: ID): {id: CreditTransferElement[ID] | CreditTransferElement[NAME], name: CreditTransferElement[NAME]}[] {
    const itemsMap = new Map<CreditTransferElement[ID] | CreditTransferElement[NAME], {id: CreditTransferElement[ID] | CreditTransferElement[NAME], name: CreditTransferElement[NAME]}>()

    this.transaction_elements.forEach(element => {
      const id = element[id_field || name_field]
      if (itemsMap.has(id)) {
        return
      }
      itemsMap.set(id, {id: id, name: element[name_field]})
    })

    return [...itemsMap.values()]
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.TransfersTable {
  ._filters {
    display: flex;
    &-selects {
      width: 900px;
      display: flex;
      flex-wrap: wrap;
    }
    &-item {
      color: white;
      padding: 4px;
      margin: 0 6px;
      flex-basis: 32%;
    }
    &-label {
      min-width: 75px;
      display: inline-block;
    }
  }
}
</style>
