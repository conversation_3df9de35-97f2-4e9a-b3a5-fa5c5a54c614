<template>
   <svg
    :viewBox="`0 0 ${width} ${height}`"
    :width="width"
    :height="height"
  >
    <g>
      <rect
        :x="4"
        :y="0"
        height="18"
        :width="width -5"
        :rx="rect_r"
        :ry="rect_r"
        :style="first_style"
      />

      <rect
        :x="4"
        :y="20"
        height="18"
        :width="width - 5"
        :rx="rect_r"
        :ry="rect_r"
        :style="second_style"
      />

      <rect
        :x="4"
        :y="40"
        height="18"
        :width="width - 5"
        :rx="rect_r"
        :ry="rect_r"
        :style="third_style"
      />

      <!-- TODO: center the text -->

      <text
        :y="12.5"
        fill="#111"
        text-anchor="middle"
      >
        <tspan :x="width / 2" font-size="10" font-weight="bold">
          {{ first_label }}
        </tspan>
      </text>

      <text
        :y="33"
        fill="#111"
        text-anchor="middle"
      >
        <tspan :x="width / 2" font-size="10" font-weight="bold">
          {{ second_label }}
        </tspan>
      </text>

      <text
        :y="53"
        fill="#111"
        text-anchor="middle"
      >
        <tspan :x="width / 2" font-size="10" font-weight="bold">
          {{ third_label }}
        </tspan>
      </text>

    </g>
  </svg>
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({
  components: {}
})
export default class ThreeStateDisplay extends Vue {

  @Prop({ required: true }) first_label: string;
  @Prop({ required: true }) second_label: string;
  @Prop({ required: true }) third_label: string;
  @Prop({ required: true }) first_color: string;
  @Prop({ required: true }) second_color: string;
  @Prop({ required: true }) third_color: string;
  @Prop({ required: true }) width: number;
  @Prop({ default: 50 }) height: number;

  rect_r = 3;

  baseStyle = {
    strokeWidth: 3,
    fillOpacity: '1.0'
  };

  get first_style() {
    return {
      ...this.baseStyle,
      fill: this.first_color
    };
  }

  get second_style() {
    return {
      ...this.baseStyle,
      fill: this.second_color
    };
  }

  get third_style() {
    return {
      ...this.baseStyle,
      fill: this.third_color
    };
  }

}
</script>

<style scoped>
</style>
