<template>
  <div class="LoginPage flex-center"
       style="width: 100%;"
       :style="`height: ${screen.page_height}px`">
    <LoginForm
      style="width: 250px;"
      @submit="submit"
    />
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import LoginForm from './LoginForm.vue';
import {Container} from 'typescript-ioc';
import {login_command, SocketConnector} from '@au21-frontend/client-connector';
import {AuScreen} from "../../../plugins/screen-plugin/AuScreen";
import {detect} from "detect-browser";

@Component({
  components: {LoginForm},
})
export default class LoginPage extends Vue {
  connector = Container.get(SocketConnector)

  screen = new AuScreen(true);

  browser = detect();

  submit({username, password}) {
    this.connector.publish(login_command({
      username,
      password
    }))
  }
}
</script>

<style lang="less" scoped>
.LoginPage {

}
</style>
