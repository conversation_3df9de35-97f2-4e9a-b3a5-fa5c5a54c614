import {AuUserRole, ClientSocketState} from '@au21-frontend/client-connector';

export function create__clearUserElement() {
  return {
    id: '',
    company_id: '',
    company_longname: '',
    company_shortname: '',
    current_auction_id: '',
    email: '',
    has_connection_problem: false,
    isAuctioneer: false,
    isObserver: false,
    isOnline: false,
    isTester: false,
    password: '',
    phone: '',
    role: AuUserRole.TRADER,
    socket_state: ClientSocketState.OPENED,
    socket_state_last_closed: null,
    termination_reason: null,
    user_id: '',
    username: ''
  };
}
