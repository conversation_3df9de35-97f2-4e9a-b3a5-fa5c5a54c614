<template>
  <div class="BrowserAgent">
    {{ `${browser.name}: ${browser.version}  (${browser.os})` }}
  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {Container} from "typescript-ioc";
import {SocketConnector} from "@au21-frontend/client-connector";

const {detect} = require('detect-browser');


@Component({
  name: 'BrowserAgent',
  components: {}
})
export default class BrowserAgent extends Vue {

  connector = Container.get(SocketConnector)

  browser = detect();

}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.BrowserAgent {

}
</style>
