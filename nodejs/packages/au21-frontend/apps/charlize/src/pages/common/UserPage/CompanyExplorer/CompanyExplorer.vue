<template>
  <div class="CompanyExplorer">
    <AuSectionBorder>
      <div class="au-header" style="margin-left: 10px !important;">Companies</div>
      <div style="flex: 1 1; display: flex; align-items: stretch;">
        <CompanyTable
          :height="bottomHeight"
          :width="tableWidth"
          :companies="companies"
          @selected="selectCompany"
        />

        <div style="flex: 1 1; background-color: #666666">
          <div style="display: flex; justify-content: center; padding-top: 6px;">
            <div class="mr-1">
              Company:
            </div>
            <a-button
              class="mr-1 au-btn"
              :disabled="!selectedCompany"
              type="primary"
              @click="startEditingCompany()"
            >
              Edit
            </a-button>
            <a-button
              class="mr-1 au-btn"
              type="primary"

              @click="startCreatingCompany()"
            >
              New
            </a-button>&nbsp;

            <a-popconfirm
              v-if="selectedCompany"
              placement="bottomRight"
              okText="Yes"
              cancelText="No"
              @confirm="deleteCompany()"
              :disabled="!selectedCompany"
            >
              <template slot="title">
                <p>Delete "{{ selectedCompany.company_longname }}"?</p>
              </template>
              <a-button
                class="au-btn"
                type="primary"
              >
                Delete
              </a-button>
            </a-popconfirm>
            <a-button disabled v-else
                      class="au-btn"
                      type="primary"
            >
              Delete
            </a-button>
          </div>

          <CompanyEdit
            v-if="selectedCompany"
            :company="selectedCompany"
            output
          />
        </div>
      </div>
    </AuSectionBorder>

    <a-modal
      title="Create company"
      class="ant-modal--no-padding"
      v-if="companyToCreate"
      :visible="true"
      @cancel="cancelCompanyEdit()"
      closable
      centered
      width="330px"
    >
      <CompanyEdit
        :company="companyToCreate"
        :style="{maxHeight: this.modalHeight + 'px'}"
      />

      <a-button
        slot="footer"
        type="primary"
        class="au-btn"
        @click="cancelCompanyEdit()"
      >
        Cancel
      </a-button>

      <a-button
        slot="footer"
        class="au-btn"
        type="primary"
        @click="saveUser(companyToCreate)"
      >
        Create
      </a-button>

    </a-modal>

    <a-modal
      title="Edit company"
      class="ant-modal--no-padding"
      v-if="companyToEdit"
      :visible="true"
      @cancel="cancelCompanyEdit()"
      closable
      centered
      width="330px"
    >
      <CompanyEdit
        v-if="companyToEdit"
        :company="companyToEdit"
        :style="{maxHeight: this.modalHeight + 'px'}"
      />

      <a-button
        slot="footer"
        type="primary"
        class="au-btn"
        @click="cancelCompanyEdit()"
      >
        Cancel
      </a-button>

      <a-button
        slot="footer"
        class="au-btn"
        type="primary"
        @click="saveUser(companyToEdit)"
      >
        Save
      </a-button>
    </a-modal>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import CompanyEdit from './CompanyEdit/CompanyEdit.vue';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import {
  company_delete_command,
  company_save_command,
  CompanyElement,
  SocketConnector,
  UserElement
} from '@au21-frontend/client-connector';
import CompanyTable from './CompanyTable.vue';
import {create__clearCompanyElement} from './CompanyElement.factory';
import {CharlizeClient} from '../../../../services/connector/CharlizeClient';
import AuSectionBorder from "../../../../ui-components/AuSectionBorder.vue";

@Component({
  components: {AuSectionBorder, CompanyTable, CompanyEdit},
})
export default class CompanyExplorer extends Vue {
  @Prop({required: true}) companies: CompanyElement[];
  @Prop({required: true}) users: UserElement[];

  client = Container.get(CharlizeClient)
  connector = Container.get(SocketConnector)
  screen = new AuScreen(true)

  selectedCompanyId: string | null = null
  companyToCreate: CompanyElement | null = null
  companyToEdit: CompanyElement | null = null

  tableWidth = 482

  created() {
    this.client.subscribe('CommandSucceeded', this.cancelCompanyEdit)
  }

  beforeDestroy() {
    this.client.unsubscribe('CommandSucceeded', this.cancelCompanyEdit)
  }

  get selectedCompany() {
    return this.companies.find(company => company.company_id === this.selectedCompanyId) || null
  }

  get bottomHeight(): number {
    return this.screen.page_height - 55
  }

  get modalHeight() {
    return this.screen.modal_height
  }

  cancelCompanyEdit() {
    this.companyToCreate = null
    this.companyToEdit = null
  }

  startCreatingCompany() {
    this.companyToCreate = create__clearCompanyElement()
  }

  startEditingCompany() {
    this.companyToEdit = {...this.selectedCompany}
  }

  saveUser(company: CompanyElement) {
    this.connector.publish(company_save_command({
      company_id: company.company_id,
      company_longname: company.company_longname,
      company_shortname: company.company_shortname,
    }))
  }

  selectCompany(company: CompanyElement) {
    this.selectedCompanyId = company.company_id
  }

  deleteCompany() {
    this.connector.publish(company_delete_command({
      company_id: this.selectedCompanyId,
    }))
  }
}
</script>

<style lang="less" scoped>
.CompanyExplorer {
  margin-left: 2px;
}
</style>
