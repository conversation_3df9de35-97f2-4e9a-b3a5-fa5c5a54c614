<template>
  <a-modal
    v-if="text"
    :visible="true"
    title="Error"
    @cancel="text = null"
    closable
    centered
    width="450px"
  >
    <div style="padding: 16px;" :style="{maxHeight: modalHeight + 'px'}">
      <div style="white-space: pre-wrap">{{ text }}</div>
    </div>

    <a-button
      slot="footer"
      type="primary"
      size="small"
      class="au-btn"
      @click="text = null"
    >
      Ok
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import {SocketConnector} from '@au21-frontend/client-connector';

@Component({})
export default class AlertModal extends Vue {

  screen = new AuScreen() // same for auctioneer or trader
  connector = Container.get(SocketConnector)

  text: string | null = null

  get modalHeight() {
    return this.screen.modal_height
  }

  // TODO Reimplement.
  // created () {
  //   this.connector.bus.$on('BrowserMessageEvent', this.onAlert)
  // }
  //
  // beforeDestroy () {
  //   this.connector.bus.$off('BrowserMessageEvent', this.onAlert)
  // }
  //
  // onAlert (event: BrowserMessageEvent) {
  //   console.log('event', event)
  //   if (event.browser_message_kind !== BrowserMessageKind.ALERT) {
  //     return
  //   }
  //   this.text = event.content.join('\r\n')
  // }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';


// THESE SEEM TO HAVE NO EFFECT !!

//.AlertModal {
//  .ant-modal-body {
//    color: @au-text-color-secondary;
//    padding: 0;
//  }
//
//  &__html-container {
//    height: 500px;
//
//    p {
//      margin-bottom: 0;
//    }
//
//    overflow: auto;
//    padding: 12px 15px;
//    background-color: @au-pseudo-input-color;
//  }
//}
</style>
