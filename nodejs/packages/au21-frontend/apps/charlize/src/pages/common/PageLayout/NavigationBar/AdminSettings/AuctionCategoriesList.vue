<template>
  <div class="AuctionCategoriesList">
    <div
      class="_item"
      v-for="auction_category in auction_categories"
      :key="auction_category.id"
    >
      <div class="_item-text">{{auction_category.name}}</div>
      <div class="spacer"/>

      <a-popconfirm
        :title="`Delete ${auction_category.name}?`"
        placement="bottom"
        @confirm="$emit('delete', auction_category)"
        okText="Yes"
        cancelText="No"
      >
        <a-button
          class="_item-action"
          icon="close"
          size="small"
        />
      </a-popconfirm>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AuctionCategory} from './AuctionCategory__demo.types';

@Component({
  name: 'AuctionCategoriesList',
})
export default class AuctionCategoriesList extends Vue {
  @Prop({required: true}) auction_categories: AuctionCategory[];
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../../au-styles/variables.less";

.AuctionCategoriesList {
  background-color: hsl(203, 6%, 9%) !important;
  color: @white;
  overflow-y: scroll;
  ._item {
    border-bottom: 1px solid hsl(0, 0%, 23%) !important;
    display: flex;
    flex-wrap: nowrap;
    padding: 3px 5px;
    justify-content: space-between;
    ._item-text {
      flex-grow: 0;
    }
    ._item-action {
      flex-grow: 0;
      background: transparent;
      &:hover {
        background: @ag-grid-background-color;
      }
      border-color: transparent;
      /deep/ .anticon {
        color: @white !important;
      }
    }
  }
}
</style>
