<template>
  <a-button
    class="au-btn"
    @click="$emit('addTraders')"
    icon="usergroup-add"
    :disabled="false"
    type="primary"
    size="small"
  >Notice

  </a-button>
  <!--  <ToolbarButton-->
  <!--    class="AuctionNoticeButton"-->
  <!--    icon="paper-clip"-->
  <!--    @click="showAuctionNotice = true"-->
  <!--  >-->
  <!--    Notice-->
  <!--  </ToolbarButton>-->
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuctionNoticeModal from './AuctionNoticeModal.vue';
import {Container} from 'typescript-ioc';
import {notice_save_command, SocketConnector} from '@au21-frontend/client-connector';
import ToolbarButton from '../../../De/DeAuctioneer/toolbar/ToolbarButton.vue';
import {CharlizeClient} from '../../../../services/connector/CharlizeClient';

@Component({
  components: { ToolbarButton, AuctionNoticeModal }
})
export default class AuctionNoticeButton extends Vue {
  @Prop({required:true}) auction_id:string;
  @Prop({required:true}) notice:string;

  connector = Container.get(SocketConnector);
  client = Container.get(CharlizeClient);

  showAuctionNotice = false;
  @Prop({ type: Boolean }) output: boolean;

  created() {
    this.client.subscribe('CommandSucceeded', this.onCommandSucceeded);
  }

  beforeDestroy() {
    this.client.unsubscribe('CommandSucceeded', this.onCommandSucceeded);
  }

  onCommandSucceeded() {
    this.showAuctionNotice = false;
  }

  saveNotice(notice) {
    this.connector.publish(notice_save_command({
      auction_id: this.auction_id,
      notice
    }));
  }
}
</script>

<style lang="less" scoped>
.AuctionNoticeButton {
}
</style>
