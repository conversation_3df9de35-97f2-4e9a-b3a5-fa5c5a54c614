<template>
  <AuAgGrid
    v-test:auction_list
    class="AuctionTable"
    ref="table"
    :columnDefs="columnDefs"
    :gridOptions="gridOptions"
    :rowData="rows"
    :height="height"
    :auto_refresh="false"
  />
</template>
<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AuctionRowElement} from '@au21-frontend/client-connector';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import AuctionTableRowRenderer from './AuctionTableRowRenderer.vue';
import {range} from 'lodash';

@Component({
  components: {AuAgGrid, AuctionTableRowRenderer},
})
export default class AuctionTable extends Vue {
  @Prop({
    type: Array,
    required: true,
  }) auction_rows: AuctionRowElement[];
  @Prop({type: <PERSON>olean}) is_auctioneer: boolean;

  no_blink_auction_ids: string[] = [];
  screen = new AuScreen(true)


  get height(): number {
    return this.screen.page_height - 55;
  }

  async created() {
    this.no_blink_auction_ids = this.auction_rows.map(row => row.auction_id)
  }

  gridOptions: GridOptions = {
    getRowNodeId: (data: AuctionRowElement) => data.id,
    animateRows: false,
    headerHeight: 0,
    defaultColDef: {
      autoHeight: true,
    },
  }

  columnDefs: ColDef[] = [
    {
      colId: '0',
      headerName: 'Auctions',
      cellRendererFramework: AuctionTableRowRenderer,
    },
  ]

  on_row_click(auction_id): void {
    this.$emit('auction_selected', auction_id);
  }

  on_delete_click(auction_id): void {
    this.$emit('auction_delete', auction_id);
  }

  get empty_row_count(): number {
    const row_height = 70; // this seems to work even though the row only has a height of 55px.
    const empty_height = this.height - (row_height * this.auction_rows.length);
    return empty_height > 0 ?
      Math.floor(empty_height / row_height)
      : 0;
  }

  get rows(): AuctionRowElement[] {
    const rows = [...this.auction_rows];
    range(this.empty_row_count + 3).forEach(index => {
      rows.push({
        id: 'empty-row.' + index,
        auction_name: '',
      } as AuctionRowElement);
    });
    return rows;
  }

  toggleHidden(auctionRow: AuctionRowElement) {
    this.$emit(auctionRow.isHidden ? 'unhide' : 'hide', auctionRow.auction_id)
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables";

.AuctionTable {

}
</style>
