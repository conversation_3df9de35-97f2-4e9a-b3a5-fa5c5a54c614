<template>
  <a-modal
    class="TransfersModal"
    title="Auction Transactions"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="1125px"
  >
    <TransactionsTable
      :transaction_elements="transaction_elements"
      :height="modalHeight"
      :width="1115"
      :use_counterparty_mode="true"
      :is_scoped_by_auction="is_scoped_by_auction"
      hide_filters
    />
    <a-button
      slot="footer"
      type="primary"
      class="au-btn"
      @click="$emit('close')"
    >
      Cancel
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';
import {CharlizeStore} from '../../../../services/connector/CharlizeStore';
import TransactionsTable from './TransfersTable.vue';
import {
  createMultipleByClosure,
  random_bool,
  random_date_time,
  random_enum,
  random_from_array,
  random_number,
  random_number_string,
  random_string,
  random_timestamp,
} from '@au21-frontend/utils';
import {CreditTransferElement, TransactionElementCause,} from './TransfersTable.types';
import _ from 'lodash';


// TODO Remove these and use real data.
const auctions = createMultipleByClosure(index => ({id: `${index}` ,name: `auction_${index}`}), 3)
const companies = createMultipleByClosure(index => ({id: `${index}` ,name: `company_${index}`}), 4)
const createDemo__TransactionElement = (): CreditTransferElement => {
  const is_volume = random_bool()
  const is_auctioneer = random_bool()

  const auction = random_from_array(auctions)
  const company = random_from_array(companies)
  const buyer_company = random_from_array(companies)
  const seller_company = random_from_array(companies.filter(company => company !== buyer_company))

  return {
    id: random_string(8),
    date_timestamp: random_timestamp(),
    date_string: random_date_time(),
    auction_id: auction.id,
    auction_name: auction.name,
    cause: random_enum(TransactionElementCause),
    // This data doesn't really follow invariants,
    // which might or might not be a problem as that's mostly backend domain.
    company_shortname: is_auctioneer ? '' : company.name,
    buyer_company_shortname: buyer_company.name,
    seller_company_shortname: seller_company.name,
    by_user: `u_${random_string(3)}`,
    is_auctioneer,
    volume_delta: is_volume ? `${random_bool() ? '' : '-'}${random_number(10)}` : null,
    volume_balance: is_volume ? `${random_number(100)}` : null,
    amount_delta: is_volume ? null : `${random_bool() ? '' : '-'}$${random_number(10_000_000)}`,
    amount_balance: is_volume ? null : `$${random_number(100_000_000)}`,
    counterparty_amount_delta: random_number_string(100_000_000),
    counterparty_amount_balance: random_number_string(100_000_000),
  }
};

@Component({
  components: { TransactionsTable}
})
export default class TransfersModal extends Vue {
  @Prop({required: true}) store: CharlizeStore;
  @Prop({ type: String }) trader_id: string;

  screen = new AuScreen(true);
  // TODO Should be retrieved from store.
  transaction_elements = _.times(20, createDemo__TransactionElement);

  get modalHeight() {
    return this.screen.modal_height > 700 ? 700 : this.screen.modal_height;
  }

  get is_scoped_by_auction () {
    return !!this.store.live_store.de_auction
  }
}
</script>

<style lang="less" scoped>
.TransfersModal {
}
</style>
