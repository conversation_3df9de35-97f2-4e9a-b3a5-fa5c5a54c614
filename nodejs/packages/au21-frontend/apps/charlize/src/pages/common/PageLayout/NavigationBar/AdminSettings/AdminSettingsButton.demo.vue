<template>
  <VbDemo>
    <VbCard>
      <AdminSettingsButton :store="store"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator'
import AdminSettingsButton from './AdminSettingsButton.vue'
import {createDemo__store_for_auctioneer} from '../../../../../demo-helpers/CharlizeStore.helper';

@Component({
  name: 'AdminSettingsButtonDemo',
  components: { AdminSettingsButton },
})
export default class AdminSettingsButtonDemo extends Vue {
  store =  createDemo__store_for_auctioneer();
}
</script>
