<template>
  <VbDemo>
    <VbCard title="default">
      <button @click="changeContents()">Change contents</button>
      <br>
      <br>
      <InfoPanel
        heading="Round price"
        :contents="contents"
        units="cpp"
        :width="65"
        :color="colors.auPrice"
      />
    </VbCard>
    <VbCard title="slot">
      <button @click="changeContents()">Change contents</button>
      <button @click="directionUp = !directionUp">Change direction</button>
      <br>
      <br>
      <InfoPanel
        heading="Round price"
        :contents="contents"
        :obs="`${contents}-${directionUp}`"
        units="cpp"
        :width="85"
        :color="colors.auPrice"
      >
        <template #after>&nbsp;{{directionUp ? '/\\' : '\\/'}}</template>
      </InfoPanel>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import InfoPanel from './InfoPanel.vue';
import {Container} from 'typescript-ioc';
import {random_number_string} from '@au21-frontend/utils';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: { InfoPanel, AuSelect }
})
export default class InfoPanelDemo extends Vue {
  colors = Container.get(AuColors)
  contents = '100.000'
  directionUp = true
  changeContents () {
    this.contents = random_number_string(100000)
  }
}
</script>
