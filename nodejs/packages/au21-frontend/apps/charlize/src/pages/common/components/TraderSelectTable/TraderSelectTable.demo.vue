<template>
  <VbDemo>
    <VbCard>
      <TraderSelectTable
        :companies="companies"
        :companyIds.sync="companyIds"
        :width="600"
        :height="300"
      />
      {{ companyIds }}
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import TraderSelectTable from './TraderSelectTable.vue';
import _ from 'lodash';
import {createDemo__CompanyElement} from '../../../../demo-helpers/CompanyElement.helper';

@Component({
  components: { TraderSelectTable },
})
export default class TraderSelectTableDemo extends Vue {
  companies = _.times(20, createDemo__CompanyElement)
  companyIds = [this.companies[0].id]
}
</script>
