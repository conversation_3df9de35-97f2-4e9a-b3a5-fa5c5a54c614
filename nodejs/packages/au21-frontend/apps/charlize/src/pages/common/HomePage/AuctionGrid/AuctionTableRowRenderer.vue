<template>
  <Blinker
    :value="auction"
    :disabled="blinkerDisabled"
    background
    initial
    @click.native="on_row_click()"
    class="AuctionTableRowRenderer"
  >
    <div class="_bottom_row" :style="`width:${auction_col_width}px`">
      <div
        v-if="auction.auction_name"
        style="height: 24px; position: relative; top: -6px"
      >
        <div class="_label" style="width: 450px">Start time:</div>
        <div class="_heading"></div>
        <div class="_label">Status:</div>
        <div class="_heading" style="width: 350px;"></div>
        <div style="float: right; position: relative; display:inline-block; top: 6px;">
          <template v-if="auction.isClosed">
            <span v-if="auction.isHidden">
              (hidden from traders)
            </span>&nbsp;
            <AButton
              v-if="is_auctioneer"
              size="small"
              class="mr-1"
              primary
              @click.stop="toggleHidden"
            >
              {{ auction.isHidden ? "Unhide" : "Hide" }}
            </AButton>
          </template>
          <AButton
            v-if="is_auctioneer"
            size="small"
            primary
            @click.stop="on_delete_click()"
          >
            Delete
          </AButton>
        </div>
      </div>

      <div class="_row_text">
        {{ auction.auction_name }}
      </div>
    </div>
  </Blinker>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {AuctionRowElement} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import {AuColors} from 'apps/charlize/src/au-styles/AuColors';
import {AuScreen} from "../../../../plugins/screen-plugin/AuScreen";
import AuctionTable from "./AuctionTable.vue";
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {sleep} from '@au21-frontend/utils';

@Component({
  components: { Blinker}
})
export default class AuctionTableRowRenderer extends Vue {
  params = null;
  colors = Container.get(AuColors);
  screen = new AuScreen(true);

  on_delete_click() {
    this.table.on_delete_click(this.auction.auction_id)
  }

  async mounted () {
    // TODO Not really great blinker implementation.
    //  The problem is that ag grid rerenders all rows on row addition.
    //  So internal state is getting reset and it's becoming pretty hard to figure,
    //  whether row was on page or has been recently added.

    // Trigger blink for new row.
    if (this.blinkerDisabled) {
      return
    }
    await sleep(500)
    if (!this.blinkerDisabled) {
      this.table.no_blink_auction_ids.push(this.auction.auction_id)
    }
  }

  on_row_click(): void {
    // Filler row click
    if (!this.auction.auction_id) {
      return
    }
    this.table.on_row_click(this.auction.auction_id)
  }

  get blinkerDisabled (): boolean {
    if (!this.auction.auction_id) {
      // Empty rows shouldn't blink
      return true
    }
    return this.table.no_blink_auction_ids.includes(this.auction.auction_id)
  }

  get table(): AuctionTable {
    return this.$parent.$parent.$parent as AuctionTable
  }

  get is_auctioneer(): boolean {
    return this.table.is_auctioneer
  }

  get blinkerEnabled (): boolean {
    return true // TODO: what happened to this?:  this.table.blinkerEnabled
  }

  get auction(): AuctionRowElement {
    return this.params.data
  }

  get auction_col_width(): number {
    return this.screen.app_width - 60
  }

  toggleHidden () {
    this.table.toggleHidden(this.auction)
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.AuctionTableRowRenderer {

  div:hover {
    background-color: hsl(0, 0%, 25%); //@au-background-light;
  }

  border-radius: 6px;
  font-size: 11px !important;
  height: 45px !important;
  margin: 5px;
  cursor: pointer;
  //&:hover {
  //  background-color: @au-background-light;
  //  //opacity: 0.1;
  //}

  ._bottom_row {
    //border: 1px solid red;
    border: 0;
    border-radius: 5px;
    margin: 0;
    padding: 0 10px;
    overflow: hidden;
   // width: 100%;
    line-height: 1.5;

    &_text {
      white-space: normal;
      word-break: break-all;
    }
  }

  ._label {
    color: @au-label-color; // hsl(77, 10%, 70%); //@au-label-color;
    display: inline-block;
    font-size: 12px;
    font-weight: bold;
    height: 20px;
    margin-right: 3px;
  }

  ._heading {
    // border: 1px solid green;
    display: inline-block;
    height: 20px;
  }
}
</style>
