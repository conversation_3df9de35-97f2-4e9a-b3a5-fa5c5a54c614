<template>
  <div class="HomePage page">
    <div
      class="au-header"
      style="display: flex; justify-content: space-between;"
    >
      <div>Auctions</div>

      <a-radio-group
        class="ml-3 mt-05"
        v-model="filter"
      >
        <a-radio value="all" class="au-legend-small">All</a-radio>
        <a-radio value="open" class="au-legend-small">Open</a-radio>
        <a-radio value="closed" class="au-legend-small">Closed</a-radio>
      </a-radio-group>

      <div class="spacer"/>

      <AuctionSettingsButton
        v-if="isAuctioneer"
        :crud="Crud.CREATE"
        :store="store"
        label="New auction"
      />
    </div>
    <div>
      <AuctionTable
        title="Open Auctions"
        :auction_rows="auction_rows"
        :is_auctioneer="is_auctioneer"
        @auction_selected="on_auction_selected"
        @auction_delete="on_auction_delete"
        @hide="hideAuction"
        @unhide="unhideAuction"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuctionSettingsButton from '../../De/DeAuctioneer/auction_settings/AuctionSettingsButton.vue';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../plugins/screen-plugin/AuScreen';
import {
  auction_row_command,
  auction_select_command,
  AuctionInstruction,
  AuctionRowElement,
  AuUserRole,
  Crud,
  SocketConnector,
} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../services/connector/CharlizeStore';
import AuctionTable from './AuctionGrid/AuctionTable.vue';

@Component({
  components: {AuctionTable, AuctionSettingsButton},
})
export default class HomePage extends Vue {
  @Prop({required: true}) store: CharlizeStore;

  connector = Container.get(SocketConnector);
  de_settings = null;

  get screen():AuScreen {
    return new AuScreen(this.store?.live_store?.session_user?.isAuctioneer || false);
  }

  filter: 'all' | 'open' | 'closed' = 'all'

  get auction_rows(): AuctionRowElement[] {
    let auction_rows = this.store.live_store.auction_rows;

    if (this.filter === 'all') {
      return auction_rows
    }
    if (this.filter === 'open') {
      return auction_rows.filter(row => !row.isClosed)
    }
    if (this.filter === 'closed') {
      return auction_rows.filter(row => row.isClosed)
    }
  }

  get is_auctioneer(): boolean {
    return this.store.live_store.session_user?.role == AuUserRole.AUCTIONEER;
  }

  saveAuction() {
    alert('saveAuction not implemented');
  }

  Crud = Crud;

  // TODO Possibly deprecated.
  hideAuction(auction: any) {
    this.connector.publish(auction_row_command({
      auction_id: auction.auction_id,
      instruction: AuctionInstruction.HIDE,
    }));
  }

  unhideAuction(auction: any) {
    this.connector.publish(auction_row_command({
      auction_id: auction.auction_id,
      instruction: AuctionInstruction.UNHIDE,
    }));
  }

  on_auction_delete(auction_id: string): void {
    this.connector.publish(auction_row_command({
      auction_id,
      instruction: AuctionInstruction.DELETE,
    }));
  }

  on_auction_selected(auction_id: string) {
    this.connector.publish(auction_select_command({auction_id}));
  }

  get listHeight(): number {
    return (this.screen.page_height - 120) / 2;
  }

  get isAuctioneer(): boolean {
    return this.store.live_store.session_user.isAuctioneer;
  }
}
</script>
<style lang="less" scoped>
.HomePage {

}
</style>
