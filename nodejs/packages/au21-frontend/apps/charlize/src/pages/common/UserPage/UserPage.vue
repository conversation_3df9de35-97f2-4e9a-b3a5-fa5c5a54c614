<template>
  <div
    class="UserPage"
    style="display: flex"
  >
    <UserExplorer
      :companies="companies"
      :users="users"/>
    <CompanyExplorer
      :companies="companies"
      :users="users"/>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import UserExplorer from './UserExplorer/UserExplorer.vue';
import CompanyExplorer from './CompanyExplorer/CompanyExplorer.vue';
import AuSectionBorder from "../../../ui-components/AuSectionBorder.vue";
import {CompanyElement, UserElement} from "@au21-frontend/client-connector";

@Component({
  components: {AuSectionBorder, CompanyExplorer, UserExplorer},
})
export default class UserPage extends Vue {
  @Prop({required: true}) companies: CompanyElement[];
  @Prop({required: true}) users: UserElement[];

}
</script>

<style lang="less" scoped>
.UserPage {
  position: relative;
  top: -6px;

}
</style>
