<template>
  <div class="SessionPage page">

  </div>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../plugins/screen-plugin/AuScreen';
import {SocketConnector} from '@au21-frontend/client-connector';

@Component({
  components: { },
})
export default class SessionPage extends Vue {
//  @Prop({required:true}) store:CharlizeStore;

  screen = new AuScreen(true);
  connector = Container.get(SocketConnector)

  get sessions(){
    //return this.charlizeStore.sessions
    return []
  }
}
</script>
<style lang="less" scoped>
.SessionPage {

}
</style>
