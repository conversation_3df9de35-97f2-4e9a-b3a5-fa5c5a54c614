<template>
  <AuAgGrid
    v-if="users.length"
    class="UserTable"
    :height="height"
    :width="width"
    :columnDefs="columnDefs"
    :rowData="users"
    :getRowHeight="() => row_height"
    :gridOptions="gridOptions"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {getAuUserRoleName} from '../../../../entity-helpers/AuUserRole';
import {UserElement} from '@au21-frontend/client-connector';
import {ICellRendererParams} from 'ag-grid-community/dist/lib/rendering/cellRenderers/iCellRenderer';

// const comparator = (valueA, valueB, nodeA, nodeB) => {
//   if (!valueA && !valueB)
//     return 0;
//   else if (!valueA)
//     return -1;
//   else if (!valueB)
//     return 1;
//   else return a.localeCompare(b);
// };

const getComparator = (valueGetter: (user: UserElement) => string) => {
  return (valueA, valueB, nodeA, nodeB, isInverted) => {
    const a: UserElement = nodeA.data;
    const b: UserElement = nodeB.data;
    return valueGetter(a).localeCompare(valueGetter(b));
  };
};

@Component({
  components: {AuAgGrid}
})
export default class UserTable extends Vue {
  @Prop({required: true, type: Array}) users: UserElement[];
  @Prop({required: true}) height: number;
  @Prop({required: true}) width: number;

  row_height = 24;
  gridOptions: GridOptions = null;

  created() {
    // We put this in created because emit goes crazy if put as class-component param.
    this.gridOptions = {
      headerHeight: 28,
      defaultColDef: {
        //
        cellStyle: () => ({padding: '0', border: '0'})
      },
      onRowSelected: (row) => {
        // Beware: ag grid calls this event on select AND deselect.
        if (row.node.isSelected()) {
          this.$emit('selected', row.data);
        }
      },
      rowSelection: 'single',
      suppressHorizontalScroll: true
    };
  }

  get columnDefs(): ColDef[] {
    return [
      {
        headerName: 'Username',
        cellRenderer: (params: ICellRendererParams) => {
          const user: UserElement = params.data;
          return `<div>${user.username}</div>`;
        },
        sortable: true,
        comparator: getComparator(user => user.username),
        sort: 'asc',
        width: 150
      },
      {
        headerName: 'Company',
        cellRenderer: (params: ICellRendererParams) => {
          const user: UserElement = params.data;
          return `<div>${user.company_longname}</div>`;
        },
        sortable: true,
        comparator: getComparator(user => user.company_longname)
      },
      {
        headerName: 'Role',
        cellRenderer: (params: ICellRendererParams) => {
          const user: UserElement = params.data;
          return `<div>${getAuUserRoleName(user.role)}</div>`;
        },
        sortable: true,
        comparator: getComparator(user => getAuUserRoleName(user.role)),
        width: 100
      }
    ];
  }

  // get empty_row_count(): number {
  //   const body_height = this.height - 28; // ie: - header_height
  //   const rows_height = this.users.length * this.row_height;
  //   const empty_height = body_height - rows_height;
  //   return empty_height > 0 ?
  //     (empty_height / this.row_height)
  //     : 0;
  //   //  console.log({body_height, rows_height, empty_height, empty_rows})
  //   // return empty_rows
  // // }

}

</script>

<style lang="less" scoped>
.UserTable {
  /deep/ .ag-cell {
    margin: 0;
    padding: 0;
    border: 0;
  }
}
</style>
