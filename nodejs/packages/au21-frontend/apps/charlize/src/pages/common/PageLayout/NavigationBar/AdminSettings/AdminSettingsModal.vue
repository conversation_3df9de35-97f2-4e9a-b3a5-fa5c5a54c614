<template>
  <a-modal
    class="AdminSettingsModal"
    title="Admin settings"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="570px"
  >
    <div class="_container">
      <div class="_alert">
        Changing auction settings would wipe all existing auctions. Take extreme caution!
      </div>

      <div class="_form-item">
        <label class="_label">Counterparty credits:&nbsp;</label>
        <a-radio-group
          v-model:value="adminSettings.use_counterparty_credits"
          :default-value="false"
        >
          <a-radio :value="true">yes</a-radio>
          <a-radio :value="false">no</a-radio>
        </a-radio-group>
      </div>


      <hr>
      <div>
        <label class="_label">Categories:</label>
      </div>
      <AuctionCategoriesCrud
        style="height: 250px"
        :auction_categories="auction_categories"
      />
     <!-- ...-->
    </div>

   <!-- --------------------- -->

    <a-button
      slot="footer"
      type="primary"
      class="au-btn"
      @click="$emit('close')"
    >
      Cancel
    </a-button>

    <a-button
      slot="footer"
      class="au-btn"
      type="primary"
      @click="$emit('save')"
    >
      Save
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuctionCategoriesCrud from './AuctionCategoriesCrud.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__AuctionCategory} from '../../../../../demo-helpers/AuctionCategory.helper';

@Component({
  name: 'AdminSettingsModal',
  components: { AuctionCategoriesCrud },
})
export default class AdminSettingsModal extends Vue {
  // TODO Should use generated types from backend.
  @Prop({required: true}) adminSettings: {use_counterparty_credits: boolean};
  auction_categories = createMultipleByClosure(createDemo__AuctionCategory, 10)
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../../au-styles/variables.less';

.AdminSettingsModal {
  background-color: #111; //  hsl(186, 12%, 30%);
  border-radius: 5px;
  border: 1px solid #111;
  font-size: 12px;
  margin: 2px;
  padding: 2px;
  width: 555px;
  white-space: normal;

  ._form-item {
    display: flex;
    align-items: baseline;
  }

  ._legend {
    border-bottom-width: 0;
    color: @au-label-color !important;
    font-size: 13px;
    font-weight: bold;
    margin-left: 4px;
    margin-right: 3px;
    width: 250px;
  }

  ._label {
    color: @au-label-color !important;
    font-weight: bold;
    padding-left: 4px;
  }

  ._container {
    padding: 30px;
  }

  ._alert {
    padding: 20px 10px;
    background-color: @au-notification-error-background;
    color: @au-text-color;
    border-radius: 5px;
    margin-bottom: 15px;
  }
}
</style>
