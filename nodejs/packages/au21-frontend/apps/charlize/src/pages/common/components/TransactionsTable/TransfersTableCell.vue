<template>
  <div class="TransactionsTableCell">{{ value }}</div>
</template>

<script lang="ts">
import Vue from 'vue';
import {Component} from 'vue-property-decorator';
import {
  CreditTransferElement,
  DummyRow,
  is_dummy_row,
  TransactionElementCause_map,
  TransferTableCellParams,
} from './TransfersTable.types';

@Component
export default class TransactionsTableCell extends Vue {
  params = null;

  get cell_params (): TransferTableCellParams {
    return this.params.colDef.cellRendererParams;
  }

  get value () {
    const transaction: CreditTransferElement | DummyRow = this.params.data;

    if (is_dummy_row(transaction)) {
      return
    }

    const field = this.cell_params.field

    if (field === 'filler') {
      return
    }

    const value = transaction[field]

    if (field === 'cause') {
      return TransactionElementCause_map[transaction[field]]
    }

    return value
  }
}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.TransactionsTableCell {
  text-align: right;
  padding-right: 2px;
}
</style>
