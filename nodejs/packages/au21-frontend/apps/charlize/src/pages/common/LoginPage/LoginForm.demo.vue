<template>
  <VbDemo>
    <VbCard dashed no-padding refresh>
      <LoginForm
        @submit="$vb.log('submit', $event)"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import LoginForm from './LoginForm.vue';
import {LogMixin} from './logMixin';
import {Component, Vue} from 'vue-property-decorator';

@Component({
  mixins: [LogMixin],
  components: {
    LoginForm,
  },
})
export default class LoginFormDemo extends Vue {
}
</script>
