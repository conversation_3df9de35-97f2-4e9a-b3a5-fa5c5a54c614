<template>
  <div
    v-test:au_logo
    class="LogoFramed"
    :style="{width: width + 'px'}"
    @click="$emit('click')"
  >
    <img
      v-if="!isBrowserIE"
      :style="logo_style"
      alt="logo" :src="logoUrl"
    />
    <Logo v-else :height="height" :width="width" :src="logoUrl" />
  </div>
</template>

<script lang="ts">
import Logo from './Logo.vue';
import {logoUrl} from '../../../../store/config';
import {isBrowserIE} from '@au21-frontend/utils';
import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({
  components: { Logo }
})
export default class LogoFramed extends Vue {
  @Prop({ default: 42 }) height: number;
  @Prop({ default: 53 }) width: number;

  logoUrl = logoUrl;

  get isBrowserIE(): boolean {
    return isBrowserIE();
  }

  get logo_style() {
    return {
      height: this.height + 'px',
      width: this.width + 'px',
      'object-fit': 'scale-down',
      'object-position': 'center',
    //  'background-color': 'white'
    };
  }

}

</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables";

.LogoFramed {
  cursor: pointer;
}
</style>
