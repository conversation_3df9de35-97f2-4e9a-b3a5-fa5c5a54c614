<template>
  <VbDemo>
    <VbCard>
      <UserEdit
        style="width: 300px"
        :user="onUserRow"
        :companies="companies"
      />
    </VbCard>
    <VbCard>
      <pre>{{ onUserRow }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script>
import UserEdit from './UserEdit.vue';
import {createDemo__UserElement_for_trader} from '../../../../../demo-helpers/UserElement.helper';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__CompanyElement} from '../../../../../demo-helpers/CompanyElement.helper';

export default {
  components: {
    UserEdit,
  },
  data() {
    return {
      onUserRow: createDemo__UserElement_for_trader(),
      companies: createMultipleByClosure(createDemo__CompanyElement, 5),
    }
  },
}
</script>
