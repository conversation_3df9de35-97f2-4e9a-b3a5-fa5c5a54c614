<template>
  <div
    class="PageLayout"
    :style="`width: ${screen.app_width}px;`"
  >
    <div
      :style="`width: ${screen.app_width - 7}px`"
      style="margin-bottom: 6px"
    >
      <TraderNavBar
        v-if="!isAuctioneerSession"
        :store="store"
      />
      <AuctioneerNavBar
        v-if="isAuctioneerSession"
        :store="store"
      />
    </div>

    <transition name="au-fade" mode="out-in">
      <div
        v-if="isLoading"
        :style="`height:${screen.layout_height}px`"
        class="PageLayout__loading-container flex-center"
      >
        <div style="text-align: center;">
          <a-spin class="mb-2" size="large" />
          <div style="color: #0e0e0e; font-weight: bold; font-size: 16px;">
            Loading...
          </div>
        </div>
      </div>
    </transition>

    <div
      class="_page-container"
      style="display: flex; flex-direction: column; justify-content: stretch;"
    >
      <slot />
    </div>

  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuctioneerNavBar from './NavigationBar/AuctioneerNavBar.vue';
import TraderNavBar from './NavigationBar/TraderNavBar.vue';
import {AuScreen} from '../../../plugins/screen-plugin/AuScreen';
import {SessionUserValue} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../services/connector/CharlizeStore';
import AuSectionBorder from '../../../ui-components/AuSectionBorder.vue';

@Component({
  components: { AuSectionBorder, AuctioneerNavBar, TraderNavBar },
})
export default class PageLayout extends Vue {
  @Prop({ required: true }) store: CharlizeStore;

  get screen (): AuScreen {
    return new AuScreen(this.store?.live_store?.session_user?.isAuctioneer || false);
  }

  // colors = Container.get(AuColors);

  @Prop({ type: Boolean, default: false }) isLoading: boolean;

  get userSession (): SessionUserValue {
    return this.store.live_store.session_user;
  }

  get isAuctioneerSession (): Boolean {
    return this.store.live_store.session_user?.isAuctioneer;
  }

}
</script>

<style lang="less" scoped>
@import (reference) '../../../au-styles/variables.less';

.PageLayout {
  // border: #9aa6b1 1px solid;
  //border: 3px solid @au-background;
  border: 1px solid hsl(0, 0%, 40%);
  border-radius: @border-radius-panel;
  background-color: @layout-color; // @au-layout-color;
  //  background-color: @au-body-background;
  color: @au-text-color;
  // overflow: hidden;
  padding: 3px;
  position: relative;
  top: 10px;

  &__loading-container {
    background-color: fade(#dcdcdc, 90%);
    border-radius: @border-radius-panel;
    cursor: default;
    //max-height: calc(100% - 100px);
    //min-height: calc(100% - 100px);
    //position: absolute;
    //top: 82px;
    //width: 100%;
    z-index: 10;
  }

  ._page-container {
    // height: 100%;
    position: relative;


    // padding: 1px;

    // Moved to: page-layout

    // border: #9aa6b1 1px solid;
    // border-radius: 8px;
    // background-color: #666666;

  }

}
</style>
