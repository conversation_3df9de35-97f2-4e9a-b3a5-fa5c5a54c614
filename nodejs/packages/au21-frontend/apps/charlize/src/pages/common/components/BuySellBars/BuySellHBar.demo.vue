<template>
  <VbDemo>
    <VbCard title="Manual SELL 20">
      <BuySellHBar
        :width="60"
        :height="20"
        :buyMax="50"
        :sellMax="50"
        :match="10"
        :order_quantity_int="20"
        order_quantity_str="20"
        :order_type="OrderType.SELL"
        :order_submission_type="OrderSubmissionType.MANUAL"
      />
      <!--      <br>-->
      <!--      <button @click="togglequantityType">toggle quantity type: {{ quantity_type }}</button>-->
      <!--      <input type="number" v-model.number="orderquantity" />-->
    </VbCard>
    <VbCard title="Manual BUY 20">
      <BuySellHBar
        :width="40"
        :height="30"
        :buyMax="50"
        :sellMax="50"
        :match="0"
        :order_quantity_int="20"
        order_quantity_str="20"
        :order_type="OrderType.BUY"
        :order_submission_type="OrderSubmissionType.MANUAL"
      />
      <!--      <BuySellHBar-->
      <!--        :width="60"-->
      <!--        :height="20"-->
      <!--        :buyMax="50"-->
      <!--        :sellMax="50"-->
      <!--        :orderquantity="5"-->
      <!--        :isSell="true"-->
      <!--      />-->
      <!--      <BuySellHBar-->
      <!--        :width="60"-->
      <!--        :height="20"-->
      <!--        :buyMax="50"-->
      <!--        :sellMax="50"-->
      <!--        :orderquantity="50"-->
      <!--        :isSell="false"-->
      <!--      />-->
      <!--      <BuySellHBar-->
      <!--        :width="60"-->
      <!--        :height="20"-->
      <!--        :buyMax="50"-->
      <!--        :sellMax="50"-->
      <!--        :orderquantity="5"-->
      <!--        :isSell="false"-->
      <!--      />-->
    </VbCard>
    <VbCard title="Manual 0 quantity">
      <BuySellHBar
        :width="60"
        :height="20"
        :buyMax="50"
        :sellMax="50"
        :match="20"
        :order_quantity_int="0"
        order_quantity_str="0"
        :order_type="OrderType.NONE"
        :order_submission_type="OrderSubmissionType.MANUAL"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import BuySellHBar from './BuySellHBar.vue';
import {OrderSubmissionType, OrderType} from '@au21-frontend/client-connector';

@Component({
  components: { BuySellHBar }
})
export default class BuySellHBarDemo extends Vue {

  OrderType = OrderType;
  OrderSubmissionType = OrderSubmissionType;

}
</script>

<style scoped>
.VbCard {
  background-color: #252525;
}
</style>
