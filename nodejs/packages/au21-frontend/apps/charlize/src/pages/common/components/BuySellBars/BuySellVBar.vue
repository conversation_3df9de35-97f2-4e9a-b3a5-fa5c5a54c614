<template>
  <div
    class='BuySellVBar'
    :style='{ width: `${width + width_adjust}px`, height: `${height}px`}'
  >
    <svg
      class='_bar'
      :viewBox='`0 0 ${width + width_adjust} ${height}`'
      :width='width + width_adjust'
      :height='height'
    >
      <rect
        class='_rect'
        :y='volBarTop'
        :x='0'
        :height='volBarHeight'
        :width='width + width_adjust'
        :fill='barColor'
      />
      <rect
        class='_rect'
        :y='matchBarTop'
        :x='0'
        :height='matchBarHeight'
        :width='width + width_adjust'
        :fill='colors.au_match_dimmed()'
      />
    </svg>

    <div
      class='_match'
      :style='{color: colors.au_match()}'
    >
      <Blinker :value='`(${match}) `' />
    </div>

    <div
      class='_quantity'
      :style='{color: au_text_color}'
    >
      <Blinker :value='orderQuantity' />
    </div>

    <div
      class='_submission_label'
      :style='{color: au_text_color}'
    >
      <Blinker :value='orderSubmissionLabel' />
    </div>

  </div>
</template>

<script lang='ts'>
import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {OrderSubmissionType, OrderType} from '@au21-frontend/client-connector';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: { Blinker }
})
export default class BuySellVBar extends Vue {
  // TODO: why do we need 'type'? doesn't seem to do anything!
  @Prop({ type: Number, default: 65 }) width: number;
  @Prop({ type: Number, default: 20 }) height: number;
  @Prop({ type: Number, required: true }) buyMax: number;
  @Prop({ type: Number, required: true }) sellMax: number;

//  @Prop({ required: true }) round_trader_element: DeRoundTraderElement | null;

  @Prop({ required: true }) match: number;
  @Prop({ required: true }) order_quantity_int: number;
  @Prop({ required: true }) order_quantity_str: string;
  @Prop({ required: true }) order_type: OrderType;
  @Prop({ required: true }) order_submission_type: OrderSubmissionType;

  OrderType = OrderType;
  OrderSubmissionType = OrderSubmissionType;

  colors = Container.get(AuColors);

  barWidth = 10;
  width_adjust = 0;

  // get orderQuantityInt(): number {
  //   return this.round_trader_element?.quantity_int || 0;
  // }

  // get order_submission_type(): OrderSubmissionType | null {
  //   return this.round_trader_element?.order_submission_type;
  // }

  // get order_type(): OrderType | null {
  //   return this.round_trader_element?.order_type;
  // }

  // get orderQuantityStr(): string {
  //   return this.round_trader_element.quantity_str;
  // }

  get isSell(): boolean {
    return this.order_type == OrderType.SELL;
  }

  get center(): number {
    return this.width / 2;
  }

  get volBarHeight(): number {
    return (this.order_quantity_int * this.height) / (this.isSell ? this.sellMax : this.buyMax);
  }

  get volBarTop(): number {
    return this.height - this.volBarHeight;
  }

  get matchBarHeight(): number {
    return (this.match * this.height) / (this.isSell ? this.sellMax : this.buyMax);
  }

  get matchBarTop(): number {
    return this.height - this.matchBarHeight;
  }

  // get barLeft(): number {
  //   return this.isSell ? 35 : 35; // 5;
  // }

  get barColor() {
    return this.isSell ?
      // this.colors.au_sell_dimmedX2() :
      'hsla(0, 92%, 65%, 0.15)' :
      // this.colors.au_buy_dimmedX2();
      'hsla(120, 47%, 64%, 0.15)';
  }

  get textColor() {
    return this.isSell ? this.colors.au_sell() : this.colors.au_buy();
  }

  get orderQuantity(): string {
        return this.order_quantity_str;
  }

  get orderSubmissionLabel(): string {
    switch (this.order_submission_type) {
      case OrderSubmissionType.MANUAL:
        return ''
      case OrderSubmissionType.DEFAULT:
        return 'd';
      case OrderSubmissionType.MANDATORY:
        return 'm';
      default:
        return '---';
    }
  }

  get au_text_color(): string {
    return this.colors.order_quantity_text_color(this.order_type);
  }

}
</script>

<style lang='less' scoped>
@import (reference) "../../../../au-styles/variables.less";

.BuySellVBar {
  border: 0;
  margin: 0;
  padding: 0;
  position: relative;

  ._bar {
    border: 0;
    overflow: hidden;
    position: absolute;
    padding: 0;
    margin: 0;
  }

  ._quantity {
    border: 0;
    display: inline-block;
    font-size: 13px;
    margin: 0;
    padding: 0;
    position: absolute;
    right: 22px;
    text-align: right;
    //   top: 12px;
    //width: 14px;
  }

  ._submission_label{
    display: inline-block;
    font-size: 11px;
    position: relative;
    left: 55px;
  }

  ._match {
    display: inline-block;
    font-size: 10px;
    position: absolute;
    right: 47px;
    text-align: right;
    //   top: 12px;
    //width: 14px;
  }


}


</style>
