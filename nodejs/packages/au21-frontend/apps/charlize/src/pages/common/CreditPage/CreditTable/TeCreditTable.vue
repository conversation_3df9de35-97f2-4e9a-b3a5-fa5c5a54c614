<template>
  <AuAgGrid
    class="TeCreditTable"
    :style="{height: height + 'px', width: width + 'px'}"
    :columnDefs="columnDefs"
    :rowData="companies"
    :gridOptions="gridOptions"
    :height="height"
    :width="width"
  />
</template>

<script lang="ts">
import {Component, Prop, PropSync, Vue} from 'vue-property-decorator';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import SelectCell from './SelectCell.vue';
import SelectHeader from './SelectHeader.vue';
import {toggle} from '@au21-frontend/utils';
import {CompanyElement} from '@au21-frontend/client-connector';
import {ICellRendererParams} from 'ag-grid-community/dist/lib/rendering/cellRenderers/iCellRenderer';

const makeComparatorForString = (valueGetter: (company: CompanyElement) => string) => {
  return (valueA, valueB, nodeA, nodeB, isInverted) => {
    const a: CompanyElement = nodeA.data
    const b: CompanyElement = nodeB.data
    return valueGetter(a).localeCompare(valueGetter(b))
  }
}

const makeComparatorForNumeric = (valueGetter: (company: CompanyElement) => string) => {
  return (valueA, valueB, nodeA, nodeB, isInverted) => {
    const a: CompanyElement = nodeA.data
    const b: CompanyElement = nodeB.data
    return Number(valueGetter(a)) - Number(valueGetter(b))
  }
}

@Component({
  components: { AuAgGrid },
  provide() {
    return {
      // @Provide doesn't work for `this` :/.
      tableComponent: this,
    }
  },
})
export default class TeCreditTable extends Vue {
  @PropSync('companyIds', { type: Array }) companyIdsProxy: string[]
  @Prop() companies: CompanyElement[]
  @Prop() height: number
  @Prop() width: number

  gridOptions: GridOptions = {
    headerHeight: 28,
    defaultColDef: {
      //
      // headerComponentFramework: AuAgGridCenteredHeader,
      cellStyle: () => ({ padding: '0', border: '0' }),
      sortable: false,
    },
    rowHeight: 24,
    suppressHorizontalScroll: true,
  }

  // Required for select checkboxes to work properly.
  get items(): CompanyElement[] {
    return this.companies
  }

  get columnDefs(): ColDef[] {
    return [
      {
        headerName: 'Select',
        field: null,
        width: 20,
        headerComponentFramework: SelectHeader,
        cellRendererFramework: SelectCell,
      },
      {
        headerName: 'Company',
        sortable: true,
        sort: 'asc',
        field: 'company_shortname',
        comparator: makeComparatorForString(company => company.company_shortname),
        cellRenderer: (params: ICellRendererParams) => {
          const company: CompanyElement = params.data
          return `<div style="padding: 3px 5px;">${company.company_shortname}</div>`
        },
      },
      // {
      //   headerName: 'Default Risk',
      //   sortable: true,
      //   field: 'default_risk',
      //   width: 110,
      //   comparator: makeComparatorForNumeric(company => company.default_risk),
      //   cellRenderer: (params: ValueGetterParams) => {
      //     const company: CompanyElement = params.data
      //     return `<div style="padding: 3px 5px; text-align: right">${company.default_risk || 'none'}</div>`
      //   },
      // },
      // {
      //   headerName: 'Credit Limit',
      //   sortable: true,
      //   field: 'credit_limit',
      //   width: 110,
      //   comparator: makeComparatorForNumeric(company => company.credit_limit),
      //   cellRenderer: (params: ValueGetterParams) => {
      //     const company: CompanyElement = params.data
      //     return `<div style="padding: 3px 5px; text-align: right">${company.credit_limit || 'no limit'}</div>`
      //   },
      // },
    ]
  }

  // This is required for selection demo-components to function.
  onSelectAll(value: boolean): void {
    this.companyIdsProxy = value ? this.companies.map(company => company.id) : []
  }

  isValueSelected(company: CompanyElement): boolean {
    return this.companyIdsProxy.includes(company.id)
  }

  get isLessThanAllSelected(): boolean {
    return !!this.companyIdsProxy.length && (this.companyIdsProxy.length < this.companies.map(company => company.id).length)
  }

  get isAnyValueSelected(): boolean {
    return !!this.companyIdsProxy.length
  }

  selectValue(company: CompanyElement) {
    this.companyIdsProxy = toggle(this.companyIdsProxy, company.id)
  }
}
</script>

<style lang="less" scoped>
.TeCreditTable {
  .ag-header-cell {
    padding: 3px 5px !important;
  }
}
</style>
