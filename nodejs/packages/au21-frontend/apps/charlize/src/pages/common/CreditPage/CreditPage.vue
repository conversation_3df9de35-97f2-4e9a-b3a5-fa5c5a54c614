<template>
  <div class="CreditPage page">
    <div
      style="display: flex; justify-content: space-between; margin-bottom: 6px"
    >
      <div class="au-header">Counter-party Credit</div>
    </div>
    <div>
      <CreditTable
        :height="credit_table_height"
        :width="screen.width_inner"
        :counterparty_credits="counterpartyCredits"
        :companies="companies"
        @companySelected="selectedSeller = $event"
      />

      <!--TODO: Data not served from backend, mock -->
      <TransactionsTable
        :transaction_elements="transaction_elements"
        :height="transactions_table_height"
        :width="screen.width_inner"
        :use_counterparty_mode="true"
      />

      <CompanyCreditModal
        v-if="selectedSeller"
        :seller_id="selectedSeller.company_id"
        :seller_shortname="selectedSeller.company_shortname"
        :seller_longname="selectedSeller.company_longname"
        :store="store"
        @close="selectedSeller = null"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import ClientCreditTable from '../../De/DeAuctioneer/credit_edit/ClientCreditTable.vue';
import NumberInput from '../../../ui-components/NumberInput/NumberInput.vue';
import CreditTable from '../../De/DeAuctioneer/credit_table/CreditTable.vue';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../plugins/screen-plugin/AuScreen';
import {CompanyElement, SocketConnector} from '@au21-frontend/client-connector';
import {CharlizeStore} from '../../../services/connector/CharlizeStore';
import CompanyCreditModal from '../../De/DeAuctioneer/credit_edit/CompanyCreditModal.vue';
import {CharlizeClient} from '../../../services/connector/CharlizeClient';
import TransactionsTable from '../components/TransactionsTable/TransfersTable.vue';
import _ from 'lodash';
import {
  createMultipleByClosure,
  random_bool,
  random_date_time,
  random_enum,
  random_from_array,
  random_number,
  random_number_string,
  random_string,
  random_timestamp,
} from '@au21-frontend/utils';
import {CreditTransferElement, TransactionElementCause,} from '../components/TransactionsTable/TransfersTable.types';


// TODO Remove these and use real data.
const auctions = createMultipleByClosure(index => ({id: `${index}` ,name: `auction_${index}`}), 3)
const companies = createMultipleByClosure(index => ({id: `${index}` ,name: `company_${index}`}), 4)
const createDemo__TransactionElement = (): CreditTransferElement => {
  const is_volume = random_bool()
  const is_auctioneer = random_bool()

  const auction = random_from_array(auctions)
  const company = random_from_array(companies)
  const buyer_company = random_from_array(companies)
  const seller_company = random_from_array(companies.filter(company => company !== buyer_company))

  return {
    id: random_string(8),
    date_timestamp: random_timestamp(),
    date_string: random_date_time(),
    auction_id: auction.id,
    auction_name: auction.name,
    cause: random_enum(TransactionElementCause),
    // This data doesn't really follow invariants,
    // which might or might not be a problem as that's mostly backend domain.
    company_shortname: is_auctioneer ? '' : company.name,
    buyer_company_shortname: buyer_company.name,
    seller_company_shortname: seller_company.name,
    by_user: `u_${random_string(3)}`,
    is_auctioneer,
    volume_delta: is_volume ? `${random_bool() ? '' : '-'}${random_number(10)}` : null,
    volume_balance: is_volume ? `${random_number(100)}` : null,
    amount_delta: is_volume ? null : `${random_bool() ? '' : '-'}$${random_number(10_000_000)}`,
    amount_balance: is_volume ? null : `$${random_number(100_000_000)}`,
    counterparty_amount_delta: random_number_string(100_000_000),
    counterparty_amount_balance: random_number_string(100_000_000),
  }
};

@Component({
  components: {
    TransactionsTable,
    CompanyCreditModal,
    NumberInput,
    CreditTable: CreditTable,
    ClientCreditTable
  }
})
export default class CreditPage extends Vue {
  @Prop({required:true}) store:CharlizeStore;

  screen = new AuScreen(true)
  connector = Container.get(SocketConnector);
  charlizeClient = Container.get(CharlizeClient);

  companyIds = [];
  creditLimit = '';
  defaultRisk = '';

  selectedSeller: CompanyElement | null = null;
  // TODO Should be retreived from store.
  transaction_elements = _.times(20, createDemo__TransactionElement);

  no_risk = true;
  no_credit_limit = true;

  get credit_table_height() {
    return (this.screen.page_height - 60) / 2
  }
  get transactions_table_height() {
    return (this.screen.page_height - 240) / 2
  }

  created() {
    this.charlizeClient.subscribe('CommandSucceeded', this.onCommandSucceeded);
  }

  beforeDestroy() {
    this.charlizeClient.unsubscribe('CommandSucceeded', this.onCommandSucceeded);
  }

  onCommandSucceeded(command: any) {
    this.selectedSeller = null;
  }

  get companies() {
    return this.store.live_store.companies;
  }

  get counterpartyCredits() {
    return this.store.live_store.counterparty_credits;
  }

  saveCredit() {
    const request = {
      credit_limit: this.no_credit_limit ? 'none' : this.creditLimit,
      company_ids: this.companyIds,
      default_risk: this.no_risk ? 'none' : this.defaultRisk
    };
    // TODO
    // this.connector.publish(credit_save_command(request))
    this.resetForm();
  }

  resetForm() {
    this.companyIds = [];
    this.creditLimit = '';
    this.defaultRisk = '';
  }

}
</script>

<style lang="less" scoped>
.CreditPage {

}

</style>
