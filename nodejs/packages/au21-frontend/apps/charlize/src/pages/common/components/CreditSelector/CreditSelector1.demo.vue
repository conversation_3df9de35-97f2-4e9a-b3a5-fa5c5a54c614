<template>
  <VbDemo>
    <VbCard>
      <CreditSelector1/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import CreditSelector1 from './CreditSelector1.vue';
import AuInput from "../../../../ui-components/AuInput/AuInput.vue";
import NewCreditCell from "../../../De/DeAuctioneer/credit_edit/NewCreditCell.vue";

@Component({
  components: {NewCreditCell, AuInput, CreditSelector1},
})
export default class CreditSelector1Demo extends Vue {
  credit_limit = '0'

  setCredit(limit){
    this.credit_limit = limit
  }
}
</script>
