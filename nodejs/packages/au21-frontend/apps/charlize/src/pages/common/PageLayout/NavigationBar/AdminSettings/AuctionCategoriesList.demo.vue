<template>
  <VbDemo>
    <VbCard style="width: 300px">
      <AuctionCategoriesList
        style="height: 250px"
        :auction_categories="auction_categories"
        @delete="$vb.log('delete', $event)"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator'
import AuctionCategoriesList from './AuctionCategoriesList.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__AuctionCategory} from '../../../../../demo-helpers/AuctionCategory.helper';

@Component({
  name: 'AuctionCategoriesListDemo',
  components: { AuctionCategoriesList },
})
export default class AuctionCategoriesListDemo extends Vue {
  auction_categories = createMultipleByClosure(createDemo__AuctionCategory, 5)

}
</script>
