<template>
  <VbDemo>
    <VbCard title="Manual SELL 20">
      <BuySellVBar
        :width="60"
        :height="20"
        :buyMax="50"
        :sellMax="50"
        :match="20"
        :order_quantity_int='20'
        :order_quantity_str='`20`'
        :order_type="OrderType.SELL"
        :order_submission_type="OrderSubmissionType.MANUAL"
      />
      <!--      <br>-->
      <!--      <button @click="toggleQuantityType">toggle quantity type: {{ quantity_type }}</button>-->
      <!--      <input type="number" v-model.number="orderQuantity" />-->
    </VbCard>
    <VbCard title="Manual BUY 20">
      <BuySellVBar
        :width="60"
        :height="20"
        :buyMax="50"
        :sellMax="50"
        :match="10"
        :order_quantity_int='20'
        :order_quantity_str='`20`'
        :order_type="OrderType.BUY"
        :order_submission_type="OrderSubmissionType.MANUAL"
      />
      <!--      <BuySellVBar-->
      <!--        :width="60"-->
      <!--        :height="20"-->
      <!--        :buyMax="50"-->
      <!--        :sellMax="50"-->
      <!--        :orderQuantity="5"-->
      <!--        :isSell="true"-->
      <!--      />-->
      <!--      <BuySellVBar-->
      <!--        :width="60"-->
      <!--        :height="20"-->
      <!--        :buyMax="50"-->
      <!--        :sellMax="50"-->
      <!--        :orderQuantity="50"-->
      <!--        :isSell="false"-->
      <!--      />-->
      <!--      <BuySellVBar-->
      <!--        :width="60"-->
      <!--        :height="20"-->
      <!--        :buyMax="50"-->
      <!--        :sellMax="50"-->
      <!--        :orderQuantity="5"-->
      <!--        :isSell="false"-->
      <!--      />-->
    </VbCard>
    <VbCard title="Manual 0 quantity">
      <BuySellVBar
        :width="60"
        :height="20"
        :buyMax="50"
        :sellMax="50"
        :match="30"
        :order_quantity_int='0'
        :order_quantity_str='`0`'
        :order_type="OrderType.NONE"
        :order_submission_type="OrderSubmissionType.MANUAL"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import BuySellVBar from './BuySellVBar.vue';
import {OrderSubmissionType, OrderType} from '@au21-frontend/client-connector';

@Component({
  components: { BuySellVBar }
})
export default class BuySellVBarDemo extends Vue {

  OrderType = OrderType;
  OrderSubmissionType = OrderSubmissionType;

}
</script>

<style scoped>
.VbCard {
  background-color: #252525;
}
</style>
