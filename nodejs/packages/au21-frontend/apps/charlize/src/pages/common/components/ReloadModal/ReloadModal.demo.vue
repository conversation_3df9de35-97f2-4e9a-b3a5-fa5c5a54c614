<template>
  <VbDemo>
    <VbCard>
      <button @clikc="value = true">Show</button>
      <ReloadModal v-if="value" />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import ReloadModal from './ReloadModal.vue';

@Component({
  name: 'ReloadModalDemo',
  components: { ReloadModal },
})
export default class ReloadModalDemo extends Vue {
  value = false
}
</script>
