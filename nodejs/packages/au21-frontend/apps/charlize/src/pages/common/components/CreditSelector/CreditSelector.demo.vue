<template>
  <VbDemo>
    <VbCard>
      <CreditSelector
        :width="120"
        v-model="credit_limit"
        :decimal-places="2"
      />
      <pre>{{ credit_limit }}</pre>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import CreditSelector from './CreditSelector.vue';

@Component({
  components: { CreditSelector },
})
export default class CreditSelectorDemo extends Vue {
  credit_limit = 'no limit';
}
</script>
