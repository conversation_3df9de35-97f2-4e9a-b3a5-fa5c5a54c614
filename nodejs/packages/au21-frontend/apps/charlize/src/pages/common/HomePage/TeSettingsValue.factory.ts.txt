import { TeBidRankingRule, TeDesign, TeSettingsValue } from "../../../_generated/generated";

export function createEmpty__TeSettingsValue (): TeSettingsValue {
  return {
    annual_discount_rate: null,
    auction_id: null,
    auction_name: '',
    bid_constraints: [],
    bid_must_improve_highest: false,
    bid_must_improve_own: false,
    bid_quiet_period: null, // could be zero
    bid_ranking_rule: TeBidRankingRule.AGGREGATE_NPV,
    contract_start_month: null,
    contract_start_year: null,
    demand_max: '',
    demand_min: '1',
    te_design: TeDesign.RATE_AND_TERM,
    rate_decimal_places: 5,
    rate_min: '',
    rate_threshold_1: '',
    rate_threshold_2: null,
    rate_threshold_3: null,
    rate_threshold_4: null,
    term_max_at_max_rate: 500,
    term_min: 1,
    term_threshold_1: null,
    term_threshold_2: null,
    term_threshold_3: null,
    term_threshold_4: null,
    threshold_count: 1,
    use_constant_days_per_month: false,
    has_second_window: false,
    window_one_opens: null,
    window_one_closes: null,
    window_two_closes: null,
    window_two_opens: null,
    withdrawals_allowed: null,
  }
}

