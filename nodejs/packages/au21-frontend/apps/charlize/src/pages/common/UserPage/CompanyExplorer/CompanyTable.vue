<template>
  <AuAgGrid
    v-if="companies.length"
    class="CompanyTable"
    :height="height"
    :width="width"
    :columnDefs="columnDefs"
    :rowData="companies"
    :getRowHeight="() => row_height"
    :gridOptions="gridOptions"
  />
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {ColDef, GridOptions} from 'ag-grid-community';
import AuAgGrid from '../../../../ui-components/AuAgGrid.vue';
import {CompanyElement} from '@au21-frontend/client-connector';
import {ICellRendererParams} from 'ag-grid-community/dist/lib/rendering/cellRenderers/iCellRenderer';

const getComparator = (valueGetter: (company: CompanyElement) => string) => {
  return (valueA, valueB, nodeA, nodeB, isInverted) => {
    const a: CompanyElement = nodeA.data
    const b: CompanyElement = nodeB.data
    return valueGetter(a).localeCompare(valueGetter(b))
  }
}

@Component({
  name: 'CompanyTable',
  components: {AuAgGrid},
})
export default class CompanyTable extends Vue {
  @Prop({required: true, type: Array}) companies: CompanyElement[]
  @Prop({required: true}) height: number;
  @Prop({required: true}) width: number;

  row_height = 24;
  gridOptions: GridOptions

  created() {
    // We put this in created because emit goes crazy if put as class-component param.
    this.gridOptions = {
      headerHeight: 28,
      defaultColDef: {
        cellStyle: () => ({padding: '0', border: '0'}),
      },
      onRowSelected: (row) => {
        // Beware: ag grid calls this event on select AND deselect.
        if (row.node.isSelected()) {
          this.$emit('selected', row.data)
        }
      },
      rowSelection: 'single',
      suppressHorizontalScroll: true,
    }
  }

  get columnDefs(): ColDef[] {
    return [
      {
        headerName: 'Short name',
        cellRenderer: (params: ICellRendererParams) => {
          const company: CompanyElement = params.data
          return `<div>${company.company_shortname}</div>`
        },
        sortable: true,
        comparator: getComparator(company => company.company_shortname),
        sort: 'asc',
        width: 100,
      },
      {
        headerName: 'Long name',
        cellRenderer: (params: ICellRendererParams) => {
          const company: CompanyElement = params.data
          return `<div>${company.company_longname || ''}</div>`
        },
        sortable: true,
        comparator: getComparator(company => company.company_longname),
      },
    ]
  }
}
</script>

<style lang="less" scoped>
.CompanyTable {
  /deep/ .ag-cell {
    margin: 0;
    padding: 0;
    border: 0;
  }
}
</style>
