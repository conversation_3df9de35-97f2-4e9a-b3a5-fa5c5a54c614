<template>
  <div class="SelectHeader" v-test:select_all_traders @click="selected = indeterminate || !selected">
    <a-checkbox
      @click.prevent :indeterminate="indeterminate" :checked="selected"
    />
  </div>
</template>

<script lang="ts">
import {Component, Inject, Vue} from 'vue-property-decorator';

@Component({})
export default class SelectHeader extends Vue {
  params = null

  @Inject() readonly tableComponent: {
    onSelectAll: (value: boolean) => void,
    isValueSelected: (value: any) => boolean,
    isAnyValueSelected: boolean,
    isLessThanAllSelected: boolean,
    selectValue: (any) => void
  }

  set selected(value: boolean) {
    this.tableComponent.onSelectAll(value)
  }

  get indeterminate(): boolean {
    return this.tableComponent.isLessThanAllSelected
  }

  get selected(): boolean {
    return this.tableComponent.isAnyValueSelected && !this.tableComponent.isLessThanAllSelected
  }
}
</script>

<style lang="less" scoped>
.SelectHeader {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
