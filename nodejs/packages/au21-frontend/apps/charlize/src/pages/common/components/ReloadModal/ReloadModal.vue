<template>
  <a-modal
    v-if="message"
    :visible="true"
    title="Error"
    @cancel="reload()"
    closable
    centered
    width="450px"
  >
    <div style="padding: 16px;" :style="{maxHeight: modalHeight + 'px'}">
      <div style="white-space: pre-wrap">{{ message }}</div>
    </div>

    <a-button
      slot="footer"
      type="primary"
      @click="reload()"
    >
      Ok
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {SocketConnector, TerminateSession} from '@au21-frontend/client-connector';
import {Container} from 'typescript-ioc';
import {AuScreen} from '../../../../plugins/screen-plugin/AuScreen';

@Component({})
export default class ReloadModal extends Vue {
  connector = Container.get(SocketConnector)

  screen = new AuScreen();

  message = ''

  get modalHeight() {
    return this.screen.modal_height
  }

  reload() {
    window.location.reload()
  }

  created() {
    // throw new Error('Not Implemented')
    // TODO: Do we really need a message bus? We lose all typing with this (This Event doesn't exist!)
    // this.connector.bus.$on('BrowserReloadWithMessageEvent', this.onAlert)
  }

  beforeDestroy() {
    // Unsubscribing is not needed as we're gonna reload the page anyway.
  }

  onAlert(event: TerminateSession) {
    this.message = event.message
  }
}
</script>

<style lang="less" scoped>
@import (reference) '../../../../au-styles/variables.less';

.ReloadModal {
  .ant-modal-body {
    color: @au-text-color-secondary;
    padding: 0;
  }

  &__html-container {
    height: 500px;

    p {
      margin-bottom: 0;
    }

    overflow: auto;
    padding: 12px 15px;
    background-color: @au-pseudo-input-color;
  }
}
</style>
