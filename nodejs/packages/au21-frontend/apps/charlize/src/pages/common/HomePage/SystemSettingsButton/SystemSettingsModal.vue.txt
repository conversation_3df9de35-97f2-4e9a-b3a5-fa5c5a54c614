<template>
  <a-modal
    class="SystemSettingsModal" title="Auction settings"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="320px"
  >
    <div :style="{maxHeight: modalHeight + 'px'}">
      <div>
        <a-checkbox v-model="site.show_bh_auctions"/>
        <label class="SystemSettingsModal__label">Backhaul Auction:&nbsp;</label>
      </div>
      <div>
        <a-checkbox v-model="site.show_de_auctions"/>
        <label class="SystemSettingsModal__label">Double Auction:&nbsp;</label>
      </div>
      <div>
        <a-checkbox v-model="site.show_mr_auctions"/>
        <label class="SystemSettingsModal__label">Multi-round Auction:&nbsp;</label>
      </div>
      <div>
        <a-checkbox v-model="site.show_te_auctions"/>
        <label class="SystemSettingsModal__label">Transport English Auction:&nbsp;</label>
      </div>
      <div>
        <a-checkbox v-model="site.show_to_auctions"/>
        <label class="SystemSettingsModal__label">Transport Optimization Auction:&nbsp;</label>
      </div>

      <pre>{{site}}</pre>
    </div>

    <a-button
      slot="footer"
      @click="$emit('saveSystemSettings', site)"
    >
      Save
    </a-button>

    <a-button
      slot="footer"
      type="dashed"
      @click="$emit('close')"
    >
      Close
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { Container } from "typescript-ioc";
import { AuScreen } from "../../../../plugins/screen-plugin/AuScreen";

@Component({
  components: {},
})
export default class SystemSettingsModal extends Vue {
  @Prop({ required: true }) site: SiteValue
  auScreen = Container.get(AuScreen)

  get modalHeight () {
    return this.auScreen.modal_height
  }
}
</script>

<style lang="less" scoped>
.SystemSettingsModal {
  &__label {
    display: inline-block;
    min-width: 210px;
    padding-left: 6px;
  }
}
</style>
