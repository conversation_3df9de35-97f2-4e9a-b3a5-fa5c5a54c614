<template>
  <VbDemo>
    <VbCard title="playground">
      <ConnectionMeter :last-ping-latency="lastPingLatency" />
      <input type="number" v-model.number="lastPingLatency">
    </VbCard>
    <VbCard title="all cases">
      <p v-for="value in values">
        <ConnectionMeter :last-ping-latency="value" /> {{value}}
      </p>
    </VbCard>
  </VbDemo>
</template>

<script>
import ConnectionMeter from './ConnectionMeter.vue';

export default {
  components: {
    ConnectionMeter,
  },
  data () {
    return {
      values: [
        19,
        9.9,
        4.9,
        0.9,
      ],
      lastPingLatency: 1,
    }
  },
}
</script>
