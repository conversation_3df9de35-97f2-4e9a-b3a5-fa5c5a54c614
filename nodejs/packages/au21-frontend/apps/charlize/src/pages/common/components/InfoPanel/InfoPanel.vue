<template>


  <div
    class="InfoPanel"
    :style="{
      width: width + 'px !important',
      height: height + 'px !important',
      color
    }"
  >

    <div v-if="label_on_top">

      <div class="au-label"
           :style="{color: label_color, 'margin-left': heading_margin_left+'px'}">
        {{ heading }}
      </div>

      <Blinker :value="obs" background class="InfoPanel__contents">

        <slot>{{ contents || '---' }}</slot>
        <slot name="after" />

      </Blinker>

      <div v-if="units"
           class="InfoPanel__units au-label"
           :style="{color: label_color}"
      >
        {{ units || '' }}

      </div>

    </div>

    <div v-else>

      <Blinker :value="obs" background class="InfoPanel__contents">

        <slot>{{ contents || '---' }}</slot>
        <slot name="after" />

      </Blinker>

      <div class="au-label"
           :style="{color: label_color, 'margin-left': heading_margin_left+'px'}">
        {{ heading }}
      </div>

      <div v-if="units"
           class="InfoPanel__units au-label"
           :style="{color: label_color}"
      >
        {{ units || '' }}

      </div>

    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuSectionBorder from '../../../../ui-components/AuSectionBorder.vue';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {Container} from 'typescript-ioc';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: { Blinker, AuSectionBorder }
})
export default class InfoPanel extends Vue {
  @Prop({ default: false }) label_on_top;
  @Prop({ default: 70 }) height: number;
  @Prop({ required: true }) width: number;
  @Prop({ required: true }) heading: string;
  @Prop({ default: 0 }) heading_margin_left: number;
  @Prop({}) contents: string;
  @Prop({}) obs: string; // NOTE: cannot use 'observable': "Incompatible override for member from VueConstructor"
  @Prop({ default: null }) units: string | null;
  @Prop({ type: String, default: null }) color: string | null;

  colors: AuColors = Container.get(AuColors);

  // klugy override of color for label if buy or sell:
  get label_color(): string {
    if ([
      this.colors.au_buy(),
      this.colors.au_sell(),
      this.colors.au_match()
      //  this.colors.au_price_up,
      //  this.colors.au_red,
      //  this.colors.logo_orange
    ].includes(this.color)) {
      return this.color;
    }
    return this.colors.au_text_color;
  }

}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.InfoPanel {
  display: inline-block;
  font-size: 12px;
  font-weight: bold;
  height: 100%;
  margin: 0;
  overflow: hidden;
  position: relative;
  text-align: center;
  top: -3px;
  width: 100%;

  &__contents {
    font-size: 18px;
  }

  &__units {
    font-size: 11px;
  }

}

</style>
