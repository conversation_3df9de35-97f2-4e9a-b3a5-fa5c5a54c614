<template>
  <VbDemo>
    <VbCard style="width: 400px">
      <AuctionCategoriesCrud
        style="height: 250px"
        :auction_categories="auction_categories"
        @delete="$vb.log('delete', $event)"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator'
import AuctionCategoriesCrud from './AuctionCategoriesCrud.vue';
import {createMultipleByClosure} from '@au21-frontend/utils';
import {createDemo__AuctionCategory} from '../../../../../demo-helpers/AuctionCategory.helper';
import {CharlizeStore} from '../../../../../services/connector/CharlizeStore';
import {createDemo__store_for_auctioneer,} from '../../../../../demo-helpers/CharlizeStore.helper';

@Component({
  name: 'AuctionCategoriesCrudDemo',
  components: { AuctionCategoriesCrud },
})
export default class AuctionCategoriesCrudDemo extends Vue {
  auction_categories = createMultipleByClosure(createDemo__AuctionCategory, 5)
  store: CharlizeStore = createDemo__store_for_auctioneer()
}
</script>
