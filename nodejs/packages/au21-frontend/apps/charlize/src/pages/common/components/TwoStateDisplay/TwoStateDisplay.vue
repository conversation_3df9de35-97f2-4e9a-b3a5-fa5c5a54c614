<template>
  <!--    style="border: 1px solid red;"-->
  <svg
    :viewBox="`0 0 ${width} ${height}`"
    :width="width"
    :height="height"
  >
    <g>
      <rect
        :x="4"
        :y="0"
        height="18"
        :width="width -5"
        :rx="rect_r"
        :ry="rect_r"
        :style="first_style"
      />


      <rect
        :x="4"
        :y="20"
        height="18"
        :width="width - 5"
        :rx="rect_r"
        :ry="rect_r"
        :style="second_style"
      />

      <!-- TODO: center the text -->

      <text
        :y="12.5"
        fill="#111"
        text-anchor="middle"
      >
        <tspan :x="width / 2" font-size="10" font-weight="bold">
          {{ first_label }}
        </tspan>
      </text>

      <text
        :y="33"
        fill="#111"
        text-anchor="middle"
      >
        <tspan :x="width / 2" font-size="10" font-weight="bold">
          {{ second_label }}
        </tspan>
      </text>

    </g>
  </svg>
</template>

<script lang="ts">

import {Component, Prop, Vue} from 'vue-property-decorator';

@Component({
  components: {}
})
export default class TwoStateDisplay extends Vue {

  @Prop({ required: true }) first_label: string;
  @Prop({ required: true }) second_label: string;
  @Prop({ required: true }) first_color: string;
  @Prop({ required: true }) second_color: string;
  @Prop({ required: true }) width: number;
  @Prop({ default: 50 }) height: number;

  rect_r = 3;

  baseStyle = {
    strokeWidth: 3,
    fillOpacity: '1.0'
  };

  get first_style() {
    return {
      ...this.baseStyle,
      fill: this.first_color
    };
  }

  get second_style() {
    return {
      ...this.baseStyle,
      fill: this.second_color
    };
  }

}
</script>

<style scoped>
</style>
