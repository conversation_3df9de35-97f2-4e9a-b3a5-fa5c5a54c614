<template>
  <div class="page">
    <div style="display: flex; align-items: center;">
      <div class="header-label">Auction:</div>
      <AuTextarea
        style="flex: 1 1; text-align: left;"
        :value="name"
        :height="40"
        :font_size="14"
        output
      />
    </div>
    <div style="display: flex; align-items: center;">
      <div class="header-label">Status:</div>
      <AuOutput
        style="flex: 1 1; font-weight: 700; text-align: left"
        text_align="center"
        :value="status"
        :width="120"
      />
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import AuTextarea from '../../../../ui-components/AuTextarea/AuTextarea.vue';
import AuOutput from '../../../../ui-components/AuOuput/AuOutput.vue';

@Component({
  components: {
    AuOutput,
    AuTextarea,
  },
})
export default class AuctionStatus extends Vue {
  @Prop({ type: String, required: true }) name: string
  @Prop({ type: String, required: true }) status: string
}
</script>

<style lang="less" scoped>
.header-label {
  width: 80px;
  text-align: right;
  padding-right: 5px;
}
</style>
