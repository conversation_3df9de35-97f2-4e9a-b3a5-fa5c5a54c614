<template>
  <VbDemo>
    <VbCard>
      <a-button
        class="au-btn"
        type="primary"
        @click="show = true"
      >
        Show
      </a-button>
      <AuctionNoticeModal
        :notice="html"
        @saveNotice="$vb.log('saveNotice', $event)"
        @close="show = false"
        :editable="editable"
        v-if="show"
      />
      <div>
        <a-checkbox v-model="editable">Editable</a-checkbox>
      </div>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuctionNoticeModal from './AuctionNoticeModal.vue';
import {Component, Vue} from 'vue-property-decorator';

@Component({
  components: { AuctionNoticeModal },
})
export default class AuctionNoticeModalDemo extends Vue {
  editable = false
  show = false
  // That's to test html sanitizing.
  html = `<p :onclick="alert("XSS success!");">Dangerous button</p>`
}
</script>
