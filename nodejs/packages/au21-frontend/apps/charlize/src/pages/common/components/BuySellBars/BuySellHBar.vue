<template>
  <div class="BuySellHBar">
    <div :style="vol_style_computed"></div>
    <div :style="match_style_computed"></div>
    <div class="_match"
         :style="{color: colors.au_match(), right: matchTextRight}">
      <Blinker :value="`(${match})`" />
    </div>
    <div class="_vol"
         :style="{color: volTextColor, right: volTextRight}">
      <Blinker :value="orderQuantityStrWithSubmissionType" />
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator';
import {Container} from 'typescript-ioc';
import Blinker from '../../../../ui-components/Blinker/Blinker.vue';
import {OrderSubmissionType, OrderType} from '@au21-frontend/client-connector';
import {AuColors} from '../../../../au-styles/AuColors';

@Component({
  components: { Blinker }
})
export default class BuySellHBar extends Vue {
  // TODO: why do we need 'type'? doesn't seem to do anything!
  @Prop({ type: Number, default: 50 }) width: number;
  @Prop({ type: Number, default: 26 }) height: number;
  @Prop({ type: Number, required: true }) buyMax: number;
  @Prop({ type: Number, required: true }) sellMax: number;

//  @Prop({ required: true }) round_trader_element: DeRoundTraderElement | null;

  @Prop({ required: true }) match: number;
  @Prop({ required: true }) order_quantity_int: number;
  @Prop({ required: true }) order_quantity_str: string;
  @Prop({ required: true }) order_type: OrderType;
  @Prop({ required: true }) order_submission_type: OrderSubmissionType;

  OrderType = OrderType;
  OrderSubmissionType = OrderSubmissionType;

  colors = Container.get(AuColors);

  get isSell(): boolean {
    return this.order_type == OrderType.SELL;
  }

  get volBarColor() {
    return this.isSell ? this.colors.au_sell_dimmedX2() : this.colors.au_buy_dimmedX2();
  }

  get volBarWidth(): number {
    return this.width * this.order_quantity_int / (this.isSell ? this.sellMax : this.buyMax);
  }

  get matchBarWidth(): number {
    return this.width * this.match / (this.isSell ? this.sellMax : this.buyMax);
  }

  get volLeft(): number {
    return this.isSell ? 0 : this.width - this.volBarWidth;
  }

  get matchLeft(): number {
    return this.isSell ? 0 : this.width - this.matchBarWidth;
  }

  get vol_style_computed(): {} {
    return {
      position: 'absolute',
      left: `${this.volLeft}px`,
      width: `${this.volBarWidth}px`,
      height: `${this.height}px`,
      'background-color': `${this.volBarColor}`
    };
  }

  get match_style_computed(): {} {
    return {
      position: 'absolute',
      left: `${this.matchLeft}px`,
      width: `${this.matchBarWidth}px`,
      height: `${this.height}px`,
      'background-color': `${this.colors.au_match_dimmedX2()}`
    };
  }

  get volTextRight(): string {
    return this.isSell ? '-30px' : '-55px';
  }

  get matchTextRight(): string {
    return this.isSell ? '-50px' : '-30px';
  }

  get volTextColor() {
    return this.isSell ? this.colors.au_sell() : this.colors.au_buy();
  }


  get barHeight(): number {
    // return (this.order_quantity_int * this.height) / (this.isSell ? this.sellMax : this.buyMax);
    return this.height;
  }


  get orderQuantityStrWithSubmissionType(): string {
    switch (this.order_submission_type) {
      case OrderSubmissionType.MANUAL:
        return this.order_quantity_str;
      case OrderSubmissionType.DEFAULT:
        return `${this.order_quantity_str}*`;
      case OrderSubmissionType.MANDATORY:
        return `${this.order_quantity_str}!`;
      default:
        return '---';
    }
  }

  // get au_text_color(): string {
  //   switch (this.order_type) {
  //     case OrderType.NONE:
  //       return '#ccc';
  //     case OrderType.SELL:
  //       return this.colors.au_sell();
  //     case OrderType.BUY :
  //       return this.colors.au_buy();
  //   }
  // }

}
</script>

<style lang="less" scoped>
@import (reference) "../../../../au-styles/variables.less";

.BuySellHBar {

  ._match {
    font-size: 10px;
    position: absolute;
    text-align: right;
    top: 3px;
  }

  ._vol {
    position: absolute;
    text-align: right;
    top: 3px;
  }

}

</style>
