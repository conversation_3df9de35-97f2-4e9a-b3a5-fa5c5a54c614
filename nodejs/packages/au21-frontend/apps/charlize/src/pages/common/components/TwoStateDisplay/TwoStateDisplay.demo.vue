<template>
  <VbDemo>
    <VbCard>
      <div
        v-for="status in statusOptions"
        :key="status"
      >
        <br>
        {{ status }}
        <br>
        <TwoStateDisplay
          :width="100"
          :height="60"
          :first_label="'one'"
          :second_label="'two'"
          :first_color="'green'"
          :second_color="'red'"
        />
      </div>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import {createDemo__DeCommonStatusValue} from '../../../../demo-helpers/DeCommonStatusValue.helper';
import AuSelect from '../../../../ui-components/AuSelect/AuSelect.vue';
import {DeAuctioneerState} from '@au21-frontend/client-connector';
import TwoStateDisplay from './TwoStateDisplay.vue';

@Component({
  components: { TwoStateDisplay, AuSelect }
})
export default class TwoStateDisplayDemo extends Vue {
  commonStatus = createDemo__DeCommonStatusValue();
  statusOptions = Object.values(DeAuctioneerState);
  auctioneerStatus = {};

  announced = false;

  announce() {
    this.announced = true;
  }

  unannounce() {
    this.announced = false;
  }
}
</script>
