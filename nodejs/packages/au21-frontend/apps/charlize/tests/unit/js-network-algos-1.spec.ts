import {FlowEdge, FlowNetwork, Ford<PERSON><PERSON><PERSON>on} from 'js-graph-algorithms';

describe('JS Algos Ford Fulkerson', () => {
  it('should calculate max flow correctly', function () {
    const g = new FlowNetwork(8)
    g.addEdge(new FlowEdge(0, 1, 10))
    g.addEdge(new FlowEdge(0, 2, 5))
    g.addEdge(new FlowEdge(0, 3, 15))
    g.addEdge(new FlowEdge(1, 4, 9))
    g.addEdge(new FlowEdge(1, 5, 15))
    g.addEdge(new FlowEdge(1, 2, 4))
    g.addEdge(new FlowEdge(2, 5, 8))
    g.addEdge(new FlowEdge(2, 3, 4))
    g.addEdge(new FlowEdge(2, 6, 6))
    g.addEdge(new FlowEdge(3, 6, 16))
    g.addEdge(new FlowEdge(4, 5, 15))
    g.addEdge(new FlowEdge(4, 7, 10))
    g.addEdge(new FlowEdge(5, 7, 10))
    g.addEdge(new FlowEdge(5, 6, 15))
    g.addEdge(new FlowEdge(6, 7, 10))

    // vertex count:
    expect(g.V).toBe(8)

    let edgeCount = 0
    for (let v = 0; v < g.V; ++v) {
      const adj_v = g.adj(v)
      edgeCount += adj_v.length
    }
    expect(edgeCount).toBe(30)

    const source = 0
    const target = 7
    const ff = new FordFulkerson(g, source, target)
    const max_flow = ff.value
    // console.log(`max-flow: ${max_flow}`)

    // TODO: not sure if this is correct??
    expect(max_flow).toBe(25)
  })
})
