import {DeMatrixEdgeElement, DeMatrixNodeElement} from '@au21-frontend/client-connector';
import {FlowCalcTrader, FlowCalcTraderType} from '../../src/services/flow-algos/domain/model';
import {
  create_solver,
  de_matrix_nodes_to_ordered_flow_calc_traders,
  de_matrix_to_flow_calc_traders_and_constraints,
  ordered_traders_to_packages
} from '../../src/services/flow-algos/domain/calculator';
import {AuFFDfsSolver} from '../../src/services/flow-algos/domain/au-ford-fulkerson';


const node_helper = (
  id: number,
  buy_max: number,
  buy_vol: number,
  sell_max: number,
): DeMatrixNodeElement => ({
  buy_match: 0,
  buy_max,
  buy_min: 0,
  buy_vol,
  cid: 't' + id,
  id: '' + id,
  round: 1,
  sell_match: 0,
  sell_max,
  sell_min: 0,
  sell_vol: 0,
  shortname: 't1',
})

const edge_helper = (
  buyer_cid: number,
  seller_cid: number,
  constraint: number,
): DeMatrixEdgeElement => ({
  buyer_cid: 't' + buyer_cid,
  id: buyer_cid + '.' + seller_cid,
  match: 0,
  capacity: constraint,
  r: 1,
  seller_cid: 't' + seller_cid,
})

// package helper:

const pkgs_to_boolean_arrs = (
  pkgs: Map<FlowCalcTrader, FlowCalcTraderType>[],
): boolean[][] =>
  pkgs.map((pkg: Map<FlowCalcTrader, FlowCalcTraderType>) =>
    [...pkg.values()].map((t) => t === FlowCalcTraderType.buyer),
  )

describe('Flow Calculator', () => {
  it('given DeMatrix nodes, it should create an ordered FlowCalcTrader list', () => {
    const matrix_nodes: DeMatrixNodeElement[] = [
      node_helper(1, 0, 0, 50),
      node_helper(2, 20, 15, 40),
      node_helper(3, 0, 0, 30),
      node_helper(4, 10, 40, 10),
    ]

    const ordered_flowtraders_using_buy_max: FlowCalcTrader[] =
      de_matrix_nodes_to_ordered_flow_calc_traders(matrix_nodes, false)

    expect(ordered_flowtraders_using_buy_max).toStrictEqual([
      {
        cid: 't2',
        vertex: 0, // source is 4
        buy: 20,
        sell: 40,
      },
      {
        cid: 't4',
        vertex: 1,
        buy: 10,
        sell: 10,
      },
      {
        cid: 't1',
        vertex: 2,
        buy: 0,
        sell: 50,
      },
      {
        cid: 't3',
        vertex: 3,
        buy: 0,
        sell: 30,
      },
    ])

    const ordered_flowtraders_using_buy_vol: FlowCalcTrader[] =
      de_matrix_nodes_to_ordered_flow_calc_traders(matrix_nodes, true)

    expect(ordered_flowtraders_using_buy_vol).toStrictEqual([
      {
        cid: 't4',
        vertex: 0, // source is 0
        buy: 40,
        sell: 10,
      },
      {
        cid: 't2',
        vertex: 1,
        buy: 15,
        sell: 40,
      },
      {
        cid: 't1',
        vertex: 2,
        buy: 0,
        sell: 50,
      },
      {
        cid: 't3',
        vertex: 3,
        buy: 0,
        sell: 30,
      },
    ])
  })

  it('given DeMatrix nodes and edges, it should create FlowCalc traders and constraints', () => {
    // arranged in order so that trader ids == vertex number
    const matrix_nodes: DeMatrixNodeElement[] = [
      node_helper(1, 20, 1, 50),
      node_helper(2, 10, 2, 40),
      node_helper(3, 0, 0, 10),
      node_helper(4, 0, 0, 30),
    ]

    const ordered_flowtraders_using_buy_max: FlowCalcTrader[] =
      de_matrix_nodes_to_ordered_flow_calc_traders(matrix_nodes, false)

    expect(ordered_flowtraders_using_buy_max).toStrictEqual([
      {
        cid: 't1',
        vertex: 0, // source is 4
        buy: 20,
        sell: 50,
      },
      {
        cid: 't2',
        vertex: 1,
        buy: 10,
        sell: 40,
      },
      {
        cid: 't3',
        vertex: 2,
        buy: 0,
        sell: 10,
      },
      {
        cid: 't4',
        vertex: 3,
        buy: 0,
        sell: 30,
      },
    ])

    const edges: DeMatrixEdgeElement[] = [
      edge_helper(1, 1, 0),
      edge_helper(1, 2, 10),
      edge_helper(1, 3, 20),
      edge_helper(1, 4, 30),

      edge_helper(2, 1, 15),
      edge_helper(2, 2, 0),
      edge_helper(2, 3, 25),
      edge_helper(2, 4, 35),

      edge_helper(3, 1, 0),
      edge_helper(3, 2, 13),
      edge_helper(3, 3, 0),
      edge_helper(3, 4, 3),

      edge_helper(4, 1, 0),
      edge_helper(4, 2, 1),
      edge_helper(4, 3, 20),
      edge_helper(4, 4, 0),
    ]

    const { ordered_traders, constraints } =
      de_matrix_to_flow_calc_traders_and_constraints(
        matrix_nodes,
        edges,
        false,
      )

    expect(constraints).toHaveLength(6)

    expect(constraints).toStrictEqual([
      {
        buyer_cid: 't1',
        buyer_vertex: 0,
        match: 10,
        seller_cid: 't2',
        seller_vertex: 1,
      },
      {
        buyer_cid: 't1',
        buyer_vertex: 0,
        match: 10,
        seller_cid: 't3',
        seller_vertex: 2,
      },
      {
        buyer_cid: 't1',
        buyer_vertex: 0,
        match: 20,
        seller_cid: 't4',
        seller_vertex: 3,
      },
      {
        buyer_cid: 't2',
        buyer_vertex: 1,
        match: 10,
        seller_cid: 't1',
        seller_vertex: 0,
      },
      {
        buyer_cid: 't2',
        buyer_vertex: 1,
        match: 10,
        seller_cid: 't3',
        seller_vertex: 2,
      },
      {
        buyer_cid: 't2',
        buyer_vertex: 1,
        match: 10,
        seller_cid: 't4',
        seller_vertex: 3,
      },
    ])
  })

  it('should calculate the packages', () => {
    // arranged in order so that trader ids == vertex number
    const traders: FlowCalcTrader[] =
      de_matrix_nodes_to_ordered_flow_calc_traders(
        [
          node_helper(1, 20, 1, 50),
          node_helper(2, 10, 2, 40),
          node_helper(3, 0, 0, 10),
          node_helper(4, 0, 0, 30),
        ],
        false,
      )

    const pkgs: boolean[][] = ordered_traders_to_packages(traders)

    expect(pkgs.length).toBe(3)

    expect(pkgs).toStrictEqual([
      [true, false, false, false],
      [false, true, false, false],
      [true, true, false, false],
    ])
  })

  it('should be have packages if there are all buyers', () => {
    // arranged in order so that trader ids == vertex number
    const traders: FlowCalcTrader[] =
      de_matrix_nodes_to_ordered_flow_calc_traders(
        [node_helper(1, 20, 1, 50), node_helper(2, 10, 2, 40)],
        false,
      )

    expect(ordered_traders_to_packages(traders).length).toBe(3)
  })

  it('should be have no packages if there are no traders', () => {
    // arranged in order so that trader ids == vertex number
    const traders: FlowCalcTrader[] = []
    expect(ordered_traders_to_packages(traders).length).toBe(0)
  })

  it('should be have no packages if there are no buyers', () => {
    // arranged in order so that trader ids == vertex number
    const traders: FlowCalcTrader[] =
      de_matrix_nodes_to_ordered_flow_calc_traders(
        [node_helper(1, 0, 1, 50), node_helper(2, 0, 2, 40)],
        false,
      )

    expect(ordered_traders_to_packages(traders).length).toBe(0)
  })

  it('given DeMatrix nodes and edges, it should calculate max flow correctly for all packages', () => {
    const { ordered_traders, constraints } =
      de_matrix_to_flow_calc_traders_and_constraints(
        [
          node_helper(0, 20, 20, 0),
          node_helper(1, 5, 5, 0),
          node_helper(2, 0, 0, 3),
          node_helper(3, 0, 0, 16),
        ],
        [
          edge_helper(0, 2, 4),
          edge_helper(0, 3, 9),
          edge_helper(1, 2, 9),
          edge_helper(1, 3, 8),
        ],
        false,
      )

    const pgks: boolean[][] = ordered_traders_to_packages(ordered_traders)

    // const pgks: Array<boolean[]> = [[true, true, false, false]];

    const results: Array<{ pkg: boolean[]; maxflow: number }> = []

    pgks.forEach((p: boolean[]) => {
      const g: AuFFDfsSolver | null = create_solver(
        constraints,
        ordered_traders,
        p,
      )
      if (g == null) {
        // console.log({ package: p, maxflow: 0 })
        results.push({ pkg: p, maxflow: 0 })
      } else {
        // console.log({ package: p, maxflow: g.getMaxFlow() })
        results.push({ pkg: p, maxflow: g.getMaxFlow() })
        const solution = g.getSolution().map((e) => e.toString(g.params))
        // console.log(p, solution)
      }
    })

    // console.log({ results });

    expect(results).toStrictEqual([
      {
        pkg: [true, false, false, false],
        maxflow: 12,
      },
      {
        pkg: [false, true, false, false],
        maxflow: 5,
      },
      {
        pkg: [true, true, false, false],
        maxflow: 17,
      },
    ])

    // expect(values).toStrictEqual([
    //   12, 5, 17
    // ]);
  })
})
