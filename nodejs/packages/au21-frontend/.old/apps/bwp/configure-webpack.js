/*
 * Modify the webpack config by exporting an Object or Function.
 *
 * If the value is an Object, it will be merged into the final
 * config using `webpack-merge`.
 *
 * If the value is a function, it will receive the resolved config
 * as the argument. The function can either mutate the config and
 * return nothing, OR return a cloned or merged version of the config.
 *
 * https://cli.vuejs.org/config/#configurewebpack
 */

// https://cli.vuejs.org/config/#indexpath
//const generateFooter = require('./generate-footer').generateFooter
//const webpack = require('webpack')

process.env.VUE_APP_VERSION = require("../../package.json").version;

const URL_PROXY = "/api";
const FAYE_SERVER = "http://localhost:9090";

module.exports = (config) => {

  const lessRule = config.module.rules.find((rule) => rule.test.exec(".less"));
  for (const { use } of lessRule.oneOf) {
    const lessLoader = use.find(({ loader }) => loader.includes("less-loader"));
    lessLoader.options.javascriptEnabled = true;
  }

  // config.plugin('define').tap(definitions => {
  //   definitions[0] = Object.assign(definitions[0], {
  //     'FOOTER_TEXT': JSON.stringify(generateFooter())
  //   })
  //   return definitions
  // })

  config.devServer = {
    // Proxy '/api/*' to 'http://localhost:9090/*'
    proxy: {
      "/api": {
        // target: process.env.VUE_APP_FAYE_PROXY || 'http://test.auctionologies.com:9090',
        target: "http://localhost:9090",
       // pathRewrite: { [`^\/api`]: "" }
      }
    },
    disableHostCheck: true // to prevent "Invalid Host Error" for external requests (ie: on staging server)
  };

};
