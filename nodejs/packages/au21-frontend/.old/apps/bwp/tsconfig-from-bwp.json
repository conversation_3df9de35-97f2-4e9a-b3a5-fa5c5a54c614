{"compilerOptions": {"allowSyntheticDefaultImports": true, "baseUrl": ".", "esModuleInterop": true, "experimentalDecorators": true, "importHelpers": true, "jsx": "preserve", "module": "esnext", "moduleResolution": "node", "outDir": "lib", "sourceMap": false, "strict": false, "target": "es5", "types": ["webpack-env", "jest"], "paths": {}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules", "temp"]}