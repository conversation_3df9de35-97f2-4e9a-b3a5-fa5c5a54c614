// https://cli.vuejs.org/config/#indexpath
const generateFooter = require('./generate-footer').generateFooter
const webpack = require('webpack')

process.env.VUE_APP_VERSION = require('./package.json').version

const URL_PROXY = '/api'
const FAYE_SERVER = 'http://localhost:9090'

module.exports = {
  transpileDependencies: [
    'jest-diff',
    'vue-epic-bus',
  ],

  css: {
    loaderOptions: {
      less: {
        modifyVars: {},
        javascriptEnabled: true
      }
    }
  },

  chainWebpack: config => {
    config.plugin('define').tap(definitions => {
      definitions[0] = Object.assign(definitions[0], {
        'FOOTER_TEXT': JSON.stringify(generateFooter())
      })
      return definitions
    })
  },

  runtimeCompiler: true,

  /*
    Some options from au-vue2-ts3 experimental:
    (some of these probably need to be included)

    pages : {
      index: "src/apps/book/App"
    },

    baseUrl: undefined,
    outputDir: undefined,
    assetsDir: undefined,
    runtimeCompiler: true,
    productionSourceMap: false,
    parallel: undefined,
    css: undefined
  */

  devServer: {
    // Proxy '/api/*' to 'http://localhost:9090/*'
    proxy: {
      '/api': {
        // target: process.env.VUE_APP_FAYE_PROXY || 'http://test.auctionologies.com:9090',
        target: 'http://localhost:9090',
        pathRewrite: {[`^\/api`]: ''}
      }
    },
    disableHostCheck: true, // to prevent "Invalid Host Error" for external requests (ie: on staging server)
  },

  publicPath: undefined,
  outputDir: undefined,
  assetsDir: undefined,
  productionSourceMap: false,
  parallel: undefined
}
