import {Role} from "../_generated/bwp-enums";
import {
    AuPayload,
    OnAlert,
    OnAuctionRow,
    OnAuctionUserRow,
    OnMessage,
    OnMrAuctioneerSummary,
    OnMrAuctionSettings,
    OnMrAuctionStatus,
    OnMrAward,
    OnMrAwardRow,
    OnMrBidderBlotterRow, OnMrBidderStatus,
    OnMrBidRow,
    OnMrTemplate,
    OnNextPage,
    OnSessionRow,
    OnUserRow
} from "../_generated/server_outputs";

export interface Oidable {
    OID: string | number // I'm not sure if all are numbers in this version
}

const role_labels: { [k in Role]: string } = {
    ADMIN: 'Admin',
    AUCTIONEER: 'Auctioneer',
    TRADER: 'Trader',
    INTERNAL_OBSERVER: 'Internal observer',
    EXTERNAL_OBSERVER: 'External observer'
}

export interface RemoteUserModel {
    username: string
    role: string
}

export class TradersAddRemovePanelModel {
    is_add = false
    traders = [] as OnAuctionUserRow[]

    reset(o: { is_add: boolean }) {
        this.is_add = o.is_add
        this.traders = []
    }
}

export class MrAuctionModel {
    auctioneer_summary = null as OnMrAuctioneerSummary
    award = null as OnMrAward
    award_rows = [] as OnMrAwardRow[]
    blotter_header_rows = {
        ROUNDPRICE: new MrBlotterFixedRow(),
        TOTALVOL: new MrBlotterFixedRow(),
        ACTIVITY: new MrBlotterFixedRow(),
        BIDDERS: new MrBlotterFixedRow(),
    }
    blotter_bidder_rows = [] as OnMrBidderBlotterRow[]
    messages = [] as OnMessage[]
    notice = ''
    settings = null as OnMrAuctionSettings
    status = null as OnMrAuctionStatus

    get auctionid(): number {
        return this.status ? this.status.AUCTIONID : 0
    }
}


export class MrTraderModel {
    award = ''
    blinded = false
    bid_volume = ''
    history_rows = [] as OnMrBidRow[]
    trader_status = null as OnMrBidderStatus
}

export interface BwpModel {
    alerts: OnAlert[]
    auctions: OnAuctionRow[]
    award_rows: OnMrAwardRow[]
    current_auction: MrAuctionModel
    next_page: OnNextPage
    remote_user: RemoteUserModel
    sessions: OnSessionRow[]
    session_id: string
    templates: OnMrTemplate[]
    trader: MrTraderModel
    traders_add_remove: TradersAddRemovePanelModel
    users: OnUserRow[]
}

export class MrBlotterFixedRow {
    label = ''
    Round_1 = ''
    Round_2 = ''
    Round_3 = ''
    Round_4 = ''
    Round_5 = ''
    Round_6 = ''
    Round_7 = ''
    Round_8 = ''
    Round_9 = ''
    Round_10 = ''
    Round_11 = ''
    Round_12 = ''
    Round_13 = ''
    Round_14 = ''
    Round_15 = ''
    Round_16 = ''
    Round_17 = ''
    Round_18 = ''
    Round_19 = ''
    Round_20 = ''
    Round_21 = ''
    Round_22 = ''
    Round_23 = ''
    Round_24 = ''
    Round_25 = ''
    Round_26 = ''
    Round_27 = ''
    Round_28 = ''
    Round_29 = ''
    Round_30 = ''
    Round_31 = ''
    Round_32 = ''
    Round_33 = ''
    Round_34 = ''
    Round_35 = ''
    Round_36 = ''
    Round_37 = ''
    Round_38 = ''
    Round_39 = ''
    Round_40 = ''
    Round_41 = ''
    Round_42 = ''
    Round_43 = ''
    Round_44 = ''
    Round_45 = ''
    Round_46 = ''
    Round_47 = ''
    Round_48 = ''
    Round_49 = ''
    Round_50 = ''
}



