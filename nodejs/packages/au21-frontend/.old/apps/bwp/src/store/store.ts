import {
    OnAlert,
    OnAuctionRow,
    OnAuctionUserRow,
    OnLogin,
    OnMessage,
    OnMrAuctioneerSummary,
    OnMrAuctionSettings,
    OnMrAuctionStatus,
    OnMrAwardRow,
    OnMrBidderBlotterRow,
    OnMrBidderStatus,
    OnMrBidRow,
    OnMrBlinding,
    OnMrClearAuctioneerPageBidTable,
    OnMrClearAwardPage,
    OnMrFixedBlotterRow,
    OnMrRemoveUserRow,
    OnMrRoundNumber,
    OnMrSubmittedBid,
    OnMrTarget,
    OnMrTemplate,
    OnMrTraderSettings,
    OnMrUpdateBid,
    OnNextPage,
    OnNotice,
    OnReload,
    OnRemoveAuctionRow,
    OnSessionRow,
    OnSystemMessage,
    OnUserOnline,
    OnUserRow,
    OnTime,
    OnMrAward,
    OnForceHome,
} from '../_generated/server_outputs'
import { compareTypes } from '../services/utils'
import { range, remove } from 'lodash'
import {
    BwpModel,
    MrAuctionModel,
    MrBlotterFixedRow,
    MrTraderModel,
    TradersAddRemovePanelModel,
} from './model'
import {error, EventBus, LOG_MESSAGES} from '../services/event-bus'
import isBrowser from 'is-browser'
import { Role } from '../_generated/bwp-enums'
import { isAuctioneerOrAdmin } from '../components/User/UserRoleSelect/Role'
import { MrPage } from '../components/Auction/__demo-helpers/MrPage'
import { getSplitTimeFromOnTime, TimeSplit } from '../helpers/date-helpers'
import {reload_page} from "../services/bwp-connector/connector"


export class BwpStore implements BwpModel {

    alerts = [] as OnAlert[]
    auctions = [] as OnAuctionRow[]
    award_rows = [] as OnMrAwardRow[]
    awards = [] as OnMrAwardRow[]
    footer_text = "Copyright 2019 Auctionologies LLC. - client v1.69, server v1.68"
    current_auction = null as MrAuctionModel
    date_time = null as TimeSplit
    forcing_home = false
    last_message_interval_secs = 0
    mr_trader_settings = null as OnMrTraderSettings
    next_page = null as OnNextPage
    notice = ''
    remote_user = {username: '', role: null as Role}
    session_id = ''
    sessions = [] as OnSessionRow[]  // this one we don't reset on next page
    system_messages = [] as OnSystemMessage[]
    templates = [] as OnMrTemplate[]
    trader = new MrTraderModel()
    traders_add_remove = new TradersAddRemovePanelModel()
    users = [] as OnUserRow[]

    has_logged_in(): boolean {
        // NOTE: THIS ONLY TRACKS LOGIN NOT SIGNOFF, SO USER COULD HAVE SIGNED OFF
        return !!this.remote_user.role
    }

    is_trader(): boolean {
        return this.remote_user.role === Role.TRADER
    }

    get is_auctioneer_or_admin(): boolean {
        if (!this.remote_user) {
            return false
        }
        return isAuctioneerOrAdmin(this.remote_user.role)
    }

    get open_auctions(): OnAuctionRow[] {
        return this.auctions.filter(row => !row.IS_CLOSED)
    }

    get closed_auctions(): OnAuctionRow[] {
        return this.auctions.filter(row => row.IS_CLOSED)
    }

    // created() {
    //     EventBus.$on(SERVER_EVENT, msg => {
    //         error('not using event bus for store')
    //     })
    // }

    handlers = {

        OnAlert: (e: OnAlert) => {
            compareTypes(e, new OnAlert())

            this.alerts = [e]

            // alternatively use event bus:
            EventBus.$emit(e.EVENT, e)

            // check that not already logged in:
            if (e.BODY.endsWith('already signed in')) {
                error(e.BODY)
            }

            // check for permission error:

        },

        OnAuctionRow: (e: OnAuctionRow) => {
            compareTypes(e, new OnAuctionRow())
            // this was the old logic:
            const existing = this.auctions.find(
                (a: OnAuctionRow) => a.AUCTION_ROW_ID === e.AUCTION_ROW_ID)
            if (existing) {
                const i = this.auctions.indexOf(existing)
                this.auctions.splice(i, 1)
            }
            if (!e.IS_DELETED && !(e.IS_HIDDEN && this.is_trader())) {
                this.auctions.push(e)
            }
        },

        OnAuctionUserRow: (e: OnAuctionUserRow) => {
            compareTypes(e, new OnAuctionUserRow())
            this.traders_add_remove.traders.push(e)
        },

        OnLogin: (e: OnLogin) => {
            compareTypes(e, new OnLogin())
            if(LOG_MESSAGES){
                console.log({e})
            }
            this.remote_user = {
                username: e.USERNAME,
                role: e.ROLE
            }
        },

        OnMessage: (e: OnMessage) => {
            compareTypes(e, new OnMessage())
            this.current_auction.messages = [...this.current_auction.messages, e]
        },

        OnMrAuctioneerSummary: (e: OnMrAuctioneerSummary) => {
            compareTypes(e, new OnMrAuctioneerSummary())
            this.current_auction.auctioneer_summary = e
        },

        OnMrAuctionSettings: (e: OnMrAuctionSettings) => {
            compareTypes(e, new OnMrAuctionSettings())
            this.current_auction.settings = e
        },

        OnMrAuctionStatus: (e: OnMrAuctionStatus) => {
            compareTypes(e, new OnMrAuctionStatus())
            this.current_auction.status = e
        },

        OnMrAward: (e: OnMrAward) => {
            compareTypes(e, new OnMrAward())
            this.trader.award = e.TEXT
        },

        OnMrAwardRow: (e: OnMrAwardRow) => {
            compareTypes(e, new OnMrAwardRow())
            remove(this.current_auction.award_rows, (row: OnMrAwardRow) => row.OID === e.OID)
            this.current_auction.award_rows.push(e)
        },

        OnMrBidderStatus: (e: OnMrBidderStatus) => {
            compareTypes(e, new OnMrBidderStatus())
            this.trader.trader_status = e
        },

        OnMrBidderBlotterRow: (e: OnMrBidderBlotterRow) => {
            // compareTypes(e, new OnMrBidderBlotterRow())
            this.current_auction.blotter_bidder_rows = this.current_auction.blotter_bidder_rows.filter(row => row.OID != e.OID)
            this.current_auction.blotter_bidder_rows.push(e)
        },

        OnMrTraderSettings: (e: OnMrTraderSettings) => {
            this.mr_trader_settings = e
        },

        OnMrBidRow: (e: OnMrBidRow) => {
            compareTypes(e, new OnMrBidRow())
            remove(this.trader.history_rows, (row: OnMrBidRow) => row.ROUND_NUMBER === e.ROUND_NUMBER)
            this.trader.history_rows = [...this.trader.history_rows, e]
        },

        OnMrBlinding: (e: OnMrBlinding) => {
            compareTypes(e, new OnMrBlinding())
            this.trader.blinded = e.IS_BLINDED
        },

        OnMrClearAuctioneerPageBidTable: (e: OnMrClearAuctioneerPageBidTable) => {
            compareTypes(e, new OnMrClearAuctioneerPageBidTable())
            this.current_auction.blotter_bidder_rows = []
        },

        OnMrClearAwardPage: (e: OnMrClearAwardPage) => {
            compareTypes(e, new OnMrClearAwardPage())
            this.current_auction.award_rows = []
        },

        OnMrFixedBlotterRow: (e: OnMrFixedBlotterRow) => {
            // compareTypes(e, new OnMrFixedBlotterRow())
            const row: MrBlotterFixedRow = this.current_auction.blotter_header_rows[e.OID]
            row.label = e.Col1
            range(1, 51).forEach(n => {
                const round = 'Round_' + n
                if (e[round]) {
                    row[round] = e [round]
                }
            })
        },

        OnForceHome: (e: OnForceHome) => {
            // HACK Backend should modify the page state directly,
            // and not to tell us make a change.

            // if (this.next_page && this.next_page.NEXT_PAGE === MrPage.HomePage) {
            //     // We do nothing if page is already home
            //   return
            // }
            this.forcing_home = true
            setTimeout(() => {
                this.forcing_home = false
            })
        },

        OnMrRemoveUserRow: (e: OnMrRemoveUserRow) => {
            compareTypes(e, new OnMrRemoveUserRow())
            this.current_auction.blotter_bidder_rows = this.current_auction.blotter_bidder_rows.filter(bidder => bidder.OID !== e.USERID)
        },

        OnMrRoundNumber: (e: OnMrRoundNumber) => {
            compareTypes(e, new OnMrRoundNumber())
            // TODO: scroll this round into view !
            // but we don't need to implment it
            // instead just watch the rounds for the last round number
        },

        OnMrSubmittedBid: (e: OnMrSubmittedBid) => {
            compareTypes(e, new OnMrSubmittedBid())
            this.trader.bid_volume = e.BID_VOLUME // ?? not sure why we have this AND trader status !
            this.trader.trader_status.CURRENT_BID = e.BID_VOLUME

            // this is a bit of a hack for the demo:
            const row_count = this.trader.history_rows.length
            this.trader.history_rows[row_count -1].BID_VOLUME = e.BID_VOLUME
        },

        OnMrTarget: (e: OnMrTarget) => {
            compareTypes(e, new OnMrTarget())
            // TODO: not sure what this does
        },

        OnMrTemplate: (e: OnMrTemplate) => {
            compareTypes(e, new OnMrTemplate())
            remove(this.templates, (t: OnMrTemplate) => t.TEMPLATEID == e.TEMPLATEID)
            if (! e.DELETED){
                this.templates.push(e)
            }
        },

        OnMrUpdateBid: (e: OnMrUpdateBid) => {
            compareTypes(e, new OnMrUpdateBid())
            const round_num = 'Round_' + e.ROUND_NUMBER
            this.current_auction.blotter_header_rows.ACTIVITY[round_num] = e.ACTIVITY
            this.current_auction.blotter_header_rows.TOTALVOL[round_num] = e.TOTALVOL
            const bidder_row = this.current_auction.blotter_bidder_rows.find(row =>
                row.OID == e.USERID)
            bidder_row[round_num] = e.BID_VOLUME
        },

        OnNextPage: (e: OnNextPage) => {
            compareTypes(e, new OnNextPage())

            this.next_page = e

            if (!e.HAS_INITED) {
                // all pages get as first event: OnNextPage, with HAS_INITED = false
                // then as last event: OnNextPage, with HAS_INITED = true

                // this is where we reset all the pages:
                switch (e.NEXT_PAGE) {
                    case MrPage.HomePage:
                        this.auctions = []
                        break
                    case MrPage.MrAuctioneerPage:
                        this.award_rows = []
                        this.current_auction = new MrAuctionModel()
                        break
                    case MrPage.MrAwardPage:
                        this.award_rows = []
                        break
                    case MrPage.MrTemplatePage:
                        this.templates = []
                        break
                    case MrPage.MrTraderPage:
                        this.current_auction = new MrAuctionModel()
                        this.trader = new MrTraderModel()
                        break
                    case MrPage.SessionPage:
                        this.sessions = []
                        break
                    case MrPage.UserPage:
                        this.users = []
                        break
                    default:
                        console.error('No handler for page: ' + e.NEXT_PAGE)
                }
            }
        },

        OnNotice: (e: OnNotice) => {
            compareTypes(e, new OnNotice())
            this.current_auction.notice = e.TEXT
        },

        OnReload: (e: OnReload) => {
            compareTypes(e, new OnReload())
            if (isBrowser) {
                reload_page(e.REASON)
            }
        },

        OnRemoveAuctionRow: (e: OnRemoveAuctionRow) => {
            compareTypes(e, new OnRemoveAuctionRow())
            error('OnRemoveAuctionRow not implemented')
        },

        OnSessionRow: (onSessionRow: OnSessionRow) => {
            compareTypes(onSessionRow, new OnSessionRow())
            remove(this.sessions, (onSessionRowExisting: OnSessionRow) => onSessionRowExisting.SESSIONID === onSessionRow.SESSIONID)
            this.sessions.push(onSessionRow)
        },

        OnTime: (e: OnTime) => {
            // NOTE On server OnTime has no _COUNT.
            // compareTypes(e, new OnTime();
            this.date_time = getSplitTimeFromOnTime(e)
            // console.log({date_time: this.date_time})
        },

        OnUserOnline: (e: OnUserOnline) => {
            compareTypes(e, new OnUserOnline())
            const onUserOnline = e as OnUserOnline
// edit the auction blotter row:
// NOT SURE ABOUT THIS, WAS IT AN OLD VERSION ?
// const existing_mbabr: OnMrBidderBlotterRow =
//     find_by_oid(this.auction.mr_auctioneer_blotter_rows, onUserOnline.USERID) as OnMrBidderBlotterRow
// if (existing_mbabr) {
//     existing_mbabr.IS_ONLINE = onUserOnline.IS_ONLINE
// }
            if (this.current_auction) {
                const bidderRow: OnMrBidderBlotterRow =
                    this.current_auction.blotter_bidder_rows.find(
                        bidder_row => bidder_row.OID == e.USERID)

                if (bidderRow) {
                    bidderRow.IS_ONLINE = onUserOnline.IS_ONLINE
                }
            }
        },

        OnUserRow: (e: OnUserRow) => {
            compareTypes(e, new OnUserRow())
            const users = this.users.filter((u: OnUserRow) => u.USERID !== e.USERID)
            if (!e.DELETED) {
                users.push(e)
            }
            this.users = users
        },

        OnSystemMessage: (e: OnSystemMessage) => {
            compareTypes(e, new OnSystemMessage())
            remove(this.system_messages, (m: OnSystemMessage) => m.MESSAGEID == e.MESSAGEID)

            this.alerts = [Object.assign(new OnAlert(), {BODY: e.TEXT})]
            this.system_messages = [...this.system_messages, e]
        }
    }

    onEvent(msg: any) {
        if (msg.EVENT !== 'OnTime') {
            if(LOG_MESSAGES){
                console.log('msg', msg)
            }
        }
        
        const handler = this.handlers[msg.EVENT]
        if (!handler) {
            error('No handler found for event: ' + msg.EVENT)
        }

        try {
            handler(msg)
        } catch (e) {
            if(LOG_MESSAGES){
                console.log({e})
            }
            throw(e)
        }
    }
}
