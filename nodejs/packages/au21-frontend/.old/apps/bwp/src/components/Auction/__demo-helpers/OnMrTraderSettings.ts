import { OnMrTraderSettings } from '../../../_generated/server_outputs'

export const createOnMrTraderSettings = () => ({
  AUCTIONID: 1155,
  DESCRIPTION: 'Test auction with long long long long long long long long long long long long long long long long long long long long long long long long long long long long long long long long',
  ELIGIBILITY: 'Your current eligibility is 1 to 100,000 Dth/day in steps of 1 Dth/day',
  HIGH_CHANGE: 'increases by 0.0030 $/Dth',
  HIGH_LABEL: 'H',
  HIGH_LIMIT: 'greater than 200,000 Dth/day',
  LOW_CHANGE: 'increases by 0.0010 $/Dth',
  LOW_LABEL: 'L',
  LOW_LIMIT: 'less than or equal to 150,000 Dth/day',
  MED_CHANGE: 'increases by 0.0020 $/Dth',
  MED_LABEL: 'M',
  MED_LIMIT: 'greater than 150,000 Dth/day',
  PRICE_DIRECTION: 'Price increases each round',
  ROUND_DURATION: 'First round: 60 seconds, remaining rounds: 60 seconds',
  VISIBILITY: 'All rounds of the auction will be visible to you.',
} as OnMrTraderSettings)
