<template>
  <a-radio-group
    :disabled="disabled"
    class="UserRoleSelect"
    v-model="valueProxy"
  >
    <a-radio
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      :style="radioStyle"
    >
      {{ option.name }}
    </a-radio>
  </a-radio-group>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { Role } from '../../../_generated/bwp-enums'
import { getRoleOptions } from './Role'

@Component({})
export default class UserRoleSelect extends Vue {
  @Prop({ type: String }) value: Role
  @Prop({ type: Boolean }) disabled: boolean
  radioStyle = {
    display: 'block',
    height: '30px',
    lineHeight: '30px',
  }
  options = getRoleOptions()

  get valueProxy (): Role {
    return this.value
  }

  set valueProxy (value) {
    this.$emit('input', value)
  }
}
</script>

<style lang="less">
.UserRoleSelect {

}
</style>
