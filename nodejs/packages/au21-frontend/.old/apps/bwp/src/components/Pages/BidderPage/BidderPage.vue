<template>
  <div class="BidderPage" style="position: relative">
    <BidderHeader
      v-if="$auStore.trader.trader_status"
      style="flex: 0 0"
      @openSettings="showSettingsModal = true"
      @openNotice="showNoticeModal = true"
      :onMrBidderStatus="$auStore.trader.trader_status"
      :onMrTraderSettings="$auStore.mr_trader_settings"
      @volumeSubmit="submitVolume"
    />
    
    <div style="flex: 1 1; display: flex; align-items: stretch; position: absolute; width: 100%" :style="{top: 174 + (this.$auStore.trader.award ? 32 : 0) + 'px'}">
      <div class="form-block">
        <div class="text-bold" style="text-align: center;">
          Round History
        </div>
        <OnMrBidRowList
          ref="roundHistoryList"
          :height="height"
          :width="$auLocalStore.config.width_inner / 2"
          :onMrBidRowList="historyRowsDebounced"
          :onMrTraderSettings="$auStore.mr_trader_settings"
        />
      </div>
      <div class="form-block" style="flex: 1 1 50%; flex-direction: column; display: flex; justify-content: stretch">
        <div class="text-bold" style="text-align: center">
          Auction Messages
        </div>
        <AuctionMessages :style="{height: height + 'px'}"/>
      </div>
    </div>

    <a-modal
      title="Auction settings"
      v-model="showSettingsModal"
      closable
      centered
      width="800px"
    >
      <BidderAuctionSettings
        v-if="$auStore.mr_trader_settings"
        :onMrTraderSettings="$auStore.mr_trader_settings"
      />
      <a-spin v-else/>

      <a-button
        slot="footer"
        @click="showSettingsModal = false"
        type="dashed"
      >
        Close
      </a-button>
    </a-modal>

    <AuctionNoticeModal
      v-if="showNoticeModal"
      :value="$auStore.current_auction.notice"
      @close="showNoticeModal = false"
      readonly
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import BidderHeader from '../../BidderHeader/BidderHeader.vue'
import BidderAuctionSettings
  from '../../BidderAuctionSettings/BidderAuctionSettings.vue'
import OnMrBidRowList from '../../RoundHistoryList/OnMrBidRowList.vue'
import AuctionMessages from '../../AuctionMessages/AuctionMessages.vue'
import PageLayout from '../../PageLayout/PageLayout.vue'
import {
  bwp_getTraderSettings,
  bwp_submitOrder,
  bwp_getTraderNotice,
} from '../../../services/bwp-connector/publisher'
import AuctionNoticeModal
  from '../../Auction/AuctionNoticeModal/AuctionNoticeModal.vue'
import { MAX_HEIGHT } from '../../../helpers/height_helper'
import {DebounceLoader} from 'asva-executors'
import { OnMrBidRow } from '../../../_generated/server_outputs'

@Component({
  components: {
    AuctionNoticeModal,
    PageLayout,
    AuctionMessages,
    OnMrBidRowList,
    BidderAuctionSettings,
    BidderHeader,
  },
})
export default class BidderPage extends Vue {
  showSettingsModal = false
  showNoticeModal = false

  @Watch('showSettingsModal')
  onShowSettingsModal (value) {
    if (value) {
      bwp_getTraderSettings(this.$auConnector)
    }
  }

  @Watch('showNoticeModal')
  onShowNoticeModal (value) {
    if (value) {
      bwp_getTraderNotice(this.$auConnector)
    }
  }

  // Prevents table from being redrawn on each update (backend might send hundreds of these depending on list size).
  historyRowsDebounced: OnMrBidRow[] = []
  historyRowsDebounceLoader = new DebounceLoader(async () => {
    this.refreshDebounceLoaderRow()
  }, 100)
  @Watch('historyRows', {deep: true, immediate: true})
  onHistoryRowsChange () {
    this.historyRowsDebounceLoader.run()
  }
  refreshDebounceLoaderRow () {
    this.historyRowsDebounced = [...this.historyRows]
  }
  get historyRows () {
    return this.$auStore.trader.history_rows
  }

  get height () {
    let height = this.$auLocalStore.config.height_inner - 194
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      height = MAX_HEIGHT - 198
    }
    if (!this.$auStore.trader.award) {
      height += 32
    }
    return height
  }

  submitVolume (volume: string) {
    bwp_submitOrder(this.$auConnector, volume)
  }
}
</script>

<style lang="less">
@import '../../../assets/variables.less';

.BidderPage {

}
</style>
