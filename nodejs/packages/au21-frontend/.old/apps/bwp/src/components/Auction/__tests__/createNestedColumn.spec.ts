import { createNestedColumn } from '../AuctionRoundBlotter/createNestedColumn'

describe('createNestedColumn', () => {
  it('createNestedColumn', () => {
    const result = createNestedColumn({
      width: 170,
      dataIndex: 'ROUND',
    }, [
      'One',
      'Two',
      'Three',
    ], 'left')

    const expected = {
      title: 'One',
      fixed: 'left',
      children: [{
        title: 'Two',
        children: [{
          title: 'Three',
          width: 170,
          dataIndex: 'ROUND',
        }],
      }],
    }

    expect(result).toEqual(expected)
  })
})
