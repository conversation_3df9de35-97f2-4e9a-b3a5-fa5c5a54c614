import { Operator } from '../../../_generated/bwp-enums'

const operators: Operator[] = [
  'GT',
  'GE',
]

const operatorToOptionNameMap: { [R in Operator]: string } = {
  'GT': '>',
  'GE': '≥',
}

export const getOperatorName = (operator: Operator) => operatorToOptionNameMap[operator]

export const getOperatorOptions = () => operators.map(
  operator => ({ value: operator, name: getOperatorName(operator) }),
)
