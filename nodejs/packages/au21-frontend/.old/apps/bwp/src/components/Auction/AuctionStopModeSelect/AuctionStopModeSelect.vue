<template>
  <div v-if="disabled" class="pseudo-input">
    {{selectedValueText}}
  </div>
  <a-select
    v-else
    class="AuctionStopModeSelect"
    size="small"
    defaultValue="lucy"
    style="width: 120px"
    v-model="valueProxy"
  >
    <a-select-option
      v-for="option in options"
      :key="option.value"
      :value="option.value"
    >
      {{option.name}}
    </a-select-option>
  </a-select>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { MRStopMode } from '../../../_generated/bwp-enums'
import { getMrStopModeName, getMrStopModeOptions } from '../__demo-helpers/MrStopMode'

@Component({})
export default class AuctionStopModeSelect extends Vue {
  @Prop({ type: String }) value: MRStopMode
  @Prop({ type: <PERSON>olean }) disabled: boolean
  options = getMrStopModeOptions()

  get valueProxy (): MRStopMode {
    return this.value
  }

  set valueProxy (value) {
    this.$emit('input', value)
  }

  get selectedValueText () {
    return getMrStopModeName(this.value)
  }
}
</script>

<style lang="less">
.AuctionStopModeSelect {

}
</style>
