<template>
  <VbDemo>
    <VbCard
      no-padding
      style="width: 100%; background-color: #28324b"
    >
      <NavigationBar :onUserRow="onUserRow"/>
    </VbCard>
    <VbCard title="Role" style="background-color: gray">
      <UserRoleSelect dashed v-model="onUserRow.ROLE"/>
    </VbCard>
    <VbCard title="Username" style="background-color: gray">
      <a-input v-model="onUserRow.USERNAME"/>
    </VbCard>
  </VbDemo>
</template>

<script>
import NavigationBar from './NavigationBar.vue'
import { createOnUserRow } from '../User/UserDisplay/OnUserRowFactory'
import UserRoleSelect from '../User/UserRoleSelect/UserRoleSelect'

export default {
  components: {
    UserRoleSelect,
    NavigationBar,
  },
  data () {
    return {
      onUserRow: createOnUserRow(),
    }
  },
}
</script>
