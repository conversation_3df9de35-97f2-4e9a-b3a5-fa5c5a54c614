<template>
  <div class="UserPage form-block">
    <div class="au-header">Users</div>
    <div style="flex: 1 1; display: flex; align-items: stretch;">
      
      <AuScrollableTableStepped
        :table_width="650"
        :table_height="userListHeight"
        :header_height="24"
        :cell_height="24"
        :visible_columns="0"
        :columns="[]"
        :fixed_columns="userListColumns"
        :header_rows="['']"
        :rows="onUserRowList"
        :columnWidthSetter="column => column.width"
        whiteFirstColumn
      >
        <div
          slot="fixed_header_cell"
          slot-scope="{column, row}"
          class="table-cell-wrapper"
          style="text-align: left; padding: 2px 6px; background-color: #666666; color: white; font-weight: 700; cursor: pointer"
          @click="onColumnSort(column)"
        >
          {{column.title}}
          <a-icon v-if="sortColumn === column.dataIndex" style="width: 12px; height: 12px" :type="sortBy === 'asc' ? 'down' : 'up'"/>
        </div>

        <div
          slot="fixed_body_cell"
          slot-scope="{column, row}"
          class="table-cell-wrapper"
          style="text-align: left; padding: 2px 6px; cursor: pointer"
          :style="{'background-color': onUserRowForDisplay === row ? '#d6d2a7': ''}"
          :title="row[column.dataIndex]"
          @click="onUserRowForDisplay = row"
        >
          {{column.dataIndex === 'ROLE' ? getUserRole(row) : row[column.dataIndex] || '&nbsp;'}}
        </div>
      </AuScrollableTableStepped>

      <div style="flex: 1 1; background-color: #666666">
        <div style="display: flex; justify-content: center; padding-top: 6px">
          User:&nbsp;
          <a-button
            :disabled="!onUserRowForDisplay"
            size="small"
            @click="startUpdatingUser()"
          >
            Edit
          </a-button>&nbsp;
          <a-button size="small" @click="startCreatingUser()">New</a-button>&nbsp;

          <a-popconfirm
            v-if="onUserRowForDisplay"
            placement="bottomRight"
            okText="Yes"
            cancelText="No"
            @confirm="saveDeletingUser()"
            :disabled="!onUserRowForDisplay"
          >
            <template slot="title">
              <p>Delete "{{onUserRowForDisplay.USERNAME}}"?</p>
            </template>
            <a-button size="small">
              Delete
            </a-button>
          </a-popconfirm>
          <a-button disabled v-else size="small">
            Delete
          </a-button>
        </div>

        <UserDisplay
          :style="{height: userListHeight - 26 + 'px'}"
          v-if="onUserRowForDisplay"
          :onUserRow="onUserRowForDisplay"
        />
      </div>

      <a-modal
        title="Create user"
        class="ant-modal--no-padding"
        :visible="!!showOnUserRowCreateModal"
        @cancel="breakCreatingUser()"
        closable
        centered
        width="330px"
      >
        <UserEdit :onUserRow="onUserRowForCreate"/>

        <a-button
          slot="footer"
          @click="saveCreatingUser()"
        >
          Create
        </a-button>

        <a-button
          slot="footer"
          type="dashed"
          @click="breakCreatingUser()"
        >
          Cancel
        </a-button>
      </a-modal>

      <a-modal
        title="Edit user"
        class="ant-modal--no-padding"
        :visible="!!showOnUserRowEditModal"
        @cancel="breakUpdatingUser()"
        closable
        centered
        width="330px"
      >
        <UserEdit v-if="onUserRowForEdit" :onUserRow="onUserRowForEdit"/>

        <a-button
          slot="footer"
          @click="saveUpdatingUser()"
        >
          Save
        </a-button>

        <a-button
          slot="footer"
          type="dashed"
          @click="breakUpdatingUser()"
        >
          Cancel
        </a-button>
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import PageLayout from '../../PageLayout/PageLayout.vue'
import {
  createCleanOnUserRow,
} from '../UserDisplay/OnUserRowFactory'
import UserDisplay from '../UserDisplay/UserDisplay.vue'
import UserEdit from '../UserEdit/UserEdit.vue'
import {
  bwp_createUser,
  bwp_editUser, bwp_deleteUser,
} from '../../../services/bwp-connector/publisher'
import { OnUserRow } from '../../../_generated/server_outputs'
import AuScrollableTableStepped from '../../../ui-components/TableScroller/AuScrollableTableStepped.vue'
import { getRoleName } from '../UserRoleSelect/Role'
import { MAX_HEIGHT } from '../../../helpers/height_helper'
import {DebounceLoader} from 'asva-executors'

type UserListColumn = {
  title: string,
  dataIndex: 'USERNAME' | 'COMPANY' | 'ROLE',
  width: number,
}

@Component({
  components: { AuScrollableTableStepped, UserEdit, UserDisplay, PageLayout },
})
export default class UserPage extends Vue {
  sortBy: 'asc' | 'desc' = 'asc'
  sortColumn: 'USERNAME' | 'COMPANY' | 'ROLE' = 'USERNAME'
  
  // TODO Width is not operational for columns
  userListColumns = [
    {
      title: 'Username',
      dataIndex: 'USERNAME',
      width: 130,
    },
    {
      title: 'Company',
      dataIndex: 'COMPANY',
      width: 390,
    },
    {
      title: 'Role',
      dataIndex: 'ROLE',
      width: 120,
    },
  ]

  onUserRowForDisplay: OnUserRow = null

  showOnUserRowCreateModal = false
  onUserRowForCreate: OnUserRow = null

  showOnUserRowEditModal = false
  onUserRowForEdit: OnUserRow = null
  
  // Prevents table from being redrawn on each update (backend might send hundreds of these depending on list size).
  userRowsDebounced: OnUserRow[] = []
  userRowsDebounceLoader = new DebounceLoader(async () => {
    this.refreshDebounceLoaderRow()
  }, 100)
  @Watch('userRows', {deep: true, immediate: true})
  onUserRowsChange () {
    this.userRowsDebounceLoader.run()
  }
  refreshDebounceLoaderRow () {
    this.userRowsDebounced = [...this.userRows]
  }
  get userRows () {
    return this.$auStore.users
  }
  
  get onUserRowList (): OnUserRow[] {
    return this.userRowsDebounced.sort(
      (a, b) => (a[this.sortColumn] > b[this.sortColumn] ? 1 : -1) * (this.sortBy === 'asc' ? 1 : -1)
    )
  }

  onColumnSort (column: UserListColumn) {
    if (this.sortColumn === column.dataIndex && this.sortBy === 'asc') {
      this.sortBy = 'desc'
      return
    }
    this.sortColumn = column.dataIndex
    this.sortBy = 'asc'
  }

  // CREATE
  startCreatingUser () {
    this.showOnUserRowCreateModal = true
    this.onUserRowForCreate = createCleanOnUserRow()
  }

  breakCreatingUser () {
    this.showOnUserRowCreateModal = false
  }

  saveCreatingUser () {
    bwp_createUser(this.$auConnector, this.onUserRowForCreate)
    this.showOnUserRowCreateModal = false
    // TODO Should update backend.
  }

  // UPDATE
  startUpdatingUser () {
    this.showOnUserRowEditModal = true
    this.onUserRowForEdit = Object.assign({}, this.onUserRowForDisplay)
  }

  breakUpdatingUser () {
    this.showOnUserRowEditModal = false
  }

  saveUpdatingUser () {
    bwp_editUser(this.$auConnector, this.onUserRowForEdit)
    this.showOnUserRowEditModal = false
  }

  saveDeletingUser () {
    bwp_deleteUser(this.$auConnector, this.onUserRowForDisplay)
  }

  getUserRole (onUserRow: OnUserRow): string {
    return getRoleName(onUserRow.ROLE)
  }

  get userListHeight () {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return MAX_HEIGHT - 10
    }

    return this.$auLocalStore.config.height_inner
  }
}
</script>

<style lang="less">
.UserPage {
  .ant-modal--no-padding {
    .ant-modal-body {
      padding: 0px;
    }
  }
}
</style>
