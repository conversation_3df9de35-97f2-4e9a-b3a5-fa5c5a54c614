import { MRVisibility } from '../../../_generated/bwp-enums'

const mrVisibilitys: MRVisibility[] = [
  'ALL',
  'FIRST_ROUND',
  'ELIGIBILITY',
]

const mrVisibilityToOptionNameMap: { [R in MRVisibility]: string } = {
  'ALL': 'All',
  'FIRST_ROUND': 'First round',
  'ELIGIBILITY': 'Eligibility',
}

export const getMrVisibilityName = (mrVisibility: MRVisibility) => mrVisibilityToOptionNameMap[mrVisibility]

export const getMrVisibilityOptions = () => mrVisibilitys.map(
  mrVisibility => ({
    value: mrVisibility,
    name: getMrVisibilityName(mrVisibility),
  }),
)
