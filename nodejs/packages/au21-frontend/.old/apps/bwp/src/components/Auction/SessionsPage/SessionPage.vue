<template>
  <div class="SessionPage">
    <div class="form-block">
      <SessionTable
        :onSessionRowList="onSessionRowList"
        :width="width"
        :height="tableHeight"
      />
    </div>
    <div class="form-block">
      <SystemMessageTable
        :onSystemMessageList="onSystemMessageList"
        :width="width"
        :height="tableHeight"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import PageLayout from '../../PageLayout/PageLayout.vue'
import SystemMessageTable from './SystemMessageTable/SystemMessageTable.vue'
import { WindowInstanceMap } from '../../../helpers/window-instance'
import SessionTable from './SessionTable/SessionTable.vue'
import { MAX_HEIGHT } from '../../../helpers/height_helper'

@Component({
  components: { SessionTable, SystemMessageTable, PageLayout },
})
export default class SessionPage extends Vue {
  windowInstance = WindowInstanceMap

  get onSessionRowList () {
    return this.$auStore.sessions
  }

  get onSystemMessageList () {
    return this.$auStore.system_messages
  }

  get width () {
    return this.$auLocalStore.config.width_inner + 2
  }

  get tableHeight () {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return (MAX_HEIGHT + 12) / 2
    }

    return (this.$auLocalStore.config.height_inner + 22) / 2
  }
}
</script>

<style lang="less">
.SessionPage {

}
</style>
