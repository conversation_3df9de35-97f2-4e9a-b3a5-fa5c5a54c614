<template>
  <VbDemo>
    <VbCard height="500px">
      <PageLayout style="width: 900px; height: 400px">
        [Page contents]
      </PageLayout>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import PageLayout from './PageLayout.vue'

@Component({
  components: { PageLayout },
})
export default class PageLayoutDemo extends Vue {

}
</script>
