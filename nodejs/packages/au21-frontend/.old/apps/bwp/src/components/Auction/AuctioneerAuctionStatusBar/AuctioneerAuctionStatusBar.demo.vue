<template>
  <VbDemo>
    <VbCard no-padding class="au-page">
      <AuctioneerAuctionStatusBar
        style="width: 858px"
        :onMrAuctioneerSummary="onMrAuctioneerSummary"
        :biddersTotal="biddersTotal"
        :biddersOnline="biddersOnline"
        :sortBy.sync="sortBy"
      />
    </VbCard>
    <VbCard>
      <p>biddersTotal: {{biddersTotal}}</p>
      <p>biddersOnline: {{biddersOnline}}</p>
      <p>sortBy: {{sortBy}}</p>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AuctioneerAuctionStatusBar from './AuctioneerAuctionStatusBar.vue'
import { createOnMrAuctioneerSummary } from '../__demo-helpers/OnMrAuctioneerSummary'

@Component({
  components: { AuctioneerAuctionStatusBar },
})
export default class AuctioneerAuctionStatusBarDemo extends Vue {
  onMrAuctioneerSummary = createOnMrAuctioneerSummary()
  biddersTotal = 6
  biddersOnline = 3
  sortBy = 'USERNAME'
}
</script>
