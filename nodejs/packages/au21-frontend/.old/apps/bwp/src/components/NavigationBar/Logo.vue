<template>
  <div class="Logo" :style="containerStyle">
    <img :style="imageStyle" :src="src"/>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

const imageCache: Record<string, HTMLImageElement> = {}

function loadImage (src: string): Promise<HTMLImageElement> {
  return new Promise((resolve) => {
    if (imageCache[src]) {
      resolve(imageCache[src])
      return
    }
    
    const logo = new Image()
    logo.onload = function () {
      resolve(logo)
    }
    logo.src = src
  })
}

// Simlar to `object-fit: scale-down`, but works in IE.
function scaleToDimensions (object: Size, boundary: Size): Size {
  const objectRatio = object.width / object.height
  const boundaryRatio = boundary.width / boundary.height

  if (object.width <= boundary.width && object.height <= boundary.height) {
    return object
  }

  // object is more landscape than boundary - fit to width
  if (objectRatio > boundaryRatio) {
    return {
      width: boundary.width,
      height: object.height * (boundary.width / object.width),
    }
  }

  // object is more portrait than boundary - fit to height
  return {
    width: object.width * (boundary.height / object.height),
    height: boundary.height,
  }
}

type Size = { width: number, height: number }

@Component({})
export default class Logo extends Vue {
  @Prop({ required: true, type: String }) src: string
  @Prop({ required: true, type: Number }) height: number
  @Prop({ required: true, type: Number }) width: number

  loadedImage: HTMLImageElement | null = null

  async created () {
    this.loadedImage = await loadImage(this.src)
  }
  
  get imageSize (): Size {
    if (!this.loadedImage) {
      return { width: 0, height: 0 }
    }
    return scaleToDimensions(this.loadedImage, {width: this.width, height: this.height})
  }
  get marginLeft (): number {
    return (this.width - this.imageSize.width)/2
  }
  get marginTop (): number {
    return (this.height - this.imageSize.height)/2
  }
  get containerStyle () {
    return {
      backgroundColor: 'white',
      width: this.width + 'px',
      height: this.height + 'px',
    }
  }
  get imageStyle () {
    return {
      height: this.imageSize.height + 'px',
      width: this.imageSize.width + 'px',
      marginLeft: this.marginLeft + 'px',
      marginTop: this.marginTop + 'px',
    }
  }
}
</script>

<style lang="less">
.Logo {

}
</style>
