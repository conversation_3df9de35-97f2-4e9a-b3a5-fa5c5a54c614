<template>
  <VbDemo>
    <VbCard>
      <SessionTable
        :height="500"
        :width="600"
        :onSessionRowList="onSessionRowList"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import SessionTable from './SessionTable.vue'
import { createMultipleByClosure } from '@au21-frontend/utils'
import { createOnSessionRow } from '../../__demo-helpers/OnSessionRow'

export default {
  data () {
    return {
      onSessionRowList: createMultipleByClosure(createOnSessionRow, 20),
    }
  },
  components: {
    SessionTable,
  },
}
</script>
