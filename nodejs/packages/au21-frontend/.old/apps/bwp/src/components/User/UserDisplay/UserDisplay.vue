<template>
  <div class="UserDisplay">
    <div class="mb-1">Username:</div>
    <a-input class="ant-input--disabled" disabled size="small" v-model="onUserRow.USERNAME"/>
    
    <div class="mt-2 mb-1">Password:</div>
    <a-input class="ant-input--disabled" disabled size="small" v-model="onUserRow.PASSWORD"/>
    
    <div class="mt-2">Role:</div>
    <UserRoleSelect disabled v-model="onUserRow.ROLE"/>
    
    <div class="mt-2">Company:</div>
    <a-input class="ant-input--disabled" disabled size="small" v-model="onUserRow.COMPANY"/>
    
    <div class="mt-2 mb-1">Email:</div>
    <a-input class="ant-input--disabled" disabled size="small" v-model="onUserRow.EMAIL"/>
    
    <div class="mt-2 mb-1">Work:</div>
    <a-input class="ant-input--disabled" disabled size="small" v-model="onUserRow.WORK"/>

    <div class="mt-2 mb-1">Mobile:</div>
    <a-input class="ant-input--disabled" disabled size="small" v-model="onUserRow.MOBILE"/>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnUserRow } from '../../../_generated/server_outputs'
import UserRoleSelect from '../UserRoleSelect/UserRoleSelect.vue'

@Component({
  components: { UserRoleSelect },
})
export default class UserDisplay extends Vue {
  @Prop({ required: true }) onUserRow: OnUserRow
}
</script>

<style lang="less">
@import "../../../assets/variables.less";

.UserDisplay {
  overflow: auto;
  &__text-display {
    width: 100%;
  }

  color: @au-text-color;
  background-color: @au-background-light;
  padding: 12px;
}
</style>
