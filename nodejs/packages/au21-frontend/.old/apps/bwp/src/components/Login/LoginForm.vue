<template>
  <div class="LoginForm">
    <p style="text-align: center" class="text-bold">Sign in to auction</p>

    <a-row class="mb-1" align="middle">
      <a-col :span="8" class="text-bold">Username:</a-col>
      <a-col :span="16">
        <a-input size="small" ref="username" @pressEnter="$refs.password.focus()" v-model="username"/>
      </a-col>
    </a-row>

    <a-row class="mb-1" align="middle">
      <a-col :span="8" class="text-bold">Password:</a-col>
      <a-col :span="16">
        <a-input size="small" ref="password" type="password" @pressEnter="submit({ username, password })" v-model="password"/>
      </a-col>
    </a-row>

    <a-row class="mb-1" align="middle" v-if="test_logins.length">
      <a-col :span="7" class="text-bold test-only">Test login:</a-col>
      <a-col :span="17" style="text-align: right">
        <a-button
          v-for="test_login in test_logins"
          :key="test_login.username"
          @click="submit(test_login)"
          size="small"
        >
          {{test_login.username}}
        </a-button>
      </a-col>
    </a-row>

    <a-button style="float: right" @click="submit({ username, password })">Sign in</a-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { bwp_login } from '../../services/bwp-connector/publisher'
import {test_logins} from './test-logins'

@Component
export default class LoginForm extends Vue {
  username = ''
  password = ''
  test_logins = test_logins

  mounted () {
    (this.$refs.username as HTMLElement).focus()
  }

  submit (payload) {
    bwp_login(this.$auConnector, payload)
  }
}
</script>

<style lang="less">
.LoginForm {

}
</style>

