<template>
  <VbDemo>
    <VbCard>
      <BidderSelectTable
        :height="300"
        :bidderList="bidderList"
        v-model="selectedUserIds"
      />
    </VbCard>
    <VbCard>
      <BidderSelectTable
        :height="300"
        :bidderList="bidderList"
        v-model="selectedUserIds"
      />
    </VbCard>
    <VbCard>
      {{selectedUserIds}}
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import BidderSelectTable from './BidderSelectTable.vue'
import { createMultipleByClosure } from '../../../helpers/array-helpers'
import { createOnUserRow } from '../../User/UserDisplay/OnUserRowFactory'

export default {
  components: {
    BidderSelectTable,
  },
  data () {
    return {
      bidderList: createMultipleByClosure(createOnUserRow, 20),
      selectedUserIds: [],
    }
  },
}
</script>
