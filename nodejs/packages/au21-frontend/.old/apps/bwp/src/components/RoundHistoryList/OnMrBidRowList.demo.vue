<template>
  <VbDemo>
    <VbCard no-padding>
      <OnMrBidRowList
        ref="onMrBidRowList"
        :onMrBidRowList="onMrBidRowList"
        :height="400"
        :width="300"
      />
    </VbCard>
    <VbCard>
      <button
        @click="$refs.onMrBidRowList.scrollToBottomRow()"
      >
        Scroll to bottom row
      </button>
    </VbCard>
  </VbDemo>
</template>

<script>
import OnMrBidRowList from './OnMrBidRowList.vue'
import { createOnMrBidRow } from '../Auction/__demo-helpers/OnMrBidRow'
import { createMultipleByClosure } from '../../helpers/array-helpers'

export default {
  components: {
    OnMrBidRowList,
  },
  data () {
    return {
      onMrBidRowList: createMultipleByClosure(createOnMrBidRow, 40),
    }
  },
}
</script>
