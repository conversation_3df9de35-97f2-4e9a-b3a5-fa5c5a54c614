<template>
  <a-modal
    class="SetRoundPriceModal"
    :title="title"
    @cancel="breakSettingPrice()"
    visible
    closable
    centered
    width="330px"
    :maskClosable="false"
  >
    <div style="padding: 12px">
      <div class="mb-1">
        <div class="SetRoundPriceModal__label mr-1">Round:</div>
        <div style="width: 130px" class="pseudo-input">{{onMrAuctionStatus.ROUND_NUMBER}}</div>
      </div>
      <div>
        <div class="SetRoundPriceModal__label mr-1">{{ clock_label }}:</div>
        <a-input style="width: 130px" size="small" v-model="localPrice"/>
      </div>
    </div>

    <a-button
      slot="footer"
      @click="saveSettingPrice()"
    >
      Save
    </a-button>

    <a-button
      slot="footer"
      type="dashed"
      @click="breakSettingPrice()"
    >
      Cancel
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { OnMrAuctionStatus } from '../../../_generated/server_outputs'
import {getMrClockLabelName} from "../__demo-helpers/MrClockLabel";
import {MRClockLabel} from "../../../_generated/bwp-enums";

@Component({})
export default class SetRoundPriceModal extends Vue {
  @Prop({ required: true }) onMrAuctionStatus: OnMrAuctionStatus
  @Prop({required:true}) clock_label:string

  localPrice = ''

  get title(){
    return "Set round " + this.clock_label
  }

  @Watch('onMrAuctionStatus', { immediate: true })
  onMrAuctionStatusHandler () {
    this.localPrice = this.onMrAuctionStatus.ROUND_PRICE
  }

  breakSettingPrice () {
    this.$emit('close')
  }

  saveSettingPrice () {
    this.$emit('saveSettingPrice', this.localPrice)
    this.$emit('close')
  }
}
</script>

<style lang="less">
@import '../../../assets/variables.less';

.SetRoundPriceModal {
  &__label {
    color: @au-text-color;
    display: inline-block;
    width: 60px;
    text-align: right;
  }
}
</style>
