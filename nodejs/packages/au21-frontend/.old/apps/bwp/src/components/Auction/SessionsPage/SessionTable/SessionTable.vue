<template>
  <div class="SessionTable" :style="{height: height + 'px', 'background-color': $themes.white}">
    <table :style="{width:'100%'}" class="au-markup-table">
      <template v-for="(onSessionRow, count) in onSessionRowList">
        <tr
          :key="`[${onSessionRow.SESSIONID}].SESSIONID`"
          :style="{
            'border-top': count < 1 ? `solid 1px ${$themes.dark}` : undefined,
            'background-color': count % 2 ? $themes.light : undefined,
          }"
        >
          <td style="width: 120px"><strong>SID:</strong></td>
          <td>{{ onSessionRow.SESSIONID }}</td>
        </tr>
        <tr
          :key="`[${onSessionRow.SESSIONID}].USERNAME`"
          :style="{
          'background-color': count % 2 ? $themes.light : undefined,
        }"
        >
          <td><strong>User:</strong></td>
          <td>{{ onSessionRow.USERNAME }}</td>
        </tr>
        <tr
          :key="`[${onSessionRow.SESSIONID}].PAGE`"
          :style="{
          'background-color': count % 2 ? $themes.light : undefined,
        }"
        >
          <td><strong>Page:</strong></td>
          <td>{{ onSessionRow.PAGE }}</td>
        </tr>
        <tr
          :key="`[${onSessionRow.SESSIONID}].AUCTION_NAME`"
          :style="{
          'background-color': count % 2 ? $themes.light : undefined,
        }"
        >
          <td><strong>Auction name:</strong></td>
          <td>{{ onSessionRow.AUCTION_NAME }}</td>
        </tr>
        <tr
          :key="`[${onSessionRow.SESSIONID}].REMOTE_ADDRESS`"
          :style="{
          'background-color': count % 2 ? $themes.light : undefined,
        }"
        >
          <td><strong>Address:</strong></td>
          <td>{{ onSessionRow.REMOTE_ADDRESS }}</td>
        </tr>
        <tr
          :key="`[${onSessionRow.SESSIONID}].USER_AGENT`"
          :style="{
          'background-color': count % 2 ? $themes.light : undefined,
        }"
        >
          <td><strong>Browser:</strong></td>
          <td>{{ onSessionRow.USER_AGENT }}</td>
        </tr>
      </template>
    </table>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnSessionRow } from '../../../../_generated/server_outputs'
import AuScrollableTableStepped
  from '../../../../ui-components/TableScroller/AuScrollableTableStepped.vue'

@Component({
  components: { AuScrollableTableStepped },
})
export default class SessionTable extends Vue {
  @Prop({ type: Array, required: true }) onSessionRowList: OnSessionRow[]
  @Prop({ type: Number }) height!: number
  @Prop({ type: Number }) width!: number

  columns = [
    {
      title: '',
      dataIndex: 'COLUMN_1',
      width: this.width * .4,
    },
    {
      title: '',
      dataIndex: 'COLUMN_2',
      width: this.width * .6,
    },
  ]
}
</script>

<style lang="less">
.SessionTable {
  border: 1px solid #e8e8e8;
  border-radius: 2px;
  overflow: auto;
  td:first-child {
    text-align: right;
  }
}
</style>
