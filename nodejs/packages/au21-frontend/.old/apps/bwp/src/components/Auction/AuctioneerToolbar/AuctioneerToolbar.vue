<template>
  <div class="AuctioneerToolbar">
    <div class="mr-1" style="flex: 0 0 60px; text-align: right">Auction:</div>
    <a-button
      class="AuctioneerToolbar__button"
      @click="addBidders()"
      size="small"
      style="flex: 0 0 60px;"
    >
      <a-icon style="width: 12px; height: 12px" type="plus"/>
      Bidders
    </a-button>
    <AddBidderModal
      v-if="showAddBidderModal"
      @close="showAddBidderModal = false"
    />

    <a-button
      class="AuctioneerToolbar__button"
      @click="removeBidders()"
      size="small"
      style="flex: 0 0 60px;"
    >
      <a-icon style="width: 12px; height: 12px" type="minus"/>
      Bidders
    </a-button>
    <AddBidderModal
      v-if="showRemoveBidderModal"
      isRemove
      @close="showRemoveBidderModal = false"
    />
    <a-button
      class="AuctioneerToolbar__button"
      @click="$emit('editNotice')"
      size="small"
    >
      Notice
    </a-button>

    <a-button
      class="AuctioneerToolbar__button"
      @click="$emit('award')"
      size="small"
    >
      Award
    </a-button>
    <a-button
      class="AuctioneerToolbar__button"
      @click="$emit('showSettings')"
      size="small"
    >
      Settings
    </a-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AuctionNoticeModal from '../AuctionNoticeModal/AuctionNoticeModal.vue'
import AuctionEdit from '../AuctionEdit.vue'
import BidderSelectTable from '../BidderSelectTable/BidderSelectTable.vue'
import AddBidderModal from '../AddBidderModal/AddBidderModal.vue'

@Component({
  components: {
    AddBidderModal,
    BidderSelectTable,
    AuctionEdit,
    AuctionNoticeModal,
  },
})
export default class AuctioneerToolbar extends Vue {
  showAddBidderModal = false
  showRemoveBidderModal = false

  addBidders () {
    this.showAddBidderModal = true
  }

  removeBidders () {
    this.showRemoveBidderModal = true
  }
}
</script>

<style lang="less">
.AuctioneerToolbar {
  display: flex;
  align-items: center;

  &__button {
    flex: 1 1;

    & + & {
      margin-left: 2px;
    }
  }
}
</style>
