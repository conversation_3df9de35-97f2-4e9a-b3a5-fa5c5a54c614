<template>
  <VbDemo>
    <VbCard>
      <BidderAuctionSettings :onMrTraderSettings="onMrTraderSettings"/>
    </VbCard>
  </VbDemo>
</template>

<script>
import BidderAuctionSettings from './BidderAuctionSettings.vue'
import { createOnMrTraderSettings } from '../Auction/__demo-helpers/OnMrTraderSettings'


export default {
  components: {
    BidderAuctionSettings,
  },
  data () {
    return {
      onMrTraderSettings: createOnMrTraderSettings(),
    }
  },
}
</script>
