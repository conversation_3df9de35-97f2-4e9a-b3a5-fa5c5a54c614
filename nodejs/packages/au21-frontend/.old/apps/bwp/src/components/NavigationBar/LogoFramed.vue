<template>
  <div class="LogoFramed form-block-container" style="width: 206px;">
    <img v-if="!isBrowserIE" style="height: 68px; width: 200px; object-fit: scale-down; object-position: center; background-color: white" alt="logo" :src="logoUrl"/>
    <Logo v-else :height="68" :width="200" :src="logoUrl"/>
  </div>
</template>

<script lang="ts">
import Logo from './Logo.vue'
import { logoUrl } from '../../store/config'
import { isBrowserIE } from '../../helpers/browser-helpers'
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: { Logo },
})
export default class LogoFramed extends Vue {
  logoUrl = logoUrl

  get isBrowserIE (): boolean {
    return isBrowserIE();
  }
}
</script>
