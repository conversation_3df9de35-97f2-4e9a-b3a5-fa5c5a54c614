<template>
  <a-list
    class="AuctionMessageList"
    itemLayout="horizontal"
    :dataSource="onMessageList"
    style="overflow: auto; flex: 1 1"
  >
    <a-list-item
      :locale="{emptyText: ''}"
      slot="renderItem"
      slot-scope="onMessage, index"
      :class="{
        'AuctionMessageList__item': true,
        'AuctionMessageList__item--not-of-sender': sender && onMessage.SENDER !== sender
      }"
    >
      <a-list-item-meta :description="`${onMessage.SENDER}: ${onMessage.TEXT}`">
        <div slot="title">{{onMessage.TIME}} {{onMessage.DATE}}</div>
      </a-list-item-meta>
    </a-list-item>
  </a-list>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { OnMessage } from '../../../_generated/server_outputs'
import Blinker from '../../../ui-components/Blinker/Blinker.vue'

@Component({
  components: { Blinker },
})
export default class AuctionMessageList extends Vue {
  @Prop({ required: true, type: Array }) onMessageList: OnMessage[]
  // Is used for highlighting messages from bidders.
  @Prop({ type: String }) sender: string
  @Watch('onMessageList')
  onMessageListChange () {
    this.$nextTick(this.scrollToBottom)
  }
  mounted () {
    this.scrollToBottom()
  }
  scrollToBottom () {
    this.$el.scrollTop = this.$el.scrollHeight
  }
}
</script>

<style lang="less">
@import (reference) '../../../assets/variables.less';

.AuctionMessageList {
  background-color: @au-background-very-light;
  white-space: pre-wrap;

  .ant-list-item {
    &:nth-child(2n) {
      background-color: #fcfcfc;
    }
    &.AuctionMessageList__item--not-of-sender {
      background-color: lighten(red, 40);
    }
    &.AuctionMessageList__item--not-of-sender + .AuctionMessageList__item--not-of-sender {
      border-top: none;
    }

    padding: 3px 3px;
  }

  .ant-list-item-meta-title {
    margin-bottom: 0;
    font-size: 12px;
    line-height: 16px;
  }

  .ant-list-item-meta-description {
    color: @au-text-color-secondary;
    font-size: 12px;
    line-height: 16px;
  }
}
</style>
