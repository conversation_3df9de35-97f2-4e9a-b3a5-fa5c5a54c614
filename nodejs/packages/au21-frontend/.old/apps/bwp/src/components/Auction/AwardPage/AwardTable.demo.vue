<template>
  <VbDemo>
    <VbCard>
      <AwardTable
        :width="594"
        :height="610"
        :onMrAwardRowList="onMrAwardRowList"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">


import AwardTable from './AwardTable.vue'

export default {
  components: {
    AwardTable,
  },
}
</script>

<template>
  <VbDemo>
    <VbCard>
      <AwardTable/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AwardTable from './AwardTable.vue'
import { createEmptyAwardRow } from '../__demo-helpers/OnMrAwardRow'
import { createMultipleByClosure } from '../../../helpers/array-helpers'

@Component({
  components: { AwardTable },
})
export default class AwardTableDemo extends Vue {
  onMrAwardRowList = createMultipleByClosure(createEmptyAwardRow, 30)
}
</script>

