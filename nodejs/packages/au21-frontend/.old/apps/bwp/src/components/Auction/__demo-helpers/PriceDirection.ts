import { PriceDirection } from '../../../_generated/bwp-enums'

const priceDirections: PriceDirection[] = [
  'UP',
  'DOWN',
]

const priceDirectionToOptionNameMap: { [R in PriceDirection]: string } = {
  'UP': 'Up',
  'DOWN': 'Down',
}

export const getPriceDirectionName = (priceDirection: PriceDirection) => priceDirectionToOptionNameMap[priceDirection]

export const getPriceDirectionOptions = () => priceDirections.map(
  priceDirection => ({
    value: priceDirection,
    name: getPriceDirectionName(priceDirection),
  }),
)
