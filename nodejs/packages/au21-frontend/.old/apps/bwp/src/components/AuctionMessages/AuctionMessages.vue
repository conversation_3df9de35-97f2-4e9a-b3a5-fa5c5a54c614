<template>
  <div class="AuctionMessages">
    <AuctionMessageList
      ref="auctionMessageList"
      :onMessageList="auction_messages"
    />

    <div style="margin: 4px 0px">Type message to auctioneer (message won't be seen by other traders)</div>

    <a-input v-model="message" @pressEnter="submit()"/>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { scrollToBottom } from '../../helpers/scroll-helpers'
import AuctionMessageList from './AuctionMessageList/AuctionMessageList.vue'
import { OnMessage } from '../../_generated/server_outputs'
import { bwp_createMessageAsTrader } from '../../services/bwp-connector/publisher'

@Component({
  components: { AuctionMessageList },
})
export default class AuctionMessages extends Vue {
  message: string = ''

  @Watch('$auStore.current_auction.messages')
  async on_messages () {
    await this.$nextTick() // Wait until item is drawn.
    this.scrollToBottom()
  }

  submit () {
    bwp_createMessageAsTrader(this.$auConnector, this.message)
    this.message = ''
  }

  scrollToBottom () {
    scrollToBottom((this.$refs.auctionMessageList as any).$el)
  }

  get auction_messages (): OnMessage[] {
    return this.$auStore.current_auction.messages
  }
}
</script>

<style lang="less">
@import "../../assets/variables.less";

.AuctionMessages {
  display: flex;
  justify-content: stretch;
  flex-direction: column;
  height: 100px;
  font-size: 12px;

  color: @au-text-color !important;
}
</style>
