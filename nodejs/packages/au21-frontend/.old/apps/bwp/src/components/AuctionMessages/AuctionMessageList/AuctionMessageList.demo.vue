<template>
  <VbDemo>
    <VbCard>
      <AuctionMessageList
        style="height: 400px"
        :onMessageList="onMessageList"
        :sender="sender"
      />
    </VbCard>
    <VbCard>
      <a-checkbox
        @input="sender = $event ? '' : 'Auctioneer'"
        :value="sender === 'Auctioneer'"
      >
        Sender Auctioneer ({{sender}})
      </a-checkbox>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuctionMessageList from './AuctionMessageList.vue'
import { createMultipleByClosure } from '../../../helpers/array-helpers'
import {
  createOnMessage,
  createBidderOnMessage,
} from '../../Auction/__demo-helpers/OnMessage'

export default {
  components: {
    AuctionMessageList,
  },
  data () {
    return {
      sender: 'Auctioneer',
      onMessageList: [
        ...createMultipleByClosure(createOnMessage, 15),
        ...createMultipleByClosure(createBidderOnMessage,5),
      ],
    }
  },
}
</script>
