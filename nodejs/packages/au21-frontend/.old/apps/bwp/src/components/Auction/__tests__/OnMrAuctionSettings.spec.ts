import { OnMrAuctionSettings } from '../../../_generated/server_outputs'
import {
  createEmptyOnMrAuctionSettings,
  getStartTimeAsMoment,
  setStartTimeFromMoment,
} from '../__demo-helpers/OnMrAuctionSettings'

describe('OnMrAuctionSettings', () => {
  it('set and get START_TIME from moment', () => {
    const onMrAuctionSettings = createEmptyOnMrAuctionSettings()
    onMrAuctionSettings.START_TIME = '02/28/2019 - 23:38:00'
    const startTimeAsMoment = getStartTimeAsMoment(onMrAuctionSettings)
    setStartTimeFromMoment(onMrAuctionSettings, startTimeAsMoment)
    expect(onMrAuctionSettings.START_TIME).toBe('02/28/2019 - 23:38:00')
  })
})
