<template>
  <div class="AwardPage">
    <div class="form-block">
      <div style="display: flex; margin-bottom: 4px">
        <div style="font-size: 18px; text-align: center; flex: 1; padding-top: 2px">
          Award auction page
        </div>
        <a-button
          style="flex: 0 0 50px;"
           @click="back()"
        >
          Back
        </a-button>
      </div>
      <div style="display: flex; align-items: center;">
        <div class="text-bold AwardPage__header-label">Auction:</div>
        <div class="pseudo-input" style="height: 30px">{{$auStore.current_auction.status.AUCTION_NAME}}</div>
      </div>
    </div>
    <div class="form-block">
      <div style="display: flex">
        <div style="text-align: center; flex: 1 1 40%" class="flex-center">
        </div>
        <div style="text-align: right; flex: 1 1 60%; margin-bottom: 4px; display: flex; justify-content: flex-end; align-items: center;">
          Target volume:
          <PriceInput
            style="width: 120px"
            v-model="target_volume"
          />
          <a-button class="ml-1" size="small" @click="calculateAllocation()">Calculate allocation</a-button>
          <a-popconfirm
            title="Award this Auction?"
            placement="bottomRight"
            @confirm="awardAuction()"
            okText="Yes"
            cancelText="No"
          >
            <a-button class="ml-1" size="small">Award auction!</a-button>
          </a-popconfirm>
        </div>
      </div>

      <AwardTable
        :width="width"
        :height="tableHeight"
        :onMrAwardRowList="onMrAwardRowList"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import PageLayout from '../../PageLayout/PageLayout.vue'
import AwardTable from './AwardTable.vue'
  import {
    bwp_awardAuction,
    bwp_calculateStandardAward,
    bwp_navigateToAuctionFromAward,
  } from '../../../services/bwp-connector/publisher'
import { MAX_HEIGHT } from '../../../helpers/height_helper'
import {DebounceLoader} from 'asva-executors'
import { OnMrAwardRow } from '../../../_generated/server_outputs'
import PriceInput from '../../../ui-components/PriceInput/PriceInput.vue'

  @Component({
  components: { PriceInput, AwardTable, PageLayout },
})
export default class AwardPage extends Vue {
  target_volume = ''

  // Prevents table from being redrawn on each update (backend might send hundreds of these depending on list size).
  onMrAwardListDebounced: OnMrAwardRow[] = []
  onMrAwardListDebounceLoader = new DebounceLoader(async () => {
    this.refreshDebounceLoaderRow()
  }, 100)
  @Watch('awardRows', {deep: true, immediate: true})
  onBlotterBidderRowsChange () {
    this.onMrAwardListDebounceLoader.run()
  }
  refreshDebounceLoaderRow () {
    this.onMrAwardListDebounced = [...this.awardRows]
  }
  get awardRows () {
    return this.$auStore.current_auction.award_rows
  }

  get hideNonBiddersProxy () {
    return this.$auLocalStore.config.hide_non_bidders
  }
  set hideNonBiddersProxy (value: boolean) {
    this.$auLocalStore.config.hide_non_bidders = value
  }

  get width () {
    return this.$auLocalStore.config.width_inner + 2
  }
  get tableHeight () {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return MAX_HEIGHT - 81
    }

    return this.$auLocalStore.config.height_inner - 71
  }
  get onMrAwardRowList () {
    let awardRows = this.onMrAwardListDebounced

    if (this.hideNonBiddersProxy) {
      // blotter_bidder_rows doesn't cleanly belong to this page, we take it from auctioneer page.
      const withBidsBidderIds = this.$auStore.current_auction.blotter_bidder_rows
        .filter(onMrBidderRow => onMrBidderRow.HAS_SEEN_AUCTION)
        .map(bidder => bidder.OID)

      awardRows = awardRows.filter((awardRow) => {
        if (!Number.isInteger(awardRow.OID)) {
          // Header row
          return true
        }
        return withBidsBidderIds.includes(awardRow.OID)
      })
    }

    return awardRows
  }
  awardAuction () {
    const onMrAwardRowList = this.onMrAwardRowList
      .filter(item => Number.isInteger(item.OID))
      .map(item => ({USERID: item.OID, award: item.AWARD}))

    bwp_awardAuction(this.$auConnector, this.award_price, onMrAwardRowList)
  }
  get award_price (): string {
    // Some award rows have headers data...
    return this.onMrAwardRowList
      .filter(item => !Number.isInteger(item.OID))
      .find(item => item.OID + '' === 'ROUND_PRICE')
      .AWARD
  }
  back() {
    bwp_navigateToAuctionFromAward(this.$auConnector)
  }
  calculateAllocation () {
    bwp_calculateStandardAward(this.$auConnector, this.target_volume)

    this.target_volume = ''
  }
}
</script>

<style lang="less">
@import (reference) '../../../assets/variables.less';

.AwardPage {

  &__header-label {
    font-size: 16px;
    flex: 0 0 60px;
    text-align: right;
    margin-right: 6px;
  }
}
</style>
