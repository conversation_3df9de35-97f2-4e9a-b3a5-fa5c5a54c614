import uuid from 'uuid'
import _ from 'lodash'
import {OnMrBidderBlotterRow} from '../../../_generated/server_outputs'


let count = 0
export const createOnMrBidderBlotterRow = () => {
    const eligibilityNumber = _.random(50000, 100000)
    return {
        _COUNT: 0,
        AUCTIONID: 0,
        Col1: 'Col 1?',
        COMPANY: 'Company',
        ELIGIBILITY: eligibilityNumber + '',
        ELIGIBILITY_NUM: eligibilityNumber,
        EVENT: 'OnMrBidderBlotterRow',
        OID: parseInt(uuid()),
        SORT_INDEX: ++count,
        USERNAME: 't1',

        IS_ONLINE: true,
        HAS_SEEN_AUCTION: true,

        Round_0: _.random(20, 22) + '',
        Round_1: _.random(19, 20) + '',
        Round_2: _.random(18, 19) + '',
        Round_3: _.random(16, 18) + '',
        Round_4: _.random(12, 16) + '',
        Round_5: _.random(10, 12) + '',
        Round_6: '',
        Round_7: '',
        Round_8: '',
        Round_9: '',
        Round_10: '',
        Round_11: '',
        Round_12: '',
        Round_13: '',
        Round_14: '',
        Round_15: '',
        Round_16: '',
        Round_17: '',
        Round_18: '',
        Round_19: '',
        Round_20: '',
        Round_21: '',
        Round_22: '',
        Round_23: '',
        Round_24: '',
        Round_25: '',
        Round_26: '',
        Round_27: '',
        Round_28: '',
        Round_29: '',
        Round_30: '',
        Round_31: '',
        Round_32: '',
        Round_33: '',
        Round_34: '',
        Round_35: '',
        Round_36: '',
        Round_37: '',
        Round_38: '',
        Round_39: '',
        Round_40: '',
        Round_41: '',
        Round_42: '',
        Round_43: '',
        Round_44: '',
        Round_45: '',
        Round_46: '',
        Round_47: '',
        Round_48: '',
        Round_49: '',
        Round_50: '',
    } as OnMrBidderBlotterRow
}


export const createEmptyOnMrBidderBlotterRow = () => ({
    _COUNT: 0,
    AUCTIONID: 0,
    Col1: '',

    COMPANY: '',
    ELIGIBILITY: '',
    ELIGIBILITY_NUM: 0,
    EVENT: 'OnMrBidderBlotterRow',
    OID: parseInt(uuid()),
    SORT_INDEX: ++count,
    USERNAME: '',

    IS_ONLINE: true,
    HAS_SEEN_AUCTION: true,

    Round_1: '',
    Round_2: '',
    Round_3: '',
    Round_4: '',
    Round_5: '',
    Round_6: '',
    Round_7: '',
    Round_8: '',
    Round_9: '',
    Round_10: '',
    Round_11: '',
    Round_12: '',
    Round_13: '',
    Round_14: '',
    Round_15: '',
    Round_16: '',
    Round_17: '',
    Round_18: '',
    Round_19: '',
    Round_20: '',
    Round_21: '',
    Round_22: '',
    Round_23: '',
    Round_24: '',
    Round_25: '',
    Round_26: '',
    Round_27: '',
    Round_28: '',
    Round_29: '',
    Round_30: '',
    Round_31: '',
    Round_32: '',
    Round_33: '',
    Round_34: '',
    Round_35: '',
    Round_36: '',
    Round_37: '',
    Round_38: '',
    Round_39: '',
    Round_40: '',
    Round_41: '',
    Round_42: '',
    Round_43: '',
    Round_44: '',
    Round_45: '',
    Round_46: '',
    Round_47: '',
    Round_48: '',
    Round_49: '',
    Round_50: '',
} as OnMrBidderBlotterRow)

// export function hasBids(onMrBidderBlotterRow: OnMrBidderBlotterRow): boolean {
//     // Try first round as that's the most common case.
//     if (onMrBidderBlotterRow.Round_1 && onMrBidderBlotterRow.Round_1 !== '-') {
//         return true
//     }
//
//     // Then go for full search.
//     for (const key in onMrBidderBlotterRow) {
//         if (key.includes('Round_') && onMrBidderBlotterRow[key] && onMrBidderBlotterRow[key] !== '-') {
//             return true
//         }
//     }
//     return false
// }
