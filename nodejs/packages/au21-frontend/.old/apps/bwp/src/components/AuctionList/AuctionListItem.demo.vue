<template>
  <VbDemo>
    <VbCard>
      <AuctionListItem
        style="width: 200px"
        :onAuctionRow="onAuctionRow"
        @select="log.info('select')"
        @hide="log.info('hide'), onAuctionRow.IS_HIDDEN = true"
        @unhide="log.info('unhide'), onAuctionRow.IS_HIDDEN = false"
        @remove="log.info('remove')"
      />
    </VbCard>
    <VbCard>
      <a-checkbox
        v-model="onAuctionRow.HAS_DATE_TIME"
      >
        Has date time: {{onAuctionRow.HAS_DATE_TIME}}
      </a-checkbox>
      <a-checkbox
        v-model="onAuctionRow.IS_CLOSED"
      >
        Is closed: {{onAuctionRow.IS_CLOSED}}
      </a-checkbox>
    </VbCard>
    <VbCard style="position: fixed; top: 0; right: 0; background: #558c55">
      <UserRoleSelect v-model="$auStore.remote_user.role"/>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuctionListItem from './AuctionListItem.vue'
import { LogMixin } from '../Login/logMixin'
import { Component, Vue } from 'vue-property-decorator'
import { createOnAuctionRow } from '../Auction/__demo-helpers/OnAuctionRow'
import UserRoleSelect from '../User/UserRoleSelect/UserRoleSelect.vue'

@Component({
  mixins: [LogMixin],
  components: {
    UserRoleSelect,
    AuctionListItem,
  },
  watch: {
    'onAuctionRow.HAS_DATE_TIME' (value) {
      // TODO Checkbox gives event on v-model. Which is broken.
      console.log('value', value)
    },
  },
})
export default class AuctionListItemDemo extends Vue {
  onAuctionRow = createOnAuctionRow()
}
</script>
