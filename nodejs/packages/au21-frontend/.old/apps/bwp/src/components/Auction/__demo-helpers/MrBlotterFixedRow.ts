import { Mr<PERSON>lotterFixedRow } from '../../../store/model'

export const createBlotterHeaderRows = (): {[key: string]: MrBlotterFixedRow} =>  {
  const blotterHeaderRows = {
    ROUNDPRICE: new MrBlotterFixedRow(),
    TOTALVOL: new MrBlotterFixedRow(),
    ACTIVITY: new MrBlotterFixedRow(),
    BIDDERS: new MrBlotterFixedRow(),
  }
  blotterHeaderRows.ROUNDPRICE.label = 'ROUNDPRICE'
  blotterHeaderRows.TOTALVOL.label = 'TOTALVOL'
  blotterHeaderRows.ACTIVITY.label = 'ACTIVITY'
  blotterHeaderRows.BIDDERS.label = 'Bidders (company)'
  return blotterHeaderRows
}
