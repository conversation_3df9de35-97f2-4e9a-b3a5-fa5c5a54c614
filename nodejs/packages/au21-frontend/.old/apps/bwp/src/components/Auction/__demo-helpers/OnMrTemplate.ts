import uuid from 'uuid'
import { OnMrTemplate } from '../../../_generated/server_outputs'
import {
  MRStopMode,
  MRVisibility,
  Operator,
  PriceDirection,
} from '../../../_generated/bwp-enums'

export const createOnMrTemplate = () => ({
  _COUNT: 0,
  EVENT: 'OnMrTemplate',
  ACTION: 'Action',
  AUCTION_NAME: 'Sun Feb 3 22:20:31 GMT+0300 2019',
  AUCTIONID: 'Auction id',
  CONTROLLER: 'Controller',
  DELETED: false,
  DESCRIPTION: 'Thu Jan 31 02:50:26 CST 2019',
  DIRECTION: 'UP' as PriceDirection,
  FOLLOWING_ROUND_DURATION: '60',
  HIGH_CHANGE: '0.0030',
  HIGH_LABEL: 'H',
  HIGH_LIMIT: '200,000',
  HIGH_OPERATOR: 'GT' as Operator,
  INITIAL_ELIGIBILITY: '100,000',
  INITIAL_ROUND_DURATION: 60,
  LOW_CHANGE: '0.0010',
  LOW_LABEL: 'L',
  MED_CHANGE: '0.0020',
  MED_LABEL: 'M',
  MED_LIMIT: '150,000',
  MED_OPERATOR: 'GT' as Operator,
  MIN_VOL: '1',
  PRICE_DECIMALS: '4',
  PRICE_LABEL: '$/Dth',
  START_TIME: '2019/02/13 22:38:53',
  STOP_MODE: 'ZERO' as MRStopMode,
  STOP_VOLUME: '0',
  TEMPLATEID: parseInt(uuid().slice(-5)),
  VISIBILITY: 'ALL' as MRVisibility,
  VOLUME_DECREMENT: '1',
  VOLUME_LABEL: 'Dth/day',
} as OnMrTemplate)
