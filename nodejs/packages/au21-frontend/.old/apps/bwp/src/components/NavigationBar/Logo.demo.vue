<template>
  <VbDemo>
    <VbCard title="too wide" dark>
      <Logo :src="getImage(200, 100)" :width="200" :height="200" />
    </VbCard>
    <VbCard title="too high" dark>
      <Logo :src="getImage(100, 200)" :width="200" :height="200" />
    </VbCard>
    <VbCard title="small" dark>
      <Logo :src="getImage(100, 100)" :width="200" :height="200" />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Logo from './Logo.vue'
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {
    Logo,
  },
})
export default class LogoDemo extends Vue {
  getImage(width, height) {
    return `https://picsum.photos/id/237/${width}/${height}`
  }
}
</script>
