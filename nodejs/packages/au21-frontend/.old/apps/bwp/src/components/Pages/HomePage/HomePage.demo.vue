<template>
  <div>
    <HomePage
      :style="{width: $auLocalStore.config.width_outer_px, height: $auLocalStore.config.height_outer + 'px'}"
      v-if="show"
    />
    <VbCard style="position: fixed; top: 0; right: 0; background: #558c55">
      <UserRoleSelect v-model="$auStore.remote_user.role"/>
    </VbCard>
  </div>
</template>

<script lang="ts">
import HomePage from './HomePage.vue'
import { Component, Vue } from 'vue-property-decorator'
import { sleep } from '../../../services/utils'
import UserRoleSelect from '../../User/UserRoleSelect/UserRoleSelect.vue'

@Component({
  components: {
    UserRoleSelect,
    HomePage,
  },
})
export default class HeightHelperDemo extends Vue {
  show = false

  async created () {
    await sleep(1000)
    this.show = true
  }
}
</script>
