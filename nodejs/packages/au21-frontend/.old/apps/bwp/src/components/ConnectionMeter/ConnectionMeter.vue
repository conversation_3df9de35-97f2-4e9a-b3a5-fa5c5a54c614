<template>
  <svg class="ConnectionMeter" width="40" height="26">
    <rect x="2"
          y="14"
          rx="1"
          ry="1"
          width="6"
          height="12"
          :style="barStyle(1)"
    />
    <rect x="10"
          y="11"
          rx="1"
          ry="1"
          width="6"
          height="15"
          :style="barStyle(2)"
    />
    <rect x="18"
          y="8"
          rx="1"
          ry="1"
          width="6"
          height="18"
          :style="barStyle(3)"
    />
    <rect x="26"
          y="5"
          rx="1"
          ry="1"
          width="6"
          height="21"
          :style="barStyle(4)"
    />
  </svg>
</template>

<script lang="ts">
import Vue from 'vue'
import {Component, Prop, Watch} from 'vue-property-decorator'

@Component
export default class ConnectionMeter extends Vue {
  @Prop({ type: Number, required: true }) lastPingLatency: number
  
  barCount = 4;
  
  @Watch("$auStore.last_message_interval_secs")
  on_interval (interval, old) {
    if (interval < 1) {
      this.barCount = 4
      return
    }
    if (interval < 5) {
      this.barCount = 3
      return
    }
    if (interval < 10) {
      this.barCount = 2
      return
    }
    this.barCount = 1
  }

  barStyle (barNumber: number) {
    const fillsForBarCounts = [
      ['red', 'orange', 'green', 'green'],
      ['grey', 'orange', 'green', 'green'],
      ['grey', 'grey', 'green', 'green'],
      ['grey', 'grey', 'grey', 'green'],
    ]

    return {
      stroke: '#333',
      'stroke-width': '1',
      opacity: '1.0',
      fill: fillsForBarCounts[barNumber - 1][this.barCount - 1],
    }
  }

  /*
  // This was the actionscript code:
      var bars:int

      if (e.latency < 1000) bars = 4
      else if (e.latency < 5000) bars = 3
      else if (e.latency < 10000) bars = 2
      else bars = 1

      showBar2 = false
      showBar3 = false
      showBar4 = false

      switch (bars) {
        case 4:
          showBar4 = true
          showBar3 = true
          showBar2 = true
          barColor = 0x00ff00;
          break;
        case 3:
          showBar4 = false
          showBar3 = true
          showBar2 = true
          barColor = 0x00ff00
          break;
        case 2:
          showBar4 = false
          showBar3 = false
          showBar2 = true
          barColor = 0xff8000;
          break;
        case 1:
          barColor = 0xff0000;
      }

    });
  
   */

}
</script>

<style lang="less">

</style>
