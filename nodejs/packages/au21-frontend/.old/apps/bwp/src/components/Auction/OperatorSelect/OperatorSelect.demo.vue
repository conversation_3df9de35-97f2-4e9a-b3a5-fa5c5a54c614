<template>
  <VbDemo>
    <VbCard class="au-page">
      <OperatorSelect v-model="value"/>
    </VbCard>
    <VbCard>
      {{ value }}
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import OperatorSelect from './OperatorSelect.vue'
import { Operator } from '../../../_generated/bwp-enums'

export default {
  components: {
    OperatorSelect,
  },
  data () {
    return {
      value: 'GE' as Operator,
    }
  },
}
</script>
