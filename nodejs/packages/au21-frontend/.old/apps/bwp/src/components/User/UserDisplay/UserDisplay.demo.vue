<template>
  <VbDemo>
    <VbCard style="color: black">
      <UserDisplay style="width: 300px" :onUserRow="onUserRow"/>
    </VbCard>
  </VbDemo>
</template>

<script>
import UserDisplay from './UserDisplay.vue'
import { createOnUserRow } from './OnUserRowFactory'

export default {
  components: {
    UserDisplay,
  },
  data () {
    return {
      onUserRow: createOnUserRow(),
    }
  },
}
</script>
