<template>
  <div class="BidderAuctionSettings form-block">
    <table style="width: 100%; table-layout: fixed">
      <tr>
        <td style="width: 120px">Auction name:</td>
        <td>
          <div class="pseudo-input">
            {{onMrTraderSettings.DESCRIPTION}}
          </div>
        </td>
      </tr>
      <tr>
        <td>Round duration:</td>
        <td>
          <div class="pseudo-input">
            {{onMrTraderSettings.ROUND_DURATION}}
          </div>
        </td>
      </tr>
      <tr>
        <td>{{ mrClockLabel }} direction:</td>
        <td>
          <div class="pseudo-input">
            {{onMrTraderSettings.PRICE_DIRECTION}}
          </div>
        </td>
      </tr>
      <tr>
        <td>Eligibility:</td>
        <td>
          <div class="pseudo-input">
            {{onMrTraderSettings.ELIGIBILITY}}
          </div>
        </td>
      </tr>
      <tr>
        <td>Visibility:</td>
        <td style="padding-bottom: 6px">
          <div class="pseudo-input">
            {{onMrTraderSettings.VISIBILITY}}
          </div>
        </td>
      </tr>
      <tr>
        <td>Activity table:</td>
        <td>
          <table class="BidderAuctionSettings__limit-table">
            <tr>
              <th>Total round activity</th>
              <th>Label</th>
              <th>Next round {{ mrClockLabel }}</th>
            </tr>

            <tr>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.HIGH_LIMIT}}</div>
              </td>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.HIGH_LABEL}}</div>
              </td>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.HIGH_CHANGE}}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.MED_LIMIT}}</div>
              </td>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.MED_LABEL}}</div>
              </td>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.MED_CHANGE}}</div>
              </td>
            </tr>
            <tr>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.LOW_LIMIT}}</div>
              </td>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.LOW_LABEL}}</div>
              </td>
              <td>
                <div class="pseudo-input">{{onMrTraderSettings.LOW_CHANGE}}</div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnMrTraderSettings } from '../../_generated/server_outputs'
import {getMrClockLabelName} from "../Auction/__demo-helpers/MrClockLabel";
import {MRClockLabel} from "../../_generated/bwp-enums";

@Component({})
export default class BidderAuctionSettings extends Vue {
  @Prop({ required: true }) onMrTraderSettings: OnMrTraderSettings

  get mrClockLabel() {
    return getMrClockLabelName(this.onMrTraderSettings.CLOCK_LABEL || MRClockLabel.PRICE)
  }
}
</script>

<style lang="less">
@import '../../assets/variables.less';

.BidderAuctionSettings {
  color: @au-text-color;

  > table > tr > td:first-child {
    padding-top: 2px;
  }

  && {
    padding: 16px;
  }

  th:first-child {
    font-weight: 700;
  }

  td {
    vertical-align: top;
  }

  &__limit-table {
    width: 100%;

    tr {
      text-align: center;
    }

    td > .pseudo-input {
      justify-content: center;
    }
  }
}
</style>
