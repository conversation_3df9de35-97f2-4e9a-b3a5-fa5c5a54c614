<template>
  <VbDemo>
    <VbCard>
      <SystemMessageTable
        :onSystemMessageList="onSystemMessageList"
        :height="400"
        :width="600"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import SystemMessageTable from './SystemMessageTable.vue'
import { createMultipleByClosure } from '@au21-frontend/utils'
import { createOnSystemMessage } from '../../__demo-helpers/OnSystemMessage'

export default {
  components: {
    SystemMessageTable,
  },
  data () {
    return {
      onSystemMessageList: createMultipleByClosure(createOnSystemMessage, 20),
    }
  },
}
</script>
