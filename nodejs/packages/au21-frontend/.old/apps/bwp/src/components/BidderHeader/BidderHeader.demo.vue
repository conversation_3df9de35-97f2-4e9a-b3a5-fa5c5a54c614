<template>
  <VbDemo>
    <VbCard>
      <BidderHeader
        :onMrBidderStatus="onMrBidderStatus"
        @volumeSubmit="log.info('volumeSubmit', $event)"
        @openSettings="log.info('openSettings')"
        @openNotice="log.info('openNotice')"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import BidderHeader from './BidderHeader.vue'
import { LogMixin } from '../Login/logMixin'
import { Component, Vue } from 'vue-property-decorator'
import { createOnMrBidderStatus } from '../Auction/__demo-helpers/OnMrBidderStatus'

@Component({
  mixins: [LogMixin],
  components: {
    BidderHeader,
  },
})
export default class BidderHeaderDemo extends Vue {
  onMrBidderStatus = createOnMrBidderStatus()
}
</script>
