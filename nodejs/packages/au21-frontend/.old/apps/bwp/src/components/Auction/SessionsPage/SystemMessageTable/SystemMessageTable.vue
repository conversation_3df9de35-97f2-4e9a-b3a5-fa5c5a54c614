<template>
  <AuScrollableTableStepped
    class="SystemMessageTable"
    :table_width="width"
    :table_height="height"
    :header_height="24"
    :cell_height="24"
    :visible_columns="0"
    :fixed_columns="columns"
    :columns="[]"
    :header_rows="['']"
    :rows="onSystemMessageList"
    :columnWidthSetter="column => column.width"
    whiteFirstColumn
  >
    <div
      slot="fixed_header_cell"
      slot-scope="{column, row}"
      class="table-cell-wrapper"
      style="text-align: left; padding: 2px 6px; background-color: #666666; color: white; font-weight: 700;"
    >
      {{ column.title }}
    </div>

    <div
      slot="fixed_body_cell"
      slot-scope="{column, row}"
      class="table-cell-wrapper"
      style="text-align: left; padding: 2px"
      :title="row[column.dataIndex]"
    >
      {{ row[column.dataIndex] }}
    </div>
  </AuScrollableTableStepped>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnSystemMessage } from '../../../../_generated/server_outputs'
import AuScrollableTableStepped
  from '../../../../ui-components/TableScroller/AuScrollableTableStepped.vue'

@Component({
  components: { AuScrollableTableStepped },
})
export default class SystemMessageTable extends Vue {
  @Prop({ type: Number }) height!: number
  @Prop({ type: Number }) width!: number
  @Prop({ type: Array, required: true }) onSystemMessageList: OnSystemMessage[]

  columns = [
    {
      title: 'Id',
      dataIndex: 'MESSAGEID',
      width: this.width * .05,
    },
    {
      title: 'Timestamp',
      dataIndex: 'TIMESTAMP',
      width: this.width * .16,
    },
    {
      title: 'Message',
      dataIndex: 'TEXT',
      width: this.width * .77,
    },
  ]
}
</script>

<style lang="less">
.SystemMessageTable {

}
</style>
