<template>
  <div class="LoginFormWrapper">

  </div>
</template>

<script>
export default {
  name: 'LoginFormWrapper',
}
</script>

<style lang="less">
.LoginFormWrapper {

}
</style>

<template>
  <div class="LoginFormWrapper">

  </div>
</template>


<script>
import { Component, Vue } from 'vue-property-decorator'

@Component({})
export class LoginFormWrapper extends Vue {

}
</script>

<style lang="less">
.LoginFormWrapper {

}
</style>

