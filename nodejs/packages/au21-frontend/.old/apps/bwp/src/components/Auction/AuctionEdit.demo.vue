<template>
  <VbDemo>
    <VbCard class="au-page">
      <AuctionEdit
        style="width: 900px"
        :onMrAuctionSettings="onMrAuctionSettings"
        :disabled="disabled"
      />
    </VbCard>
    <VbCard>
      <pre>{{onMrAuctionSettings}}</pre>
    </VbCard>
  </VbDemo>
</template>

<script>
import AuctionEdit from './AuctionEdit.vue'
import { createOnMrAutionSettingsForEdit } from './__demo-helpers/OnMrAuctionSettings'

export default {
  components: {
    AuctionEdit,
  },
  data () {
    return {
      onMrAuctionSettings: createOnMrAutionSettingsForEdit(),
      disabled: true,
    }
  },
}
</script>
