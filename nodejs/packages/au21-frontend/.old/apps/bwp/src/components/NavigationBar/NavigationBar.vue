<template>
  <a-row class="NavigationBar" type="flex" justify="space-between" align="middle">
    <a-col :span="5">
      <LogoFramed/>
    </a-col>

    <a-col :span="7" class="text-center">
      <template v-if="$auStore.date_time">
        {{ $auStore.date_time[0] }}
        <span style="font-size: x-large">{{ $auStore.date_time[1] }}</span> {{ $auStore.date_time[2] }} Houston
        <br>
        <div style="font-size: 12px">session id: {{this.$auStore.session_id}}</div>
      </template>
    </a-col>

    <a-col :span="10" push style="text-align: right">
      <template  v-if="remote_user.role">
        <a-button-group>
          <a class="text-link" @click="initHomePage()">Home</a>
          &nbsp; | &nbsp;
          <template v-if="isAuctioneerOrAdmin">
            <a class="text-link" @click="initUserPage()">Users</a>
            &nbsp; | &nbsp;
            <a class="text-link" @click="initSessionPage()">Sessions</a>
            &nbsp; | &nbsp;
          </template>
          <a class="text-link" @click="signOut()">Sign out</a>
        </a-button-group>
        <p></p>
        Connection:
        <ConnectionMeter
          style="margin-bottom: -1px; margin-top: -10px"
          :lastPingLatency="lastPingLatency"
        />
        <span style="margin-left: 14px">Signed in as {{ roleName }}: {{ remote_user.username }}</span>
      </template>
    </a-col>
  </a-row>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import ConnectionMeter from '../ConnectionMeter/ConnectionMeter.vue'
import { getRoleName, isAuctioneerOrAdmin } from '../User/UserRoleSelect/Role'
import { Role } from '../../_generated/bwp-enums'
import {
  bwp_signoff,
  bwp_initUserPage,
  bwp_initSessionPage,
  bwp_initHomePage,
} from '../../services/bwp-connector/publisher'
import LogoFramed from './LogoFramed.vue'
// import format from 'date-fns/format'

@Component({
  components: { LogoFramed, ConnectionMeter },
})
export default class NavigationBar extends Vue {
  @Prop({ required: true }) remote_user: { username: string, role: Role }
  
  lastPingLatency = 1000

  initHomePage () {
    bwp_initHomePage(this.$auConnector)
  }

  initUserPage () {
    bwp_initUserPage(this.$auConnector)
  }

  initSessionPage () {
    bwp_initSessionPage(this.$auConnector)
  }

  signOut () {
    bwp_signoff(this.$auConnector)
  }

  get isAuctioneerOrAdmin () {
    return isAuctioneerOrAdmin(this.remote_user.role)
  }

  get roleName () {
    return getRoleName(this.remote_user.role)
  }
}
</script>

<style lang="less">
@import '../../assets/variables.less';

.NavigationBar {
  color: @au-text-color;
  padding: 4px 0;
  font-size: 14px;
}
</style>
