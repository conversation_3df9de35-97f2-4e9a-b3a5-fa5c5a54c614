<template>
  <AuScrollableTableStepped
    class="AwardTable"
    :table_width="width"
    :table_height="height"
    :header_height="header_height"
    :cell_height="cell_height"
    :visible_columns="0"
    :fixed_columns="columns"
    :columns="[]"
    :header_rows="header_rows"
    :rows="award_rows"
    :columnWidthSetter="column => 213.2"
    header_table_class="AuctionRoundBlotter__table table--border-center border-left border-bottom"
    left_table_class="AuctionRoundBlotter__table table--border-center"
    body_table_class="AuctionRoundBlotter__table table--border-center border-left"
    whiteFirstColumn
  >
    <template
      slot="fixed_header_cell"
      slot-scope="{column, row, row_index}"
    >
      <div
        v-if="row_index === 0"
        class="table-cell-wrapper"
        style="text-align: left; padding: 2px 6px; background-color: #666666; color: white; font-weight: 700; cursor: pointer;"
        @click="onColumnSort(column.field)"
      >
        {{ row[column.field] }}
        <a-icon v-if="sortColumn === column.field" style="width: 12px; height: 12px" :type="sortBy === 'asc' ? 'down' : 'up'"/>
      </div>
      <div
        v-else
        class="table-cell-wrapper"
        style="text-align: right; padding: 2px 6px;"
      >
        {{ row[column.field] }}
      </div>
    </template>

    <template
      slot="fixed_body_cell"
      slot-scope="{column, row}"
    >
      <div
        v-if="column.field !== 'AWARD'"
        class="table-cell-wrapper"
        style="text-align: right; padding: 2px 6px"
      >
        <template>
          {{row[column.field] }}
        </template>
      </div>
      <template v-else-if>
        <a-input
          ref="awardInput"
          v-if="editedAward === row"
          v-model="row.AWARD"
          @pressEnter="editedAward = null"
          @blur="editedAward = null"
        />
        <div
          v-else
          @click="editAward(row)"
          class="table-cell-wrapper"
          style="text-align: right; padding: 2px 6px; cursor: pointer"
        >
          {{row[column.field] }}
        </div>
      </template>
    </template>
  </AuScrollableTableStepped>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import AuScrollableTableStepped
  from '../../../ui-components/TableScroller/AuScrollableTableStepped.vue'
import { OnMrAwardRow } from '../../../_generated/server_outputs'
import { sortWithDirection } from '../../../helpers/array-helpers'

type AwardTableColumn ='Col1' | 'PREVIOUS' | 'AWARD' | 'FINAL'

@Component({
  components: {
    AuScrollableTableStepped,
  },
})
export default class AwardTable extends Vue {
  @Prop({ required: true, type: Number }) height: number
  @Prop({ required: true, type: Number }) width: number
  @Prop({ required: true }) onMrAwardRowList: OnMrAwardRow[]

  sortBy: 'asc' | 'desc' = 'asc'
  sortColumn: AwardTableColumn = 'Col1'
  
  editedAward: OnMrAwardRow = null
  editedAwardValue = ''

  header_height = 24
  cell_height = 24

  get header_rows () {
    return [
      {
        Col1: '',
        PREVIOUS: 'Previous (adjusted)',
        FINAL: 'Final',
        AWARD: 'Award',
      },
      ...this.onMrAwardRowList.filter(item => !Number.isInteger(item.OID)),
    ]
  }

  get award_rows () {
    // onMrAwardRowList stores both headers and bidder rows,
    // but we don't need headers here.
    const onMrAwardRowList = this.onMrAwardRowList
      .filter(item => Number.isInteger(item.OID))

    return sortWithDirection(onMrAwardRowList, this.sortColumn, this.sortBy)
  }

  onColumnSort (column: AwardTableColumn) {
    if (this.sortColumn === column && this.sortBy === 'asc') {
      this.sortBy = 'desc'
      return
    }
    this.sortColumn = column
    this.sortBy = 'asc'
  }

  get columns (): any[] {
    return [
      {
        field: 'Col1',
      },
      {
        field: 'PREVIOUS',
      },
      {
        field: 'FINAL',
      },
      {
        field: 'AWARD',
      },
    ]
  }

  editAward (onMrAwardRow: OnMrAwardRow) {
    this.editedAwardValue = onMrAwardRow.AWARD
    this.editedAward = onMrAwardRow
    this.$nextTick(() => {
      const antInput = this.$refs.awardInput as any
      const input = antInput.$el as HTMLInputElement
      input.focus()
      input.setSelectionRange(0, input.value.length)
    })
  }
}
</script>

<style lang="less">
@import (reference) '../../../assets/variables.less';

.AwardTable {

}
</style>
