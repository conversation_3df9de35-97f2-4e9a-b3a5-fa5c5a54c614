<template>
  <BidderPage
    style="height: 100%"
  />
</template>

<script lang="ts">
import BidderPage from './BidderPage.vue'
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {
    BidderPage,
  },
})
export default class BidderPageDemo extends Vue {
  show = false

  async created () {
    // TODO Demo not finished.
    // We need to do first grab an auction and only then come to BidderPage
    // await sleep(1000)
    // bwp_initHomePage(this.$auConnector)
    // await sleep(1000)
    // await sleep(1000)
    // this.show = true
  }
}
</script>
