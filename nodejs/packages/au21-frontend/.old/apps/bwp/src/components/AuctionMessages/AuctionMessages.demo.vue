<template>
  <VbDemo>
    <VbCard class="au-page">
      <AuctionMessages style="height: 300px" ref="auctionMessages"/>
    </VbCard>
    <VbCard>

      <button
        @click="$refs.auctionMessages.scrollToBottom()"
      >
        Scroll to bottom row
      </button>
    </VbCard>
  </VbDemo>
</template>

<script>
import AuctionMessages from './AuctionMessages.vue'

export default {
  components: {
    AuctionMessages,
  },
}
</script>
