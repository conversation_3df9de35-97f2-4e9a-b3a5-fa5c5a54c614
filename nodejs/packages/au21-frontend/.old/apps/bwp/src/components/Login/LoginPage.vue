<template>
  <div class="LoginPage flex-center" style="width: 100%; height: 100%">
    <LoginForm style="width: 250px;"/>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import LoginForm from './LoginForm.vue'
import PageLayout from '../PageLayout/PageLayout.vue'

@Component({
  components: { PageLayout, LoginForm },
})
export default class LoginPage extends Vue {
  get footer () {
    // Jan 17, 2021: footer generation not working, hard coding for now:
    // return FOOTER_TEXT
    return "Copyright Auctionologies LLC, 2011-2021"
  }
}
</script>

<style lang="less">
.LoginPage {

}
</style>
