import { OnAuctionRow } from '../../../_generated/server_outputs'
import uuid from 'uuid'

export const createOnAuctionRow = () => ({
  _COUNT: 0,
  EVENT: 'OnAuctionRow',
  AMPM: '',
  AUCTION_NAME: 'Auction name',
  AUCTION_ROW_ID: parseInt(uuid().slice(-6)),
  AUCTION_TYPE: '',
  DAY_OF_MONTH: 28,
  DAY_OF_WEEK: 4,
  HAS_DATE_TIME: true,
  HOUR: '11',
  IS_CLOSED: false,
  IS_DELETED: false,
  IS_HIDDEN: false,
  IS_SOON: false,
  MINUTE: 15,
  MONTH: 3,
  TIMESTAMP: 1111, // used for sorting
}) as OnAuctionRow
