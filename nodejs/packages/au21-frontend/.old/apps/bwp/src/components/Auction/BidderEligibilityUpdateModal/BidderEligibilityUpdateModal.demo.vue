<template>
  <VbDemo>
    <VbCard>
      <a-button @click="show = true">Show</a-button>
      <BidderEligibilityUpdateModal
        v-if="show"
        @close="show = false"
        :onMrBidderBlotterRow="onMrBidderBlotterRow"
        @saveBidderEligibilityUpdate="log.info('saveBidderEligibilityUpdate', $event)"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import BidderEligibilityUpdateModal from './BidderEligibilityUpdateModal.vue'
import { createOnMrBidderBlotterRow } from '../__demo-helpers/OnMrBidderBlotterRow'
import { LogMixin } from '../../Login/logMixin'

export default {
  mixins: [LogMixin],
  components: {
    BidderEligibilityUpdateModal,
  },
  data () {
    return {
      onMrBidderBlotterRow: createOnMrBidderBlotterRow(),
      show: false,
    }
  },
}
</script>
