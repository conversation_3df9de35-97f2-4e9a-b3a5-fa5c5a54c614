import { OnMrAuctionStatus } from '../../../_generated/server_outputs'

let count = 0
export const createOnMrAuctionStatus = () => ({
  AUCTIONID: ++count,
  AUCTION_NAME: '1551603738494',
  AUCTION_STATUS: 'Starting price announced. Waiting for auction to start',
  PRICE_LABEL: '$/Dth',
  ROUND_CONTROLLER_STATUS: 'ROUND_PRICE_SET',
  ROUND_NUMBER: 1,
  ROUND_PRICE: '1.0000',
  VOLUME_LABEL: 'Dth/day',
} as OnMrAuctionStatus)
