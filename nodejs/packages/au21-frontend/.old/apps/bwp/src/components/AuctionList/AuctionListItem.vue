<template>
  <div class="AuctionListItem"
       @click="$emit('select')"
  >
    <div style="display:flex; justify-content: space-between;">
      <div class="AuctionListItem__date">{{ dateComputed }}</div>

      <div v-if="$auStore.is_auctioneer_or_admin">
        <template v-if="onAuctionRow.IS_CLOSED">
          <span
            class="AuctionListItem__hidden-message"
            v-if="onAuctionRow.IS_HIDDEN"
          >
          (hidden from traders)
          </span>
          &nbsp;
          <span
            class="text-link-inverted"
            @click.stop="$emit(onAuctionRow.IS_HIDDEN ? 'unhide' : 'hide')"
          >{{ onAuctionRow.IS_HIDDEN ? 'Unhide' : 'Hide' }}</span>
        </template>
        &nbsp;
        <a-popconfirm
          title="Delete this Auction?"
          placement="bottomRight"
          @confirm="$emit('remove')"
          okText="Yes"
          cancelText="No"
        >
          <span
            @click.stop.prevent
            class="text-link-inverted"
          >
            Delete
          </span>
        </a-popconfirm>
      </div>
    </div>

    <div>{{ onAuctionRow.AUCTION_NAME }}</div>
  </div>
</template>

<script lang="ts">
import { OnAuctionRow } from '../../_generated/server_outputs'
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component({})
export default class AuctionListItem extends Vue {
  @Prop({ required: true }) onAuctionRow: OnAuctionRow

  selectAuction () {
    this.$emit('selected')
  }

  get dateComputed () {
    const auction = this.onAuctionRow
    if (!auction.HAS_DATE_TIME) {
      return ''
    }
    const status = auction.IS_CLOSED ? 'Started' : 'Starting'
    const time = auction.HOUR ? auction.HOUR + ':' + (Number(auction.MINUTE) < 10 ? '0' : '') + auction.MINUTE + ' ' + auction.AMPM : ''

    return `${status}: ${time} ${this.dayOfWeek} ${this.month} ${auction.DAY_OF_MONTH}`
  }

  get dayOfWeek (): string {
    const daysOfWeek = {
      1: 'Monday',
      2: 'Tuesday',
      3: 'Wednesday',
      4: 'Thursday',
      5: 'Friday',
      6: 'Saturday',
      7: 'Sunday',
    }
    return daysOfWeek[this.onAuctionRow.DAY_OF_WEEK + '']
  }

  get month (): string {
    const months = {
      1: 'Jan',
      2: 'Feb',
      3: 'Mar',
      4: 'Apr',
      5: 'May',
      6: 'Jun',
      7: 'Jul',
      8: 'Aug',
      9: 'Sep',
      10: 'Oct',
      11: 'Nov',
      12: 'Dec',
    }
    return months[this.onAuctionRow.MONTH + '']
  }
}
</script>

<style lang="less">
@import (reference) '../../assets/variables.less';

.AuctionListItem {
  min-height: 50px;
  padding: @au-spacing-one;
  cursor: pointer;
  color: @au-text-color-secondary;
  border-bottom: 1px solid #d7d7d7;

  &:hover {
    background-color: #d7dff3;
  }

  &__date {
    font-weight: bold;
  }

  &__hidden-message {
    color: #c0c0c0;
  }
}
</style>
