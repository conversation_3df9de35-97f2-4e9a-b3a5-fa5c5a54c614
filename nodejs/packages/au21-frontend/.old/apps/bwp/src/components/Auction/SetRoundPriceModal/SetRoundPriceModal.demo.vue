<template>
  <VbDemo>
    <VbCard>
      <a-button @click="show = true">Show</a-button>
      <SetRoundPriceModal
        v-if="show"
        @close="show = false"
        :onMrAuctionStatus="onMrAuctionStatus"
        @saveSettingPrice="log.info('saveSettingPrice', $event)"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import SetRoundPriceModal from './SetRoundPriceModal.vue'
import { createOnMrAuctionStatus } from '../__demo-helpers/OnMrAuctionStatus'
import { LogMixin } from '../../Login/logMixin'

@Component({
  components: { SetRoundPriceModal },
  mixins: [LogMixin],
})
export default class SetRoundPriceModalDemo extends Vue {
  show = false
  onMrAuctionStatus = createOnMrAuctionStatus()
}
</script>
