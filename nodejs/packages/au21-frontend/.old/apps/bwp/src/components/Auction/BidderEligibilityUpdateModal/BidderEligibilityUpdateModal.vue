<template>
  <a-modal
    class="BidderEligibilityUpdateModal"
    title="Set Eligibility"
    visible
    @cancel="cancelUpdatingEligibility()"
    closable
    centered
    width="330px"
    :maskClosable="false"
  >
    <table>
      <tr>
        <td>Username:</td>
        <td>
          <div style="width: 170px" class="pseudo-input">{{onMrBidderBlotterRow.USERNAME}}&nbsp;</div>
        </td>
      </tr>
      <tr>
        <td>Company:</td>
        <td>
          <div style="width: 170px" class="pseudo-input">{{onMrBidderBlotterRow.COMPANY}}&nbsp;</div>
        </td>
      </tr>
      <tr>
        <td>Current eligibility:</td>
        <td>
          <div style="width: 170px" class="pseudo-input">{{onMrBidderBlotterRow.ELIGIBILITY}}&nbsp;</div>
        </td>
      </tr>
      <tr>
        <td>New eligibility:</td>
        <td>
          <PriceInput style="width: 170px" v-model="editedEligibility"/>
        </td>
      </tr>
    </table>

    <a-button
      slot="footer"
      @click="saveUpdatingEligibility()"
    >
      Save
    </a-button>

    <a-button
      slot="footer"
      type="dashed"
      @click="cancelUpdatingEligibility()"
    >
      Cancel
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnMrBidderBlotterRow } from '../../../_generated/server_outputs'
import PriceInput from '../../../ui-components/PriceInput/PriceInput.vue'

@Component({
  components: { PriceInput },
})
export default class BidderEligibilityUpdateModal extends Vue {
  @Prop({ required: true }) onMrBidderBlotterRow!: OnMrBidderBlotterRow
  editedEligibility = this.onMrBidderBlotterRow.ELIGIBILITY

  cancelUpdatingEligibility () {
    this.$emit('close')
  }

  saveUpdatingEligibility () {
    this.$emit('saveBidderEligibilityUpdate', this.editedEligibility)
    this.$emit('close')
  }
}
</script>

<style lang="less">
.BidderEligibilityUpdateModal {
  td {
    padding: 2px;
  }

  td + td {
    padding-left: 10px;
  }

  .ant-modal-body {
    text-align: right;
    padding: 16px;
  }
}
</style>
