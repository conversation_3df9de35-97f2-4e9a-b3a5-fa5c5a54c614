<template>
  <SessionPage
    :style="{width: $auLocalStore.config.width_outer_px}"
    v-if="show"
  />
</template>

<script lang="ts">
import SessionPage from './SessionPage.vue'
import { sleep } from '../../../services/utils'
import { bwp_initSessionPage } from '../../../services/bwp-connector/publisher'
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {
    SessionPage,
  },
})
export default class HeightHelperDemo extends Vue {
  show = false

  async created () {
    await sleep(1000)
    bwp_initSessionPage(this.$auConnector)
    await sleep(1000)
    await sleep(1000)
    this.show = true
  }
}
</script>
