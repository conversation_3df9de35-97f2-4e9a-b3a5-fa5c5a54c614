<template>
  <VbDemo>
    <VbCard>
      <AuctionList
        style="width: 400px; height: 400px"
        @selected="log.info('selected', $event)"
        title="Closed auctions"
        :onAuctionRows="onAuctionRows"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuctionList from './AuctionList.vue'
import { LogMixin } from '../Login/logMixin'
import { Component, Vue } from 'vue-property-decorator'
import { createMultipleByClosure } from '../../helpers/array-helpers'
import { createOnAuctionRow } from '../Auction/__demo-helpers/OnAuctionRow'

@Component({
  mixins: [LogMixin],
  components: {
    AuctionList,
  },
})
export default class AuctionListDemo extends Vue {
  onAuctionRows = createMultipleByClosure(createOnAuctionRow, 20)
}
</script>
