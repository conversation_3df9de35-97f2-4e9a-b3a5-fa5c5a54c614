<template>
  <AuScrollableTableStepped
    ref="AuScrollableTableStepped"
    class="OnMrBidRowList"
    :table_width="width"
    :table_height="height"
    :header_height="26"
    :cell_height="26"
    :visible_columns="0"
    :columns="[]"
    :fixed_columns="columns"
    :header_rows="['']"
    :rows="onMrBidRowList"
    :columnWidthSetter="column => column.width"
    whiteFirstColumn
  >
    <div
      slot="fixed_header_cell"
      slot-scope="{column, row}"
      class="table-cell-wrapper"
      style="text-align: left; padding: 2px 6px; background-color: #666666; color: white; font-weight: 700;"
    >
      {{ column.title }}
    </div>

    <div
      slot="fixed_body_cell"
      slot-scope="{column, row}"
      class="table-cell-wrapper"
      style="text-align: center; padding: 2px;"
    >
      {{ row[column.dataIndex] }}
    </div>
  </AuScrollableTableStepped>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import {OnMrBidRow, OnMrTraderSettings} from '../../_generated/server_outputs'
import AuScrollableTableStepped from '../../ui-components/TableScroller/AuScrollableTableStepped.vue'
import {MRClockLabel} from "../../_generated/bwp-enums";

@Component({
  components: { AuScrollableTableStepped },
})
export default class OnMrBidRowList extends Vue {
  @Prop({ type: Number }) height: number
  @Prop({ type: Number }) width: number
  @Prop({ required: true, type: Array }) onMrBidRowList: OnMrBidRow[]
  @Prop({ required: true }) onMrTraderSettings!: OnMrTraderSettings

  @Watch('onMrBidRowList')
  onMrBidRowListChange () {
    this.scrollToBottomRow()
  }

  columns = [
    {
      title: 'Round',
      dataIndex: 'ROUND_NUMBER' as keyof OnMrBidRow,
      width: 60,
    },
    {
      title: this.onMrTraderSettings.CLOCK_LABEL == MRClockLabel.TERM ? 'Term' : 'Price',
      dataIndex: 'ROUND_PRICE' as keyof OnMrBidRow,
      width: 121,
    },
    {
      title: 'Volume',
      dataIndex: 'BID_VOLUME' as keyof OnMrBidRow,
      width: 121,
    },
    {
      title: 'Total activity',
      dataIndex: 'ROUND_ACTIVITY' as keyof OnMrBidRow,
      width: 122,
    },
  ]

  scrollToBottomRow () {
    this.$nextTick(() => {
      (this.$refs.AuScrollableTableStepped as any).scrollBottom()
    })
  }
}
</script>

<style lang="less">
@import (reference) '../../assets/variables.less';

.OnMrBidRowList {
  background-color: @au-text-color;
  .ant-table {
    td {
      text-align: center;
    }
  }
}
</style>
