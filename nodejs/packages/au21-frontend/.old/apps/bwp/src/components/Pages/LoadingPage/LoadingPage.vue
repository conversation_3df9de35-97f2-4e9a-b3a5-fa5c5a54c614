<template>
  <PageLayout class="LoadingPage">
    <div
      style="min-height: 100%; background-color: #dcdcdc;"
      class="flex-center"
    >
      <div style="text-align: center;">
        <a-spin class="mb-2" size="large"/>
        <div style="color: #0e0e0e; font-weight: bold; font-size: 16px;">
          Loading...
        </div>
      </div>
    </div>
  </PageLayout>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import LogoFramed from '../../NavigationBar/LogoFramed.vue'
import PageLayout from '../../PageLayout/PageLayout.vue'
@Component({
  components: { PageLayout, LogoFramed },
})
export default class LoadingPage extends Vue {
  get footer () {
    // Jan 17, 2021: footer generation not working, hard coding for now:
    // return FOOTER_TEXT
    return "Copyright Auctionologies LLC, 2011-2021"  }
}
</script>

<style lang="less">
.LoadingPage {

}
</style>
