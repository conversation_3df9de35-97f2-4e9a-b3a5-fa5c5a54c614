import uuid from 'uuid'
import { OnSessionRow } from '../../../_generated/server_outputs'

export const createOnSessionRow = () => ({
  SESSIONID: uuid(),
  USERNAME: 'b1',
  PAGE: 'SessionPage dskf jdsl',
  SESSION_AUCTIONID: parseInt(uuid().slice(-4)),
  AUCTION_NAME: 'AUCTION_NAME  fkjsdl fakjdsa lfdksj dsa fds afsda fsda fdsa fsda fdsa fsad fdas fdsaf  fkjsdl fakjdsa lfdksj dsa fds afsda fsda fdsa fsda fdsa fsad fdas fdsaf ',
  OFFLINE_REASON: 'OFFLINE_REASON',
  USER_AGENT: 'USER_AGENT',
  REMOTE_ADDRESS: 'REMOTE_ADDRESS',
} as OnSessionRow)
