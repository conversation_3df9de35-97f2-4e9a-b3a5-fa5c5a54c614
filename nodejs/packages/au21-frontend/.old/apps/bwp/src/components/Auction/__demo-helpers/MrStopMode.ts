import {MR<PERSON><PERSON><PERSON>abe<PERSON>, MRStopMode} from '../../../_generated/bwp-enums'

const mrStopModes: MRStopMode[] = [
    MRStopMode.LT,
    MRStopMode.LE,
    MRStopMode.ZERO,
]

const mrStopModeToOptionNameMap: { [R in MRStopMode]: string } = {
    [MRStopMode.LT]: 'Less than:',
    [MRStopMode.LE]: 'Less than or equal to:',
    [MRStopMode.ZERO]: 'No automatic stop',
}

const mrStopModeToShortNameMap: { [R in MRStopMode]: string } = {
    [MRStopMode.LT]: '<',
    [MRStopMode.LE]: '≤',
    [MRStopMode.ZERO]: '',
}

export const getMrStopModeName = (mrStopMode: MRStopMode) => mrStopModeToOptionNameMap[mrStopMode]
export const getMrStopModeShortName = (mrStopMode: MRStopMode) => mrStopModeToShortNameMap[mrStopMode]

export const getMrStopModeOptions = () => mrStopModes.map(
    mrStopMode => ({value: mrStopMode, name: getMrStopModeName(mrStopMode)}),
)
