
import {MR<PERSON>lockLabel} from '../../../_generated/bwp-enums'

const mrClockLabels: MRClockLabel[] = [
    MRClockLabel.PRICE,
    MRClockLabel.TERM
]

const mrClockLabelToOptionNameMap: { [R in MRClockLabel]: string } = {
    [MRClockLabel.PRICE]: 'Price',
    [MRClockLabel.TERM]: 'Term'
}

export const getMrClockLabelName = (mrClockLabel: MRClockLabel):String => mrClockLabelToOptionNameMap[mrClockLabel]

export const getMrClockLabelOptions = () => mrClockLabels.map(
    mrClockLabel => ({value: mrClockLabel, name: getMrClockLabelName(mrClockLabel)}),
)
