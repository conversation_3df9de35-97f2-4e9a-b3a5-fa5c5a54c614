<template>
  <div class="HomePage form-block">
    <div style="display: flex; justify-content: space-between; margin-bottom: 6px">
      <div class="au-header">Auctions</div>
      <a-button
        @click="createNewAuction()"
        v-if="$auStore.is_auctioneer_or_admin"
      >
        New auction
      </a-button>
    </div>
    <div>
      <AuctionList
        style="margin-bottom: 6px"
        :height="listHeight"
        :onAuctionRows="openAuctionsDebounced"
        title="Open Auctions"
        @select="selectAuction"
        @remove="removeAuction"
      />
      <AuctionList
        :height="listHeight"
        :onAuctionRows="closedAuctionsDebounced"
        title="Closed Auctions"
        @select="selectAuction"
        @hide="hideAuction"
        @unhide="unhideAuction"
        @remove="removeAuction"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import PageLayout from '../../PageLayout/PageLayout.vue'
import AuctionList from '../../AuctionList/AuctionList.vue'
import { OnAuctionRow } from '../../../_generated/server_outputs'
import {
  bwp_deleteAuction,
  hide_auction,
  unhide_auction,
  bwp_selectAuction,
  bwp_gotoTemplatePage,
} from '../../../services/bwp-connector/publisher'
import { MAX_HEIGHT } from '../../../helpers/height_helper'
import {DebounceLoader} from 'asva-executors'

@Component({
  components: { AuctionList, PageLayout },
})
export default class HomePage extends Vue {

  // Prevents table from being redrawn on each update (backend might send hundreds of these depending on list size).
  closedAuctionsDebounced: OnAuctionRow[] = []
  openAuctionsDebounced: OnAuctionRow[] = []
  auctionListDebounceLoader = new DebounceLoader(async () => {
    this.refreshDebounceLoaderRow()
  }, 100)
  @Watch('auctionsComputed', {deep: true, immediate: true})
  onAuctionsChange () {
    this.auctionListDebounceLoader.run()
  }
  refreshDebounceLoaderRow () {
    this.closedAuctionsDebounced = [...this.$auStore.closed_auctions]
    this.openAuctionsDebounced = [...this.$auStore.open_auctions]
  }
  get auctionsComputed () {
    return this.$auStore.auctions
  }
  
  
  selectAuction (auction: OnAuctionRow) {
    bwp_selectAuction(this.$auConnector, auction)
  }

  hideAuction (auction: OnAuctionRow) {
    hide_auction(this.$auConnector, auction)
  }

  unhideAuction (auction: OnAuctionRow) {
    unhide_auction(this.$auConnector, auction)
  }

  removeAuction (auction: OnAuctionRow) {
    bwp_deleteAuction(this.$auConnector, auction)
  }

  get listHeight (): number {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return (MAX_HEIGHT - 94) / 2
    }

    // 74 was empirically proven.
    return (this.$auLocalStore.config.height_inner - 84) / 2
  }

  createNewAuction () {
    bwp_gotoTemplatePage(this.$auConnector)
  }
}
</script>

<style lang="less">
.HomePage {

}
</style>
