<template>
  <VbDemo>
    <VbCard>
      <UserEdit style="width: 300px" :onUserRow="onUserRow"/>
    </VbCard>
    <VbCard>
      <pre>{{onUserRow}}</pre>
    </VbCard>
  </VbDemo>
</template>

<script>
import UserEdit from './UserEdit.vue'
import { createOnUserRow } from '../UserDisplay/OnUserRowFactory'

export default {
  components: {
    UserEdit,
  },
  data () {
    return {
      onUserRow: createOnUserRow(),
    }
  },
}
</script>
