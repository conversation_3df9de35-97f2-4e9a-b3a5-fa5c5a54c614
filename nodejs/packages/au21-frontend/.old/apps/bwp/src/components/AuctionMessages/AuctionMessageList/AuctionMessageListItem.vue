<template>
  <a-list-item
    class="AuctionMessageListItem"
    :locale="{emptyText: ''}"
    slot="renderItem"
    slot-scope="onMessage, index"
  >
    <a-list-item-meta :description="`${onMessage.SENDER}: ${onMessage.TEXT}`">
      <div slot="title">{{onMessage.TIMESTAMP}}</div>
    </a-list-item-meta>
  </a-list-item>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({})
export default class AuctionMessageListItem extends Vue {

}
</script>

<style lang="less">
.AuctionMessageListItem {

}
</style>
