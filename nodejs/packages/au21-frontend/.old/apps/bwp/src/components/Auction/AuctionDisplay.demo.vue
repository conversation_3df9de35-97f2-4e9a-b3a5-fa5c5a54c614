<template>
  <VbDemo>
    <VbCard class="au-page">
      <AuctionDisplay
        style="width: 900px"
        :onMrAuctionSettings="onMrAuctionSettings"
      />
    </VbCard>
    <VbCard class="au-page">
      <AuctionEdit
        style="width: 900px"
        :onMrAuctionSettings="onMrAuctionSettings"
      />
    </VbCard>
    <VbCard>
      <pre>{{onMrAuctionSettings}}</pre>
    </VbCard>
  </VbDemo>
</template>

<script>
import AuctionDisplay from './AuctionDisplay.vue'
import AuctionEdit from './AuctionEdit.vue'
import { createAuctionForDisplay } from './__demo-helpers/OnMrAuctionSettings'

export default {
  components: {
    AuctionDisplay,
    AuctionEdit,
  },
  data () {
    return {
      onMrAuctionSettings: createAuctionForDisplay(),
    }
  },
}
</script>
