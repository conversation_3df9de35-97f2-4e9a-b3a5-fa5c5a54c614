<template>
  <div class="AuctioneerAuctionStatusBar">
    <div class="AuctioneerAuctionStatusBar__block text-bold">
      Hide<br>non-bidders
    </div>
    <div class="AuctioneerAuctionStatusBar__block">
      <a-radio-group
        v-model="hideNonBiddersProxy"
      >
        <a-radio
          :value="false"
          :style="{display: 'block'}"
        >
          <span style="font-size: 12px">no</span>
        </a-radio>
        <a-radio
          :value="true"
        >
          <span style="font-size: 12px">yes</span>
        </a-radio>
      </a-radio-group>
    </div>
    
    <div class="AuctioneerAuctionStatusBar__block">
      <table>
        <tr>
          <td></td>
          <td><span style="position: relative; left: -2px">Total</span></td>
          <td style="border-left: solid 1px white">Online</td>
        </tr>
        <tr>
          <td class="mr-1 text-bold">Bidders:</td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__small-text-display pseudo-input">
              {{biddersTotal}}
            </div>
          </td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__small-text-display pseudo-input">
              {{biddersOnline}}
            </div>
          </td>
        </tr>
      </table>
    </div>
    <div class="AuctioneerAuctionStatusBar__block">
      <table>
        <tr>
          <td></td>
          <td>First</td>
          <td style="border-left: solid 1px white">Prev</td>
          <td style="border-left: solid 1px white">Curr</td>
        </tr>
        <tr>
          <td class="mr-1 text-bold">Bids:</td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__small-text-display pseudo-input">
              {{onMrAuctioneerSummary.BIDS_FIRST_ROUND || '0'}}
            </div>
          </td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__small-text-display pseudo-input">
              {{onMrAuctioneerSummary.BIDS_PREV_ROUND || '&nbsp;'}}
            </div>
          </td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__small-text-display pseudo-input">
              {{onMrAuctioneerSummary.BIDS_CURRENT_ROUND || '0'}}
            </div>
          </td>
        </tr>
      </table>
    </div>
    <div class="AuctioneerAuctionStatusBar__block">
      <table>
        <tr>
          <td></td>
          <td style="text-align: center">Previous</td>
          <td style="border-left: solid 1px white; text-align: center">Current</td>
          <td style="border-left: solid 1px white; text-align: center">Reduced</td>
        </tr>
        <tr>
          <td class="mr-1 text-bold">Volume:</td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__large-text-display pseudo-input">
              {{onMrAuctioneerSummary.VOLUME_PREVIOUS_ROUND || '&nbsp;'}}
            </div>
          </td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__large-text-display pseudo-input">
              {{onMrAuctioneerSummary.VOLUME_CURRENT_ROUND || '&nbsp;'}}
            </div>
          </td>
          <td style="padding-right: 1px; padding-left: 1px">
            <div class="AuctioneerAuctionStatusBar__large-text-display pseudo-input">
              {{onMrAuctioneerSummary.VOLUME_REDUCED || '&nbsp;'}}
            </div>
          </td>
        </tr>
      </table>
    </div>
    <div class="AuctioneerAuctionStatusBar__block">
      <div>force stop if activity</div>
      <div style="text-align: center; font-size: 11px">{{onMrAuctioneerSummary.STOP_VOLUME_LABEL}}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { OnMrAuctioneerSummary } from '../../../_generated/server_outputs'

@Component({})
export default class AuctioneerAuctionStatusBar extends Vue {
  @Prop({ required: true }) onMrAuctioneerSummary: OnMrAuctioneerSummary
  @Prop({ required: true }) biddersTotal: number
  @Prop({ required: true }) biddersOnline: number

  // -
  @Prop(Boolean) hideNonBidders: boolean
  get hideNonBiddersProxy () {
    return this.hideNonBidders
  }
  set hideNonBiddersProxy (hideNonBidders) {
    this.$emit('update:hideNonBidders', hideNonBidders)
  }
}
</script>

<style lang="less">
.AuctioneerAuctionStatusBar {
  font-size: 12px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  td {
    padding: 0 2px 0 4px;
  }

  & &__block {
    justify-content: center;
  }

  & &__small-text-display {
    width: 30px;
    justify-content: center;
  }

  & &__large-text-display {
    justify-content: center;
    width: 70px;
  }
}
</style>
