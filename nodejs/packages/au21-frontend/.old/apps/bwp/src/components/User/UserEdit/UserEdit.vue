<template>
  <div class="UserEdit" :style="{maxHeight: maxHeight}">
    <div class="mb-1">Username:</div>
    <a-input size="small" v-model="onUserRow.USERNAME"/>
    <div class="mt-2 mb-1">Password:</div>
    <a-input size="small" v-model="onUserRow.PASSWORD"/>
    <div class="mt-2">Role:</div>
    <UserRoleSelect v-model="onUserRow.ROLE"/>
    <div class="mt-2 mb-1">Company:</div>
    <a-input size="small" v-model="onUserRow.COMPANY"/>
    <div class="mt-2 mb-1">Email:</div>
    <a-input size="small" v-model="onUserRow.EMAIL"/>
    <div class="mt-2 mb-1">Work:</div>
    <a-input size="small" v-model="onUserRow.WORK"/>
    <div class="mt-2 mb-1">Mobile:</div>
    <a-input size="small" v-model="onUserRow.MOBILE"/>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnUserRow } from '../../../_generated/server_outputs'
import UserRoleSelect from '../UserRoleSelect/UserRoleSelect.vue'
import { MAX_HEIGHT } from '../../../helpers/height_helper'

@Component({
  components: { UserRoleSelect },
})
export default class UserEdit extends Vue {
  @Prop({ required: true }) onUserRow: OnUserRow
  
  get maxHeight () {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT + 70) {
      return this.$auLocalStore.config.height_inner - 50 + 'px'
    }

    return null
  }
}
</script>

<style lang="less">
@import '../../../assets/variables.less';

.UserEdit {
  color: @au-text-color;
  background-color: @au-background-light;
  padding: 15px;
  overflow: auto;
}
</style>

