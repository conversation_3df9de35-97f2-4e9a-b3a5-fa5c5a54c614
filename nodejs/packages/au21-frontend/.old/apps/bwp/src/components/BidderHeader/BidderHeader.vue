<template>
  <div class="BidderHeader">
    <div class="form-block BidderHeader__head">
      <div class="text-bold">Auction:</div>
      <div style="height: 45px; margin: 0 4px" class="pseudo-input">{{onMrBidderStatus.AUCTION_NAME}}</div>
      <a-button @click="openSettings()">
        Settings
      </a-button>
      &nbsp;
      <a-button @click="openNotice()">
        Notice
      </a-button>
    </div>

    <div style="display: flex; align-items: stretch; text-align: center">
      <div
        class="form-block"
        style="flex: 1 1 35%; display: flex; flex-direction: column; padding: 8px"
      >
        <div class="text-bold">Auction status</div>
        <div class="pseudo-input flex-center text-bold" style="flex: 1 1">
          {{onMrBidderStatus.AUCTION_STATUS}}
        </div>
      </div>
      <div
        class="form-block"
        style="flex: 1 1 65%; display: flex; flex-direction: column; padding: 8px"
      >
        <div class="text-bold">Round details</div>
        <table style="width: 100%;">
          <tr>
            <td style="width: 60px">Round</td>
            <td style="width: 18%">{{ mrClockLabel }}</td>
            <td style="width: 18%">Eligible Vol</td>
            <td style="width: 18%">Current Vol</td>
            <td style="width: 18%">New Vol</td>
            <td></td>
          </tr>
          <tr style="vertical-align: top">
            <td rowspan="2">
              <div
                style="top: 0"
                class="pseudo-input BidderHeader__round BidderHeader__text-display"
              >
                {{onMrBidderStatus.ROUND_NUMBER}}
              </div>
            </td>
            <td style="vertical-align: top">
              <div class="pseudo-input BidderHeader__number BidderHeader__text-display">
                {{onMrBidderStatus.ROUND_PRICE}}
              </div>
            </td>
            <td style="vertical-align: top">
              <div class="pseudo-input BidderHeader__number BidderHeader__text-display">
                {{onMrBidderStatus.CURRENT_ELIGIBILITY}}
              </div>
            </td>
            <td style="vertical-align: top">
              <div class="pseudo-input BidderHeader__number BidderHeader__text-display">
                {{onMrBidderStatus.CURRENT_BID || '-'}}
              </div>
            </td>
            <td style="vertical-align: top">
              <PriceInput
                class="BidderHeader__number"
                style="width: 120px"
                size="default"
                v-model="newVolume"
                @keyup.enter="submitNewVolume()"
              />
            </td>
            <td style="vertical-align: top">
              <a-button
                style="width: 75px; margin-left: 1px;"
                @click="submitNewVolume()"
              >
                Enter
              </a-button>
            </td>
          </tr>
          <tr style="line-height: 14px">
            <!-- roundNumber -->
            <td>{{onMrBidderStatus.PRICE_LABEL}}</td>
            <td>{{onMrBidderStatus.VOLUME_LABEL}}</td>
            <td>{{onMrBidderStatus.VOLUME_LABEL}}</td>
            <td>{{onMrBidderStatus.VOLUME_LABEL}}</td>
            <td></td>
          </tr>
        </table>
      </div>
    </div>

    <div
      class="form-block"
      v-if="$auStore.trader.award"
      style="flex: 1 1 50%; display: flex; flex-direction: column;"
    >
      <div class="BidderHeader__award" style="text-align: center">
        {{ $auStore.trader.award }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import {OnMrBidderStatus, OnMrTraderSettings} from '../../_generated/server_outputs'
import PriceInput from '../../ui-components/PriceInput/PriceInput.vue'
import {getMrClockLabelName} from "../Auction/__demo-helpers/MrClockLabel";
import {MRClockLabel} from "../../_generated/bwp-enums";

@Component({
  components: { PriceInput },
})
export default class BidderHeader extends Vue {
  @Prop({ required: true }) onMrBidderStatus: OnMrBidderStatus
  @Prop({ required: true }) onMrTraderSettings: OnMrTraderSettings

  get mrClockLabel() {
    return getMrClockLabelName(this.onMrTraderSettings.CLOCK_LABEL || MRClockLabel.PRICE)
  }
  
  newVolume = null

  submitNewVolume () {
    this.$emit('volumeSubmit', this.newVolume)
    this.newVolume = ''
  }

  openSettings () {
    this.$emit('openSettings')
  }

  openNotice () {
    this.$emit('openNotice')
  }
}
</script>

<style lang="less">
@import '../../assets/variables.less';

.BidderHeader {
  color: @au-text-color;

  &__text-display {
    width: 100%;
    justify-content: center;
      border-radius: 3px;
  }

  & &__head {
    display: flex;
    align-items: center;
  }

  & &__round {
    font-size: 36px;
    line-height: 46px;
  }

  & &__number {
    font-size: 18px;
    line-height: 29px;
  }

  & &__award {
    color: #ffff00;
    font-size: 16px;
  }
}
</style>
