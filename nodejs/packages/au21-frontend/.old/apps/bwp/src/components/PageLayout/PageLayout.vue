<template>
  <div class="PageLayout au-page">
    <NavigationBar :remote_user="remote_user"/>

    <transition name="au-fade" mode="out-in">
      <div
        v-if="!has_inited"
        :style="styleComputed"
        class="PageLayout__loading-container flex-center"
      >
        <div style="text-align: center;">
          <a-spin class="mb-2" size="large"/>
          <div style="color: #0e0e0e; font-weight: bold; font-size: 16px;">
            Loading...
          </div>
        </div>
      </div>
    </transition>

    <div
      class="form-block-container"
      style="display: flex; flex-direction: column; justify-content: stretch; max-height: calc(100% - 100px);"
      :style="styleComputed"
    >
      <slot/>
    </div>

    <div class="text-warning text-small" style="text-align: center">
      {{footer}}
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import NavigationBar from '../NavigationBar/NavigationBar.vue'
import { MAX_HEIGHT } from '../../helpers/height_helper'

@Component({
  components: { NavigationBar },
})
export default class PageLayout extends Vue {
  get has_inited(): boolean {
    if (!this.$auStore.next_page) {
      return true
    }
    return this.$auStore.next_page.HAS_INITED
  }
  get remote_user () {
    return this.$auStore.remote_user
  }
  get styleComputed () {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return {
        minHeight: MAX_HEIGHT + 40 + 'px'
      }
    }
  }
  get footer () {
    // Jan 17, 2021: footer generation not working, hard coding for now:
    // return FOOTER_TEXT
    return "Copyright Auctionologies LLC, 2011-2021"  }
}
</script>

<style lang="less">
@import (reference) '../../assets/variables.less';

.PageLayout {
  color: @au-text-color;
  position: relative;
  &__loading-container {
    color: @au-text-color;
    border-radius: 6px;
    top: 82px;
    cursor: default;

    z-index: 10;
    position: absolute;
    min-height: calc(100% - 100px);
    max-height: calc(100% - 100px);
    width: 100%;
    background-color: fade(#dcdcdc, 90%);
  }
}
</style>
