import { OnMessage } from '../../../_generated/server_outputs'
import uuid from 'uuid'

export const createOnMessage = () => ({
  AUCTIONID: parseInt(uuid()),
  TIMESTAMP: '9:34:56 AM, 01/25/19',
  TIME: '9:34:56 AM',
  DATE: '01/25/19',
  TEXT: 'Message text. With added length.',
  SENDER: 'Auctioneer',
}) as OnMessage

export const createBidderOnMessage = () => ({
  AUCTIONID: parseInt(uuid()),
  TIMESTAMP: '9:34:56 AM, 01/25/19',
  TIME: '9:34:56 AM',
  DATE: '01/25/19',
  TEXT: 'This message is from bidder!',
  SENDER: 'b1',
})
