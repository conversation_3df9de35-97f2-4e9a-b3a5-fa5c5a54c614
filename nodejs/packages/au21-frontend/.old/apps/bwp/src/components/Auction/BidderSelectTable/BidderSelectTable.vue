<template>
  <AuScrollableTableStepped
    class="BidderSelectTable"
    :table_width="700"
    :table_height="height - 2"
    :header_height="24"
    :cell_height="24"
    :visible_columns="0"
    :fixed_columns="columns"
    :columns="[]"
    :header_rows="headerRows"
    :rows="bidderListSorted"
    :columnWidthSetter="column => column.width"
    whiteFirstColumn
  >
    <template
      slot="fixed_header_cell"
      slot-scope="{column, row, row_index}"
    >
      <div 
        v-if="column.field === '__SELECT'"
        style="text-align: center; padding-top: 1px; background-color: #666666;"
        class="table-cell-wrapper"
      >
        <a-checkbox v-model="allSelectedProxy"/>
      </div>
      <div
        v-else
        class="table-cell-wrapper"
        style="text-align: left; padding: 2px 6px; background-color: #666666; color: white; font-weight: 700; cursor: pointer;"
        @click="onColumnSort(column.field)"
      >
        {{ column.title }}
        <a-icon v-if="sortBy === column.field" style="width: 12px; height: 12px" :type="sortDirection === 'asc' ? 'down' : 'up'"/>
      </div>
    </template>

    <template
      slot="fixed_body_cell"
      slot-scope="{column, row}"
    >
      <div 
        style="text-align: center; padding-top: 1px" 
        v-if="column.field === '__SELECT'"
      >
        <a-checkbox
          :checked="isBidderSelected(row)"
          @change="toggleBidder(row)"
        />
      </div>
      <div
        v-else
        class="table-cell-wrapper"
        style="padding: 2px 6px; cursor: pointer; text-align: left"
      >
        {{ column.customRender ? column.customRender(row[column.field]) : row[column.field] }}
      </div>
    </template>
  </AuScrollableTableStepped>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { getRoleName } from '../../User/UserRoleSelect/Role'
import { OnUserRow } from '../../../_generated/server_outputs'
import { sortWithDirection } from '../../../helpers/array-helpers'
import AuScrollableTableStepped
  from '../../../ui-components/TableScroller/AuScrollableTableStepped.vue'
import { Role } from '../../../_generated/bwp-enums'

@Component({
  components: { AuScrollableTableStepped },
})
export default class BidderSelectTable extends Vue {
  @Prop({ type: Array }) value: number[]
  @Prop({ type: Array }) bidderList: OnUserRow[]
  @Prop({ type: Number }) height: number

  sortDirection: 'asc' | 'desc' = 'asc'
  sortBy: 'COMPANY' | 'USERNAME' | 'ROLE' = 'USERNAME'

  get columns (): any[] {
    return [
      {
        title: '',
        field: '__SELECT',
        width: 50,
      },
      {
        title: 'User name',
        field: 'USERNAME',
        width: 210,
      },
      {
        title: 'Company',
        field: 'COMPANY',
        width: 210,
      },
      {
        title: 'Role',
        field: 'ROLE',
        customRender: (record) => getRoleName(record),
        width: 210,
      },
    ]
  }
  
  set allSelectedProxy (value) {
    this.$emit('input', value ? this.bidderList.map(bidder => bidder.USERID) : [])
  }
  get allSelectedProxy () {
    return !!this.value.length
  }
  
  headerRows = [[
    {
      title: '',
      dataIndex: '__SELECT',
      width: 50,
    },
    {
      title: 'User name',
      dataIndex: 'USERNAME',
      width: 150,
    },
    {
      title: 'Company',
      dataIndex: 'COMPANY',
      width: 150,
    },
    {
      title: 'Role',
      dataIndex: 'ROLE',
      customRender: (record) => getRoleName(record),
      width: 200,
    },
  ]]

  isBidderSelected(bidder: OnUserRow): boolean {
    return this.value.includes(bidder.USERID)
  }
  toggleBidder(bidder: OnUserRow): void {
    if (this.isBidderSelected(bidder)) {
      this.$emit('input', this.value.filter(bidderId => bidderId !== bidder.USERID))
    } else {
      this.$emit('input', [...this.value, bidder.USERID])
    }
  }

  get bidderListComputed () {
    if (!this.sortBy) {
      return this.bidderList
    }
    return sortWithDirection(this.bidderList, this.sortBy, this.sortDirection)
  }

  get bidderListSorted () {
    return sortWithDirection(this.bidderList, this.sortBy, this.sortDirection)
  }
  
  onColumnSort (column: 'COMPANY' | 'USERNAME' | 'ROLE') {
    if (this.sortBy === column && this.sortDirection === 'asc') {
      this.sortDirection = 'desc'
      return
    }
    this.sortBy = column
    this.sortDirection = 'asc'
  }

  get rowSelection () {
    return {
      onChange: (selectedRowKeys, selectedRows) => {
        this.$emit('input', selectedRowKeys)
      },
      selectedRowKeys: this.value,
    }
  }
}
</script>

<style lang="less">
.BidderSelectTable {
  text-align: left;
  .ant-table-thead > tr > th {
    background-color: #666666;
    color: white;
    padding: 2px 6px;
  }

  .ant-table-tbody > tr > td {
    padding: 2px 6px;
  }
}
</style>
