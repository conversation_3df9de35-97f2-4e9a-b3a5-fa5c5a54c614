<template>
  <div class="AuctionDisplay">
    <a-row>
      <a-col :span="24" class="mb-2">
        <div style="display: flex;" class="mb-1">
          <span style="flex: 0 0 100px; white-space: nowrap; text-align: right; "
                class="mr-1"
          >
            Auction name:
          </span>
          <div
            class="pseudo-input"
            style="resize: none; flex: 1 1; height: 46px"
          >{{onMrAuctionSettings.AUCTION_NAME}}
          </div>
        </div>

        <div style="display: flex;" class="mb-1">
          <span style="flex: 0 0 100px; white-space: nowrap; text-align: right; "
                class="mr-1"
          >
          Start time:
        </span>
          <div
            style="width: 200px"
            class="mr-1 pseudo-input"
          >{{onMrAuctionSettings.START_TIME}}
          </div>
          (Only used as a label, these auctions still need to be started manually)
        </div>


      </a-col>

      <a-col :span="11">
        <div class="mb-3">
          <div class="text-bold text-center mb-1">Round timer</div>
          <div class="mb-1">
            <div class="AuctionDisplay__field-label">First round:</div>
            <div
              style="width: 130px"
              class="mr-1 pseudo-input"
            >{{onMrAuctionSettings.INITIAL_ROUND_DURATION}}
            </div>
            seconds
          </div>
          <div>
            <div class="AuctionDisplay__field-label">Following:</div>
            <div
              style="width: 130px"
              class="mr-1 pseudo-input"
            >{{onMrAuctionSettings.FOLLOWING_ROUND_DURATION}}
            </div>
            seconds
          </div>
        </div>

        <div class="mb-3">
          <div class="text-bold text-center mb-1">Clock settings</div>

          <div class="mb-1">
            <div class="AuctionEdit__field-label">Clock label:</div>
            <AuctionClockLabelSelect
                disabled
                style="width: 130px"
                class="mr-1" 
                v-model="onMrAuctionSettings.CLOCK_LABEL"/>
          </div>
          
          <div class="mb-1">
            <div class="AuctionDisplay__field-label">{{ mrClockLabel }} direction:</div>
            <a-radio-group
              disabled
              class="UserRoleSelect"
              v-model="onMrAuctionSettings.DIRECTION"
            >
              <a-radio
                v-for="priceDirectionOption in priceDirectionOptions"
                :key="priceDirectionOption.value"
                :value="priceDirectionOption.value"
              >
                {{ priceDirectionOption.name }}
              </a-radio>
            </a-radio-group>
          </div>
          <div class="mb-1">
            <div class="AuctionDisplay__field-label">{{ mrClockLabel }} label:</div>
            <div
              style="width: 130px"
              class="mr-1 pseudo-input"
            >{{onMrAuctionSettings.PRICE_LABEL}}
            </div>
            e.g. $/Dth
          </div>
          <div>
            <div class="AuctionDisplay__field-label">Decimals:</div>
            <div
              style="width: 130px"
              class="mr-1 pseudo-input"
            >{{onMrAuctionSettings.PRICE_DECIMALS}}
            </div>
            e.g. enter 3 for 0.000
          </div>
        </div>

        <div class="text-bold text-center mb-1">Volume settings</div>

        <div class="mb-1">
          <div class="AuctionDisplay__field-label">Volume label:</div>
          <div
            style="width: 130px"
            class="mr-1 pseudo-input"
          >{{onMrAuctionSettings.VOLUME_LABEL}}
          </div>
          e.g. Dth/day
        </div>
        <div class="mb-1">
          <div class="AuctionDisplay__field-label">Initial eligibility:</div>
          <div
            style="width: 130px"
            class="mr-1 pseudo-input"
          >{{onMrAuctionSettings.INITIAL_ELIGIBILITY}}
          </div>
          {{onMrAuctionSettings.VOLUME_LABEL}}
        </div>
        <div class="mb-1">
          <div class="AuctionDisplay__field-label">Vol decrement:</div>
          <div
            style="width: 130px"
            class="mr-1 pseudo-input"
          >{{onMrAuctionSettings.VOLUME_DECREMENT}}
          </div>
          {{onMrAuctionSettings.VOLUME_LABEL}}
        </div>
        <div>
          <div class="AuctionDisplay__field-label">Minimum vol:</div>
          <div
            style="width: 130px"
            class="mr-1 pseudo-input"
          >{{onMrAuctionSettings.MIN_VOL}}
          </div>
          {{onMrAuctionSettings.VOLUME_LABEL}}
        </div>
      </a-col>

      <a-col :span="11" :offset="2">
        <div class="mb-3">
          <div class="text-bold text-center mb-1">Bidder visibility</div>
          <div>
            <div class="AuctionDisplay__field-label">Requirement:</div>
            <a-radio-group
              disabled
              class="UserRoleSelect"
              v-model="onMrAuctionSettings.VISIBILITY"
            >
              <a-radio
                v-for="MRVisibilityOption in MRVisibilityOptions"
                :key="MRVisibilityOption.value"
                :value="MRVisibilityOption.value"
              >
                {{ MRVisibilityOption.name }}
              </a-radio>
            </a-radio-group>
          </div>
        </div>

        <div class="mb-3">
          <div class="text-bold text-center">Activity Table</div>
          <AuctionActivityEdit displayOnly :onMrAuctionSettings="onMrAuctionSettings"/>
        </div>

        <div class="text-bold mb-1">Stop action if activity is:</div>
        <div>
          <AuctionStopModeSelect
            disabled
            style="width: 160px"
            class="mr-1"
            v-model="onMrAuctionSettings.STOP_MODE"
          />
          <div
            class="mr-1 pseudo-input"
            style="width: 100px"
          >{{onMrAuctionSettings.STOP_VOLUME}}&nbsp;
          </div>
          {{onMrAuctionSettings.VOLUME_LABEL}}
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnMrAuctionSettings } from '../../_generated/server_outputs'
import { getPriceDirectionOptions } from './__demo-helpers/PriceDirection'
import { getMrVisibilityOptions } from './__demo-helpers/MrVIsibility'
import AuctionActivityEdit from './AuctionActivityEdit/AuctionActivityEdit.vue'
import AuctionClockLabelSelect from "./AuctionClockLabelSelect/AuctionClockLabelSelect.vue";
import AuctionStopModeSelect from './AuctionStopModeSelect/AuctionStopModeSelect.vue'
import { getStartTimeAsMoment, setStartTimeFromMoment } from './__demo-helpers/OnMrAuctionSettings'
import {getMrClockLabelName} from "./__demo-helpers/MrClockLabel";
import {MRClockLabel} from "../../_generated/bwp-enums";

@Component({
  components: { AuctionClockLabelSelect, AuctionStopModeSelect, AuctionActivityEdit },
})
export default class AuctionDisplay extends Vue {
  @Prop({ required: true }) onMrAuctionSettings: OnMrAuctionSettings
  priceDirectionOptions = getPriceDirectionOptions()
  MRVisibilityOptions = getMrVisibilityOptions()

  get mrClockLabel() {
    return getMrClockLabelName(this.onMrAuctionSettings.CLOCK_LABEL || MRClockLabel.PRICE)
  }
  
  get startTimeProxy () {
    return getStartTimeAsMoment(this.onMrAuctionSettings)
  }

  set startTimeProxy (value) {
    setStartTimeFromMoment(this.onMrAuctionSettings, value)
  }
}
</script>

<style lang="less">
@import '../../assets/variables.less';

.AuctionDisplay {
  &__field-label {
    display: inline-block;
    width: 100px;
    text-align: right;
    margin-right: @au-spacing-two;
  }
}
</style>
