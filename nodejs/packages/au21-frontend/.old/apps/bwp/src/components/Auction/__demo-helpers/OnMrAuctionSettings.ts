import {MRClockLabel, MRStopMode, MRVisibility, Operator, PriceDirection,} from '../../../_generated/bwp-enums'
import {OnMrAuctionSettings} from '../../../_generated/server_outputs'
import moment from 'moment'

export const createEmptyOnMrAuctionSettings = () => ({
  _COUNT: 0,
  EVENT: 'OnMrAuctionSettings',
  ACTION: '',
  AUCTION_NAME: '',
  AUCTIONID: 0,
  CLOCK_LABEL: null as MRClockLabel,
  CONTROLLER: '',
  DIRECTION: null as PriceDirection,
  FOLLOWING_ROUND_DURATION: '0',
  HIGH_CHANGE: '',
  HIGH_LABEL: '',
  HIGH_LIMIT: '',
  HIGH_OPERATOR: null as Operator,
  INITIAL_ELIGIBILITY: '',
  INITIAL_ROUND_DURATION: '0',
  LOW_CHANGE: '',
  LOW_LABEL: '',
  MED_CHANGE: '',
  MED_LABEL: '',
  MED_LIMIT: '',
  MED_OPERATOR: null as Operator,
  MIN_VOL: '',
  PRICE_DECIMALS: '',
  PRICE_LABEL: '',
  START_TIME: moment().format('MM/DD/YYYY - HH:mm:ss').toString(),
  STOP_MODE: null as MRStopMode,
  STOP_VOLUME: '',
  VISIBILITY: null as MRVisibility,
  VOLUME_DECREMENT: '',
  VOLUME_LABEL: '',
} as OnMrAuctionSettings)

export const createOnMrAutionSettingsForEdit = () => ({
  _COUNT: 0,
  EVENT: 'OnMrAuctionSettings',
  ACTION: 'Action',
  AUCTION_NAME: 'Sun Feb 3 22:20:31 GMT+0300 2019',
  AUCTIONID: 0,
  CLOCK_LABEL: MRClockLabel.PRICE,
  CONTROLLER: 'Controller',
  DIRECTION: 'UP' as PriceDirection,
  FOLLOWING_ROUND_DURATION: '60',
  HIGH_CHANGE: '0.0030',
  HIGH_LABEL: 'H',
  HIGH_LIMIT: '200,000',
  HIGH_OPERATOR: 'GT' as Operator,
  INITIAL_ELIGIBILITY: '100,000',
  INITIAL_ROUND_DURATION: '60',
  LOW_CHANGE: '0.0010',
  LOW_LABEL: 'L',
  MED_CHANGE: '0.0020',
  MED_LABEL: 'M',
  MED_LIMIT: '150,000',
  MED_OPERATOR: 'GT' as Operator,
  MIN_VOL: '1',
  PRICE_DECIMALS: '4',
  PRICE_LABEL: '$/Dth',
  START_TIME: moment().format('MM/DD/YYYY - HH:mm:ss').toString(),
  STOP_MODE: 'ZERO' as MRStopMode,
  STOP_VOLUME: '0',
  VISIBILITY: 'ALL' as MRVisibility,
  VOLUME_DECREMENT: '1',
  VOLUME_LABEL: 'Dth/day',
} as OnMrAuctionSettings)

// NOTE This is not test factory. Used for actual code.
export const createSampleAuction = () => ({
  _COUNT: 0,
  EVENT: 'OnMrAuctionSettings',
  ACTION: '',
  AUCTION_NAME: Date(),
  AUCTIONID: 0,
  CLOCK_LABEL: MRClockLabel.PRICE,
  CONTROLLER: '',
  DIRECTION: 'UP' as PriceDirection,
  FOLLOWING_ROUND_DURATION: '60',
  HIGH_CHANGE: '0.0030',
  HIGH_LABEL: 'H',
  HIGH_LIMIT: '200,000',
  HIGH_OPERATOR: 'GT' as Operator,
  INITIAL_ELIGIBILITY: '100,000',
  INITIAL_ROUND_DURATION: '60',
  LOW_CHANGE: '0.0010',
  LOW_LABEL: 'L',
  MED_CHANGE: '0.0020',
  MED_LABEL: 'M',
  MED_LIMIT: '150,000',
  MED_OPERATOR: 'GT' as Operator,
  MIN_VOL: '1',
  PRICE_DECIMALS: '4',
  PRICE_LABEL: '$/Dth',
  START_TIME: moment().format('MM/DD/YYYY - HH:mm:ss').toString(),
  STOP_MODE: 'ZERO' as MRStopMode,
  STOP_VOLUME: '0',
  VISIBILITY: 'ALL' as MRVisibility,
  VOLUME_DECREMENT: '1',
  VOLUME_LABEL: 'Dth/day',
} as OnMrAuctionSettings)

export const createAuctionForDisplay = createOnMrAutionSettingsForEdit

export const setStartTimeFromMoment = (onMrAuctionSettings: OnMrAuctionSettings, moment: moment.Moment) => {
  onMrAuctionSettings.START_TIME = moment.format('MM/DD/YYYY - HH:mm:ss').toString()
}
export const getStartTimeAsMoment = (onMrAuctionSettings: OnMrAuctionSettings): moment.Moment => {
  return moment(onMrAuctionSettings.START_TIME, 'MM/DD/YYYY - HH:mm:ss')
}
