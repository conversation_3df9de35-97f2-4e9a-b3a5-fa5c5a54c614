interface TestLogin {
  username: string,
  password: string,
}

const generateTestLogins = (): TestLogin[] => {
  if (process.env.VUE_APP_LOGINS) {
    // Override logins from env.
    return JSON.parse(process.env.VUE_APP_LOGINS)
  }

  const generatePassword = username => ({ username, password: '1' })
  return [
    'a',
    'a2',
    'b1',
    'b2',
    'b3',
    'b4',
  ].map(generatePassword)
}


export const test_logins = generateTestLogins()
