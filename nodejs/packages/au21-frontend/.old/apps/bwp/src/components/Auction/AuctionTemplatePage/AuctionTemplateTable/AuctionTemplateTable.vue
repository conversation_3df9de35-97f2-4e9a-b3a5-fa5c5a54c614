<template>
  <div>
    <AuScrollableTableStepped
      class="AuctionTemplateTable"
      :table_width="width"
      :table_height="height"
      :header_height="24"
      :cell_height="24"
      :visible_columns="0"
      :fixed_columns="columns"
      :columns="[]"
      :header_rows="['']"
      :rows="templatesComputed"
      :columnWidthSetter="column => column.width"
      whiteFirstColumn
    >
      <div
        slot="fixed_header_cell"
        slot-scope="{column, row}"
        class="table-cell-wrapper"
        style="text-align: left; padding: 2px 6px; background-color: #666666; color: white; font-weight: 700;"
      >
        {{ column.title }}
      </div>

      <div
        slot="fixed_body_cell"
        slot-scope="{column, column_index, row}"
        class="table-cell-wrapper"
        style="text-align: left; padding: 2px; cursor: pointer"
        :title="row[column.dataIndex]"
        @click="$emit('onMrTemplateSelected', row)"
      >
        <template v-if="column_index === 0">
          &nbsp;{{ row[column.dataIndex] }}
        </template>
        <template v-else>
          &nbsp;
          <a @click="startRenamingAuctionTemplate(row)" href="javascript:;">Rename</a>
          <a-divider type="vertical"/>
          <a-popconfirm
            title="Delete this Auction Template?"
            placement="bottomRight"
            @confirm="removeAuctionTemplate(row)"
            okText="Yes"
            cancelText="No"
          >
            <a href="#">Delete</a>
          </a-popconfirm>
        </template>
      </div>
    </AuScrollableTableStepped>

    <a-modal
      v-if="editedAuctionTemplate"
      title="Rename Template"
      @cancel="cancelRenamingAuctionTemplate()"
      visible
      closable
      centered
      width="430px"
    >
      Old name:
      <div class="pseudo-input" style="height: 70px">
        {{editedAuctionTemplate.DESCRIPTION}}
      </div>
      New name:
      <a-textarea v-model="newDescription" :autosize="{ minRows: 3, maxRows: 6 }"/>

      <a-button
        slot="footer"
        @click="saveRenamingAuctionTemplate()"
      >
        Save
      </a-button>

      <a-button
        slot="footer"
        type="dashed"
        @click="cancelRenamingAuctionTemplate()"
      >
        Cancel
      </a-button>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { OnMrTemplate } from '../../../../_generated/server_outputs'
import {
  bwp_renameTemplate,
  bwp_deleteTemplate,
} from '../../../../services/bwp-connector/publisher'
import AuScrollableTableStepped
  from '../../../../ui-components/TableScroller/AuScrollableTableStepped.vue'
import {DebounceLoader} from 'asva-executors'

@Component({
  components: { AuScrollableTableStepped },
})
export default class AuctionTemplateTable extends Vue {
  @Prop({ type: Number }) height!: number
  @Prop({ type: Number }) width!: number
  editedAuctionTemplate: OnMrTemplate = null
  newDescription = ''
  columns = [
    {
      title: 'Description',
      dataIndex: 'DESCRIPTION',
      width: this.width - 148 - 16,
    },
    {
      title: '',
      width: 153,
      scopedSlots: { customRender: 'action' },
    },
  ]

  // Prevents table from being redrawn on each update (backend might send hundreds of these depending on list size).
  templatesDebounced: OnMrTemplate[] = []
  templatesDebounceLoader = new DebounceLoader(async () => {
    this.refreshDebounceLoaderRow()
  }, 100)
  @Watch('templates', {deep: true, immediate: true})
  onTemplatesChange () {
    this.templatesDebounceLoader.run()
  }
  refreshDebounceLoaderRow () {
    this.templatesDebounced = [...this.templates]
  }
  get templates () {
    return this.$auStore.templates
  }


  get templatesComputed () {
    return this.templatesDebounced
      .sort((a, b) => a.TEMPLATEID > b.TEMPLATEID ? 1 : -1)
  }

  removeAuctionTemplate (onMrTemplate: OnMrTemplate) {
    bwp_deleteTemplate(this.$auConnector, onMrTemplate)
  }

  startRenamingAuctionTemplate (onMrTemplate: OnMrTemplate) {
    this.editedAuctionTemplate = onMrTemplate
    this.newDescription = ''
  }

  saveRenamingAuctionTemplate () {
    bwp_renameTemplate(this.$auConnector, this.editedAuctionTemplate, this.newDescription)
    this.editedAuctionTemplate = null
  }

  cancelRenamingAuctionTemplate () {
    this.editedAuctionTemplate = null
  }

  getCustomRowConfig (record) {
    return {
      on: {
        click: () => {
          this.$emit('onMrTemplateSelected', record)
        },
      },
    }
  }
}
</script>

<style lang="less">
.AuctionTemplateTable {

}
</style>
