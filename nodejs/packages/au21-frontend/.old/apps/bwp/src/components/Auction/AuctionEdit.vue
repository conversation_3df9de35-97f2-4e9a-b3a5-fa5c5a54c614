<template>
  <div class="AuctionEdit">
    <a-row>
      <a-col :span="24" class="mb-2">
        <div style="display: flex;" class="mb-1">
          <span style="flex: 0 0 100px; white-space: nowrap; text-align: right; "
                class="mr-1"
          >
            Auction name:
          </span>
          <a-textarea
              size="small"
              style="resize: none;"
              rows="2"
              v-model="onMrAuctionSettings.AUCTION_NAME"
          />
        </div>

        <div style="display: flex;" class="mb-1">
          <span style="flex: 0 0 100px; white-space: nowrap; text-align: right; "
                class="mr-1"
          >
          Start time:
        </span>
          <a-date-picker
              size="small"
              class="mr-1"
              showTime
              format="MM/DD/YYYY - HH:mm"
              placeholder=""
              v-model="startTimeProxy"
          />
          (Only used as a label, these auctions still need to be started manually)
        </div>


      </a-col>

      <a-col :span="11">
        <div class="mb-3">
          <div class="text-bold text-center mb-1">Round timer</div>
          <div class="mb-1">
            <div class="AuctionEdit__field-label">First round:</div>
            <a-input style="width: 130px" class="mr-1" size="small" :min="0"
                     v-model="onMrAuctionSettings.INITIAL_ROUND_DURATION"/>
            seconds
          </div>
          <div>
            <div class="AuctionEdit__field-label">Following:</div>
            <a-input style="width: 130px" class="mr-1" size="small" :min="0"
                     v-model="onMrAuctionSettings.FOLLOWING_ROUND_DURATION"/>
            seconds
          </div>
        </div>

        <div class="mb-3">
          <div class="text-bold text-center mb-1">Clock settings</div>

          <div class="mb-1">
            <div class="AuctionEdit__field-label">Clock label:</div>
            <AuctionClockLabelSelect 
                style="width: 130px" 
                class="mr-1" 
                v-model="onMrAuctionSettings.CLOCK_LABEL" />
          </div>

          <div class="mb-1">
            <div class="AuctionEdit__field-label">{{ mrClockLabel }} direction:</div>
            <a-radio-group
                class="UserRoleSelect"
                v-model="onMrAuctionSettings.DIRECTION"
            >
              <a-radio
                  v-for="priceDirectionOption in priceDirectionOptions"
                  :key="priceDirectionOption.value"
                  :value="priceDirectionOption.value"
              >
                {{ priceDirectionOption.name }}
              </a-radio>
            </a-radio-group>
          </div>


          <div class="mb-1">
            <div class="AuctionEdit__field-label">{{ mrClockLabel }} label:</div>
            <a-input style="width: 130px" class="mr-1" size="small" :min="0" v-model="onMrAuctionSettings.PRICE_LABEL"/>
            e.g. $/Dth, months
          </div>
          <div>
            <div class="AuctionEdit__field-label">Decimals:</div>
            <a-input style="width: 130px" class="mr-1" size="small" :min="0"
                     v-model="onMrAuctionSettings.PRICE_DECIMALS"/>
            e.g. enter 3 for 0.000
          </div>
        </div>


        <div class="text-bold text-center mb-1">Volume settings</div>

        <div class="mb-1">
          <div class="AuctionEdit__field-label">Volume label:</div>
          <a-input style="width: 130px" class="mr-1" size="small" :min="0" v-model="onMrAuctionSettings.VOLUME_LABEL"/>
          e.g. Dth/day
        </div>
        <div class="mb-1">
          <div class="AuctionEdit__field-label">Initial eligibility:</div>
          <PriceInput
              class="mr-1"
              style="width: 130px"
              v-model="onMrAuctionSettings.INITIAL_ELIGIBILITY"
          />
          {{ onMrAuctionSettings.VOLUME_LABEL }}
        </div>
        <div class="mb-1">
          <div class="AuctionEdit__field-label">Vol decrement:</div>
          <PriceInput
              class="mr-1"
              style="width: 130px"
              v-model="onMrAuctionSettings.VOLUME_DECREMENT"
          />
          {{ onMrAuctionSettings.VOLUME_LABEL }}
        </div>
        <div>
          <div class="AuctionEdit__field-label">Minimum vol:</div>
          <PriceInput
              class="mr-1"
              style="width: 130px"
              v-model="onMrAuctionSettings.MIN_VOL"
          />
          {{ onMrAuctionSettings.VOLUME_LABEL }}
        </div>
      </a-col>

      <a-col :span="11" :offset="2">
        <div class="mb-3">
          <div class="text-bold text-center mb-1">Bidder visibility</div>
          <div>
            <div class="AuctionEdit__field-label">Requirement:</div>
            <a-radio-group
                class="UserRoleSelect"
                v-model="onMrAuctionSettings.VISIBILITY"
            >
              <a-radio
                  v-for="MRVisibilityOption in MRVisibilityOptions"
                  :key="MRVisibilityOption.value"
                  :value="MRVisibilityOption.value"
              >
                {{ MRVisibilityOption.name }}
              </a-radio>
            </a-radio-group>
          </div>
        </div>

        <div class="mb-3">
          <div class="text-bold text-center">Activity Table</div>
          <AuctionActivityEdit :onMrAuctionSettings="onMrAuctionSettings"/>
        </div>

        <div class="text-bold mb-1">Stop action if activity is:</div>
        <div>
          <AuctionStopModeSelect style="width: 160px" class="mr-1" v-model="onMrAuctionSettings.STOP_MODE"/>
          <a-input
              class="mr-1"
              :disabled="onMrAuctionSettings.STOP_MODE === 'ZERO'"
              style="width: 100px"
              size="small"
              v-model="onMrAuctionSettings.STOP_VOLUME"
          />
          {{ onMrAuctionSettings.VOLUME_LABEL }}
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator'
import {OnMrAuctionSettings} from '../../_generated/server_outputs'
import {getPriceDirectionOptions} from './__demo-helpers/PriceDirection'
import {getMrVisibilityOptions} from './__demo-helpers/MrVIsibility'
import AuctionActivityEdit from './AuctionActivityEdit/AuctionActivityEdit.vue'
import AuctionStopModeSelect from './AuctionStopModeSelect/AuctionStopModeSelect.vue'
import {getStartTimeAsMoment, setStartTimeFromMoment,} from './__demo-helpers/OnMrAuctionSettings'
import PriceInput from '../../ui-components/PriceInput/PriceInput.vue'
import AuctionClockLabelSelect from "./AuctionClockLabelSelect/AuctionClockLabelSelect.vue";
import {getMrClockLabelName} from "./__demo-helpers/MrClockLabel";
import {MRClockLabel} from "../../_generated/bwp-enums";

@Component({
  components: {AuctionClockLabelSelect, PriceInput, AuctionStopModeSelect, AuctionActivityEdit},
})
export default class AuctionEdit extends Vue {
  @Prop({required: true}) onMrAuctionSettings: OnMrAuctionSettings
  priceDirectionOptions = getPriceDirectionOptions()
  MRVisibilityOptions = getMrVisibilityOptions()

  get mrClockLabel() {
    return getMrClockLabelName(this.onMrAuctionSettings.CLOCK_LABEL || MRClockLabel.PRICE)
  }
  
  get startTimeProxy() {
    return getStartTimeAsMoment(this.onMrAuctionSettings)
  }

  set startTimeProxy(value) {
    setStartTimeFromMoment(this.onMrAuctionSettings, value)
  }
}
</script>

<style lang="less">
@import '../../assets/variables.less';

.AuctionEdit {
  color: @au-text-color;
  overflow: auto;

  &__field-label {
    display: inline-block;
    width: 100px;
    text-align: right;
    margin-right: @au-spacing-two;
  }
}
</style>
