import {
    bpw_getAvailableUsers,
    bwp_addUsers,
    bwp_command,
    bwp_create_auction,
    bwp_createMessage,
    bwp_getNotice,
    bwp_gotoTemplatePage,
    bwp_login,
    bwp_selectAuction,
    bwp_setNotice,
    bwp_setPrice,
    bwp_signoff,
    bwp_submitOrder
} from "../publisher";
import {create_test_session} from "./test-helpers";
import {OnAuctionRow} from "../../../_generated/server_outputs";
import {create_sample_auction} from "../../../_generated/server_inputs";
import {sleep} from "../../utils";
import {errors} from "../../event-bus";
import { MRCommand } from '../../../_generated/bwp-enums'

const a = create_test_session('a')
const b1 = create_test_session('b1')

const timeout = 400
const auction_name = new Date().getTime() + ''
const message_text = 'Test message'
const notice_text = 'Test notice'

describe('Bwp create auction', () => {

    afterAll(async done => {
        bwp_signoff(a.connector)
        await sleep(timeout)
        done()
    })

    it('connects', async done => {
        a.connector.connect()
        await sleep(timeout)
        expect(a.connector.is_connected()).toEqual(true)
        expect(a.connector.bwp_session_id).not.toBeNull()
        done()
    })

    it('auctioneer logs in', async done => {
        bwp_login(a.connector, {username: a.username, password: a.password})
        await sleep(timeout)
        expect(a.store.remote_user).not.toBeNull()
        done()
    })

    it('goto template page auction', async done => {
        bwp_gotoTemplatePage(a.connector)
        await sleep(timeout)
        expect(a.store.next_page.NEXT_PAGE).toBe('MRTemplatePage')
        done()
    })

    it('create auction', async done => {
        bwp_create_auction(a.connector, create_sample_auction(auction_name))
        await sleep(timeout)
        expect(a.store.auctions.find(
            (o: OnAuctionRow) => o.AUCTION_NAME === auction_name)
        ).not.toBeNull()
        done()
    })

    it('auctioneer selects auction', async done => {
        const auction_row = a.store.auctions.find(row => row.AUCTION_NAME === auction_name)
        expect(auction_row).not.toBeNull()
        bwp_selectAuction(a.connector, auction_row)
        await sleep(timeout)
        expect(a.store.current_auction.status.AUCTION_NAME).toEqual(auction_name)
        done()
    })

    it('getAvailableUsers', async done => {
        bpw_getAvailableUsers(a.connector)
        expect(a.store.traders_add_remove).toEqual({
            is_add: true,
            traders: []
        })
        await sleep(timeout)
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)
        done()
    })

    it('adds traders', async done => {
        expect(a.store.current_auction.blotter_bidder_rows).toEqual([])
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)
        bwp_addUsers(a.connector, a.store.traders_add_remove.traders.map(trader => trader.USERID))
        await sleep(timeout)
        expect(a.store.current_auction.blotter_bidder_rows.length)
            .toEqual(a.store.traders_add_remove.traders.length)
        done()
    })

    it('sets price', async done => {
        expect(a.store.current_auction.status.ROUND_PRICE).toEqual('0.0000')
        bwp_setPrice(a.connector, '1.0')
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_PRICE).toEqual('1.0000')
        expect(a.store.current_auction.blotter_header_rows.ROUNDPRICE.Round_1).toEqual('1.0000')
        done()
    })

    it('sends a message', async done => {
        expect(a.store.current_auction.messages).toEqual([])
        bwp_createMessage(a.connector, message_text)
        await sleep(timeout)
        expect(a.store.current_auction.messages.length).toEqual(1)
        expect(a.store.current_auction.messages[0].TEXT).toEqual(message_text)
        done()
    })

    it('edits the auction notice', async done => {
        bwp_setNotice(a.connector, notice_text)
        await sleep(timeout)
        bwp_getNotice(a.connector)
        await sleep(timeout)
        expect(a.store.current_auction.notice).toEqual(notice_text)
        done()
    })

    it('starts round', async done => {
        bwp_command(a.connector, MRCommand.GO)
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_CONTROLLER_STATUS).toEqual('ROUND_CLOCK_RUNNING')
        done()
    })

    it('auctioneer logs out', async done => {
        bwp_signoff(a.connector)
        await sleep(timeout)
        done()
    })


    it('b1 connects', async done => {
        b1.connector.connect()
        await sleep(timeout)
        expect(b1.connector.is_connected()).toEqual(true)
        expect(b1.connector.bwp_session_id).not.toBeNull()
        done()
    })


    it('b1 logs in', async done => {
        bwp_login(b1.connector, {username: b1.username, password: b1.password})
        await sleep(timeout)
        expect(b1.store.remote_user).not.toBeNull()
        done()
    })


    it('b1 selects auction', async done => {
        const auction_row = b1.store.auctions.find(row => row.AUCTION_NAME === auction_name)
        expect(auction_row).not.toBeNull()
        bwp_selectAuction(b1.connector, auction_row)
        await sleep(timeout)
        expect(b1.store.current_auction.status.AUCTION_NAME).toEqual(auction_name)
        expect(b1.store.trader.blinded).toEqual(false)
        done()
    })


    it('b1 bids', async done => {
        bwp_submitOrder(b1.connector, '12,000')
        await sleep(timeout)
        expect(b1.store.trader.bid_volume).toEqual('12,000')
        done()
    })


    it('b1 sends message', async done => {
        await sleep(timeout)
        done()
    })

    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })
})

