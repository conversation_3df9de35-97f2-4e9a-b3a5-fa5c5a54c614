import {MRCommand, Role} from "../../_generated/bwp-enums"
import {
    OnAuctionRow,
    OnAuctionUserRow,
    OnMrAuctionSettings,
} from '../../_generated/server_outputs'
import {BwpConnector, reload_page} from "./connector";
import {MrCreateAuction} from "../../_generated/server_inputs";
import { MrPage } from '../../components/Auction/__demo-helpers/MrPage'

const commands = {
    addUsers: {
        page: MrPage.AuctioneerPage,
        action: 'addUsers'
    },
    awardAuction: {
        page: MrPage.AwardPage,
        action: 'awardAuction'
    },
    calculateStandardAward: {
        page: MrPage.AwardPage,
        action: 'calculateStandardAward'
    },
    create_auction: {
        page: MrPage.TemplatePage,
        action: 'create'
    },
    hideAuction: {
        page: MrPage.AuctioneerPage,
        action: 'hideAuction',
    },
    unHideAuction: {
        page: MrPage.AuctioneerPage,
        action: 'unHideAuction',
    },
    createMessage: {
        page: MrPage.AuctioneerPage,
        action: 'createMessage'
    },
    createMessageAsTrader: {
        page: MrPage.TraderPage,
        action: 'createMessage'
    },
    command: {
        page: MrPage.AuctioneerPage,
        action: 'command'
    },
    deleteAuction: {
        page: MrPage.AuctioneerPage,
        action: 'deleteAuction'
    },
    deleteTemplate: {
        page: MrPage.TemplatePage,
        action: 'deleteTemplate'
    },
    deleteUser: {
        page: MrPage.UserPage,
        action: 'deleteUser'
    },
    navigateToAuctionFromAward: {
        page: MrPage.AuctioneerPage,
        action: 'init'
    },
    getAvailableUsers: {
        page: MrPage.AuctioneerPage,
        action: 'getAvailableUsers'
    },
    getCurrentUsers: {
        page: MrPage.AuctioneerPage,
        action: 'getCurrentUsers'
    },
    getNotice: {
        page: MrPage.AuctioneerPage,
        action: 'getNotice'
    },
    getTraderNotice: {
        page: MrPage.TraderPage,
        action: 'getNotice'
    },
    getSettings: {
        page: MrPage.AuctioneerPage,
        action: 'getSettings'
    },
    getTraderSettings: {
        page: MrPage.TraderPage,
        action: 'getSettings'
    },
    gotoTemplatePage: {
        page: MrPage.HomePage,
        action: 'gotoTemplatePage'
    },
    init_homePage: {
        page: MrPage.HomePage,
        action: 'init'
    },
    init_awardPage: {
        page: MrPage.AwardPage,
        action: 'init'
    },
    init_sessionPage: {
        page: MrPage.SessionPage,
        action: 'init'
    },
    init_userPage: {
        page: MrPage.UserPage,
        action: 'init'
    },
    login: {
        page: MrPage.LoginPage,
        action: 'login'
    },
    pageReloaded: {
        page: MrPage.SessionPage,
        action: 'pageReloaded'
    },
    removeUsers: {
        page: MrPage.AuctioneerPage,
        action: 'removeUsers'
    },
    renameTemplate: {
        page: MrPage.TemplatePage,
        action: 'renameTemplate'
    },
    saveSettings: {
        page: MrPage.AuctioneerPage,
        action: 'saveSettings'
    },
    saveTemplate: {
        page: MrPage.AuctioneerPage,
        action: 'saveTemplate'
    },
    selectAuction: {
        page: MrPage.HomePage,
        action: 'selectAuction'
    },
    setEligibility: {
        page: MrPage.AuctioneerPage,
        action: 'setEligibility'
    },
    setUser: {
        page: MrPage.UserPage,
        action: 'setUser'
    },
    signoff: {
        page: MrPage.SessionPage,
        action: 'signoff'
    },
    setNotice: {
        page: MrPage.AuctioneerPage,
        action: 'setNotice'
    },
    setPrice: {
        page: MrPage.AuctioneerPage,
        action: 'setPrice'
    },
    submitOrder: {
        page: 'TraderPage',
        action: 'submitOrder'
    }
}


export function ping_request() {
    // publish_socket(
    //   new Session_Ping_Request({
    //     CLIENT_TIMESTAMP: last_ping_timestamp,
    //     LAST_LATENCY    : last_ping_interval
    //   })
    // )
}

export function bwp_login(conn: BwpConnector, o: { username: string, password: string }) {
    conn.publish(commands.login, {
        USERNAME: o.username,
        PASSWORD: o.password
    })
}

export function bwp_signoff(conn: BwpConnector) {
    conn.publish(commands.signoff)
    reload_page(null)
}

export function bwp_selectAuction(conn: BwpConnector, row: { AUCTION_ROW_ID: number }) {
    conn.publish(commands.selectAuction, {
        AUCTIONID: row.AUCTION_ROW_ID
    })
}

export function bwp_initHomePage(conn: BwpConnector) {
    conn.publish(commands.init_homePage)
}

export function bwp_gotoTemplatePage(conn: BwpConnector) {
    conn.publish(commands.gotoTemplatePage)
}

export function bwp_create_auction(conn: BwpConnector, req: MrCreateAuction) {
    conn.publish(commands.create_auction, req)
}
export function hide_auction(conn: BwpConnector, a: OnAuctionRow) {
    conn.publish(commands.hideAuction, {AUCTIONID: a.AUCTION_ROW_ID})
}
export function unhide_auction(conn: BwpConnector, a: OnAuctionRow) {
    conn.publish(commands.unHideAuction, {AUCTIONID: a.AUCTION_ROW_ID})
}

export function bpw_getAvailableUsers(conn: BwpConnector) {
    // I hate having to mutate the store here, but unavoidable with the bwp system:
    conn.store.traders_add_remove.reset({is_add: true})
    conn.publish(commands.getAvailableUsers, {AUCTIONID: conn.store.current_auction.auctionid})
}

export function bwp_getCurrentUsers(conn: BwpConnector) {
    conn.store.traders_add_remove.reset({is_add: false})
    conn.publish(commands.getCurrentUsers, {AUCTIONID: conn.store.current_auction.auctionid})
}

export function bwp_removeUsers(conn: BwpConnector, userIds: number[]) {
    conn.publish(commands.removeUsers, userIds.reduce(
        (acc, USERID) => ({...acc, ['USERID_' + USERID]: true}),
        {AUCTIONID: conn.store.current_auction.auctionid}
    ))
}

export function bwp_addUsers(conn: BwpConnector, userIds: number[]) {
    conn.publish(commands.addUsers, userIds.reduce(
        (acc, USERID) => ({...acc, ['USERID_' + USERID]: true}),
        {AUCTIONID: conn.store.current_auction.auctionid}
    ))
}

export function bwp_getNotice(conn: BwpConnector) {
    conn.publish(commands.getNotice, {AUCTIONID: conn.store.current_auction.auctionid})
}

export function bwp_getTraderNotice(conn: BwpConnector) {
    conn.publish(commands.getTraderNotice, {AUCTIONID: conn.store.current_auction.auctionid})
}

export function bwp_setNotice(conn: BwpConnector, text: string) {
    conn.publish(commands.setNotice, {
        AUCTIONID: conn.store.current_auction.auctionid,
        NOTICE_TEXT: text
    })
}


export function bwp_setPrice(conn: BwpConnector, price: string) {
    conn.publish(commands.setPrice, {
        AUCTIONID: conn.store.current_auction.auctionid,
        ROUND_PRICE: price
    })
}

export function bwp_command(conn: BwpConnector, command: MRCommand) {
    conn.publish(commands.command, {
        AUCTIONID: conn.store.current_auction.auctionid,
        COMMAND: command
    })
}

export function bwp_initAwardPage(conn: BwpConnector) {
    conn.publish(commands.init_awardPage, {
        AUCTIONID: conn.store.current_auction.auctionid
    })
}

export function bwp_calculateStandardAward(conn: BwpConnector, target: string) {
    conn.publish(commands.calculateStandardAward, {
        AUCTIONID: conn.store.current_auction.auctionid,
        TARGET: target
    })
}

export function bwp_navigateToAuctionFromAward(conn: BwpConnector) {
    conn.publish(commands.navigateToAuctionFromAward, {
      AUCTIONID: conn.store.current_auction.auctionid,
    })
}

export function bwp_awardAuction(conn: BwpConnector,
                                 award_price: string,
                                 trader_awards: { USERID: number, award: string }[]) {
    conn.publish(commands.awardAuction, trader_awards.reduce(
        (acc, {USERID, award}) => ({...acc, ['USERID_' + USERID]: award}),
        {
            AUCTIONID: conn.store.current_auction.auctionid,
            AWARD_PRICE: award_price + ''
        }
    ))
}

export function bwp_deleteAuction(conn: BwpConnector, auction_row: { AUCTION_ROW_ID: number }) {
    conn.publish(commands.deleteAuction, {AUCTIONID: auction_row.AUCTION_ROW_ID})
}

export function bwp_pageReloaded(conn: BwpConnector) {
    conn.publish(commands.pageReloaded)
}

export function bwp_createMessage(conn: BwpConnector, text: string) {
    conn.publish(commands.createMessage, {
        AUCTIONID: conn.store.current_auction.status.AUCTIONID,
        TEXT: text
    })
}

export function bwp_createMessageAsTrader(conn: BwpConnector, text: string) {
    conn.publish(commands.createMessageAsTrader, {
        AUCTIONID: conn.store.current_auction.status.AUCTIONID,
        TEXT: text
    })
}

export function bwp_setEligibility(conn: BwpConnector, userid: number, eligibility: string) {
    conn.publish(commands.setEligibility, {
        AUCTIONID: conn.store.current_auction.auctionid,
        ELIGIBILITY: eligibility,
        USERID: userid
    })
}

export function bwp_getSettings(conn: BwpConnector) {
    conn.publish(commands.getSettings, {AUCTIONID: conn.store.current_auction.auctionid})
}

export function bwp_getTraderSettings(conn: BwpConnector) {
    conn.publish(commands.getTraderSettings, {AUCTIONID: conn.store.current_auction.auctionid})
}


export function bwp_saveSettings(conn: BwpConnector, onMrAuctionSettings: OnMrAuctionSettings) {
    // NOTE: very inconsistent pattern here, we save settings on client then broadcast
    // ie: change of settings IS NOT PUSHED TO CLIENTS AND OTHER AUCTIONEERS!! very bad!!
    // So: not exactly clear what happens if change of settings fails !
    // also: auctioneers and bidders need to re-select the auction to see changes, very bad!
    conn.publish(commands.saveSettings, onMrAuctionSettings)
}

export function bwp_submitOrder(conn: BwpConnector, bid_volume: string) {
    conn.publish(commands.submitOrder, {
        AUCTIONID: conn.store.current_auction.auctionid,
        BID_VOLUME: bid_volume
    })
}

export function bwp_initUserPage(conn: BwpConnector) {
    conn.publish(commands.init_userPage)
}


export function bwp_createUser(conn: BwpConnector, o: {
    COMPANY: string,
    EMAIL: string,
    MOBILE: string,
    PASSWORD: string,
    ROLE: Role,
    USERNAME: string,
    WORK: string,
}) {
    conn.publish(commands.setUser, {
        COMPANY: o.COMPANY,
        EMAIL: o.EMAIL,
        MOBILE: o.MOBILE,
        PASSWORD: o.PASSWORD,
        ROLE: o.ROLE,
        USERNAME: o.USERNAME,
        USERID: '',
        WORK: o.WORK
    })
}

export function bwp_editUser(conn: BwpConnector, o: {
    COMPANY: string,
    EMAIL: string,
    MOBILE: string,
    PASSWORD: string,
    ROLE: string,
    USERID: number,
    USERNAME: string,
    WORK: string,
}) {
    conn.publish(commands.setUser, {
        COMPANY: o.COMPANY,
        EMAIL: o.EMAIL,
        MOBILE: o.MOBILE,
        PASSWORD: o.PASSWORD,
        ROLE: o.ROLE,
        USERNAME: o.USERNAME,
        USERID: o.USERID,
        WORK: o.WORK
    })
}

export function bwp_deleteUser(conn: BwpConnector, o: { USERID: number }) {
    conn.publish(commands.deleteUser, o)
}

export function bwp_initSessionPage(conn: BwpConnector) {
    conn.publish(commands.init_sessionPage)
}

export function bwp_saveTemplate(conn: BwpConnector) {
    conn.publish(commands.saveTemplate, {
        AUCTIONID: conn.store.current_auction.auctionid
    })
}

export function bwp_deleteTemplate(conn:BwpConnector, o:{TEMPLATEID: number}) {
    conn.publish(commands.deleteTemplate, {TEMPLATEID: o.TEMPLATEID})
}

export function bwp_renameTemplate(conn:BwpConnector, o:{TEMPLATEID:number}, description:string) {
    conn.publish(commands.renameTemplate, {
        TEMPLATEID: o.TEMPLATEID,
        DESCRIPTION: description
    })
}
