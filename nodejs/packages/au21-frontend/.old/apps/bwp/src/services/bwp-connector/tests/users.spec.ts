import {
    bwp_createUser,
    bwp_deleteAuction,
    bwp_deleteUser,
    bwp_editUser,
    bwp_initUserPage,
    bwp_login,
    bwp_signoff,
} from '../publisher'
import { BwpStore } from '../../../store/store'
import { BwpConnector } from '../connector'
import { sleep } from '../../utils'
import { errors } from '../../event-bus'
import { last, random } from 'lodash'
import { Role } from '../../../_generated/bwp-enums'


const username = 'a'
const timeout = 300

const store = new BwpStore()
const conn = new BwpConnector(store)

describe('Bwp users spec', () => {

    afterAll(async done => {
        bwp_signoff(conn)
        await sleep(timeout)
        done()
    })

    it('connects', async done => {
        conn.connect()
        await sleep(timeout)
        expect(conn.is_connected()).toBe(true)
        expect(conn.bwp_session_id).not.toBeNull()
        done()
    })

    it('login', async done => {
        bwp_login(conn, {username: username, password: '1'})
        await sleep(timeout)

        // must delete all auctions else cannot delete users:
        store.auctions.forEach( a => bwp_deleteAuction(conn, a))
        await sleep(timeout)
        expect(store.remote_user).not.toBeNull()
        expect(store.auctions).toEqual([])

        done()
    })

    it('goto user page', async done => {
        bwp_initUserPage(conn)
        await sleep(timeout)
        expect(store.users).not.toEqual([])
        done()
    })

    it('username or password cannot be blank', async done => {
        await sleep(timeout)
        done()
    })

    it('username cannot be longer than 10 character', async done => {
        await sleep(timeout)
        done()
    })

    it('creates user', async done => {
        const current_user_count = store.users.length
        const new_username = random(999999) + ''
        bwp_createUser(conn, {
            COMPANY: '',
            EMAIL: '',
            MOBILE: '',
            PASSWORD: '1',
            ROLE: Role.TRADER,
            USERNAME: new_username,
            WORK: '',
        })
        await sleep(timeout)
        expect(store.users.length).toEqual(current_user_count + 1)
        const u = last(store.users)
        expect(u.PASSWORD).toEqual('1')
        expect(u.ROLE).toEqual('TRADER')
        expect(u.USERNAME).toEqual(new_username)
        done()
    })

    it('edits user', async done => {
        const u = {...last(store.users)}
        const new_password = random(10) + ''
        bwp_editUser(conn, {...u, PASSWORD: new_password})
        await sleep(timeout)
        expect(last(store.users).PASSWORD).toEqual(new_password)
        done()
    })

    it('deletes user', async done => {
        const user_count_pre = store.users.length
        const u = last(store.users)
        bwp_deleteUser(conn, {USERID: u.USERID})
        await sleep(timeout)
        expect(store.users.length).toEqual(user_count_pre - 1)
        expect(store.users.find(uu => uu.USERID == u.USERID)).toBeFalsy()
        done()
    })

    it('delete extra users', async done => {
        store.users.forEach(u => {
            if (!u.USERNAME.startsWith('b') && !u.USERNAME.startsWith('a')) {
                bwp_deleteUser(conn, u)
            }
        })
        await sleep(timeout)
        expect(store.users.find(u =>
            !u.USERNAME.startsWith('b') && !u.USERNAME.startsWith('a'))
        ).toBeFalsy()

        done()
    })

    it('has no alerts', async done => {
        await sleep(timeout)
        expect(store.alerts).toEqual([])
        done()
    })

    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })

})

