import axios from 'axios'
import * as <PERSON> from 'faye'
import {BwpStore} from "../../store/store";
import {error, LOG_MESSAGES} from "../event-bus";
import {is_development} from '../utils'
import isBrowser from 'is-browser'
import {bwp_pageReloaded} from "./publisher";

const APPNAME = 'Storage'
const FAYE_MOUNT = '/faye'
const FAYE_CONNECT = '/ConnectFaye'
const HANDLE = '/handle'

const url = is_development ? '/api' : ''

type BwpConnectionState =
    | 'initial'
    | 'connecting'
    | 'connected'
    | 'closed'

/*
OFF SWITCH:
this is set to true whenever we need to reload the page, ie:

a) latency > 15 seconds (set in this page)
b) server sends an OnReload message (store)

if this is set true then:
1) we don't send any commands, including pings
2) we don't renew the ping timer, or the server heartbeat latency
3) we don't respond to anything else from the server

*/
let off_switch = false

export function reload_page(reason:String) {
    off_switch = true
    if(reason) {
        alert(reason)
    }
    window.location.reload(true)
}

export class BwpConnector {

    constructor(public store: BwpStore) {
        this.set_last_message_interval()
    }

    current_state: BwpConnectionState = 'initial'

    last_message_time: number = new Date().getTime()
    last_message_interval_msec: number = 0

    is_connected(): boolean {
        return this.current_state === 'connected'
    }

    client: Faye = null
    private
    session_id: string = null

    get bwp_session_id() {
        return this.session_id
    }

    connect() {
        if (this.current_state !== 'initial') {
            error('unable to connect unless state = initial')
        }
        this.current_state = 'connecting'
        axios.get(url + FAYE_CONNECT + "?ts=" + new Date().getTime().toString())
            .then(response => {
                this.current_state = 'connected'
                this.session_id = response.data.SESSIONID
                this.store.session_id = this.session_id

                if(LOG_MESSAGES) {
                    console.log("Received session id from node: " + this.session_id)
                }
                this.client = new Faye.Client(url + FAYE_MOUNT)
                // Disabling transports, see: https://faye.jcoglan.com/browser.html
                this.client.disable('websocket');
                this.client.disable('callback-polling');
                // this.client.disable('long-polling');
                this.client.subscribe('/' + this.session_id, msg => {

                    if(off_switch){
                        return
                    }

                    try {
                        // EventBus.$emit(SERVER_EVENT, msg)
                        this.store.onEvent(msg)
                        if(LOG_MESSAGES) {
                            console.log("received:", msg)
                        }
                        this.last_message_time = new Date().getTime()
                    } catch (e) {
                        error('error on session id channel: ', {e})
                    }
                });

                this.client.subscribe('/time', time => {

                    if(off_switch){
                        return
                    }

                    try {
                        this.last_message_time = new Date().getTime()
                        // Prevent spam.
                        if (!(is_development && process.env.VUE_APP_NO_TIME_CONNECTION)) {
                            this.store.onEvent(JSON.parse(time))
                        }
                    } catch (e) {
                        error('error on channel /time:', {e})
                    }
                });

                this.ping() // start pinging

                // set the page unload handler:
                if (isBrowser) {
                    window.addEventListener('beforeunload', (e) => {
                        bwp_pageReloaded(this)
                    })
                }
            })
            .catch(err => {
                error(err)
            })
    }

    publish(command: { page: string, action: string }, o = {}) {

        if(off_switch){
            return
        }

        o['APPNAME'] = APPNAME
        o['SESSIONID'] = this.session_id
        o['CONTROLLER'] = command.page
        o['ACTION'] = command.action

        // if (!this.store.has_logged_in() && ['login' ,'ping', 'connect'].indexOf(command.action) < 0) {
        //     error('you must be logged in to perform action: ' + command.action)
        // }

        // const publication = client.publish('handle', o)
        // NOTE: USING XHR BECAUSE FAYE NOT PUBLISHING FOR SOME REASON
        if(LOG_MESSAGES && (command.page != "Ping")) {
            console.log("sending request:", o)
        }
        axios.post(url + HANDLE, o)
            .catch(err => {
                console.log(err)

            })
    }

    ping() {

        if(off_switch){
            return
        }

        this.publish({page: 'Ping', action: ''})
        setTimeout(() => {
            this.ping()
        }, 5000)
    }

    set_last_message_interval() {

        if(off_switch){
            return
        }

        // every time we get a message (/time or /sid) it sets the last_message_time in this object
        // then every second we calculate how long ago that was and set on store
        this.last_message_interval_msec = new Date().getTime() - this.last_message_time
        this.store.last_message_interval_secs = this.last_message_interval_msec / 1000

        if(this.last_message_interval_msec > 15_000){
            if(LOG_MESSAGES){
                console.log(`last_message_interval_msec=${this.last_message_interval_msec}`)
            }
            reload_page("session disconnected")
        }

        setTimeout(() => {
            this.set_last_message_interval()
        }, 1000)
    }


}
