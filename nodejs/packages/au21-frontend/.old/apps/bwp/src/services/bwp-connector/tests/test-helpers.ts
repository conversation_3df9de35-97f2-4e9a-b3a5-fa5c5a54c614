import {BwpStore} from "../../../store/store";
import {BwpConnector} from "../connector";

export interface Test_Session {
    username: string
    password: string
    store: BwpStore
    connector: BwpConnector
}

export function create_test_session(username: string): Test_Session {
    const store = new BwpStore()
    return {
        username,
        password: '1',
        store,
        connector: new BwpConnector(store)
    }
}
