import {bwp_login, bwp_signoff} from "../publisher";
import {BwpStore} from "../../../store/store";
import {BwpConnector} from "../connector";
import {sleep} from "../../utils";
import {errors} from "../../event-bus";


const username = 'a'
const timeout = 300

const store = new BwpStore()
const conn = new BwpConnector(store)

describe('Bwp Login Spec', () => {

    afterAll(async done => {
        bwp_signoff(conn)
        await sleep(timeout)
        done()
    })

    it('connects', async done => {
        expect(conn.current_state).toBe('initial')
        expect(conn.bwp_session_id).toBeNull()
        conn.connect()
        expect(conn.current_state).toBe('connecting')
        await sleep(timeout)

        expect(conn.is_connected()).toBe(true)
        expect(conn.bwp_session_id).not.toBeNull()
        done()
    })

    it('logs in', async done => {
        bwp_login(conn, {username: username, password: '1'})
        await sleep(timeout)
        expect(store.remote_user.username).toEqual(username)
        expect(store.remote_user.role).toEqual('AUCTIONEER')
        done()
    })

    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })

    it('logs in again', async done => {
        bwp_login(conn, {username: username, password: '1'})
        await sleep(timeout)
        expect(errors).toEqual([username + ' already signed in'])
        done()
    })


})

