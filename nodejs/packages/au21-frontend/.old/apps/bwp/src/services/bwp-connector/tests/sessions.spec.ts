import {bwp_initSessionPage, bwp_login, bwp_signoff} from "../publisher";
import {BwpStore} from "../../../store/store";
import {BwpConnector} from "../connector";
import {sleep} from "../../utils";
import {errors} from "../../event-bus";


const username = 'a'
const timeout = 300

const store = new BwpStore()
const conn = new BwpConnector(store)

describe('Bwp sessions page spec', () => {

    afterAll(async done => {
        bwp_signoff(conn)
        await sleep(timeout)
        done()
    })

    it('connects', async done => {
        conn.connect()
        await sleep(timeout)
        expect(conn.is_connected()).toBe(true)
        expect(conn.bwp_session_id).not.toBeNull()
        done()
    })

    it('login', async done => {
        bwp_login(conn, {username: username, password: '1'})
        await sleep(timeout)
        expect(store.remote_user).not.toBeNull()
        done()
    })

    it('init session page', async done => {
        bwp_initSessionPage(conn)
        await sleep(timeout)
        expect(store.sessions).not.toEqual([])
        done()
    })

    it('has no alerts', async done => {
        await sleep(timeout)
        expect(store.alerts).toEqual([])
        done()
    })

    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })

})

