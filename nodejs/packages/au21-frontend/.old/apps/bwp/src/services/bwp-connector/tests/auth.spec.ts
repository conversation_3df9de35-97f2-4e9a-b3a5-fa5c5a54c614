import {bwp_selectAuction, bwp_signoff} from "../publisher";
import {BwpStore} from "../../../store/store";
import {BwpConnector} from "../connector";
import {sleep} from "../../utils";
import {errors} from "../../event-bus";


const username = 'a'
const timeout = 300

const store = new BwpStore()
const conn = new BwpConnector(store)

describe('Bwp Auth Spec', () => {

    afterAll(async done => {
        bwp_signoff(conn)
        await sleep(timeout)
        done()
    })

    it('connects', async done => {
        expect(conn.current_state).toBe('initial')
        expect(conn.bwp_session_id).toBeNull()
        conn.connect()
        expect(conn.current_state).toBe('connecting')
        await sleep(timeout)

        expect(conn.is_connected()).toBe(true)
        expect(conn.bwp_session_id).not.toBeNull()
        done()
    })

    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })


    // it('fails to select auction', done => {
    //     expect(() =>
    //         bwp_selectAuction(conn, {AUCTION_ROW_ID: 1})
    //     ).toThrowError("you must be logged in to perform action: selectAuction")
    //     done()
    // })



})

