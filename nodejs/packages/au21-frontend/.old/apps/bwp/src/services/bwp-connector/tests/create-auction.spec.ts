import {
    bpw_getAvailableUsers,
    bwp_addUsers,
    bwp_create_auction,
    bwp_createMessage,
    bwp_deleteTemplate,
    bwp_getCurrentUsers,
    bwp_getNotice,
    bwp_getSettings,
    bwp_gotoTemplatePage,
    bwp_login,
    bwp_removeUsers,
    bwp_renameTemplate,
    bwp_saveSettings,
    bwp_saveTemplate,
    bwp_selectAuction,
    bwp_setEligibility,
    bwp_setNotice,
    bwp_setPrice,
    bwp_signoff
} from "../publisher";
import {create_test_session} from "./test-helpers";
import {
    OnAuctionRow,
    OnMrAuctionSettings,
} from '../../../_generated/server_outputs'
import {create_sample_auction} from "../../../_generated/server_inputs";
import {sleep} from "../../utils";
import {errors} from "../../event-bus";


const a = create_test_session('a')

const timeout = 400
const auction_name = new Date().getTime() + ''

describe('Bwp create auction', () => {

    afterAll(async done => {
        bwp_signoff(a.connector)
        await sleep(timeout)
        done()
    })

    it('connects', async done => {
        a.connector.connect()
        await sleep(timeout)
        expect(a.connector.is_connected()).toEqual(true)
        expect(a.connector.bwp_session_id).not.toBeNull()
        done()
    })

    it('log in', async done => {
        bwp_login(a.connector, {username: a.username, password: a.password})
        await sleep(timeout)
        expect(a.store.remote_user).not.toBeNull()
        done()
    })

    it('goto template page', async done => {
        bwp_gotoTemplatePage(a.connector)
        await sleep(timeout)
        expect(a.store.next_page.NEXT_PAGE).toBe('MRTemplatePage')
        done()
    })

    it('create auction', async done => {
        bwp_create_auction(a.connector, create_sample_auction(auction_name))
        await sleep(timeout)
        expect(a.store.auctions.find(
            (o: OnAuctionRow) => o.AUCTION_NAME === auction_name)
        ).not.toBeNull()
        expect(a.store.auctions.length).toBeGreaterThan(0)
        done()
    })

    it('select auction', async done => {
        expect(a.store.auctions.length).toBeGreaterThan(0)
        const auction_row = a.store.auctions.find(row => row.AUCTION_NAME === auction_name)
        expect(auction_row).not.toBeNull()
        bwp_selectAuction(a.connector, auction_row)
        await sleep(timeout)
        expect(a.store.current_auction.status.AUCTION_NAME).toEqual(auction_name)
        done()
    })

    it('getAvailableUsers', async done => {
        bpw_getAvailableUsers(a.connector)
        expect(a.store.traders_add_remove).toEqual({
            is_add: true,
            traders: []
        })
        await sleep(timeout)
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)
        done()
    })

    it('adds traders', async done => {
        expect(a.store.current_auction.blotter_bidder_rows).toEqual([])
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)
        bwp_addUsers(a.connector, a.store.traders_add_remove.traders.map(trader => trader.USERID))
        await sleep(timeout)
        expect(a.store.current_auction.blotter_bidder_rows.length)
            .toEqual(a.store.traders_add_remove.traders.length)
        done()
    })

    it('gets current traders', async done => {
        bwp_getCurrentUsers(a.connector)
        expect(a.store.traders_add_remove).toEqual({
            is_add: false,
            traders: []
        })
        await sleep(timeout)
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)
        done()
    })

    it('removes traders', async done => {
        bwp_removeUsers(a.connector, a.store.traders_add_remove.traders.map(trader => trader.USERID))
        await sleep(timeout)
        expect(a.store.current_auction.blotter_bidder_rows).toEqual([])
        done()
    })

    it('getAvailableUsers again', async done => {
        bpw_getAvailableUsers(a.connector)
        expect(a.store.traders_add_remove).toEqual({
            is_add: true,
            traders: []
        })
        await sleep(timeout)
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)
        done()
    })

    it('adds traders again', async done => {
        expect(a.store.current_auction.blotter_bidder_rows).toEqual([])
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)
        bwp_addUsers(a.connector, a.store.traders_add_remove.traders.map(trader => trader.USERID))
        await sleep(timeout)
        expect(a.store.current_auction.blotter_bidder_rows.length)
            .toEqual(a.store.traders_add_remove.traders.length)
        done()
    })

    it('sets price', async done => {
        expect(a.store.current_auction.status.ROUND_PRICE).toEqual('0.0000')
        bwp_setPrice(a.connector, '1.0')
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_PRICE).toEqual('1.0000')
        expect(a.store.current_auction.blotter_header_rows.ROUNDPRICE.Round_1).toEqual('1.0000')
        done()
    })

    it('sends a message', async done => {
        expect(a.store.current_auction.messages).toEqual([])
        bwp_createMessage(a.connector, 'Test message')
        await sleep(timeout)
        expect(a.store.current_auction.messages.length).toEqual(1)
        expect(a.store.current_auction.messages[0].TEXT).toEqual('Test message')
        done()
    })

    it('edits the auction notice', async done => {
        bwp_setNotice(a.connector, 'Test notice')
        await sleep(timeout)
        bwp_getNotice(a.connector)
        await sleep(timeout)
        expect(a.store.current_auction.notice).toEqual('Test notice')
        expect(a.store.alerts[0].BODY).toEqual('Notice saved')

        done()
    })

    it('changes traders eligibility', async done => {
        const userid = a.store.current_auction.blotter_bidder_rows[0].OID
        bwp_setEligibility(a.connector, userid, '200,000')
        await sleep(timeout)
        const bidder_row = a.store.current_auction.blotter_bidder_rows.find(row =>
            row.OID == userid)
        expect(bidder_row.ELIGIBILITY).toEqual('200,000')
        done()
    })

    it('gets settings', async done => {
        bwp_getSettings(a.connector)
        await sleep(timeout)
        expect(a.store.current_auction.settings).not.toBeNull()
        done()
    })

    it('changes some auction settings', async done => {
        expect(a.store.current_auction.settings.INITIAL_ROUND_DURATION).not.toEqual('90')
        const auctionUpdated = Object.assign(
          new OnMrAuctionSettings(),
          a.store.current_auction.settings,
          {INITIAL_ROUND_DURATION: '90'}
          )
        bwp_saveSettings(a.connector, auctionUpdated)
        await sleep(timeout)

        // Backend does only provide us with `OnAlert: Settings saved` on save
        // and that's it. So we have to get settings once again to figure
        // if save was a success.
        bwp_getSettings(a.connector)
        await sleep(timeout)

        expect(a.store.current_auction.settings.INITIAL_ROUND_DURATION).toEqual(90)
        done()
    })

    it('saves, renames and deletes template', async done => {
        a.store.templates.forEach(t => bwp_deleteTemplate(a.connector, {TEMPLATEID: t.TEMPLATEID}))
        bwp_gotoTemplatePage(a.connector)
        await sleep(timeout)
        expect(a.store.templates).toEqual([])

        bwp_selectAuction(a.connector, {AUCTION_ROW_ID: a.store.current_auction.auctionid})
        bwp_saveTemplate(a.connector)
        bwp_gotoTemplatePage(a.connector)
        await sleep(timeout)
        expect(a.store.templates.length).toEqual(1)

        const description = 'new description'
        bwp_renameTemplate(a.connector,
            a.store.templates[0],
            description)
        await sleep(timeout)
        expect(a.store.templates[0].DESCRIPTION).toEqual(description)

        done()
    })


    it('renames template', async done => {
        await sleep(timeout)
        done()
    })


    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })


})

