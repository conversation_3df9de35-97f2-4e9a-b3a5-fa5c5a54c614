import {bwp_deleteAuction, bwp_login, bwp_signoff} from "../publisher";
import {BwpStore} from "../../../store/store";
import {BwpConnector} from "../connector";
import {sleep} from "../../utils";
import {errors} from "../../event-bus";


const username = 'a'
const timeout = 300

const store = new BwpStore()
const conn = new BwpConnector(store)

describe('Delete Auctions', () => {

    afterAll(async done => {
        bwp_signoff(conn)
        await sleep(timeout)
        done()
    })

    it('connects', async done => {
        conn.connect()
        await sleep(timeout)
        expect(conn.is_connected()).toBe(true)
        expect(conn.bwp_session_id).not.toBeNull()
        done()
    })

    it('login', async done => {
        bwp_login(conn, {username: username, password: '1'})
        await sleep(timeout)
        expect(store.remote_user).not.toBeNull()
        done()
    })

    it('delete auctions', async done => {
        store.auctions.forEach((a) => bwp_deleteAuction(conn, a))
        await sleep(timeout)
        expect(store.auctions.length).toEqual(0)
        done()
    })

    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })

})

