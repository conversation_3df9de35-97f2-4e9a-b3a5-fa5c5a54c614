import Vue from 'vue'
import {Oidable} from "../store/model";
// import diff from 'jest-diff'
// import {error} from "./event-bus";

export const sleep = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
}

export function compareTypes(a, b) {
    // Did broke IE10 because babel didn't transpile root node_modules
    return
    // const result = diff(
    //     Object.keys(a).sort(),
    //     Object.keys(b).sort())

    // if (result.indexOf('Compared values have no visual difference.') < 0) {
    //     console.log(result)
    //     error('objects should be equal ' + (a.EVENT ? a.EVENT : ''))
    // }
}

export function is_blank(o: null | undefined | string): boolean {
    switch (o) {
        case null:
            return true
        case 'undefined': // TODO: not sure if this works ??
            return true
        case '':
            return true
        default:
            return false
    }
}

export const is_development =
    process.env.NODE_ENV === 'development'

export function find_by_oid(list: Oidable[], oid) {
    return list.find(o => o.OID == oid)
}

export function remove_by_oid(list: Oidable[], oid): void {
    const existing = find_by_oid(list, oid)
    if (existing) {
        list.splice(list.indexOf(existing), 1)
    }
}


// export function reify(o:any):any{
//     return JSON.parse(JSON.stringify(o))
// }


/* from: https://blog.usejournal.com/reactive-window-parameters-in-vuejs-fc5de75d7ab5
Usage:

// AppNav.vue
import WindowInstanceMap from './WindowInstanceMap.js'
export default {
  computed: {
    scrollY () { return WindowInstanceMap.scrollY }
  }
}

see here for throttler: https://developer.mozilla.org/en-US/docs/Web/Events/resize
see example with requestanimation frame

(function() {

  window.addEventListener("resize", resizeThrottler, false);

  var resizeTimeout;
  function resizeThrottler() {
    // ignore resize events as long as an actualResizeHandler execution is in the queue
    if ( !resizeTimeout ) {
      resizeTimeout = setTimeout(function() {
        resizeTimeout = null;
        actualResizeHandler();

       // The actualResizeHandler will execute at a rate of 15fps
       }, 66);
    }
  }

  function actualResizeHandler() {
    // handle the resize event
    ...
  }

}());

*/

const WindowInstanceMap = new Vue({
    data() {
        return {
            height: 0,
            width: 0,
            scrollbar_size: 0
        }
    },
    created() {

        // 1) window size:
        // TODO: throttle
        const resize = () => {
            this.height = window.innerHeight
            this.width = window.innerWidth
        }
        window.addEventListener("resize", resize)
        resize()

        // 2) scrollbar size:
        this.scrollbar_size = (() => {
            let div1, div2;

            div1 = window.document.createElement("div");
            div2 = window.document.createElement("div");

            div1.style.width = "100px";
            div1.style.overflowX = "scroll";
            div2.style.width = "100px";

            window.document.body.appendChild(div1);
            window.document.body.appendChild(div2);

            const _size = div1.offsetHeight - div2.offsetHeight;

            window.document.body.removeChild(div1);
            window.document.body.removeChild(div2);
            return _size
        })()
    },
})

