import {
    bpw_getAvailableUsers,
    bwp_addUsers,
    bwp_awardAuction,
    bwp_calculateStandardAward,
    bwp_command,
    bwp_create_auction,
    bwp_deleteAuction,
    bwp_initAwardPage,
    bwp_login,
    bwp_selectAuction,
    bwp_setPrice,
    bwp_signoff,
    bwp_submitOrder,
} from '../publisher'
import { create_test_session } from './test-helpers'
import { OnAuctionRow } from '../../../_generated/server_outputs'
import { create_sample_auction } from '../../../_generated/server_inputs'
import { sleep } from '../../utils'
import { errors } from '../../event-bus'
import { MRCommand } from '../../../_generated/bwp-enums'


const a = create_test_session('a')
const b1 = create_test_session('b1')
const b2 = create_test_session('b2')
const b3 = create_test_session('b3')

const bidders = [b1, b2, b3]

const timeout = 300

const auction_name = new Date().getTime() + ''


describe('Auction Scenario One', () => {

    afterAll(async done => {
        bwp_signoff(a.connector)

        bidders.forEach(s => {
            if (s.store.has_logged_in()) {
                bwp_signoff(s.connector)
            }
        })
        await sleep(timeout)
        done()
    })

    it('auctioneer connects and log in', async done => {
        a.connector.connect()
        await sleep(timeout)
        expect(a.connector.is_connected()).toBeTruthy()

        bwp_login(a.connector, {username: a.username, password: a.password})
        await sleep(timeout)
        expect(a.connector.store.has_logged_in()).toBeTruthy()

        a.store.auctions.forEach(row => bwp_deleteAuction(a.connector, row))
        await sleep(timeout)
        expect(a.store.auctions).toEqual([])

        done()
    })

    it('auctioneer creates an auction, adds traders, sets price', async done => {
        bwp_create_auction(a.connector, create_sample_auction(auction_name))
        await sleep(timeout)

        const row = a.store.auctions.find(
            (o: OnAuctionRow) => o.AUCTION_NAME === auction_name)

        expect(row).not.toBeNull()

        bwp_selectAuction(a.connector, row)
        await sleep(timeout)
        expect(a.store.current_auction.status.AUCTION_NAME).toEqual(auction_name)

        bpw_getAvailableUsers(a.connector)
        expect(a.store.traders_add_remove.traders).toEqual([])
        await sleep(timeout)
        expect(a.store.traders_add_remove.traders.length).toBeGreaterThan(0)

        bwp_addUsers(a.connector, a.store.traders_add_remove.traders.map(trader => trader.USERID))
        await sleep(timeout)
        expect(a.store.current_auction.blotter_bidder_rows.length).toEqual(
            a.store.traders_add_remove.traders.length)

        bwp_setPrice(a.connector, '10.0000')
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_PRICE).toEqual('10.0000')

        done()
    })

    it('bidders connect, log in and select auction', async done => {
        bidders.forEach(b => b.connector.connect())
        await sleep(timeout)
        bidders.forEach(b => expect(b.connector.is_connected()).toBeTruthy())

        bidders.forEach(b => bwp_login(b.connector, {username: b.username, password: b.password}))
        await sleep(timeout)
        bidders.forEach(b => expect(b.connector.store.has_logged_in()).toBeTruthy())

        bidders.forEach(b => bwp_selectAuction(b.connector, b.store.auctions[0]))
        await sleep(timeout)
        bidders.forEach(b => expect(b.store.current_auction).not.toBeNull())

        done()
    })

    it('auctioneer runs first round', async done => {
        bwp_command(a.connector, MRCommand.GO)
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_CONTROLLER_STATUS).toEqual('ROUND_CLOCK_RUNNING')

        bidders.forEach(b => bwp_submitOrder(b.connector, '100,000'))
        await sleep(timeout)
        expect(a.store.current_auction.blotter_header_rows.ACTIVITY['Round_1']).toEqual('H')
        expect(a.store.current_auction.blotter_header_rows.TOTALVOL['Round_1']).toEqual('300,000')

        bwp_command(a.connector, MRCommand.PAUSE)
        bwp_command(a.connector, MRCommand.END_ROUND)
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_CONTROLLER_STATUS).toEqual('ROUND_CLOSED')

        done()
    })

    it('auctioneer runs second round', async done => {
        bwp_command(a.connector, MRCommand.NEXT_ROUND)
        bwp_command(a.connector, MRCommand.GO)
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_CONTROLLER_STATUS).toEqual('ROUND_CLOCK_RUNNING')

        bidders.forEach(b => bwp_submitOrder(b.connector, '10,000'))
        await sleep(timeout)
        expect(a.store.current_auction.blotter_header_rows.ACTIVITY['Round_2']).toEqual('L')
        expect(a.store.current_auction.blotter_header_rows.TOTALVOL['Round_2']).toEqual('30,000')

        bwp_command(a.connector, MRCommand.PAUSE)
        await sleep(timeout)
        bwp_command(a.connector, MRCommand.END_ROUND)
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_CONTROLLER_STATUS).toEqual('ROUND_CLOSED')

        done()
    })

    it('auctioneer awards the auction', async done => {
        bwp_initAwardPage(a.connector)
        bwp_calculateStandardAward(a.connector, '90,000')
        bwp_awardAuction(
            a.connector,
            a.store.current_auction.status.ROUND_PRICE,
            a.store.award_rows.map(row => ({
                USERID: row.OID,
                award: '10,000'
            })))
        await sleep(timeout)
        expect(a.store.current_auction.status.ROUND_CONTROLLER_STATUS).toEqual("AUCTION_CLOSED")
        done()
    })

    it('has no errors', async done => {
        await sleep(timeout)
        expect(errors).toEqual([])
        done()
    })

// it('bidders each bid', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer closes the round and starts the next round', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer edits b1 eligibility', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('bidders all bid', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer stops the rounds and ends the round', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer awards the auction', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer deletes the auction', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer creates bidder', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer edits bidder', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })
//
// it('auctioneer deletes bidder', done => {
//     setTimeout(() => {
//         done()
//     }, timeout)
// })


})

