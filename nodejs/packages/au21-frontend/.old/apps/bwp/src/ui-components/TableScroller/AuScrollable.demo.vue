<template>
  <div>
    <div style="color: white;">
      <story-slider
        label="left"
        :min="0"
        :max="100"
        :value.sync="left"/>
      <story-slider
        label="top"
        :min="0"
        :max="100"
        :value.sync="top"/>
      <story-slider
        label="scroll_left"
        :min="0"
        :max="100"
        :value.sync="scroll_left"/>
      <story-slider
        label="scroll_top"
        :min="0"
        :max="100"
        :value.sync="scroll_top"/>
    </div>
    <scrollable style="color:green"
                :left="left"
                :top="top"
                :table_width="100"
                :table_height="100"
                :content_width="1000"
                :content_height="1000"
                :scroll_left="scroll_left"
                :scroll_top="scroll_top">

      {{text}}
    </scrollable>
  </div>
</template>

<script lang="ts">
import StorySlider from '../VuebookHelpers/StorySlider.vue'
import { range } from 'lodash'
import { Component, Vue } from 'vue-property-decorator'
import { default as Scrollable } from './AuScrollable.vue'

@Component({
  components: {
    Scrollable,
    StorySlider,
  },
})
export default class AuScrollableDemo extends Vue {

  text = range(100).map(i => 'Lorem ipsum dolor').join('')

  scroll_left = 0
  scroll_top = 0
  left = 0
  top = 0
}
</script>


<style scoped
       lang="less">

</style>
