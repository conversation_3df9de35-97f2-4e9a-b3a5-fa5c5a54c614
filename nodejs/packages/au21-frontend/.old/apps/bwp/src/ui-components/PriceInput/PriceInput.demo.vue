<template>
  <VbDemo>
    <VbCard title="thousand separators string">
      <PriceInput v-model="valueDefault"/>
      <p>{{valueDefault}}</p>
    </VbCard>
    <VbCard title="emptyValue">
      <PriceInput v-model="emptyValue"/>
      <p>{{emptyValue}}</p>
    </VbCard>
    <VbCard title="undefinedValue (broken)" color="tomato">
      <PriceInput v-model="undefinedValue"/>
      <p>{{undefinedValue}}</p>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import PriceInput from './PriceInput.vue'

@Component({
  components: { PriceInput },
})
export default class PriceInputDemo extends Vue {
  valueDefault = '10,000,000'
  emptyValue = ''
  nullValue = null
  undefinedValue = undefined
}
</script>
