<template>
  <VbDemo>
    <VbCard>
      <Blinker :value="value">{{value}}</Blinker>
    </VbCard>
    <VbCard>
      <button @click="refresh">Change value</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import Blinker from './Blinker.vue'

export default {
  components: {
    Blinker,
  },
  data () {
    return {
      value: Math.floor(Math.random() * 100000),
    }
  },
  methods: {
    refresh () {
      this.value = Math.floor(Math.random() * 100000)
    },
  },
}
</script>
