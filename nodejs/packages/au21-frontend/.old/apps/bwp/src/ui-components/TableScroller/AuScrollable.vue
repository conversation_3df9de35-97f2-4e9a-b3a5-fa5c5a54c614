<template>
    <div class="AuScrollable" style="position:relative">

        <div :style="outer"
             ref="outer">

            <div :style="inner">
                <slot/>
            </div>

            <!--
                <div :style="drag_layer_style"
                     @mousedown="mousedown">
                </div>
            -->
        </div>
    </div>
</template>

<script lang="ts">
    import {Component, Prop, Vue, Watch} from 'vue-property-decorator'

    @Component({components: {}})
    export default class AuScrollable extends Vue {

        @Prop({required: true}) table_width: number
        @Prop({required: true}) table_height: number

        @Prop({default: null}) content_width: null | number
        @Prop({default: null}) content_height: null | number

        @Prop({default: 0}) left: number
        @Prop({default: 0}) top: number

        @Prop({default: 0}) scroll_left: number
        @Prop({default: 0}) scroll_top: number
        
        @Prop() containerStyle: Record<string, string>

        get outer() {
            return {
                border: 0,
                height: this.table_height + 'px',
                left: this.left + 'px',
                margin: 0,
                overflow: 'hidden',
                padding: 0,
                position: 'absolute',
                top: this.top + 'px',
                width: this.table_width + 'px',
                ...this.containerStyle,
            }
        }

        get inner() {
            return {
                border: 0,
                height: (this.content_height || this.table_height) + 'px',
                margin: 0,
                padding: 0,
                position: 'relative',
                width: (this.content_width || this.table_width) + 'px',
            }
        }

        @Watch('scroll_left')
        on_left(val, old) {
            window.requestAnimationFrame(() => {
                [this.$refs['outer']].forEach((it: HTMLElement) => it.scrollLeft = val)
            })
        }

        @Watch('scroll_top')
        on_top(val, old) {
            window.requestAnimationFrame(() => {
                [this.$refs['outer']].forEach((it: HTMLElement) => it.scrollTop = val)
            })
        }

        /*

        get drag_layer_style(){
          return {
            //"background-color": "red",
            position: "absolute",
            top     : 0,
            left    : 0,
            width   : this.table_width + "px",
            height  : this.table_height + "px"
          };
        }

        last_mouse_down = { x: 0, y: 0 };

        mousedown( e: MouseEvent ){
          // added to catch mouseup outside of scrollbar viewport
          document.addEventListener("mouseup", this.mouseup);
          document.addEventListener("mousemove", this.mousemove);
          this.last_mouse_down = {
            x: e.clientX,
            y: e.clientY
          };
        }


        mousemove( e: MouseEvent ){

          const current_mouse = {
            x: e.clientX,
            y: e.clientY
          };

          const movement = {
            x: current_mouse.x - this.last_mouse_down.x,
            y: current_mouse.y - this.last_mouse_down.y
          };

          this.last_mouse_down = {
            x: current_mouse.x,
            y: current_mouse.y
          };

          if( this.content_width ){
            this.$emit("update:scroll_left", bounded({
              min  : 0,
              max  : this.content_width - this.table_width,
              value: this.scroll_left - movement.x
            }));
          }

          if( this.content_height ){
            this.$emit("update:scroll_top", bounded({
              min  : 0,
              max  : this.content_height - this.table_height,
              value: this.scroll_top - movement.y
            }));
          }

        }

        mouseup( e: MouseEvent ){
          document.removeEventListener("mouseup", this.mouseup);
          document.removeEventListener("mousemove", this.mousemove);
        }
        */
    }
</script>


<style lang="less">

</style>
