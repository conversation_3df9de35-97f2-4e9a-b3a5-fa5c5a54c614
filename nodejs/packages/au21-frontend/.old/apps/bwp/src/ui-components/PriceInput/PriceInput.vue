<template>
  <a-input-number
    :size="size"
    v-model="valueProxy"
    :formatter="formatNumber"
    :parser="parseNumber"
    :min="0"
    @keyup="$emit('keyup', $event)"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({})
export default class PriceInput extends Vue {
  @Prop({default: 'small'}) size: 'small' | 'default'
  @Prop() value: any
  get valueProxy (): string {
    let value = this.value
    if (value === null) {
      value = ''
    }
    if (value === undefined) {
      value = ''
    }
    return `${value}`.replace(/\$\s?|(,*)/g, '')
  }
  set valueProxy (value: string) {
    if (value === undefined) {
      value = ''
    }
    this.$emit('input', value)
  }

  formatNumber (value): string {
    return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  parseNumber (value): string {
    return value.replace(/\$\s?|(,*)/g, '')
  }
}
</script>
