<template>
  <VbDemo>
    <VbCard>
      <HtmlEditor
        style="width: 700px; height: 500px"
        v-model="value"
      />
    </VbCard>
    <VbCard>
      <code>{{ value }}</code>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import HtmlEditor from './HtmlEditor.vue'

export default {
  components: {
    HtmlEditor,
  },
  data () {
    return {
      value: `<h1 class="ql-align-center">
                    <span class="ql-font-serif"><span class="ql-cursor">﻿</span>I am Example 2!</span></span>
                  </h1>
                  <p><br></p>
                  <p><span class="ql-font-serif">Whenever you play the game of thrones, you either win or die. There is no middle ground.</span></p>
                  <p><br></p>
                  <p><strong class="ql-font-serif">Some war against sword and spear to win, and the others the crow and the paper to win.</strong></p>
                  `,
    }
  },
}
</script>
