<template>
  <quill-editor
    class="HtmlEditor"
    ref="myTextEditor"
    v-model="valueProxy"
    :options="editorOptions"
  />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component({})
export default class HtmlEditor extends Vue {
  @Prop({ type: String }) value: string
  editorOptions = {
    placeholder: '',
    modules: {
      toolbar: [
        [{ 'font': [] }, { 'size': ['small', false, 'large', 'huge'] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'align': [] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['link'],
      ],
    },
  }

  get valueProxy () {
    return this.value
  }

  set valueProxy (value) {
    this.$emit('input', value)
  }
}
</script>

<style lang="less">
@import '../../assets/variables.less';

.HtmlEditor {
  height: 100%;

  .ql-toolbar {
    background-color: @au-background-very-light;
  }

  .ql-container {
    height: calc(100% - 40px);
    background-color: @au-background-input;
  }
}
</style>
