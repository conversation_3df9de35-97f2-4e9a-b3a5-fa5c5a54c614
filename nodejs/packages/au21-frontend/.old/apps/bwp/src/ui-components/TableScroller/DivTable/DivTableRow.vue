<template>
  <div
    class="DivTableRow"
    :style="{
      height: height + 'px'
    }"
  >
    <slot/>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({})
export default class DivTableRow extends Vue {
  @Prop({ required: true, type: Number }) height: number

  get widthSetter (): (column) => number {
    return (this.$parent as any).widthSetter
  }

  get columns (): any[] {
    return (this.$parent as any).columns
  }

  getWidthForSlot (slotComponent) {
    const slotIndex = this.$slots.default.findIndex(slot => slot.componentInstance === slotComponent)
    return this.widthSetter(this.columns[slotIndex])
  }
}
</script>

<style lang="less">
.DivTableRow {
  width: 100%;
  white-space: nowrap;
  transition: background-color ease .3s;
}
</style>
