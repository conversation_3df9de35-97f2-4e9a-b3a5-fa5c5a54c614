<template>
  <VbDemo>
    <VbCard title="Controls">
      <story-slider
        label="first_column_width"
        :max="200"
        v-model="first_column_width"
      />
      <story-slider
        label="header_height"
        :max="200"
        v-model="header_height"
      />
      <story-slider
        label="footer_height"
        :max="200"
        v-model="footer_height"
      />
      <story-slider
        label="cell_height"
        v-model="cell_height"
      />
      <story-slider
        label="visible_columns"
        :max="10"
        v-model="visible_columns"
      />
      <story-slider
        label="table_height"
        :min="300"
        :max="800"
        v-model="table_height"
      />
      <story-slider
        label="trader_count"
        :min="0"
        :max="200"
        :value.sync="trader_count"
      />
      <story-slider
        label="round_count"
        :min="0"
        :max="50"
        :value.sync="round_count"
      />

      <button @click="$refs.scrollableTableStepped.scrollLeft()">Left</button>
      <button @click="$refs.scrollableTableStepped.scrollRight()">Right</button>
      <button @click="$refs.scrollableTableStepped.scrollTop()">Top</button>
      <button @click="$refs.scrollableTableStepped.scrollBottom()">Bottom</button>
    </VbCard>

    <VbCard>
      <story-slider
        label="Scroll to column"
        :min="1"
        :max="50"
        v-model="columnToScroll"
      />
      <button @click="$refs.scrollableTableStepped.scrollToColumn(columnToScroll)">Scroll</button>
    </VbCard>

    <VbCard>
      <AuScrollableTableStepped
        ref="scrollableTableStepped"
        :table_width="500"
        :table_height="table_height"
        :first_column_width="first_column_width"
        :header_height="header_height"
        :footer_height="footer_height"
        :cell_height="cell_height"
        :visible_columns="visible_columns"
        :columnWidthSetter="column => 50"
        :columns="columns"
        :header_rows="header_rows"
        :rows="rows"
        :footer_rows="footer_rows"
      >
        <!-- fixed column -->

        <template
          slot="fixed_header_cell"
          slot-scope="{row}"
        >
          fixed header cell
        </template>

        <template
          slot="fixed_body_cell"
          slot-scope="{row}"
        >
          {{row.id}}
        </template>
        <template slot="fixed_footer_cell">
          fixed footer
        </template>

        <!-- scrollable column -->
        <template
          slot="scrolling_header_cell"
          slot-scope="{row, column}"
        >
          {{ row && row.id }}
        </template>

        <template
          slot="scrolling_body_cell"
          slot-scope="{row, column}"
        >
          {{column.label}}
          <br>
          {{row.id}}
        </template>

        <template
          slot="scrolling_footer_cell"
          slot-scope="{column}"
        >
          <div>
            {{column.label}}
          </div>
          <div v-for="(row, row_index) in footer_rows">
            {{row.id}}
          </div>
        </template>
      </AuScrollableTableStepped>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">

import AuScrollableTableStepped from './AuScrollableTableStepped.vue'
import { Column, Row } from './helpers'
import StorySlider from '../VuebookHelpers/StorySlider.vue'
import { range } from 'lodash'
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {
    AuScrollableTableStepped,
    StorySlider,
  },
})

export default class AuScrollableTableSteppedDemo extends Vue {
  table_height = 500
  first_column_width = 150
  header_height = 24
  footer_height = 60
  cell_height = 50
  visible_columns = 4
  columnToScroll = 5

  trader_count = 100
  round_count = 50

  random = ''

  get date (): string {
    return this.random
  }

  get columns (): Column<string>[] {
    return range(this.round_count).map(r => ({
      field: 'ROUND_' + (r + 1),
      label: 'Round ' + (r + 1),
    }))
  }

  get header_rows (): Row<string>[] {
    return ['Round', 'Price'].map(it => ({ id: it }))
  }

  get rows (): Row<string>[] {
    return range(this.trader_count).map(t => ({
      id: 'Trader ' + t,
    }))
  }

  get footer_rows (): Row<string>[] {
    return ['Total', 'Activity'].map(it => ({ id: it }))
  }
}
</script>
