// TODO: do we need this ??

import { bounded } from '../../helpers/math-helpers'

export class Column<T> {
  field?: string // can be blank
  label?: string // can be blank
  width?: number
  data?: T
}

export class Row<T> {
  id?: string
  content?: string
  data?: T

  static to_rows (arr: Array<string>): Array<Row<string>> {
    return arr.map((it: string) => ({
      id: it.toUpperCase(),
      content: it,
      data: it,
    }))
  }
}


export const createArrayFromStartAndEnd = (start: number, end: number) => {
  return Array.apply(null, Array(end - start + 1)).map((value, index) => index + start)
}

export const getVisibleColumnIndexes = (data: {
  position: number, // 0 - 1
  total: number,
  visible: number
}) => {
  const position = bounded({min: 0, max: 1, value: data.position})
  let startPosition = 0
  let endPostition = 0
  if (data.visible >= data.total) {
    endPostition = data.total
    return createArrayFromStartAndEnd(startPosition, endPostition - 1)
  }
  startPosition = Math.round((data.total - data.visible) * position)
  endPostition = startPosition + data.visible - 1
  return createArrayFromStartAndEnd(startPosition, endPostition)
}
