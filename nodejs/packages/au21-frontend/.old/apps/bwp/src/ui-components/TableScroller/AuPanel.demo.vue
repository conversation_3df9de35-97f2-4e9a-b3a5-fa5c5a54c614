<template>
  <VbDemo>
    <VbCard title="Controls">
      <story-slider
        label="Width"
        :min="0"
        :max="200"
        :value.sync="width"
      />
      <story-slider
        label="Height"
        :min="0"
        :max="200"
        :value.sync="height"
      />
      <story-slider
        label="Top"
        :min="0"
        :max="200"
        :value.sync="top"
      />
      <story-slider
        label="Left"
        :min="0"
        :max="200"
        :value.sync="left"
      />
      <button @click="inline = !inline">
        {{inline ? 'inline' : 'block'}}
      </button>
    </VbCard>
    <VbCard>
      <AuPanel
        style="background-color: #9e9c86"
        :width="width"
        :height="height"
        :top="top"
        :left="left"
      />
    </VbCard>
  </VbDemo>
</template>

<script>
import AuPanel from './AuPanel.vue'
import StorySlider from '../VuebookHelpers/StorySlider'

export default {
  components: { StorySlider, AuPanel },
  data () {
    return {
      inline: false,
      width: 50,
      height: 50,
      top: 50,
      left: 50,
    }
  },
}
</script>
