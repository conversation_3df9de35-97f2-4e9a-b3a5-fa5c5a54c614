<template>
    <div
            class="AuScrollbar"
            ref="outer"
            :style="outer_style"
            @scroll="on_scroll"
    >
        <div :style="inner_style"/>
    </div>
</template>

<script lang="ts">

    import {WindowInstanceMap} from '../../helpers/window-instance'
    import {Component, Prop, Vue, Watch} from 'vue-property-decorator'

    @Component
    export default class AuScrollbar extends Vue {
        @Prop({type: Boolean, default: false}) is_vertical: boolean

        @Prop({required: true}) outer_length: number
        @Prop({required: true}) data_length: number

        @Prop({required: true}) top: number
        @Prop({required: true}) left: number

        @Prop({type: Number}) value: number

        get scrollbar_size(): number {
            return WindowInstanceMap.scrollbar_size
        }

        get outer_style() {
            return {
                height: (this.is_vertical ? this.outer_length : this.scrollbar_size) + 'px',
                left: this.left + 'px',
                overflowX: this.is_vertical ? 'hidden' : 'scroll',
                overflowY: this.is_vertical ? 'scroll' : 'hidden',
                top: this.top + 'px',
                width: (this.is_vertical ? this.scrollbar_size : this.outer_length) + 'px',
            }
        }

        get inner_style() {
            return {
                width: (this.is_vertical ? this.scrollbar_size : this.data_length) + 'px',
                height: (this.is_vertical ? this.data_length : this.scrollbar_size) + 'px',
            }
        }

        on_scroll(e) {
            const value = this.is_vertical ?
                e.target.scrollTop :
                e.target.scrollLeft
            this.$emit('input', value)
        }

        @Watch('value')
        on_value(val, old) {
            // if(WindowInstanceMap.is_mouse_down) {
            //   // attempt to make scrolling smoother
            //   return
            // }

            const outer: any = this.$refs.outer

            if (this.is_vertical) {
                outer.scrollTop = this.value
            } else {
                outer.scrollLeft = this.value
            }
        }
    }
</script>

<style lang="less">
    .AuScrollbar {
        position: absolute;
        z-index: 10;
    }
</style>
