<template>
  <VbDemo>
    <VbCard title="Controls">
      <div>scroll_left: {{scroll_left}}</div>
      <div>scroll_top: {{scroll_top}}</div>
      <StorySlider
        label="scroll_left"
        :min="0"
        :max="100"
        v-model="scroll_left"
      />
      <StorySlider
        label="scroll_top"
        :min="0"
        :max="100"
        v-model="scroll_top"
      />
    </VbCard>
    <VbCard>
      <div style="width: 500px; height: 500px; position: relative">
        <AuScrollbar
          :is_vertical="false"
          :outer_length="500"
          :data_length="1200"
          :top="500"
          v-model="scroll_left"
        />
        <AuScrollbar
          :is_vertical="true"
          :outer_length="500"
          :data_length="1200"
          :top="0"
          :left="500"
          v-model="scroll_top"
        />
      </div>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuScrollbar from './AuScrollbar.vue'
import StorySlider from '../VuebookHelpers/StorySlider.vue'
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {
    AuScrollbar,
    StorySlider,
  },
})
export default class AuScrollbarDemo extends Vue {
  scroll_left = 0
  scroll_top = 0
}
</script>
