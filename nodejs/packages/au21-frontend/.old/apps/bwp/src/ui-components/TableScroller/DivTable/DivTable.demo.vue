<template>
  <VbDemo>
    <VbCard>
      <DivTable
        :columns="columns"
        :widthSetter="column => column.width"
      >
        <DivTableRow
          v-for="product in products"
          :key="product.id + '00'"
          :height="40"
        >
          <DivTableCell>
            {{product.id}}
          </DivTableCell>
          <DivTableCell>
            {{ product.text }}
          </DivTableCell>
          <DivTableCell>
            {{ product.is_active }}
          </DivTableCell>
        </DivTableRow>
        <DivTableRow
          v-for="product in products"
          :key="product.id"
          :height="24"
        >
          <DivTableCell style="padding: 5px">
            {{product.id}}
          </DivTableCell>
          <DivTableCell class="table-cell-wrapper">
            {{ product.text }} Some long text long long
          </DivTableCell>
          <DivTableCell>
            {{ product.is_active }}
          </DivTableCell>
        </DivTableRow>
      </DivTable>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import DivTable from './DivTable.vue'
import DivTableRow from './DivTableRow.vue'
import DivTableCell from './DivTableCell.vue'

@Component({
  components: {
    DivTable,
    DivTableRow,
    DivTableCell,
  },
})
export default class DivTableDemo extends Vue {
  columns = [
    { width: 30 },
    { width: 60 },
    { width: 90 },
  ]
  products = [
    {
      id: '1',
      text: 'One',
      is_active: false,
    },
    {
      id: '2',
      text: 'Two',
      is_active: true,
    },
    {
      id: '3',
      text: 'Three',
      is_active: false
    },
  ]
}
</script>
