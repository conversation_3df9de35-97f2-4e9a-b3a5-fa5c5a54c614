<template>
  <VbDemo>
    <VbCard>
      <DivTableRow
        :columns="columns"
        :widthSetter="column => column.width"
        :height="24"
      >
        <DivTableCell
          v-for="(column, index) in columns"
          :key="index"
        >
          {{column.width}}px
        </DivTableCell>
      </DivTableRow>
    </VbCard>

    <VbCard>
      <button @click="columns.push({width: 50})">
        Add column
      </button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import DivTableRow from './DivTableRow.vue'
import DivTableCell from './DivTableCell.vue'

@Component({
  components: {
    DivTableRow,
    DivTableCell,
  },
})
export default class DivTableRowDemo extends Vue {
  columns = [
    {width: 30},
    {width: 40},
  ]
}
</script>
