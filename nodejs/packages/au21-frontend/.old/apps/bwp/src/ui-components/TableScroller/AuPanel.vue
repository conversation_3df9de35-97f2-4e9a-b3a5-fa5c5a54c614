<template>
  <div class="AuPanel" :style="style">
    <slot/>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component({ components: {} })
export default class AuPanel extends Vue {
  @Prop({ type: Boolean }) inline: boolean
  @Prop({ required: true }) height: number
  @Prop({ required: true }) width: number
  @Prop({ default: 0 }) top: number
  @Prop({ default: 0 }) left: number

  get style () {
    return {
      display: this.inline ? 'inline-block' : 'block',
      top: this.top + 'px',
      left: this.left + 'px',
      height: this.height + 'px',
      width: this.width + 'px',
    }
  }
}
</script>

<style lang="less">
@import (reference) '../../assets/variables.less';

.AuPanel {
  border: none;
  margin: 0;
  padding: 0;
  overflow: hidden;
  position: relative;
}
</style>
