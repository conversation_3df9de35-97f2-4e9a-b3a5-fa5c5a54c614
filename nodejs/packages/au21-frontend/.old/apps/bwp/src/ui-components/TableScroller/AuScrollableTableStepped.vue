<template>
    <div
            class="AuScrollableTableStepped"
            :style="styleComputed"
            @wheel="onWheel"
    >
        <!-- 1. FIXED COLUMN -->
        <AuScrollable
          class="AuScrollableTableStepped__left__top"
          :table_width="fixedColumnsWidth"
                :table_height="fullHeaderHeight"
        >
            <DivTable
                    :columns="fixed_columns"
                    :widthSetter="columnWidthSetter"
            >
                <DivTableRow
                        v-for="(row, row_index) in header_rows"
                        :key="row_index"
                        :height="cell_height"
                        @mouseenter.native="hoveredRow = row"
                        @mouseleave.native="hoveredRow = null"
                        :style="{
            'background-color': hoveredRow === row ? '#e2e6ef' : row_index % 2 ? 'white' : `#eff3fa`
          }"
                >
                    <DivTableCell
                            v-for="(column, column_index) in fixed_columns"
                            :key="column_index"
                            :style="{
              'border-right': fixed_columns.length - 1 === column_index ? '' : 'solid 1px #868080',
            }"
                    >
                        <slot
                                name="fixed_header_cell"
                                :row_index="row_index"
                                :row="row"
                                :column_index="column_index"
                                :column="column"
                        />
                    </DivTableCell>
                </DivTableRow>
            </DivTable>
        </AuScrollable>

        <AuScrollable
                class="AuScrollableTableStepped__left__middle"
                ref="fixed_body"
                :table_width="fixedColumnsWidth"
                :table_height="body_height"
                :top="fullHeaderHeight"
                :left="0"
                :content_height="content_height"
                :scroll_top="scroll_top"
                :containerStyle="{'background-color': whiteFirstColumn ? '#efefef' : '#444444'}"
        >
            <DivTable
                    :columns="fixed_columns"
                    :widthSetter="columnWidthSetter"
            >
                <DivTableRow
                        v-for="(row, row_index) in rows"
                        :key="row.id"
                        :height="cell_height"
                        @mouseenter.native="hoveredRow = row"
                        @mouseleave.native="hoveredRow = null"
                        :style="{
            'background-color': hoveredRow === row ? '#e2e6ef' : row_index % 2 ? 'white' : `#eff3fa`,
          }"
                >
                    <DivTableCell
                            v-for="(column, column_index) in fixed_columns"
                            :key="column_index"
                            :style="{'border-right': fixed_columns.length - 1 === column_index ? '' : 'solid 1px #868080' }"
                    >
                        <slot
                                name="fixed_body_cell"
                                :row_index="row_index"
                                :row="row"
                                :column_index="column_index"
                                :column="column"
                        />
                    </DivTableCell>
                </DivTableRow>
            </DivTable>
        </AuScrollable>

        <AuScrollable
                class="AuScrollableTableStepped__left__bottom"
                :table_width="fixedColumnsWidth"
                :table_height="footer_height"
                :top="header_height + body_height"
        >
            <AuPanel
                    :width="fixedColumnsWidth"
                    :height="footer_height"
            >
                <slot name="fixed_footer_cell"/>
            </AuPanel>
        </AuScrollable>

        <!-- 2. SCROLLING COLUMN -->
        <AuScrollable
                class="AuScrollableTableStepped__top"
                ref="scrolling_header"
                :table_width="bodyWidth"
                :table_height="header_height * header_rows.length"
                :left="fixedColumnsWidth"
                :content_width="bodyWidth"
        >
            <DivTable
                    :columns="columns"
                    :widthSetter="column => cellWidthComputed"
            >
                <DivTableRow
                        v-for="(row, row_index) in header_rows"
                        :key="row_index"
                        :height="header_height"
                        @mouseenter.native="hoveredRow = row"
                        @mouseleave.native="hoveredRow = null"
                        :style="{
            'background-color': hoveredRow === row ? '#e2e6ef' : row_index % 2 ? '#ddd' : `#eff3fa`,
          }"
                >
                    <DivTableCell
                            v-for="(column, column_index) in visibleColumnsComputed"
                            :key="row_index + '-' + column_index"
                            :style="{'border-left': 'solid 1px #868080' }"
                    >
                        <slot
                                name="scrolling_header_cell"
                                :column="column"
                                :row="row"
                                :column_index="column_index"
                                :row_index="row_index"
                        />
                    </DivTableCell>
                </DivTableRow>
            </DivTable>
        </AuScrollable>

        <AuScrollable
                class="AuScrollableTableStepped__body"
                ref="scrolling_body"
                :table_width="bodyWidth"
                :table_height="body_height"
                :left="fixedColumnsWidth"
                :top="fullHeaderHeight"
                :content_width="bodyWidth"
                :content_height="content_height"
                :scroll_top="scroll_top"
                :containerStyle="{'background-color': '#efefef'}"
        >
            <DivTable
                    :columns="visibleColumnsComputed"
                    :widthSetter="column => cellWidthComputed"
            >
                <DivTableRow
                        v-for="(row, row_index) in rows"
                        :key="row_index"
                        :height="cell_height"
                        @mouseenter.native="hoveredRow = row"
                        @mouseleave.native="hoveredRow = null"
                        :style="{
            'background-color': hoveredRow === row ? '#e2e6ef' : row_index % 2 ? '#ddd' : `#eff3fa`,
          }"
                >
                    <DivTableCell
                            v-for="(column, column_index) in visibleColumnsComputed"
                            :key="column.field"
                            :style="{
              'border-left': 'solid 1px #868080'
            }"
                    >
                        <slot
                                name="scrolling_body_cell"
                                :row="row"
                                :row_index="row_index"
                                :column="column"
                                :column_index="column_index"
                        />
                    </DivTableCell>
                </DivTableRow>
            </DivTable>
        </AuScrollable>

        <AuScrollable
                v-if="footer_height"
                class="AuScrollableTableStepped__bottom"
                ref="scrolling_footer"
                :table_width="bodyWidth"
                :table_height="footer_height"
                :left="fixedColumnsWidth"
                :top="header_height + body_height"
                :content_width="content_width"
        >
            <DivTable
                    :columns="visibleColumnsComputed"
                    :widthSetter="column => cellWidthComputed"
            >
                <DivTableRow
                        v-for="(row, row_index) in 1"
                        :key="row_index"
                        :height="footer_height"
                >
                    <DivTableCell
                            v-for="(column, column_index) in visibleColumnsComputed"
                            :key="column.field"
                    >
                        <slot
                                name="scrolling_header_cell"
                                :column="column"
                                :column_index="column_index"
                        />
                    </DivTableCell>
                </DivTableRow>
            </DivTable>

            <div
                    class="AuScrollableTableStepped__table"
                    :style="{width: content_width, height: footer_height}"
            >
                <div>
                    <div
                            style="display: inline-block"
                            v-for="(column, column_index) in visibleColumnsComputed"
                            :key="column.field"
                            :style="{width: cellWidthComputed + 'px', height: header_height + 'px'}"
                    >
                        <div
                                class="table-cell-wrapper"
                        >
                            <slot
                                    name="scrolling_header_cell"
                                    :column="column"
                                    :column_index="column_index"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </AuScrollable>

        <!-- 3. SCROLLBARS -->
        <AuScrollbar
                :left="fixedColumnsWidth"
                :top="fullHeaderHeight + body_height + footer_height"
                :outer_length="bodyWidth"
                :data_length="content_width"
                :value="scroll_horizontal"
                @input="scrollHorizontalDebounceLoader.run($event)"
        />
        <div :style="{
      left: 0,
      top: fullHeaderHeight + body_height + footer_height + 'px',
      width: fixedColumnsWidth + bodyWidth + scrollbar_size + 'px',
      height: scrollbar_size + 'px',
      backgroundColor: 'rgb(102, 102, 102)',
      position: 'absolute',
    }"/>
        <AuScrollbar
                is_vertical
                :left="fixedColumnsWidth + bodyWidth"
                :top="fullHeaderHeight"
                :outer_length="body_height"
                :data_length="content_height"
                :value="scroll_vertical"
                @input="scrollVerticalDebounceLoader.run($event)"
        />
        <div :style="{
      left: fixedColumnsWidth + bodyWidth + 'px',
      top: 0,
      width: scrollbar_size + 'px',
      height: fullHeaderHeight + body_height + scrollbar_size + 'px',
      backgroundColor: 'rgb(102, 102, 102)',
      position: 'absolute',
    }"/>
    </div>
</template>

<script lang="ts">
    import {WindowInstanceMap} from '../../helpers/window-instance'
    import AuPanel from './AuPanel.vue'
    import AuScrollable from './AuScrollable.vue'
    import AuScrollbar from './AuScrollbar.vue'
    import {Column, getVisibleColumnIndexes, Row} from './helpers'
    import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
    import {bounded} from '../../helpers/math-helpers'
    import DivTable from './DivTable/DivTable.vue'
    import DivTableRow from './DivTable/DivTableRow.vue'
    import DivTableCell from './DivTable/DivTableCell.vue'
    import { DebounceLoader } from 'asva-executors'

    @Component({
        components: {
            DivTable,
            AuPanel,
            AuScrollable,
            AuScrollbar,
            DivTableRow,
            DivTableCell,
        },
    })
    export default class AuScrollableTableStepped extends Vue {

        // whole table dimensions:
        @Prop({required: true}) table_width: number
        @Prop({required: true}) table_height: number

        // fixed header and footer:
        @Prop({default: 0}) header_height: number
        @Prop({default: 0}) footer_height: number

        // cells:
        @Prop({default: 0}) cell_height: number

        @Prop({default: 5}) visible_columns: number
        @Prop({default: false, type: Boolean}) whiteFirstColumn: boolean

        @Prop({required: true}) columns: Column<any>[]
        @Prop({default: () => []}) rows: Row<any>[]
        @Prop({default: () => []}) header_rows: any[]
        @Prop({default: () => []}) fixed_columns: any[]

        @Prop({
            type: Function,
            default: () => 0,
        }) columnWidthSetter: (column: any) => number

        scroll_top = 0
        scroll_left = 0

        scroll_vertical = 0
        scroll_horizontal = 0
        
        hoveredRow: any = null

        scrollVerticalDebounceLoader = new DebounceLoader(this.onScrollVerticalTrigger, 0)
        @Watch('scroll_vertical')
        on_scroll_vertical(val, old) {
            this.scroll_top = Math.ceil(val / this.cell_height) * this.cell_height
        }
        async onScrollVerticalTrigger (value) {
            this.scroll_vertical = value
        }

        scrollHorizontalDebounceLoader = new DebounceLoader(this.onScrollHorizontalTrigger, 0)
        @Watch('scroll_horizontal')
        on_scroll_horizontal(val, old) {
            this.scrollHorizontalDebounceLoader.run(val)
        }
        async onScrollHorizontalTrigger (value) {
            this.scroll_horizontal = value
        }
        

        get body_height(): number {
            return this.table_height - 4 - this.fullHeaderHeight - this.footer_height - this.scrollbar_size
        }

        get bodyWidth(): number {
            return this.table_width - 4 - this.fixedColumnsWidth - this.scrollbar_size
        }

        get content_height(): number {
            return this.cell_height * this.rows.length
        }

        get content_width(): number {
            return this.cellWidthComputed * this.columns.length
        }

        get scrollbar_size(): number {
            return WindowInstanceMap.scrollbar_size
        }

        get cellWidthComputed(): number {
            return this.bodyWidth / this.visible_columns
        }

        get fullHeaderHeight(): number {
            return this.header_height * this.header_rows.length
        }

        get maxScrollLeft(): number {
            // NOTE `countent_width` could be fractional, which is not the case for scroll.
            return Math.ceil(this.content_width - this.bodyWidth)
        }

        get visibleColumnsComputed() {
            const visible_column_indexes = getVisibleColumnIndexes({
                position: this.maxScrollLeft ? (this.scroll_horizontal / this.maxScrollLeft) : 0,
                total: this.columns.length,
                visible: this.visible_columns,
            })
            return visible_column_indexes.map(column_index => this.columns[column_index])
        }

        get fixedColumnsWidth() {
            return this.fixed_columns
                .map(this.columnWidthSetter)
                .reduce((previousValue, value) => previousValue + value, 0)
        }

        get styleComputed() {
            return {
                height: this.table_height - 2 + 'px',
                width: this.table_width - 2 + 'px',
                // To prevent rescale for flex.
                maxHeight: this.table_height - 2 + 'px',
                maxWidth: this.table_width - 2 + 'px',
            }
        }

        scrollLeft() {
            this.scroll_horizontal = 0
        }

        scrollRight() {
            this.scroll_horizontal = this.maxScrollLeft
        }

        scrollToColumn(columnNumber: number): void {
            if (columnNumber <= this.visible_columns) {
                return
            }
            this.scroll_horizontal = (columnNumber - this.visible_columns) / (this.columns.length - this.visible_columns) * this.maxScrollLeft
        }

        scrollTop() {
            this.scroll_vertical = 0
        }

        scrollBottom() {
            this.scroll_vertical = this.content_height
        }

        onWheel(e: WheelEvent) {
            if (e.shiftKey) {
                this.scroll_horizontal = bounded({
                    min: 0,
                    max: this.maxScrollLeft,
                    value: this.scroll_horizontal + e.deltaY,
                })
                return
            }
            this.scroll_vertical = bounded({
                min: 0,
                max: this.content_height - this.body_height,
                value: this.scroll_vertical + e.deltaY,
            })
        }
    }
</script>

<style lang="less">
    @import (reference) '../../assets/variables.less';

    .AuScrollableTableStepped {
        border: 1px solid #e8e8e8;
        border-radius: 2px;
        color: @au-text-color-secondary;
        // background-color: @au-text-color; // this doesn't appear to do anything
        position: relative;
        top: 0;
        left: 0;
    }
</style>
