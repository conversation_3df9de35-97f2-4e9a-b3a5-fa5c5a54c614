import { createArrayFromStartAndEnd, getVisibleColumnIndexes } from '../helpers'

describe('helpers', () => {
  it('createArrayFromStartAndEnd', () => {
    expect(createArrayFromStartAndEnd(1, 1)).toEqual([1])
    expect(createArrayFromStartAndEnd(3, 5)).toEqual([3, 4, 5])
  })
  describe('getVisibleColumnIndexes', () => {
    it('start', () => {
      expect(getVisibleColumnIndexes({
        position: 0,
        total: 20,
        visible: 3,
      })).toEqual([0, 1, 2])
    })
    it('near start', () => {
      expect(getVisibleColumnIndexes({
        position: 0.01,
        total: 20,
        visible: 3,
      })).toEqual([0, 1, 2])
    })
    it('center', () => {
      expect(getVisibleColumnIndexes({
        position: 0.5,
        total: 20,
        visible: 3,
      })).toEqual([9, 10, 11])
    })
    it('near end', () => {
      expect(getVisibleColumnIndexes({
        position: 0.99,
        total: 20,
        visible: 3,
      })).toEqual([17, 18, 19])
    })
    it('end', () => {
      expect(getVisibleColumnIndexes({
        position: 1,
        total: 20,
        visible: 3,
      })).toEqual([17, 18, 19])
    })
    it('works when `visible` equals `total`', () => {
      expect(getVisibleColumnIndexes({
        position: 0,
        total: 4,
        visible: 4,
      })).toEqual([0, 1, 2, 3])
    })
  })
})
