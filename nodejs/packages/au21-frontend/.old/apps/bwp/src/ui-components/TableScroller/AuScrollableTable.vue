<template>
  <div class="no-bmp
              au-table-scroller"
       :style="outer_style">
    <!--@mousedown="mousedown">-->

    <!-- 1. FIXED COLUMN -->
    <au-scrollable ref="fixed_header"
                   :table_width="first_column_width"
                   :table_height="header_height">

      <au-panel :inline="false"
                :width="first_column_width"
                :height="header_height">

        <div class="fixed_header_cell">

          <slot name="fixed_header_cell">
          </slot>

        </div>
      </au-panel>
    </au-scrollable>

    <au-scrollable ref="fixed_body"
                   :table_width="first_column_width"
                   :table_height="body_height"
                   :top="header_height"
                   :left="0"
                   :content_height="content_height"
                   :scroll_top="scroll_top">

      <au-panel v-for="(row, row_index) in rows"
                :key="row.id"
                :inline="false"
                :width="first_column_width"
                :height="cell_height">

        <div class="fixed_body_cell"
             :class="{light_row: row % 2}">

          <slot name="fixed_body_cell"
                :row_index="row_index"
                :row="row">
          </slot>
        </div>
      </au-panel>
    </au-scrollable>


    <au-scrollable ref="fixed_footer"
                   :table_width="first_column_width"
                   :table_height="footer_height"
                   :top="header_height + body_height">

      <au-panel :inline="false"
                :width="first_column_width"
                :height="footer_height">

        <div class="fixed_footer_cell">

          <slot name="fixed_footer_cell">
          </slot>
        </div>
      </au-panel>
    </au-scrollable>

    <!-- 2. SCROLLING COLUMN -->

    <au-scrollable ref="scrolling_header"
                   :table_width="body_width"
                   :table_height="header_height"
                   :left="first_column_width"
                   :content_width="content_width"
                   :scroll_left="scroll_left">

      <AuPanel :inline="false"
               :width="content_width"
               :height="header_height">

        <AuPanel v-for="(column, col_index) in columns"
                 :key="column.field"
                 :inline="true"
                 :height="header_height"
                 :width="cell_width">

          <div class="scrolling_header_cell">
            <slot name="scrolling_header_cell"
                  :column="column"
                  :col_index="col_index">
            </slot>
          </div>
        </AuPanel>
      </AuPanel>
    </au-scrollable>

    <au-scrollable ref="scrolling_body"
                   :table_width="body_width"
                   :table_height="body_height"
                   :left="first_column_width"
                   :top="header_height"
                   :content_width="content_width"
                   :content_height="content_height"
                   :scroll_left="scroll_left"
                   :scroll_top="scroll_top">

      <AuPanel v-for="(row, row_index) in rows"
               :key="row.id"
               :inline="false"
               :height="cell_height"
               :width="content_width">

        <AuPanel v-for="(column, col_index) in columns"
                 :key="column.field"
                 :inline="true"
                 :height="cell_height"
                 :width="cell_width">

          <div class="scrolling_body_cell"
               :class="{light_row: row_index % 2}">

            <slot name="scrolling_body_cell"
                  :row="row"
                  :row_index="row_index"
                  :column="column"
                  :col_index="col_index">
            </slot>
          </div>
        </AuPanel>
      </AuPanel>
    </au-scrollable>

    <au-scrollable ref="scrolling_footer"
                   :table_width="body_width"
                   :table_height="footer_height"
                   :left="first_column_width"
                   :top="header_height + body_height"
                   :content_width="content_width"
                   :scroll_left="scroll_left">

      <AuPanel :inline="false"
               :height="footer_height"
               :width="content_width">

        <AuPanel v-for="(column, col_index) in columns"
                 :key="column.field"
                 :inline="true"
                 :height="footer_height"
                 :width="cell_width">

          <div class="scrolling_footer_cell">

            <slot name="scrolling_footer_cell"
                  :column="column"
                  :col_index="col_index">
            </slot>
          </div>
        </AuPanel>
      </AuPanel>
    </au-scrollable>

    <!-- 3. SCROLLBARS -->

    <au-scrollbar ref="horizontal_scrollbar"
                  class="scrollbar"
                  :is_vertical="false"
                  :left="first_column_width"
                  :top="header_height + body_height + footer_height"
                  :outer_length="body_width"
                  :data_length="content_width"
                  :position.sync="scroll_left"/>

    <au-scrollbar ref="vertical_scrollbar"
                  class="scrollbar"
                  :is_vertical="true"
                  :left="first_column_width + body_width"
                  :top="header_height"
                  :outer_length="body_height"
                  :data_length="content_height"
                  :position.sync="scroll_top"/>

  </div>
</template>

<script lang="ts">
import { HeightHelper } from '../../helpers/height_helper'
import { WindowInstanceMap } from '../../helpers/window-instance'
import AuPanel from './AuPanel.vue'
import AuScrollable from './AuScrollable.vue'
import AuScrollbar from './AuScrollbar.vue'
import { Column, Row } from './helpers'
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component({
  components: { AuPanel, AuScrollable, AuScrollbar },
})
export default class AuScrollableTable extends Vue {

  // whole table dimensions:
  @Prop({ required: true }) table_width: number
  @Prop({ required: true }) table_height_helper: HeightHelper

  // fixed column:
  @Prop({ default: 0 }) first_column_width: number

  // fixed header and footer:
  @Prop({ default: 0 }) header_height: number
  @Prop({ default: 0 }) footer_height: number

  // cells:
  @Prop({ default: 0 }) cell_height: number
  @Prop({ default: 0 }) cell_width: number

  @Prop({ required: true }) columns: Column<any>[] // ie: number of rows
  @Prop({ default: () => [] }) rows: Row<any>[] // ie: trader users

  scroll_top = 0
  scroll_left = 0
  is_mouseover = false

  get body_height (): number {
    return this.table_height_helper.height - this.header_height - this.footer_height - this.scrollbar_size - 6 // for the padding
  }

  get body_width (): number {
    return this.table_width - this.first_column_width - this.scrollbar_size - 6 // also padding
  }

  get content_height (): number {
    return this.cell_height * this.rows.length
  }

  get content_width (): number {
    return this.cell_width * this.columns.length
  }

  get scrollbar_size (): number {
    return WindowInstanceMap.scrollbar_size
  }

  get outer_style () {
    return {
      backgroundColor: '#efefef',
      position: 'relative',
      top: 0,
      left: 0,
      height: this.table_height_helper.height + 'px',
      width: this.table_width + 'px',
    }
  }

  /*
  last_mouse_down = {x: 0, y: 0};

  mouseup( e: MouseEvent ){
    document.removeEventListener("mouseup", this.mouseup);
    document.removeEventListener("mousemove", this.mousemove);
  }

  mousedown( e: MouseEvent ){
    // added to catch mouseup outside of scrollbar viewport
    document.addEventListener("mouseup", this.mouseup);
    document.addEventListener("mousemove", this.mousemove);
    this.last_mouse_down = {
      x: e.clientX,
      y: e.clientY
    };
  }

  mousemove( e: MouseEvent ){

    const current_mouse = {
      x: e.clientX,
      y: e.clientY
    };

    const movement = {
      x: current_mouse.x - this.last_mouse_down.x,
      y: current_mouse.y - this.last_mouse_down.y
    };

    this.last_mouse_down = {
      x: current_mouse.x,
      y: current_mouse.y
    };

    if( this.content_width ){
      this.scroll_left = bounded({
        min  : 0,
        max  : this.content_width - this.table_width,
        value: this.scroll_left - movement.x
      })
      // this.$emit("update:scroll_left",
    }

    if( this.content_height ){
      this.scroll_top =  bounded({
        min  : 0,
        max  : this.content_height - this.table_height_helper.height,
        value: this.scroll_top - movement.y
      })
      // this.$emit("update:scroll_top",
    }

  }
  */

  /*
  // only for demo purposes ??

  get horizontal_scrollbar(){
    return {
      position            : "absolute",
      width               : this.body_width + "px",
      height              : this.scrollbar_size + 2 + "px",
      top                 : this.header_height + this.body_height + this.footer_height + "px",
      left                : this.first_column_width + "px",
      "overflow-x"        : "scroll",
      "overflow-y"        : "hidden",
      "-ms-overflow-style": "scrollbar"
    };
  }

  get vertical_scrollbar(){
    return {
      "overflow-y"        : "scroll",
      "overflow-x"        : "hidden",
      "-ms-overflow-style": "scrollbar"
    };
  }

  on_horiz_scroll( e, { scrollLeft, scrollTop } ){
    this.scroll_left = scrollLeft;
    this.$emit("scroll_left", this.scroll_left);
  }

  on_vert_scroll( e, { scrollLeft, scrollTop } ){
    this.scroll_top = scrollTop;
    this.$emit("scroll_top", this.scroll_top);
  }


  @Watch("scroll_left")
  on_left( val, old ){
    [
      this.$refs["scrolling_header"],
      this.$refs["scrolling_body"],
      this.$refs["scrolling_footer"],
      this.$refs["horizontal_scrollbar"]
    ].forEach(( it: HTMLElement ) => it.scrollLeft = val);
  }

  @Watch("scroll_top")
  on_top( val, old ){
    [
      this.$refs["scrolling_body"],
      this.$refs["vertical_scrollbar"]
    ].forEach(( it: HTMLElement ) => it.scrollTop = val);
  }
  */
}
</script>

<style lang="less"
       scoped>

</style>
