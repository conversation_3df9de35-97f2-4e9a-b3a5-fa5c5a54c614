<template>
  <div class="slider_outer">
    <span class="label">{{label}}:</span>
    <input class="range"
           type="range"
           :min="min"
           :max="max"
           :step="1"
           @input="change"
           :value="value">
    <div class="value">{{value}}</div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Component, Prop } from 'vue-property-decorator'

@Component({ components: {} })
export default class StorySlider extends Vue {
  @Prop({ required: true }) label: string
  @Prop({ required: true }) value: number

  @Prop({ default: 0 }) min: number
  @Prop({ default: 100 }) max: number

  change (e) {
    this.$emit('update:value', +e.target.value) // '+' to force as number
    this.$emit('input', +e.target.value) // '+' to force as number
  }
}
</script>


<style scoped lang="less">

.slider_outer {
  font-size: 10px;
  height: 20px;
  width: 300px;
}

.range {
  width: 100px;
  display: inline-block;
}

.label {
  display: inline-block;
  font-size: 13px;
  padding-right: 5px;
  position: relative;
  text-align: right;
  top: -5px;
  width: 150px;
}

.value {
  display: inline-block;
  font-size: 12px;
  position: relative;
  text-align: right;
  top: -5px;
  width: 25px;
}
</style>
