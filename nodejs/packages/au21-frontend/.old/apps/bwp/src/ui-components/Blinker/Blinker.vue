<template>
  <span class="Blinker" :style="{color: isBlinking ? `red` : undefined}">
    <slot>{{value}}</slot>
  </span>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

@Component({})
export default class Blinker extends Vue {
  @Prop({ required: true }) value: any
  isBlinking = false

  @Watch('value', { immediate: true })
  onValueChange () {
    this.isBlinking = true
    setTimeout(() => {
      this.isBlinking = false
    }, 400)
  }
}
</script>

<style lang="less">
.Blinker {
  transition: color 0.3s ease-out;
}
</style>
