<template>
  <VbDemo>
    <VbCard title="Controls">
      <story-slider
        label="first_column_width"
        :min="0"
        :max="200"
        v-model="first_column_width"
      />
      <story-slider
        label="header_height"
        :min="0"
        :max="200"
        v-model="header_height"
      />
      <story-slider
        label="footer_height"
        :min="0"
        :max="200"
        v-model="footer_height"
      />
      <story-slider
        label="cell_height"
        :min="0"
        :max="100"
        v-model="cell_height"
      />
      <story-slider
        label="cell_width"
        :min="0"
        :max="200"
        v-model="cell_width"
      />

      <div>Offset height {{offset_height}} (ie: offset from viewport height)
      </div>
      <div style="color:#7a7128">Table height: {{table_height_helper.height}}
        (test by resizing browser)
      </div>
      <story-slider
        label="trader_count"
        :min="0"
        :max="200"
        :value.sync="trader_count"
      />
      <story-slider
        label="round_count"
        :min="0"
        :max="50"
        :value.sync="round_count"
      />

      [TODO]
      <button>first col</button>
      <button>last col</button>
      <button>first row</button>
      <button>last row</button>
    </VbCard>
    <VbCard>
      <AuScrollableTable
        :table_width="500"
        :table_height_helper="table_height_helper"
        :first_column_width="first_column_width"
        :header_height="header_height"
        :footer_height="footer_height"
        :cell_height="cell_height"
        :cell_width="cell_width"
        :columns="columns"
        :header_rows="header_rows"
        :rows="rows"
        :footer_rows="footer_rows"
      >

        <!-- fixed column -->
        <template slot="fixed_header_cell"
                  slot-scope="{header_row}">
          <div class="fixed_header_cell">
            fixed header cell
          </div>
        </template>

        <template
          slot="fixed_body_cell"
          slot-scope="{row}"
        >
          <div
            class="fixed_body_cell"
          >
            {{row.id}}
            <!--
            <background-tweener
              bg_normal="white"
              bg_highlight="red"
              :value="date">
            </background-tweener>
            -->
          </div>
        </template>


        <template slot="fixed_footer_cell">
          <div class="fixed_footer_cell">
            fixed footer
          </div>
        </template>


        <!-- scrollable column -->

        <template
          slot="scrolling_header_cell"
          slot-scope="{column}"
        >
          <div class="scrolling_header_cell">
            <div>
              {{column.label}}
            </div>
            <div v-for="(row, row_index) in header_rows">
              {{row.id}}
            </div>
          </div>
        </template>
        <template
          slot="scrolling_body_cell"
          slot-scope="{row, column}"
        >
          <div class="scrolling_body_cell">
            {{column.label}}
            <br>
            {{row.id}}
          </div>
        </template>
        <template
          slot="scrolling_footer_cell"
          slot-scope="{column}"
        >
          <div class="scrolling_footer_cell">
            <div>
              {{column.label}}
            </div>
            <div v-for="(row, row_index) in footer_rows">
              {{row.id}}
            </div>
          </div>
        </template>
      </AuScrollableTable>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">

import { OffsetHeight } from '../../helpers/height_helper'
import AuScrollableTable from './AuScrollableTable.vue'
import { Column, Row } from './helpers'
import StorySlider from '../VuebookHelpers/StorySlider.vue'
import { range } from 'lodash'
import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {
    AuScrollableTable,
    StorySlider,
  },
})
export default class AuScrollableTableDemo extends Vue {
  offset_height = 200
  table_height_helper = new OffsetHeight(this.offset_height)
  first_column_width = 150
  header_height = 60
  footer_height = 60
  cell_height = 50
  cell_width = 80


  trader_count = 100
  round_count = 50

  random = ''

  get date (): string {
    return this.random
  }

  get columns (): Column<string>[] {
    return range(this.round_count).map(r => ({
      field: 'ROUND_' + (r + 1),
      width: this.cell_width,
      label: 'Round ' + (r + 1),
    }))
  }

  get header_rows (): Row<string>[] {
    return ['Round', 'Price'].map(it => ({ id: it }))
  }

  get rows (): Row<string>[] {
    return range(this.trader_count).map(t => ({
      id: 'Trader ' + t,
    }))
  }

  get footer_rows (): Row<string>[] {
    return ['Total', 'Activity'].map(it => ({ id: it }))
  }
}
</script>

<style lang="less"
       scoped>

</style>
