import { OnTime } from '../_generated/server_outputs'

export type TimeSplit = [string, string, string]

// NOTE This is taken from old flash app and is working as clients expect.
// It's not advisable to use it in other places apart from current header
// as well as modify in any way.

/*
  YEP, would not do it again like this.
  Actionscript did not have gazillions of libraries like npm :)
 */
export class OnTickParsed {
  year: string
  month: string
  date: string

  day: string

  hour: string
  min: string
  sec: string
  amPm: string

  time: string
  timeSplit: TimeSplit

  dateObj: Date

  onTickToStrings (e: OnTime): void {
    const y = Number(e.YEAR)
    this.year = String(y)

    const m = Number(e.MONTH)
    this.month = this.getMonthNameFromDate(m)

    const d = Number(e.DATE)
    this.date = String(d)
    if (d < 10) {
      this.date = '0' + this.date
    }

    this.day = this.getDayOfWeek(Number(e.DAY))

    let h = Number(e.HOUR)
    if (h > 12) {
      h -= 12
      this.amPm = 'pm'
    } else {
      this.amPm = 'am'
    }
    this.hour = String(h)
    // if (h < 10) hour = "0" + hour

    const mn = Number(e.MINS)
    this.min = String(mn)
    if (mn < 10) {
      this.min = '0' + this.min
    }

    const s = Number(e.SECS)
    this.sec = String(s)
    if (s < 10) {
      this.sec = '0' + this.sec
    }

    this.dateObj = new Date(y, m, d, h, mn, s)

    this.time = `${this.day} ${this.year}/${this.month}/${this.date} ${this.hour}:${this.min}:${this.sec}`

    this.timeSplit = [this.day, `${this.hour}:${this.min}:${this.sec}`, this.amPm]
  }

  addSeconds (secs: number): Date {
    const epoch: number = this.dateObj.valueOf()
    return new Date(epoch + secs * 1000)
  }

  getMonthNameFromDate (m: number): string {
    switch (m) {
      case 0  :
        return 'Jan'
      case 1  :
        return 'Feb'
      case 2  :
        return 'Mar'
      case 3  :
        return 'Apr'
      case 4  :
        return 'May'
      case 5  :
        return 'Jun'
      case 6  :
        return 'Jul'
      case 7  :
        return 'Aug'
      case 8  :
        return 'Sep'
      case 9  :
        return 'Oct'
      case 10 :
        return 'Nov'
      case 11 :
        return 'Dec'
      default :
        return ''
    }
  }

  getDayOfWeek (n: number): string {
    switch (n) {
      case 0 :
        return 'Sunday'
      case 1 :
        return 'Monday'
      case 2 :
        return 'Tuesday'
      case 3 :
        return 'Wednesday'
      case 4 :
        return 'Thursday'
      case 5 :
        return 'Friday'
      case 6 :
        return 'Saturday'
      default:
        return ''
    }
  }

}

export function getSplitTimeFromOnTime(onTime: OnTime): TimeSplit {
  const onTickParsed = new OnTickParsed()
  onTickParsed.onTickToStrings(onTime)
  return onTickParsed.timeSplit
}
