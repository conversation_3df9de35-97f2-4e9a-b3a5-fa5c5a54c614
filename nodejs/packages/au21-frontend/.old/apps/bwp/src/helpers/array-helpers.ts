import { lowerCaseIfString } from './other-helpers'

export function createMultipleByClosure<T> (closure: (index: number) => T, times: number): T[] {
  return Array.apply(null, Array(times)).map((value, index) => closure(Number(index)))
}

export function sortWithDirection<T extends Record<string, any>> (items: T[], sortBy: string, sortDirection: 'asc' | 'desc'): T[] {
  return [...items].sort((a, b) => {
    const aFixed = lowerCaseIfString(a[sortBy])
    const bFixed = lowerCaseIfString(b[sortBy])
    
    let comparisonResult: boolean
    if (typeof aFixed === 'string') {
      comparisonResult = aFixed > bFixed
    } else {
      comparisonResult = aFixed < bFixed
    }
    return (comparisonResult ? 1 : -1) * (sortDirection === 'asc' ? 1 : -1)
  })
}
