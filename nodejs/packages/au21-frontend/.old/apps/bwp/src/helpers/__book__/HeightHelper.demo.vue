<template>
  <div style="background-color: #ccc">
    <span>
      <input type="range"
             v-model="range1"
             min="0"
             max="100"/>
      <span>{{range1}}</span>
    </span>
    <div style="background-color: white; width: 100px">
    </div>
  </div>
</template>

<script lang="ts">

    import {Component, Vue} from "vue-property-decorator"

    @Component
  export default class HeightHelperDemo extends Vue{
    range1 = 100
  }
</script>

<style lang="less"
       scoped>

</style>
