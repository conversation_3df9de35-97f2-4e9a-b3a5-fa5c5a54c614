import {BwpConnector} from '../../services/bwp-connector/connector'

export const auConnectorInjectKey = Symbol('auConnectorInjectKey')

export const ConnectorPlugin = {
  install (Vue, options): void {
    Vue.mixin({
      inject: {
        $auConnector: {
          from: auConnectorInjectKey,
          default: undefined,
        },
      },
    })
  },
}

declare module 'vue/types/vue' {
  interface Vue {
    $auConnector: BwpConnector
  }
}
