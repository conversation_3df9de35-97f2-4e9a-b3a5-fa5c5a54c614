import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Icon,
  Input,
  InputNumber,
  Layout,
  List,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Select,
  Table,
  TimePicker,
  Spin,
  notification,
} from 'ant-design-vue'
import './ant-design-vue-plugin.less'
import Vue from 'vue'

// We serve components similarly to how ant-design itself does
// See `node_modules/ant-design-vue/es/index.js`

const components = [
  Button,
  Input,
  InputNumber,
  Row,
  Col,
  List,
  Card,
  Table,
  Modal,
  Layout,
  Radio,
  Popconfirm,
  DatePicker,
  TimePicker,
  Select,
  Checkbox,
  Icon,
  Divider,
  Spin,
]

components.forEach(component => Vue.use(component))

Vue.prototype.$notification = notification
declare module 'vue/types/vue' {
  interface Vue {
    $notification: typeof notification
  }
}
