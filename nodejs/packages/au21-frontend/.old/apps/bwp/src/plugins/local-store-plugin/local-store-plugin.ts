export const auLocalStoreInjectKey = Symbol('auLocalStoreInjectKey')

export const LocalStorePlugin = {
  install (Vue, options): void {
    Vue.mixin({
      inject: {
        $auLocalStore: {
          from: auLocalStoreInjectKey,
          default: undefined,
        },
      },
    })
  },
}

import { LocalStore } from './LocalStore'

declare module 'vue/types/vue' {
  interface Vue {
    $auLocalStore: LocalStore
  }
}
