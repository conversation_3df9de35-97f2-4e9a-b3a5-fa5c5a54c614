import { WindowInstanceMap } from '../../helpers/window-instance'

class LocalConfig {
   get height_outer () {
      return WindowInstanceMap.height
   }
   get height_inner () {
      return this.height_outer - 150
   }
   width_outer = 880
   get width_outer_px() {
      return this.width_outer + 'px'
   }
   get width_inner () {
      return this.width_outer - 8
   }
   get width_inner_px() {
      return this.width_inner + 'px'
   }
   hide_non_bidders = false
}

export class LocalStore{
   // Additional level of nesting is required to make object reactive.
   config = new LocalConfig()
}
