import {BwpStore} from '../../store/store'
// import { BwpConnector } from '../../services/bwp-connector/bwp_connector'

export const auStoreInjectKey = Symbol('auStoreInjectKey')

export const StorePlugin = {
  install (Vue, options): void {
    Vue.mixin({
      inject: {
        $auStore: {
          from: auStoreInjectKey,
          default: undefined,
        },
      },
    })
  },
}

declare module 'vue/types/vue' {
  interface Vue {
    $auStore: BwpStore
  }
}
