@import '~ant-design-vue/dist/antd.less';
@import '../../assets/variables.less';

@body-background: #28324b;
@component-background: #eee;
@background-color-light: #cecece;

@font-family: Helvetica;

@card-shadow: none;
@card-head-padding: 6px;
@card-padding-base: 0;
@card-padding-wider: 32px;
@card-head-background: #666666;

@btn-font-weight : 700;
@btn-font-size-sm: 12px;
@btn-height-sm : 20px;


.ant-card-head {
  min-height: 0;
}

.ant-card-type-inner .ant-card-head-title {
  padding: 6px;
}

.ant-btn.ant-btn-default {
  background-color: #e4e4e4;
  background-image: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 51%);
}

input.ant-input-number-input, input.ant-input {
  border-radius: 0;
  transition: box-shadow 0.1s;
  &:focus {
    box-shadow: 0 0 8px 3px rgb(167, 221, 238);
  }
}


// TABLE
.ant-table-wrapper {
  background-color: white;
}

.ant-table {
  .ant-table-thead > tr:first-child > th:last-child {
    border-top-right-radius: 0;
  }

  table {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
  td {
    border: solid #e7e7e7 1px !important;

  }
  .ant-table-thead > tr > th {
    font-weight: 700;
  }

  // NOTE This removes scroll from header making table look nicer.
  // But the downside is that table columns width is not calculated properly,
  // so head and body columns get out of sync.
  &.ant-table-fixed-header .ant-table-scroll {
    //.ant-table-header {
    //  overflow-y: auto !important;
    //}
    //.ant-table-body {
    //  overflow-y: auto !important;
    //}
  }

  th {
    background-color: #c3c3c3;
    border: solid 1px #696969 !important;
    border-color: #696969 !important;
  }
  th, td {
    background-color: white;
  }
}
.ant-table-thead > tr:hover:not(.ant-table-expanded-row) > td, .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
  background: @au-background-very-light;
}

.ant-modal-body {
  background-color: @au-background-light;
  color: @au-text-color;
  padding: 6px;
}

// RADIO
.ant-radio-wrapper {
  color: @au-text-color;

  span.ant-radio + * {
    padding-left: 4px;
    padding-right: 4px;
  }
}
.ant-radio-disabled + span {
  color: @au-text-color;
  opacity: 0.5;
}

.ant-radio-disabled .ant-radio-inner:after {
  background-color: @au-background;
}

// INPUT
.ant-input {
  background-color: @au-background-input;
  border-color: @au-border-input;
  padding-left: 3px;
  padding-right: 3px;
  padding-top: 1px;
  padding-bottom: 1px;
  border-radius: 0px;
  &--disabled {
    background-color: @au-pseudo-input-color;
    border: solid @au-background 1px !important;
    border-radius: 4px !important;
    color: black;
    cursor: text;
  }
}
.ant-input-number {
  background-color: @au-background-input;
  border-color: @au-border-input;
}

// SELECT
.ant-select {
  &.ant-select-enabled {
    .ant-select-selection {
      background-color: @au-background-input;
      border-color: @au-border-input;
      border-radius: 0;
    }
  }
}

// DATE PICKER
.ant-calendar-picker {
  i.ant-calendar-picker-clear {
    background-color: @au-background-input !important;
  }
}
.ant-calendar-picker-container {
  .ant-calendar-input {
    background-color: @au-background-input;
    border-color: @au-border-input;
  }
}

  // Notification (top popping thingy)
.ant-notification-notice {
  background-color: @au-background-input !important;
  white-space: pre-wrap;
  //color: white;
  //.ant-notification-notice-message {
  //  color: white;
  //  font-weight: 700;
  //}
  //.ant-notification-notice-close {
  //  color: white;
  //}
}
