# au-2019-ui-vue

## Styling rules

Aug 3, 2019:

1. Global styles and variables are in :
    - src/assets
2. Ant design overrides are here:
    - src/plugins/ant-design-vue-plugin/ant-design-vue-plugin.less
3. Local styles are in components
4. In ant design overrides there are variables, classes overrides, and some !importants.
    - You want to use first variables (rarely works), 
    - then class overrides (works in majority of cases), 
    - then !important's.

## Development

### Commands

Main application:

```bash
yarn serve
```

Vue-book:

```bash
yarn serve:book
```

Unit tests: 

```bash
yarn test:unit
```

Connector tests:

```bash
yarn test:connector
```

## Server urls

REVISITED MARCH 21, 2021, docs not up to date!

For dev environment we have: 
* `localhost:8080` (possibly other port), that runs frontend with hot reload.
* some staging (like `http://test.auctionologies.com:9090`) that runs backend.

The way the communication works is that frontend makes request to `/api`, which is proxied to backend url (defaults to `http://staging.auctionologies.com:9090`).

To run local dev server you have to create a file in the `client` folder root called `.env.local` with following content.

```env
VUE_APP_FAYE_PROXY="http://localhost:9090"
```

### Test logins
You can provide additional logins for testing in `.env.local`. They will appear as buttons below login form. Usage example:
```
VUE_APP_LOGINS=[{"username": "a3", "password": "1"}]
```
