// NOTE This is node.js function, not intended for browser use!
function generateFooter () {
  const commit = getLastCommitHash()
  const timestamp = new Date().toUTCString()
  return `Copyright 2011-2019 Auctionologies LLC, Build: ${commit}, ${timestamp}`
}

function getLastCommitHash () {
  const hash = require('child_process').execSync('git rev-parse HEAD')
  return hash.slice(0, 6)
}

module.exports = {
  generateFooter
}
