import "reflect-metadata";
import * as faye from "faye";
import * as http from "http";
import * as moment from "moment-timezone";
import * as nodestatic from "node-static";
import * as prettyjson from "prettyjson";
import * as redis from "redis";
import * as path from "path";
import { EngineClientCommandsEnvelope, SessionClientCommandsEnvelope } from "@au21-frontend/client-connector";


// TODO: set some of these as env variables with defaults
const PORT = process.env.port || 9090;
const REDIS_URL = process.env.REDIS_URL || "redis://localhost:6379";

if(!process.env.DIST_DIR){
  throw Error("DIST_DIR not specified")
}
const DIST_DIR = path.join(__dirname, process.env.DIST_DIR);
console.log({ dirname: __dirname, DIST_DIR });

const ENGINE_INPUT_CHANNEL = "ENGINE_INPUT_CHANNEL";
const ENGINE_OUTPUT_CHANNEL = "ENGINE_OUTPUT_CHANNEL";
const faye_mount = "/faye";
const faye_timeout = 15;
const redis_publisher = redis.createClient(REDIS_URL);
const redis_subscriber = redis.createClient(REDIS_URL);
const PUBLISH_TIME = true;

const session_envelope_count_since_last_restart_map = new Map<string, number>();

const log = (msg) => {
  console.log("\n" + prettyjson.render(msg));
}

log({
  PORT,
  REDIS_URL,
  DIST_DIR,
  ENGINE_INPUT_CHANNEL,
  ENGINE_OUTPUT_CHANNEL,
  PUBLISH_TIME,
  faye_mount,
  faye_timeout
});


//
// faye
//

const faye_adapter = new faye.NodeAdapter({
  mount: faye_mount,
  timeout: faye_timeout
});
const faye_client = faye_adapter.getClient();


//
// REDIS:
//

redis_subscriber.on("subscribe", function(channel, count) {
  log({ subject: "Subscribed to redis", channel, count });
});


redis_subscriber.on("message", function(channel, json) {

  try {

    log({ subject: "Received from redis", channel, json });

    if (channel !== ENGINE_OUTPUT_CHANNEL) {
      // noinspection ExceptionCaughtLocallyJS
      throw Error(`expected channel to be: ${ENGINE_OUTPUT_CHANNEL}, but was: ${channel}`);
    }

    const engine_envelope: EngineClientCommandsEnvelope = JSON.parse(json) as EngineClientCommandsEnvelope;


    log({engine_envelope})

    if (!engine_envelope.client_command_maps && engine_envelope.client_command_maps.length) {
      // noinspection ExceptionCaughtLocallyJS
      throw Error("expected transaction results to be an array");
    }

    // we build up the SessionClientCommandsEnvelope by demux of the engine envelope:
    const client_commands_envelope = new Map<string, SessionClientCommandsEnvelope>();

    const get_session_results = (session_id: string): SessionClientCommandsEnvelope => {
      let client_commands = client_commands_envelope[session_id];
      if (!client_commands) {
        client_commands = {
          session_id: session_id,
          // increment the package count for the session:
          envelope_count_since_last_restart: 0,
          commands: []
        };
        client_commands_envelope[session_id] = client_commands;
      }
      client_commands.envelope_count_since_last_restart++;
      return client_commands;
    }

    // DEMULTIPLEX: the commands across sessions:
    engine_envelope.client_command_maps.forEach(({ command, sessionIds }) => {

      log({command, sessionIds})
      sessionIds.forEach(session_id => {
        get_session_results(session_id).commands.push(command);
      });
    });

    for (const session_id in client_commands_envelope) {
      const envelope = client_commands_envelope[session_id];

      faye_client.publish(`/${session_id}`, envelope);

      log({ message: `published on /${session_id}`, envelope });
      log("")
    }

  } catch (e) {
    log({ Error: e.message, e });
  }
});

redis_subscriber.subscribe(ENGINE_OUTPUT_CHANNEL);


//
// WEB SERVER:
//

/*
// EXPRESS CREATED BY NX: (using node-static for now):
app.get("/api", (req, res) => {
  res.send({ message: "Welcome to fayeserver!" });
});
const port = process.env.port || 3333;
const server = app.listen(port, () => {
  console.log(`Listening at http://localhost:${port}/api`);
});
server.on("error", console.error);
*/

// Created static resources server:
const web = new nodestatic.Server(DIST_DIR);

const server = http.createServer((request, response) => {
  const { url, headers } = request;
  log({ url, host: headers.host });
  request.addListener("end", () => {
    web.serve(request, response);
  }).resume();
});

faye_adapter.attach(server);
server.listen(PORT);

log({ subject: "started server and faye listener on port " + PORT });

faye_adapter.getClient().subscribe("/handle", function(engine_command_envelope) {
  const c = engine_command_envelope["simple_name"];
  if (c !== undefined && c !== "Ping") {
    log({
      subject: "publishing to redis",
      command: engine_command_envelope
    });
  }

  redis_publisher.publish(ENGINE_INPUT_CHANNEL, JSON.stringify(engine_command_envelope));

  // const engine_input_channel = payload.engine_input_channel;
  // if (!engine_input_channel) {
  //   log("request hasn't provided an engine input channel!");
  // } else {
  //   redis_publisher.publish(engine_input_channel, JSON.stringify(payload));
  // }
});

//
// TIME
//

if (PUBLISH_TIME) {
  console.log("time publishing is on");
  setInterval(() => {

    // NB: THIS STILL NEEDS TO WORK WITH THE OLD FLASH UI !!

    const m = moment().tz("US/Central");

    const year = m.year();
    const month = m.month();
    const day_of_month = m.date();
    const day_of_week = m.day();
    const hour = m.hour();
    const mins = m.minute();
    const secs = m.second();

    // // TODO: why were we sending two versions of the time signal??
    // faye_client.publish("/time-bwp", // client timestamp
    //   JSON.stringify({
    //     EVENT: "OnTime",
    //     YEAR: year,
    //     MONTH: month,
    //     DATE: day_of_month,
    //     DAY: day_of_week,
    //     HOUR: hour,
    //     MINS: mins,
    //     SECS: secs
    //   })
    // );

    // console.log({year, month, date, day, hour, mins, secs})

    // TODO: create a common time event in libs (kotlin):
    faye_client.publish("/time", // client timestamp
      JSON.stringify({
        city: "Houston",
        date_time: {
          day_of_month: day_of_month,
          day_of_week: day_of_week,
          hour: hour,
          minutes: mins,
          month: month, // Feb 3, 2020: reverted back to 0-based months
          seconds: secs,
          year: year
        }
      }));

  }, 1000);
}

process.on("uncaughtException", function(err) {
  log({ subject: "uncauctionException", ERROR: err.toString(), err });
});
