// TODO: should we not be using express? does it cache? test?
// TODO: replace http with carrier for handler connection

/*
 - ConnectFaye () // POST
 - handle ()  // Websocket faye
 */

const PORT = 9090,
  REDIS_HOST = process.env.REDIS_HOST || 'localhost',
  DIST_DIR = './dist', // this is relative to the node command which runs in bwp-client !
  // REQUEST_CHANNEL = 'NPV-ENGINE', // comes from client now
  RESPONSE_CHANNEL = "CHARLIZE-FAYE-SERVER",
  PUBLISH_TIME = true,
  http = require('http'),
  moment = require('moment-timezone'),
  nodestatic = require('node-static'),
  {dump} = require('dumper.js'),
  prettyjson = require('prettyjson'),
  util = require('./util/Util'),
  redis = require("redis"),
  faye = require('faye'),
  faye_path = '/faye',
  faye_timeout = 15,
  redis_publisher = redis.createClient(`redis://${REDIS_HOST}:6379`),
  redis_subscriber = redis.createClient(`redis://${REDIS_HOST}:6379`),
  session_package_counter = {}

const log = (msg) => console.log("\n" + prettyjson.render(msg))

log({
  PORT,
  // REQUEST_CHANNEL,
  RESPONSE_CHANNEL,
  faye_path,
  faye_timeout
})

// REDIS:

redis_subscriber.on("subscribe", function (channel, count) {
  log({subject: 'Subscribed to redis', channel, count})
});

redis_subscriber.on("message", function (channel, transaction_results) {

  try {

    if (channel !== RESPONSE_CHANNEL) {
      throw Error(`expected channel to be: ${RESPONSE_CHANNEL}, but was: ${channel}`)
    }

    // Looks like we only use part of the transaction results object
    /*
    Kotlin:
    data class AuTransactionResults(
        val is_heartbeat: Boolean,
        val request: AuSessionRequest?, // null in case of heartbeat
        val messages: List<ServerMessage>)
     */
    const {messages} = JSON.parse(transaction_results)

    if (!messages && messages.length) {
      throw Error('expected tranction result messages to be an array')
    }

    log({subject: "Received from redis", channel, messages: messages})

    // Message buffering:
    // - goal send all session messages in one array.
    // - options:
    //   - a) package them here
    //        - trying here, but concern:
    //            server resources, when large json array is stringified by faye
    //   - b) send start and end messages
    //        - will try this if performance issues with (a) on large messages

    const session_packages = {} // {session: {session, count, messages}} for each session
    const get_session_package = (session) =>
      session_packages[session] || (() => {
        const session_package = {
          session,
          // increment the package count for the session:
          count: session_package_counter[session] = (session_package_counter[session] || 0) + 1,
          messages: []
        }
        session_packages[session] = session_package
        return session_package
      })()

    messages.forEach(message => {

      const {sessions, payload} = message

      sessions.forEach(session =>
        get_session_package(session).messages.push(payload))
    })

    for (let [session, msgs] of Object.entries(session_packages)) {
      faye_client.publish('/' + session, msgs)
    }

  } catch (e) {
    log("Error", {ERROR: e.message, e});
    log(e)
  }
});

redis_subscriber.subscribe(RESPONSE_CHANNEL);

// FAYE:
const faye_adapter = new faye.NodeAdapter({
  mount: faye_path,
  timeout: faye_timeout
})

const faye_client = faye_adapter.getClient();

// WEB SERVER:

const web = new nodestatic.Server(DIST_DIR)
// const server = http.createServer(function (req, res) {
//   log({req})
//   req.on('end', function () {
//     log({subject: "url", url: req.url})
//     web.serve(req, res);
//   })
// })
const server = http.createServer((request, response) => {
  const {url, headers} = request
  log({url, host: headers.host})
  request.addListener('end', () => {
    web.serve(request, response);
  }).resume();
})

faye_adapter.attach(server);
server.listen(PORT);

log({subject: "started server and faye listener on port " + PORT});
// -----  /handle -> redis 'ResponseQueue' --------------


faye_adapter.getClient().subscribe('/handle', function (payload) {
  const c = payload['type']
  if (c !== undefined && c !== 'Ping') {
    log({
      subject: "publishing to redis",
      payload: payload
    })
  }
  const request_channel = payload.request_channel
  if (!request_channel) {
    log('request not provided on payload')
  } else {
    redis_publisher.publish(request_channel, JSON.stringify(payload));
  }
})


if (PUBLISH_TIME) {
  setInterval(() => {

    // NB: THIS STILL NEEDS TO WORK WITH THE OLD FLASH UI !!

    const m = moment().tz("US/Central")

    const year = m.year()
    const month = m.month()
    const day_of_month = m.date()
    const day_of_week = m.day()
    const hour = m.hour()
    const mins = m.minute()
    const secs = m.second()

    faye_client.publish('/time', // client timestamp
      JSON.stringify({
        EVENT: 'OnTime',
        YEAR: year,
        MONTH: month,
        DATE: day_of_month,
        DAY: day_of_week,
        HOUR: hour,
        MINS: mins,
        SECS: secs
      })
    );

    // console.log({year, month, date, day, hour, mins, secs})

    faye_client.publish('/time-charlize', // client timestamp
      JSON.stringify({
        city: 'Houston',
        date_time: {
          day_of_month: day_of_month,
          day_of_week: day_of_week,
          hour: hour,
          minutes: mins,
          month: month, // Feb 3, 2020: reverted back to 0-based months
          seconds: secs,
          year: year
        }
      }))

  }, 1000)
}

process.on('uncaughtException', function (err) {
  log({subject: 'uncauctionException', ERROR: err.toString(), err});
});

/*

      // const posDisconnectedSession = body.indexOf('DISCONNECTED_SESSIONID')
      // if (posDisconnectedSession > -1) {
      //
      //   const disconnectedSessionId = body.substring('DISCONNECTED_SESSIONID'.length + 1)
      //   log({subject: 'DISCONNECTED_SESSIONID:' + disconnectedSessionId});
      //
      //   // obj ['ACTION'] = 'pageReloaded';
      //   // obj ['SESSIONID'] = disconnectedSessionId;
      //
      //
      // } else {
      const session =

        log({subject: 'New session=' + session});



        // obj ['ACTION'] = 'connect';
        // obj ['SESSIONID'] = sessionId;
        // obj ['USER_AGENT'] = req.headers['user-agent'];
        // obj ['REMOTE_ADDRESS'] = req.connection.remoteAddress;
      }

      // log({subject: "Received on " + faye_connect, body: obj})

    } else if (req.url.indexOf('/handle') >= 0) {

      // 2) HANDLE:
      //const payload = JSON.parse(body)
      handle(JSON.parse(body))
      res.end()
 */
