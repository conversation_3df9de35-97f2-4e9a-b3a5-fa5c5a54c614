<template>
  <div class="component">

    <div style="width: 100%; text-align: center">
      <div>Starting price</div>
      <div style="position: absolute; top: 0; left: 250px; border: 1px solid red">
        <div style="display: inline-block; margin-right: 10px; font-size: 10px; font-weight: bold">Autopilot:</div>
        <ASwitch
          style="width: 40px;"
          size="small"
          :checked="true"
          primary
        />
      </div>
    </div>

    <div class="three-state-panel">
      <div class="three-state-button">NOT SET</div>
      <div class="three-state-button">SET</div>
      <div class="three-state-button">ANNOUNCED</div>
    </div>

    <div
      style="
            border: 1px solid green;
            font-size: 12px;
            left: 90px;
            position: absolute;
            width: 90px;
            height: 50px;
       "
    >
      STARTING<br>PRICE
    </div>

    <div
      style="
            border: 1px solid green;
            font-size: 12px;
            left: 190px;
            position: absolute;
            width: 160px;
            height: 50px;
       "
    >
      ERRORS / WARNINGS
    </div>

  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import {
  AutopilotMode,
  DeCommonState,
  DeCommonStatusValue,
  DeRoundState,
  DeTimeState
} from '@au21-frontend/client-connector';
import { find } from 'lodash';
import chroma from 'chroma-js';
import { Switch } from 'ant-design-vue';
import ThreeStateDisplay from '../../../../../apps/charlize/src/pages/common/components/ThreeStateDisplay/ThreeStateDisplay.vue';

Vue.use(Switch);

@Component({
  components: { ASwitch: Switch, ThreeStateDisplay }
})
export default class DeAuctioneerStatePanel extends Vue {

  @Prop({ required: true }) commonStatus: DeCommonStatusValue | null;
  @Prop({ required: true }) timeState: DeTimeState | null;
  @Prop({ required: true }) roundState: DeRoundState | null;
  @Prop({ required: true }) autopilot: AutopilotMode | null;
  @Prop({ required: true }) closeable: boolean;
  @Prop({ required: true }) awardable: boolean;

  height = 80;
  width = 360;
  radius = 12;
  cy = 50;
  rect_r = 3;
  color = 'green';

  auRBGScale = chroma.scale(['#0f0', '#F00']).mode('lrgb');

  disabled_color = '#aaa';
  //
  // state_colors: { [state in DeCommonState]: string } = {
  //   STARTING_PRICE_NOT_SET: this.auRBGScale(0.5).hex(),
  //   STARTING_PRICE_SET: this.auRBGScale(0.9).hex(), //this.auRBGScale(0.50).hex(), //'#ddad00',
  //   STARTING_PRICE_ANNOUNCED: this.auRBGScale(0.5).hex(), //  this.auRBGScale(0.50).hex(), // '#ddad00',
  //   ROUND_OPEN: this.auRBGScale(0.25).hex(), // this.auRBGScale(0.50).hex(),  // 'rgba(52, 168, 83, 1.000)', //'#68c675',
  //   ROUND_CLOSED: this.auRBGScale(0.90).hex(),
  //   AUCTION_CLOSED: '#999'
  // };

  click(e, a) {
    console.log({ e, a });
    a.stopImmediatePropagation();
    a.defaultPrevented = true;
  }

  get rect_style() {
    return {
      strokeWidth: 3,
      fillOpacity: '1.0',
      fill: 'red'
    };
  }

  baseStyle = {
    strokeWidth: 3,
    fillOpacity: '1.0'
  };

  get left_style() {
    return {
      ...this.baseStyle,
      fill:
        this.commonStatus.auction_trader_state == DeCommonState.SETUP ?
          this.color :
          this.disabled_color
    };
  }

  get auctionState(): DeCommonState {
    return this.commonStatus?.auction_trader_state;
  }


  get auction_started(): boolean {
    return find([
      DeCommonState.ROUND_OPEN,
      DeCommonState.ROUND_CLOSED,
      DeCommonState.AUCTION_CLOSED
    ], s => s == this.commonStatus.auction_trader_state) != null;
  }

  cx(zero_based_position: number): number {
    return 10 + (this.radius * 2) + (zero_based_position * 65);
  }

  get starting_price_not_set_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.SETUP) {
      return '#FFB66C';
    }
    return 'gray';
  }

  get starting_price_set_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.SETUP) {
      return 'red';
    }
    return 'gray';
  }

  get starting_price_announced_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.STARTING_PRICE_ANNOUNCED) {
      return 'red';
    }
    return 'gray';
  }

}
</script>

<style scoped lang="less">
.component {
   position: relative;
  width: 360px;
  height: 80px;

  .three-state-panel {
    position: absolute;
     background-color: #333;
    font-size: 10px;
    text-align: center;
    top: 20px;
    width: 80px;

    .three-state-button {
      background-color: white;
       border-radius: 3px;
      color: black;
    }
  }
}
</style>
