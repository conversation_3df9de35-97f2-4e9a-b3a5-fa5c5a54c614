<template>
  <VbDemo>
    <VbCard>
      <table style="border: 1px solid white;">
        <tr>
          <td class="starting-price">Starting price</td>
          <td class="autopilot">Autopilot</td>
          <td class="panel">Before announcement time</td>
          <td class="panel">Before start time</td>
          <td class="panel">After start time</td>
        </tr>

        <tr>
          <td class="starting-price">not set</td>
          <td class="autopilot">OFF
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_set__autopilot_off__before_announce.common"
              :timeState="before_announced"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_set__autopilot_off__before_start"
              :timeState="before_start"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_set__autopilot_off__after_start"
              :timeState="after_start"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
        </tr>


        <tr>
          <td class="starting-price">set</td>
          <td class="autopilot">OFF</td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_announced__autopilot_off__before_announce"
              :timeState="before_announced"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_announced__autopilot_off__before_start"
              :timeState="before_start"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_announced__autopilot_off__after_start"
              :timeState="after_start"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
        </tr>


        <tr>
          <td class="starting-price">set</td>
          <td class="autopilot">ON</td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_announced__autopilot_on__before_announce"
              :timeState="before_announced"
              :autopilot="engaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_announced__autopilot_on__before_start"
              :timeState="before_start"
              :autopilot="engaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_not_announced__autopilot_on__after_start"
              :timeState="after_start"
              :autopilot="engaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
        </tr>


        <tr>
          <td class="starting-price">announced</td>
          <td class="autopilot">OFF</td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_announced__autopilot_off__before_announce"
              :timeState="before_announced"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_announced__autopilot_off__before_start"
              :timeState="before_start"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_announced__autopilot_off__after_start"
              :timeState="after_start"
              :autopilot="disengaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
        </tr>


        <tr>
          <td class="starting-price">announced</td>
          <td class="autopilot">ON</td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_announced__autopilot_on__before_announce"
              :timeState="before_announced"
              :autopilot="engaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_announced__autopilot_on__before_start"
              :timeState="before_start"
              :autopilot="engaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
          <td>
            <DeAuctioneerStatePanelBeforeAuction
              :commonStatus="starting_price_announced__autopilot_on__after_start"
              :timeState="after_start"
              :autopilot="engaged"
              roundState="NOT_OPEN"
              :closeable="false"
              :awardable="false"
            />
          </td>
        </tr>
      </table>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import AuSelect from '../../../../../../apps/charlize/src/ui-components/AuSelect/AuSelect.vue';
import {
  AutopilotMode,
  DeAuctioneerInfoLevel,
  DeAuctioneerStatusValue,
  DeAuctioneerState,
  DeCommonState,
  DeCommonStatusValue,
  DeRoundState,
  DeTimeState,
  PriceDirection
} from '@au21-frontend/client-connector';
import DeAuctioneerStatePanel from '../DeAuctioneerStatePanel.vue';
import DeAuctioneerStatePanelBeforeAuction from './DeAuctioneerStatePanelBeforeAuction.vue';
import { demo_controls } from '../../../../../../apps/charlize/src/demo-helpers/DeAuctioneerStatusValue.helper';

type State = {
  auctioneer: DeAuctioneerStatusValue,
  common: DeCommonStatusValue
}

@Component({
  components: { DeAuctioneerStatePanelBeforeAuction, DeAuctioneerStatePanel, AuSelect }
})
export default class DeAuctioneerStatePanelBeforeAuctionDemo extends Vue {

  engaged = AutopilotMode.ENGAGED;
  disengaged = AutopilotMode.DISENGAGED;

  before_announced = DeTimeState.BEFORE_ANNOUNCE_TIME;
  before_start = DeTimeState.BEFORE_START_TIME;
  after_start = DeTimeState.AFTER_START_TIME;

  base_common_state: DeCommonStatusValue = {
    auction_trader_state: DeCommonState.SETUP,
    auction_trader_state_label: '',
    current_price_direction: PriceDirection.UP,
    isClosed: false,
    round_number: 1,
    round_price: '',
    round_seconds: 0
  };

  base_auctioneer_state: DeAuctioneerStatusValue = {
    announced: false,
    auctioneer_state: DeAuctioneerState.ROUND_OPEN_ALL_ORDERS_IN,
    auctioneer_state_text: '',
    auctioneer_round_feedback: '',
    autopilot: AutopilotMode.DISENGAGED,
    awardable: false,
    controls: demo_controls,
    info_level: DeAuctioneerInfoLevel.NORMAL,
    round_open_min_secs: null,
    round_state: DeRoundState.GREEN,
    starting_price: '',
    time_state: DeTimeState.BEFORE_ANNOUNCE_TIME
  };


  starting_price_not_set__autopilot_off__before_announce = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_NOT_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };

  starting_price_not_set__autopilot_off__before_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_NOT_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_not_set__autopilot_off__after_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_NOT_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };


  starting_price_not_announced__autopilot_off__before_announce = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_not_announced__autopilot_off__before_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_not_announced__autopilot_off__after_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };


  starting_price_not_announced__autopilot_on__before_announce = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_not_announced__autopilot_on__before_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_not_announced__autopilot_on__after_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_SET
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };


  starting_price_announced__autopilot_off__before_announce = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_ANNOUNCED
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_announced__autopilot_off__before_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_ANNOUNCED
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_announced__autopilot_off__after_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_ANNOUNCED
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };


  starting_price_announced__autopilot_on__before_announce = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_ANNOUNCED
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_announced__autopilot_on__before_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_ANNOUNCED
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };
  starting_price_announced__autopilot_on__after_start = {
    common: {
      ...this.base_common_state,
      auctioneer_state: DeAuctioneerState.STARTING_PRICE_ANNOUNCED
    },
    auctioneer: {
      ...this.base_auctioneer_state
    }
  };

}

</script>
<style scoped lang="less">
.starting-price {
  width: 120px;
  text-align: center;
}

.autopilot {
  width: 90px;
  text-align: center;
}

.panel {
  width: 300px;
  text-align: center;
}

td {
  border: 1px solid white;
  border-collapse: collapse;
  padding: 5px;
}
</style>
