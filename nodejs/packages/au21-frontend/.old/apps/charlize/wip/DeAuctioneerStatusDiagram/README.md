Sep 29, 2021:
This directory is a somewhat experimental attempt to add automation to the Auction.

ie:   
- to set a starting price and announce time 
- and then let the system automatically run the auction:
  - announce the starting price
  - start the first round
  - continue running rounds until the auction is 'awardable'

However, the complexity of handling all possible states makes it somewhat unweildy
- there are 23+ different states and substates so far
- each with different auctioneer and trader messages

So, for now, we'll go back to manual, and implement automation later when we've run a few test auctions.

