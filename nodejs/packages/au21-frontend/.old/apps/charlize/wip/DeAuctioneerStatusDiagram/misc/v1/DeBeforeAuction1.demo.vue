<template>
  <VbDemo>
    <VbCard>
      <DeBeforeAuction1
        :commonStatus="commonStatus"
        :timeState="time_state"
        :autopilot="autopilot"
        roundState="NOT_OPEN"
        :closeable="closeable"
        :awardable="awardable"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import {
  AutopilotMode,
  DeAuctioneerStatusValue,
  DeAuctioneerState,
  DeCommonStatusValue,
  DeTimeState
} from '@au21-frontend/client-connector';
import { map } from 'lodash';
import { createDemo__DeCommonStatusValue } from '../../../../../../../apps/charlize/src/demo-helpers/DeCommonStatusValue.helper';
import DeBeforeAuction1 from './DeBeforeAuction1.vue';


type State = {
  auctioneer: DeAuctioneerStatusValue,
  common: DeCommonStatusValue
}

@Component({
  components: { DeBeforeAuction1 }
})
export default class DeBeforeAuction1Demo extends Vue {

  statusOptions = map(DeAuctioneerState); // Object.keys(DeAuctioneerState).filter(x => !(parseInt(x) >= 0));

  commonStatus = createDemo__DeCommonStatusValue()

  autopilot: AutopilotMode = null
  time_state: DeTimeState = null
  closeable = false
  awardable = false

}
</script>
