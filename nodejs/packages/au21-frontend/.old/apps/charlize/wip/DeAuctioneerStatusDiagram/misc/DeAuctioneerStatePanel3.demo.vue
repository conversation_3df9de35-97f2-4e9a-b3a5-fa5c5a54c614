<template>
  <VbDemo>
    <VbCard>
      <a-button @click="set_starting_price">Set starting price</a-button>
      <a-button @click="announce_starting_price">Announce</a-button>
      <a-button @click="start_auction">Start auction</a-button>
      <a-button @click="close_round">Close round</a-button>
      <a-button @click="next_round">Next round</a-button>
      <a-button @click="award_auction">Award auction</a-button>
    </VbCard>
    <VbCard>
      <a-button @click="reset">Reset</a-button>
      <a-button @click="toggle_autopilot">Autopilot</a-button>
      <a-button @click="announce_time_passed">Announce time passed</a-button>
      <a-button @click="start_time_passed">Starting time passed</a-button>
      <a-button @click="toggle_closeable">Closeable</a-button>
      <a-button @click="toggle_awardable">Awardable</a-button>
    </VbCard>
    <VbCard>
      <DeAuctioneerStatePanel
        :commonStatus="commonStatus"
        :timeState="time_state"
        :autopilot="autopilot"
        roundState="NOT_OPEN"
        :closeable="closeable"
        :awardable="awardable"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import AuSelect from '../../../../../../apps/charlize/src/ui-components/AuSelect/AuSelect.vue';
import {
  AutopilotMode,
  DeAuctioneerStatusValue,
  DeCommonState,
  DeCommonStatusValue,
  DeTimeState
} from '@au21-frontend/client-connector';
import { map } from 'lodash';
import { createDemo__DeCommonStatusValue } from '../../../../../../apps/charlize/src/demo-helpers/DeCommonStatusValue.helper';
import DeAuctioneerStatePanel from '../DeAuctioneerStatePanel.vue';

type State = {
  auctioneer: DeAuctioneerStatusValue,
  common: DeCommonStatusValue
}

@Component({
  components: { DeAuctioneerStatePanel, AuSelect }
})
export default class DeAuctioneerStatePanel3Demo extends Vue {

  statusOptions = map(DeCommonState); // Object.keys(DeCommonState).filter(x => !(parseInt(x) >= 0));

  commonStatus = createDemo__DeCommonStatusValue()

  autopilot: AutopilotMode = null
  time_state: DeTimeState = null
  closeable = false
  awardable = false

  mounted() {
    this.reset()
  }

  reset() {
    this.commonStatus.auction_trader_state = DeCommonState.SETUP;
    this.commonStatus.round_price = '';
    this.time_state = DeTimeState.BEFORE_ANNOUNCE_TIME;
    this.autopilot = AutopilotMode.DISENGAGED;
    this.closeable = false
    this.awardable = false
  }

  toggle_autopilot() {
    if (this.autopilot == AutopilotMode.DISENGAGED)
      this.autopilot = AutopilotMode.ENGAGED;
    else
      this.autopilot = AutopilotMode.DISENGAGED;
  }

  set_starting_price() {
    if (this.commonStatus.auction_trader_state == DeCommonState.SETUP) {
      this.commonStatus.round_price = '100.000';
      this.commonStatus.auction_trader_state = DeCommonState.SETUP;
    }
  }

  announce_starting_price() {
    if (this.commonStatus.auction_trader_state == DeCommonState.SETUP) {
      this.commonStatus.auction_trader_state = DeCommonState.STARTING_PRICE_ANNOUNCED;
    }
  }

  start_auction() {
    if (this.commonStatus.auction_trader_state == DeCommonState.STARTING_PRICE_ANNOUNCED) {
      this.commonStatus.auction_trader_state = DeCommonState.ROUND_OPEN;
    }
  }

  close_round() {
    if (this.commonStatus.auction_trader_state == DeCommonState.ROUND_OPEN) {
      this.commonStatus.auction_trader_state = DeCommonState.ROUND_CLOSED;
    }
  }

  next_round() {
    if (this.commonStatus.auction_trader_state == DeCommonState.ROUND_CLOSED) {
      this.commonStatus.auction_trader_state = DeCommonState.ROUND_OPEN;
    }
  }

  award_auction() {
    if (this.commonStatus.auction_trader_state == DeCommonState.ROUND_CLOSED) {
      this.commonStatus.auction_trader_state = DeCommonState.AUCTION_CLOSED;
    }
  }

  announce_time_passed(){
    if(this.time_state == DeTimeState.BEFORE_ANNOUNCE_TIME){
      this.time_state = DeTimeState.BEFORE_START_TIME
    }
  }

  start_time_passed(){
    if(this.time_state == DeTimeState.BEFORE_START_TIME){
      this.time_state = DeTimeState.AFTER_START_TIME
    }
  }

  toggle_closeable(){
    this.closeable = !this.closeable
  }

  toggle_awardable(){
    this.awardable = !this.awardable
  }


  //
  // commonStatus(state: DeCommonState): DeCommonStatusValue {
  //   return {
  //     auction_trader_state: state,
  //     common_status_label: '',
  //     isClosed: false,
  //     round_number: 1,
  //     round_price: '',
  //     round_seconds: 10
  //   };
  // }
  //
  // auctioneerStatus(state: DeCommonState): DeAuctioneerStatusValue {
  //   return {
  //     auctioneer_round_feedback: '',
  //     auctioneer_state_label: '',
  //     autopilot: this.autopilot,
  //     controls: demo_controls,
  //     info_level: DeAuctioneerInfoLevel.NORMAL,
  //     isAwardable: false,
  //     last_buyers: '',
  //     last_excess: '',
  //     last_match: '',
  //     last_ratio: '',
  //     last_round: 1,
  //     last_sell_dec: '',
  //     last_sellers: '',
  //     last_total_buy: '',
  //     last_total_sell: '',
  //     pen_buyers: '',
  //     pen_excess: '',
  //     pen_match: '',
  //     pen_ratio: '',
  //     pen_round: '',
  //     pen_sell_dec: '',
  //     pen_sellers: '',
  //     pen_total_buy: '',
  //     pen_total_sell: '',
  //     potential: '',
  //     round_open_min_secs: 1,
  //     round_state: DeRoundState.RED,
  //     starting_price: '',
  //     time_state: this.time_state
  //   };
  // }
  //

}
</script>
