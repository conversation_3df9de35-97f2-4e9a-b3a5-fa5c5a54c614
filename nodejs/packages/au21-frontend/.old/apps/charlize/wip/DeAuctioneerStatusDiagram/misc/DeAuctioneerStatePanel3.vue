<template>
  <table  >
    <tr>

      <td>
        <div>
          <svg
            :width="width"
            :height="height"
            :viewBox="`0 0 ${width} ${height}`"
          >
            <rect
              x="0" y="0"
              :width="width" :height="height"
              fill="#1C252F" stroke="white"
            ></rect>

            <line x1="200" y1="10" x2="200" y2="70" stroke="white" stroke-width="0.5"></line>

            <!--  STARTING_PRICE_NOT_SET -->

            <!--            <g transform="translate(100,0)">-->
            <g v-if="!auction_started">
              <text
                :y="15"
                fill="white"
                font-size="11"
                font-weight="bold"
              >
                <tspan :x="60" dy="0em">Starting price</tspan>
              </text>

              <rect
                :x="10"
                :y="40"
                height="18"
                width="55"
                :rx="rect_r"
                :ry="rect_r"
                :style="rect_style"
              />

              <text
                :y="15"
                fill="white"
                font-size="11"
              >
                <tspan :x="17" dy="1.5em">Not Set</tspan>
                <tspan :x="89" dy="0em">Set</tspan>
                <tspan :x="133" dy="0em">Announced</tspan>
              </text>


              <circle
                :r="radius" :cx="cx(0)" :cy="cy"
                stroke="gray" :fill="starting_price_not_set_color"
              />

              <!--  STARTING_PRICE_SET -->

              <circle
                :r="radius" :cx="cx(1)" :cy="cy"
                stroke="gray" :fill="starting_price_set_color"
              />

              <!--  STARTING_PRICE_ANNOUNCED -->

              <circle
                :r="radius" :cx="cx(2)" :cy="cy"
                stroke="gray" :fill="starting_price_announced_color"
              />

              <!-- NEXT ROW -->

              <text
                :y="100"
                fill="white"
                font-size="11"
              >
                <tspan :x="10" dy="0em" font-weight="bold">Autopilot:</tspan>
                <tspan :x="70" dy="0em">{{ autopilot }}</tspan>
              </text>

            </g>

            <g transform="translate(180,0)">

              <!--   POST_START             -->

              <text
                :y="15"
                fill="white"
                font-size="11"
                font-weight="bold"
              >
                <tspan :x="240" dy="0em">Round</tspan>
              </text>

              <text
                :y="15"
                fill="white"
                font-size="11"
              >
                <tspan :x="214" dy="0em">Open</tspan>
                <tspan :x="275" dy="0em">Closed</tspan>
              </text>


              <!--  ROUND_OPEN -->

              <circle
                :r="radius" :cx="cx(3)" :cy="cy"
                stroke="gray" :fill="round_open_color"
              />

              <!--  ROUND_CLOSED -->

              <circle
                :r="radius" :cx="cx(4)" :cy="cy"
                stroke="gray" :fill="round_closed_color"
              />

            </g>

          </svg>
        </div>

      </td>
      <td>
        <div style="width: 20px">&nbsp;</div>
      </td>

      <td style="vertical-align: top; width: 300px;">
        <div style="font-size: 11px; top: 3px; position: relative">
          <div>auction state: {{ auctionState }}</div>
          <div>round state: {{ roundState }}</div>
          <div>time state: {{ timeState }}</div>
          <div>autopilot: {{ autopilot }}</div>
          <div>closeable: {{ closeable }}</div>
          <div>awardable: {{ awardable }}</div>
        </div>
      </td>
    </tr>
  </table>

</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import {
  AutopilotMode,
  DeCommonState,
  DeCommonStatusValue,
  DeRoundState,
  DeTimeState
} from '@au21-frontend/client-connector';
import { find } from 'lodash';
import chroma from 'chroma-js';

@Component({
  components: {}
})
export default class DeAuctioneerStatePanel3 extends Vue {

  @Prop({required:true}) commonStatus: DeCommonStatusValue | null;
  @Prop({ required: true }) timeState: DeTimeState | null;
  @Prop({ required: true }) roundState: DeRoundState | null;
  @Prop({ required: true }) autopilot: AutopilotMode | null;
  @Prop({ required: true }) closeable: boolean;
  @Prop({ required: true }) awardable: boolean;

  height = 120;
  width = 400;
  radius = 12;
  cy = 50;
  rect_r = 3;
  color = 'green'

  auRBGScale = chroma.scale(['#0f0', '#F00']).mode('lrgb');

  disabled_color = '#aaa';
  //
  // state_colors: { [state in DeCommonState]: string } = {
  //   STARTING_PRICE_NOT_SET: this.auRBGScale(0.5).hex(),
  //   STARTING_PRICE_SET: this.auRBGScale(0.9).hex(), //this.auRBGScale(0.50).hex(), //'#ddad00',
  //   STARTING_PRICE_ANNOUNCED: this.auRBGScale(0.5).hex(), //  this.auRBGScale(0.50).hex(), // '#ddad00',
  //   ROUND_OPEN: this.auRBGScale(0.25).hex(), // this.auRBGScale(0.50).hex(),  // 'rgba(52, 168, 83, 1.000)', //'#68c675',
  //   ROUND_CLOSED: this.auRBGScale(0.90).hex(),
  //   AUCTION_CLOSED: '#999'
  // };

  get rect_style() {
    return {
        strokeWidth: 3,
        fillOpacity: '1.0',
        fill: 'red'
    }
  }

  baseStyle = {
    strokeWidth: 3,
    fillOpacity: '1.0'
  };

  get left_style() {
    return {
      ...this.baseStyle,
      fill:
        this.commonStatus.auction_trader_state == DeCommonState.SETUP ?
          this.color :
          this.disabled_color
    };
  }

  get auctionState():DeCommonState {
    return this.commonStatus?.auction_trader_state
  }


  get auction_started(): boolean {
    return find([
      DeCommonState.ROUND_OPEN,
      DeCommonState.ROUND_CLOSED,
      DeCommonState.AUCTION_CLOSED
    ], s => s == this.commonStatus.auction_trader_state) != null;
  }

  cx(zero_based_position: number): number {
    return 10 + (this.radius * 2) + (zero_based_position * 65);
  }

  get starting_price_not_set_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.SETUP) {
      return '#FFB66C';
    }
    return 'gray';
  }

  get starting_price_set_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.SETUP) {
      return 'red';
    }
    return 'gray';
  }

  get starting_price_announced_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.STARTING_PRICE_ANNOUNCED) {
      return 'red';
    }
    return 'gray';
  }

  get round_open_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.ROUND_OPEN) {
      return 'red';
    }
    return 'gray';
  }

  get round_closed_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.ROUND_CLOSED) {
      return 'red';
    }
    return 'gray';
  }


  get auction_closed_color(): string {
    if (this.commonStatus.auction_trader_state == DeCommonState.AUCTION_CLOSED) {
      return 'red';
    }
    return 'gray';
  }


}
</script>

<style scoped>

</style>
