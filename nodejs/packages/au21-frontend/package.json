{"name": "au21-frontend", "version": "0.0.9", "license": "Copyright Auctionologies LLC", "private": true, "scripts": {"update-ts-generated": "npm i @auctionologies/au21-engine-tsgenerated@latest", "version:patch": "npm version patch --no-git-tag-version", "npm:install": "npm i --legacy-peer-deps", ">> playwright >>": "", "playwright:charlize": "nx run charlize-e2e:e2e", "xunit": " xunit-viewer -r junit.xml -w", "playwright:charlize:run-and-show": "npm run playwright:charlize && npm run xunit && open index.html", "<< playwright <<": "", ">> tests >>": "", "test": "nx test", "<< tests <<": "", ">> updates : run these 3 command in order >>": "", "update:nx": "nx migrate latest && npm i", "update:nx-vue": "nx migrate latest @nx-plus/vue && npm i", "update": "npm-check -u", "old:update:rest-except-node": "ncu -u --reject @types/node && npm i", "<< updates <<": "", "old:update:rest-inc-node": "ncu -u && npm i", "build:all": "nx run-many --target=build --projects=charlize", "docker:build:client-connector-test": "devops/docker/client-connector-test/build-and-push-image.sh", "docker:compose:client-connector-test": "docker-compose -f devops/docker/client-connector-test/docker-compose.yml up", "showCongif:vue": "nx test vue --showConfig", "create:node-app": "nx g @nrwl/node:application", "serve:client-connector-test": "nx serve client-connector-test", "nx": "nx", "start": "nx serve", "serve:charlize:win": "wt -d . --title=AU2021 cmd /k wsl yarn serve:charlize:app; split-pane -d . cmd /k wsl yarn serve:charlize:book --port 8081", "build:charlize:app": "APP_MODE=app nx build charlize:build -c production", "build:charlize:book": "APP_MODE=book nx build charlize:build -c production --publicPath=/book --dest=dist/apps/charlize/book", "serve:charlize:app": "APP_MODE=app nx serve charlize", "serve:charlize:book": "APP_MODE=book nx serve charlize", "serve:charlize:app:node:18": "NODE_OPTIONS=--openssl-legacy-provider APP_MODE=app nx serve charlize", "serve:charlize:book:node:18": "NODE_OPTIONS=--openssl-legacy-provider APP_MODE=book nx serve charlize", "build:ci": "npm run build:charlize:app && npm run build:charlize:book", "http-server": "npx http-server dist/apps/charlize --push-state -p 3000", "build": "NODE_ENV=production nx build", "lint": "nx workspace-lint && nx lint", "affected:apps": "nx affected:apps", "affected:libs": "nx affected:libs", "affected:build": "nx affected:build", "affected:test": "nx affected:test", "affected:lint": "nx affected:lint", "affected:dep-graph": "nx affected:dep-graph", "affected": "nx affected", "format": "nx format:write", "format:write": "nx format:write", "format:check": "nx format:check", "workspace-generator": "nx workspace-generator", "dep-graph": "nx dep-graph", "help": "nx help", "postinstall": "node node_modules/@nx-plus/vue/patch-nx-dep-graph.js"}, "dependencies": {"@kouts/vue-modal": "^2.1.0", "@nestjs/common": "^7.6.18", "@nestjs/core": "^7.6.18", "@nestjs/platform-express": "^7.6.18", "@nestjs/platform-ws": "^7.6.18", "@nestjs/websockets": "^7.6.18", "@types/lodash-es": "^4.17.5", "@vueuse/motion": "^2.0.0-beta.9", "ag-grid-community": "^29.1.0", "ag-grid-vue": "^29.1.0", "ant-design-vue": "^1.7.8", "asva-executors": "^0.1.26", "chroma-js": "^2.1.2", "core-js": "^3.20.3", "date-fns": "^2.28.0", "detect-browser": "^5.3.0", "es6-promise": "^4.2.8", "express": "^4.17.2", "fast-json-patch": "^3.1.0", "faye": "^1.4.0", "fflate": "^0.7.3", "graphql": "^16.1.0", "graphql-request": "^3.7.0", "gsap": "^3.9.1", "is-browser": "^2.1.0", "js-graph-algorithms": "^1.0.18", "jsondiffpatch": "^0.4.1", "jszip": "^3.7.1", "lodash": "^4.17.21", "moment-timezone": "^0.5.34", "node-static": "^0.7.11", "npm-check": "^5.9.2", "pako": "^2.0.4", "prettyjson": "^1.2.5", "ravendb": "^5.2.0", "redis": "^4.0.2", "reflect-metadata": "^0.1.13", "superagent": "^7.1.1", "threads": "^1.7.0", "ts-enum-util": "^4.0.2", "tslib": "^2.3.1", "typescript-ioc": "^3.2.2", "uuid": "^8.3.2", "v-drag": "^2.1.3", "vue": "^2.6.14", "vue-class-component": "^7.2.6", "vue-cleave-component": "^2.1.3", "vue-epic-bus": "^0.1.5", "vue-json-compare": "^3.0.0", "vue-json-component": "^0.4.1", "vue-json-pretty": "^1.8.2", "vue-json-viewer": "^2.2.21", "vue-pose": "^0.5.1", "vue-property-decorator": "^9.1.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.3", "vue-text-transition": "^1.0.19", "vue-vis-network": "^1.0.2", "vue-window-size": "^1.1.2", "ws": "^8.4.1"}, "devDependencies": {"@angular-devkit/core": "^12.2.3", "@babel/preset-env": "^7.16.11", "@nestjs/schematics": "^7.3.1", "@nestjs/testing": "^7.6.18", "@nrwl/cli": "12.8.0", "@nrwl/eslint-plugin-nx": "12.8.0", "@nrwl/express": "12.8.0", "@nrwl/jest": "12.8.0", "@nrwl/linter": "12.8.0", "@nrwl/nest": "12.8.0", "@nrwl/node": "12.8.0", "@nrwl/tao": "12.8.0", "@nrwl/workspace": "12.8.0", "@nx-plus/vue": "^12.2.0", "@types/chroma-js": "^2.1.3", "@types/express": "^4.17.13", "@types/jest": "^27.4.0", "@types/node": "14.17.0", "@types/pako": "^1.0.3", "@types/prettyjson": "^0.0.30", "@types/superagent": "^4.1.14", "@types/vue-json-compare": "^3.0.0", "@types/ws": "^8.2.2", "@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "@vue/cli-plugin-typescript": "^4.5.15", "@vue/cli-service": "^4.5.15", "@vue/eslint-config-typescript": "^9.1.0", "@vue/test-utils": "^1.3.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^27.4.5", "dot": "^2.0.0-beta.1", "dotenv": "10.0.0", "ejs": "^3.1.6", "eslint": "^8.7.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-vue": "^8.3.0", "file-loader": "^6.2.0", "global-jsdom": "^8.4.0", "jest": "^27.4.5", "jest-serializer-vue": "^2.0.2", "jest-transform-stub": "^2.0.0", "jsdom": "^19.0.0", "mini-css-extract-plugin": "^1.6.2", "npm-check-updates": "^12.2.1", "prettier": "^2.5.1", "sass": "^1.49.0", "sass-loader": "^12.4.0", "spa-http-server": "^0.9.0", "speed-measure-webpack-plugin": "^1.5.0", "threads-plugin": "^1.4.0", "ts-jest": "^27.1.1", "ts-node": "^10.4.0", "typescript": "^4.5.5", "vue-book": "0.1.0-alpha.43", "vue-jest": "^3.0.7", "vue-template-compiler": "^2.6.14"}}