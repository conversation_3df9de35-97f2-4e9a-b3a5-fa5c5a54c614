{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2016", "module": "esnext", "lib": ["es2019", "dom", "esnext.array"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@au21-frontend/client-connector": ["libs/client-connector/src/index.ts"], "@au21-frontend/utils": ["libs/utils/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}