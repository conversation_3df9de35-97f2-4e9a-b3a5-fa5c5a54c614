This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

================================================================
Directory Structure for project au21-frontend
================================================================
libs/
  client-connector/
    src/
      _generated/
        generated.ts
      lib/
        ClientCommandHandler.ts
        SocketConnector.ts

================================================================
Files
================================================================

================
File: libs/client-connector/src/_generated/generated.ts
================
/***************************************************************************
 *
 * ENUMS
 *
 ***************************************************************************/

export enum ActivityRule {
	ABSOLUTE = 'ABSOLUTE',
	RATIO = 'RATIO'
}

export enum AuMessageType {
	AUCTIONEER_BROADCAST = 'AUCTIONEER_BROADCAST',
	AUCTIONEER_TO_TRADER = 'AUCTIONEER_TO_TRADER',
	TRADER_TO_AUCTIONEER = 'TRADER_TO_AUCTIONEER',
	SYSTEM_BROADCAST = 'SYSTEM_BROADCAST',
	SYSTEM_TO_TRADER = 'SYSTEM_TO_TRADER',
	SYSTEM_TO_AUCTIONEER = 'SYSTEM_TO_AUCTIONEER'
}

export enum ClientSocketState {
	OPENED = 'OPENED',
	CLOSED = 'CLOSED'
}

export enum SessionTerminationReason {
	BROWSER_UNLOADED = 'BROWSER_UNLOADED',
	COMPANY_DELETED = 'COMPANY_DELETED',
	COMPANY_NAME_EDITED = 'COMPANY_NAME_EDITED',
	FORCED_OFF = 'FORCED_OFF',
	LOGIN_FROM_ANOTHER_BROWSER = 'LOGIN_FROM_ANOTHER_BROWSER',
	SERVER_REBOOT = 'SERVER_REBOOT',
	SERVER_SWEPT_STALE_SESSION = 'SERVER_SWEPT_STALE_SESSION',
	SIGNED_OFF = 'SIGNED_OFF',
	USER_EDITED = 'USER_EDITED',
	USER_DELETED = 'USER_DELETED'
}

export enum AuUserRole {
	AUCTIONEER = 'AUCTIONEER',
	TRADER = 'TRADER'
}

export enum AuctionInstruction {
	HIDE = 'HIDE',
	UNHIDE = 'UNHIDE',
	DELETE = 'DELETE'
}

export enum AutopilotMode {
	DISENGAGED = 'DISENGAGED',
	ENGAGED = 'ENGAGED'
}

export enum Crud {
	CREATE = 'CREATE',
	READ = 'READ',
	UPDATE = 'UPDATE',
	DELETE = 'DELETE',
	ADD = 'ADD',
	REMOVE = 'REMOVE',
	CLEAR = 'CLEAR'
}

export enum Operator {
	label = 'label',
	GT = 'GT',
	GE = 'GE'
}

export enum OrderSubmissionType {
	MANUAL = 'MANUAL',
	DEFAULT = 'DEFAULT',
	MANDATORY = 'MANDATORY'
}

export enum OrderType {
	BUY = 'BUY',
	SELL = 'SELL',
	NONE = 'NONE'
}

export enum PriceDirection {
	UP = 'UP',
	DOWN = 'DOWN'
}

export enum StopMode {
	LT = 'LT',
	LE = 'LE',
	NONE = 'NONE'
}

export enum Visibility {
	ALL = 'ALL',
	FIRST_ROUND = 'FIRST_ROUND',
	ELIGIBILITY = 'ELIGIBILITY'
}

export enum ResultType {
	ALERT = 'ALERT',
	OBJECT = 'OBJECT',
	ARRAY_ITEM = 'ARRAY_ITEM'
}

export enum DeAuctioneerInfoLevel {
	NORMAL = 'NORMAL',
	WARNING = 'WARNING',
	ERROR = 'ERROR'
}

export enum DeAuctioneerState {
	STARTING_PRICE_NOT_SET = 'STARTING_PRICE_NOT_SET',
	STARTING_PRICE_SET = 'STARTING_PRICE_SET',
	STARTING_PRICE_ANNOUNCED = 'STARTING_PRICE_ANNOUNCED',
	ROUND_OPEN_ALL_ORDERS_NOT_IN = 'ROUND_OPEN_ALL_ORDERS_NOT_IN',
	ROUND_OPEN_ALL_ORDERS_IN = 'ROUND_OPEN_ALL_ORDERS_IN',
	ROUND_CLOSED_NOT_AWARDABLE = 'ROUND_CLOSED_NOT_AWARDABLE',
	ROUND_CLOSED_AWARDABLE = 'ROUND_CLOSED_AWARDABLE',
	AUCTION_CLOSED = 'AUCTION_CLOSED'
}

export enum DeCommonState {
	SETUP = 'SETUP',
	STARTING_PRICE_ANNOUNCED = 'STARTING_PRICE_ANNOUNCED',
	ROUND_OPEN = 'ROUND_OPEN',
	ROUND_CLOSED = 'ROUND_CLOSED',
	AUCTION_CLOSED = 'AUCTION_CLOSED'
}

export enum DeCreditSetMode {
	MANUAL = 'MANUAL',
	MINIMUM = 'MINIMUM'
}

export enum DeFlowControlType {
	HEARTBEAT = 'HEARTBEAT',
	SET_STARTING_PRICE = 'SET_STARTING_PRICE',
	ANNOUNCE_STARTING_PRICE = 'ANNOUNCE_STARTING_PRICE',
	START_AUCTION = 'START_AUCTION',
	CLOSE_ROUND = 'CLOSE_ROUND',
	REOPEN_ROUND = 'REOPEN_ROUND',
	NEXT_ROUND = 'NEXT_ROUND',
	AWARD_AUCTION = 'AWARD_AUCTION'
}

export enum DeRoundOpenState {
	GREEN = 'GREEN',
	ORANGE = 'ORANGE',
	RED = 'RED'
}

export enum DeRoundState {
	NOT_OPEN = 'NOT_OPEN',
	GREEN = 'GREEN',
	ORANGE = 'ORANGE',
	RED = 'RED'
}

export enum DeTimeState {
	BEFORE_ANNOUNCE_TIME = 'BEFORE_ANNOUNCE_TIME',
	BEFORE_START_TIME = 'BEFORE_START_TIME',
	AUCTION_HAS_STARTED = 'AUCTION_HAS_STARTED'
}

export enum SampleOrderMove {
	INCREASE = 'INCREASE',
	DECREASE = 'DECREASE'
}

export enum PageName {
	CREDITOR_AUCTIONEER_PAGE = 'CREDITOR_AUCTIONEER_PAGE',
	CREDITOR_TRADER_PAGE = 'CREDITOR_TRADER_PAGE',
	HOME_PAGE = 'HOME_PAGE',
	LOGIN_PAGE = 'LOGIN_PAGE',
	SESSION_PAGE = 'SESSION_PAGE',
	USER_PAGE = 'USER_PAGE',
	BH_AUCTIONEER_PAGE = 'BH_AUCTIONEER_PAGE',
	BH_SETUP_PAGE = 'BH_SETUP_PAGE',
	BH_TRADER_PAGE = 'BH_TRADER_PAGE',
	DE_AUCTIONEER_PAGE = 'DE_AUCTIONEER_PAGE',
	DE_SETUP_PAGE = 'DE_SETUP_PAGE',
	DE_TRADER_PAGE = 'DE_TRADER_PAGE',
	MR_AUCTIONEER_PAGE = 'MR_AUCTIONEER_PAGE',
	MR_SETUP_PAGE = 'MR_SETUP_PAGE',
	MR_TRADER_PAGE = 'MR_TRADER_PAGE',
	TE_AUCTIONEER_PAGE = 'TE_AUCTIONEER_PAGE',
	TE_SETUP_PAGE = 'TE_SETUP_PAGE',
	TE_TRADER_PAGE = 'TE_TRADER_PAGE',
	TO_AUCTIONEER_PAGE = 'TO_AUCTIONEER_PAGE',
	TO_SETUP_PAGE = 'TO_SETUP_PAGE',
	TO_TRADER_PAGE = 'TO_TRADER_PAGE'
}

export enum BrowserMessageIcon {
	SUCCESS = 'SUCCESS',
	INFO = 'INFO',
	WARNING = 'WARNING',
	AUCTIONEER_MESSAGE = 'AUCTIONEER_MESSAGE',
	TRADER_MESSAGE = 'TRADER_MESSAGE',
	SYSTEM_MESSAGE = 'SYSTEM_MESSAGE',
	ORDER_CONFIRMATION = 'ORDER_CONFIRMATION'
}

export enum BrowserMessageKind {
	ALERT = 'ALERT',
	NOTIFICATION = 'NOTIFICATION'
}

export enum CommandType {
	CommandSucceeded = 'CommandSucceeded',
	ShowMessage = 'ShowMessage',
	TerminateSession = 'TerminateSession',
	NetworkDown = 'NetworkDown',
	NetworkUp = 'NetworkUp',
	SetLiveStore = 'SetLiveStore',
	AddElements = 'AddElements'
}

/***************************************************************************
 *
 * ENGINE COMMAND (aka Requests) HELPERS
 *
 ***************************************************************************/
export interface EngineCommandEnvelope {
    session_id: string
    readonly simplename: string
    readonly classname: string
    readonly command: EngineCommand
}

function create_command<T extends EngineCommand>(
    simplename: string,
    classname: string,
    command: T
): EngineCommandEnvelope {
    return {
        session_id: "",
        simplename,
        classname,
        command
    } as EngineCommandEnvelope
}
export const auction_row_command = (req: AuctionRowCommand) => create_command('AuctionRowCommand', 'au21.engine.domain.common.commands.AuctionRowCommand', req)

export const auction_select_command = (req: AuctionSelectCommand) => create_command('AuctionSelectCommand', 'au21.engine.domain.common.commands.AuctionSelectCommand', req)

export const client_socket_command = (req: ClientSocketCommand) => create_command('ClientSocketCommand', 'au21.engine.domain.common.commands.ClientSocketCommand', req)

export const company_delete_command = (req: CompanyDeleteCommand) => create_command('CompanyDeleteCommand', 'au21.engine.domain.common.commands.CompanyDeleteCommand', req)

export const company_save_command = (req: CompanySaveCommand) => create_command('CompanySaveCommand', 'au21.engine.domain.common.commands.CompanySaveCommand', req)

export const db_delete_auctions_command = () => create_command('DbDeleteAuctionsCommand', 'au21.engine.domain.common.commands.DbDeleteAuctionsCommand', {})

export const db_init_command = () => create_command('DbInitCommand', 'au21.engine.domain.common.commands.DbInitCommand', {})

export const errors_send_command = (req: ErrorsSendCommand) => create_command('ErrorsSendCommand', 'au21.engine.domain.common.commands.ErrorsSendCommand', req)

export const login_command = (req: LoginCommand) => create_command('LoginCommand', 'au21.engine.domain.common.commands.LoginCommand', req)

export const message_send_command = (req: MessageSendCommand) => create_command('MessageSendCommand', 'au21.engine.domain.common.commands.MessageSendCommand', req)

export const notice_save_command = (req: NoticeSaveCommand) => create_command('NoticeSaveCommand', 'au21.engine.domain.common.commands.NoticeSaveCommand', req)

export const page_set_command = (req: PageSetCommand) => create_command('PageSetCommand', 'au21.engine.domain.common.commands.PageSetCommand', req)

export const session_terminate_command = (req: SessionTerminateCommand) => create_command('SessionTerminateCommand', 'au21.engine.domain.common.commands.SessionTerminateCommand', req)

export const user_delete_command = (req: UserDeleteCommand) => create_command('UserDeleteCommand', 'au21.engine.domain.common.commands.UserDeleteCommand', req)

export const user_save_command = (req: UserSaveCommand) => create_command('UserSaveCommand', 'au21.engine.domain.common.commands.UserSaveCommand', req)

export const de_auction_award_command = (req: DeAuctionAwardCommand) => create_command('DeAuctionAwardCommand', 'au21.engine.domain.de.commands.DeAuctionAwardCommand', req)

export const de_auction_save_command = (req: DeAuctionSaveCommand) => create_command('DeAuctionSaveCommand', 'au21.engine.domain.de.commands.DeAuctionSaveCommand', req)

export const de_auctions_tick_command = () => create_command('DeAuctionsTickCommand', 'au21.engine.domain.de.commands.DeAuctionsTickCommand', {})

export const de_create_sample_db_command = (req: DeCreateSampleDbCommand) => create_command('DeCreateSampleDbCommand', 'au21.engine.domain.de.commands.DeCreateSampleDbCommand', req)

export const de_credit_set_command = (req: DeCreditSetCommand) => create_command('DeCreditSetCommand', 'au21.engine.domain.de.commands.DeCreditSetCommand', req)

export const de_flow_control_command = (req: DeFlowControlCommand) => create_command('DeFlowControlCommand', 'au21.engine.domain.de.commands.DeFlowControlCommand', req)

export const de_order_submit_command = (req: DeOrderSubmitCommand) => create_command('DeOrderSubmitCommand', 'au21.engine.domain.de.commands.DeOrderSubmitCommand', req)

export const de_round_history_command = (req: DeRoundHistoryCommand) => create_command('DeRoundHistoryCommand', 'au21.engine.domain.de.commands.DeRoundHistoryCommand', req)

export const de_template_delete_command = (req: DeTemplateDeleteCommand) => create_command('DeTemplateDeleteCommand', 'au21.engine.domain.de.commands.DeTemplateDeleteCommand', req)

export const de_template_save_command = () => create_command('DeTemplateSaveCommand', 'au21.engine.domain.de.commands.DeTemplateSaveCommand', {})

export const de_trader_limits_command = (req: DeTraderLimitsCommand) => create_command('DeTraderLimitsCommand', 'au21.engine.domain.de.commands.DeTraderLimitsCommand', req)

export const de_traders_add_command = (req: DeTradersAddCommand) => create_command('DeTradersAddCommand', 'au21.engine.domain.de.commands.DeTradersAddCommand', req)

export const de_traders_remove_command = (req: DeTradersRemoveCommand) => create_command('DeTradersRemoveCommand', 'au21.engine.domain.de.commands.DeTradersRemoveCommand', req)

export const heartbeat_command = () => create_command('HeartbeatCommand', 'au21.engine.framework.commands.HeartbeatCommand', {})

/***************************************************************************
 *
 * ENGINE COMMAND (REQUEST) CLASSES
 *
 ***************************************************************************/
export interface AuctionRowCommand extends EngineCommand {
    auction_id: string;
    instruction: AuctionInstruction;
}

export interface AuctionSelectCommand extends EngineCommand {
    auction_id: string;
}

export interface ClientSocketCommand extends EngineCommand {
    browser_name: string | null;
    browser_os: string | null;
    browser_version: string | null;
    sid: string;
    state: ClientSocketState;
}

export interface CompanyDeleteCommand extends EngineCommand {
    company_id: string;
}

export interface CompanySaveCommand extends EngineCommand {
    company_id: string;
    company_longname: string;
    company_shortname: string;
}

export interface DbDeleteAuctionsCommand extends EngineCommand {
}

export interface DbInitCommand extends EngineCommand {
}

export interface DeAuctionAwardCommand extends EngineCommand {
    auction_id: string;
    round_number: string;
}

export interface DeAuctionSaveCommand extends EngineCommand {
    auction_id: string;
    auction_name: string;
    cost_multiplier: string;
    excess_level_0_label: string;
    excess_level_1_label: string;
    excess_level_1_quantity: string;
    excess_level_2_label: string;
    excess_level_2_quantity: string;
    excess_level_3_label: string;
    excess_level_3_quantity: string;
    excess_level_4_label: string;
    excess_level_4_quantity: string;
    month_is_1_based: boolean;
    price_change_initial: string;
    price_change_post_reversal: string;
    price_decimal_places: string;
    price_label: string;
    quantity_label: string;
    quantity_minimum: string;
    quantity_step: string;
    round_closed_min_secs: string;
    round_open_min_seconds: string;
    round_orange_secs: string;
    round_red_secs: string;
    starting_day: string;
    starting_hour: string;
    starting_mins: string;
    starting_month: string;
    starting_price_announcement_mins: string;
    starting_year: string;
    use_counterparty_credits: string;
}

export interface DeAuctionsTickCommand extends EngineCommand {
}

export interface DeCreateSampleDbCommand extends EngineCommand {
    auction_count: number;
    auctioneer_count: number;
    close_last_round: boolean;
    round_count: number;
    trader_count: number;
    use_counterparty_credits: boolean;
}

export interface DeCreditSetCommand extends EngineCommand {
    auction_id: string;
    borrower_id: string;
    credit_limit: string;
    lender_id: string;
}

export interface DeFlowControlCommand extends EngineCommand {
    auction_id: string;
    control: DeFlowControlType;
    starting_price: string | null;
}

export interface DeOrderSubmitCommand extends EngineCommand {
    auction_id: string;
    company_id: string;
    order_type: OrderType;
    quantity: string;
    round: string;
}

export interface DeRoundHistoryCommand extends EngineCommand {
    auction_id: string;
    round_number: string;
}

export interface DeTemplateDeleteCommand extends EngineCommand {
    template_id: string;
}

export interface DeTemplateSaveCommand extends EngineCommand {
}

export interface DeTraderLimitsCommand extends EngineCommand {
    auction_id: string;
    buying_cost_limit: string;
    company_id: string;
    selling_quantity_limit: string;
}

export interface DeTradersAddCommand extends EngineCommand {
    auction_id: string;
    company_ids: string[];
}

export interface DeTradersRemoveCommand extends EngineCommand {
    auction_id: string;
    company_ids: string[];
}

export interface EngineCommand {
}

export interface ErrorsSendCommand extends EngineCommand {
    auction_id: string;
    error: string;
    trader_session_id: string;
}

export interface HeartbeatCommand extends EngineCommand {
}

export interface LoginCommand extends EngineCommand {
    password: string;
    username: string;
}

export interface MessageSendCommand extends EngineCommand {
    auction_id: string;
    message: string;
}

export interface NoticeSaveCommand extends EngineCommand {
    auction_id: string;
    notice: string;
}

export interface PageSetCommand extends EngineCommand {
    page: PageName;
}

export interface SessionTerminateCommand extends EngineCommand {
    reason: SessionTerminationReason;
}

export interface UserDeleteCommand extends EngineCommand {
    user_id: string;
}

export interface UserSaveCommand extends EngineCommand {
    company_id: string;
    email: string;
    password: string;
    phone: string;
    role: AuUserRole;
    user_id: string;
    username: string;
}
/***************************************************************************
 *
 * CLIENT STORE COMMANDS (aka Results)
 *
 ***************************************************************************/
export interface AddElements extends StoreCommand {
    command: CommandType;
    elements: StoreElement[] | null;
    path: string;
}

export interface AuctionRowElement extends StoreElement {
    auction_design: string;
    auction_id: string;
    auction_name: string;
    common_state_text: string;
    id: string;
    isClosed: boolean;
    isHidden: boolean;
    starting_time_text: string;
}

export interface ClientCommand {
    command: CommandType;
}

export interface CommandSucceeded extends ClientCommand {
    command: CommandType;
}

export interface CompanyElement extends StoreElement {
    company_id: string;
    company_longname: string;
    company_shortname: string;
    id: string;
}

export interface CounterpartyCreditElement extends StoreElement {
    buyer_id: string;
    buyer_longname: string;
    buyer_shortname: string;
    id: string;
    limit_str: string;
    seller_id: string;
    seller_longname: string;
    seller_shortname: string;
}

export interface Date {
}

export interface DateTimeValue extends StoreValue {
    day_of_month: number;
    day_of_week: number;
    hour: number;
    minutes: number;
    month: number;
    seconds: number;
    year: number;
}

export interface DeAuctionValue extends StoreValue {
    auction_counterparty_credits: CounterpartyCreditElement[];
    auction_id: string | null;
    auctioneer_info: DeAuctioneerInfoValue | null;
    auctioneer_status: DeAuctioneerStatusValue | null;
    award_value: DeAwardValue | null;
    blotter: DeBlotter | null;
    common_status: DeCommonStatusValue | null;
    matrix_last_round: DeMatrixRoundElement | null;
    messages: MessageElement[];
    notice: string;
    settings: DeSettingsValue | null;
    trader_history_rows: DeTraderHistoryRowElement[];
    trader_info: DeTraderInfoValue | null;
    users_that_have_seen_auction: string[];
}

export interface DeAuctioneerInfoValue extends StoreValue {
    allow_credit_editing: boolean;
    last_buyers: string;
    last_excess: string;
    last_match: string;
    last_round: number;
    last_sell_dec: string;
    last_sellers: string;
    last_total_buy: string;
    last_total_sell: string;
    pen_buyers: string;
    pen_excess: string;
    pen_match: string;
    pen_round: string;
    pen_sell_dec: string;
    pen_sellers: string;
    pen_total_buy: string;
    pen_total_sell: string;
    potential: string;
}

export interface DeAuctioneerStatusValue extends StoreValue {
    announced: boolean;
    auctioneer_state: DeAuctioneerState;
    auctioneer_state_text: string;
    autopilot: AutopilotMode;
    awardable: boolean;
    controls: { [key in DeFlowControlType]: boolean };
    excess_level: string;
    excess_side: OrderType;
    price_has_overshot: boolean;
    round_open_min_secs: number | null;
    starting_price: string;
    time_state: DeTimeState | null;
}

export interface DeAwardValue extends StoreValue {
    round_results: DeRoundResultVM[];
}

export interface DeBidConstraints {
    max_buy_quantity: number;
    max_sell_quantity: number;
    min_buy_quantity: number;
    min_sell_quantity: number;
}

export interface DeBlotter {
    round_traders: DeRoundTraderElement[];
    rounds: DeRoundElement[];
    traders: DeTraderElement[];
}

export interface DeCommonStatusValue extends StoreValue {
    common_state: DeCommonState;
    common_state_text: string;
    isClosed: boolean;
    price_direction: PriceDirection | null;
    price_has_reversed: boolean;
    round_number: number;
    round_price: string;
    round_seconds: number;
    starting_price_announced: boolean;
    starting_time_text: string;
}

export interface DeInitialLimits {
    initial_buying_cost_limit: number;
    initial_buying_cost_limit_str: string;
    initial_selling_quantity_limit: number;
    initial_selling_quantity_limit_str: string;
}

export interface DeMatrixEdgeElement extends StoreElement {
    buy_quantity_limit: number;
    buyer_cid: string;
    buyer_shortname: string;
    capacity: number;
    credit_quantity_limit: number;
    credit_str: string;
    id: string;
    match: number;
    r: number;
    seller_cid: string;
    seller_shortname: string;
    selling_quantity_limit: number;
    value: number;
    value_str: string;
}

export interface DeMatrixNodeElement extends StoreElement {
    buy_match: number;
    buy_max: number;
    buy_min: number;
    buy_quantity: number;
    cid: string;
    cost: number;
    cost_str: string;
    id: string;
    round: number;
    sell_match: number;
    sell_max: number;
    sell_min: number;
    sell_quantity: number;
    shortname: string;
    side: OrderType;
}

export interface DeMatrixRoundElement extends StoreElement {
    edges: DeMatrixEdgeElement[];
    id: string;
    nodes: DeMatrixNodeElement[];
    round_number: number;
}

export interface DeRoundElement extends StoreElement {
    all_orders_in_next_round_will_be_mandatory: boolean;
    buy_quantity: number;
    buyer_count: number;
    excess_indicator: string;
    excess_quantity: number;
    excess_side: OrderType;
    has_reversed: boolean;
    id: string;
    match_quantity_changed: number;
    matched: number;
    potential: number;
    potential_changed: number;
    raw_matched: number;
    round_direction: PriceDirection | null;
    round_duration: string;
    round_number: number;
    round_price: number | null;
    round_price_str: string;
    sell_quantity: number;
    sell_quantity_change: number;
    seller_count: number;
}

export interface DeRoundResultVM {
    buy_total: string;
    match_total: string;
    matches: DeScenarioMatchVM[];
    round_number: number;
    round_price: string;
    sell_total: string;
    trader_flows: DeTraderFlowVM[];
}

export interface DeRoundTraderElement extends StoreElement {
    bid_while_closed: boolean;
    buyer_credit_limit: number;
    buyer_credit_limit_str: string;
    changed: boolean;
    cid: string;
    company_shortname: string;
    constraints: DeBidConstraints;
    id: string;
    match: number;
    order_submission_type: OrderSubmissionType;
    order_submitted_by: string;
    order_type: OrderType;
    quantity_int: number;
    quantity_str: string;
    round: number;
    timestamp_formatted: string;
}

export interface DeScenarioMatchVM {
    actual_match: number;
    actual_match_str: string;
    buyer_id: string;
    buyer_shortname: string;
    round_number: number;
    seller_id: string;
    seller_shortname: string;
}

export interface DeSettingsValue extends StoreValue {
    auction_name: string;
    cost_multiplier: string;
    excess_level_0_label: string;
    excess_level_1_label: string;
    excess_level_1_quantity: string;
    excess_level_2_label: string;
    excess_level_2_quantity: string;
    excess_level_3_label: string;
    excess_level_3_quantity: string;
    excess_level_4_label: string;
    excess_level_4_quantity: string;
    price_change_initial: string;
    price_change_post_reversal: string;
    price_decimal_places: number;
    price_label: string;
    quantity_label: string;
    quantity_minimum: string;
    quantity_step: string;
    round_closed_min_secs: number;
    round_open_min_secs: number;
    round_orange_secs: number;
    round_red_secs: number;
    starting_price_announcement_mins: number;
    starting_time: DateTimeValue | null;
    use_counterparty_credits: boolean;
}

export interface DeTraderElement extends StoreElement {
    company_id: string;
    has_seen_auction: boolean;
    id: string;
    rank: number | null;
    shortname: string;
}

export interface DeTraderFlowVM {
    company_id: string;
    company_shortname: string;
    order_type: OrderType;
    quantity: string;
}

export interface DeTraderHistoryRowElement extends StoreElement {
    auction_id: string;
    bid_constraints: DeBidConstraints | null;
    company_id: string;
    excess_level: string;
    excess_side: OrderType | null;
    id: string;
    order_submission_type: OrderSubmissionType;
    order_submitted_by: string;
    order_type: OrderType | null;
    price_direction: PriceDirection | null;
    price_has_reversed: boolean;
    price_suffix: string;
    quantity: string;
    round_number: string;
    round_price: string;
    value: string;
}

export interface DeTraderInfoValue extends StoreValue {
    auction_id: string;
    award_direction: string;
    award_line: string | null;
    awarded_price: string;
    awarded_quantity: string;
    awarded_round_number: string;
    awarded_value: string;
    bid_constraints: DeBidConstraints;
    company_id: string;
    initial_limits: DeInitialLimits;
    order_quantity: number;
    order_submission_type: OrderSubmissionType;
    order_type: OrderType;
    price_label: string;
    quantity_label: string;
    round_number: number;
    round_price: string;
    value: string;
}

export interface MessageElement extends StoreElement {
    from: string;
    id: string;
    message: string;
    message_type: AuMessageType;
    message_type_label: string;
    timestamp: number;
    timestamp_label: string;
    to: string;
}

export interface NetworkDown extends ClientCommand {
    command: CommandType;
}

export interface NetworkUp extends ClientCommand {
    command: CommandType;
}

export interface SessionUserValue extends StoreValue {
    company_id: string;
    company_longname: string;
    company_shortname: string;
    current_auction_id: string;
    current_page: PageName;
    isAuctioneer: boolean;
    isOnline: boolean;
    role: AuUserRole | null;
    session_id: string;
    socket_state: ClientSocketState;
    user_id: string;
    username: string;
}

export interface SetLiveStore extends StoreCommand {
    command: CommandType;
    store: LiveClientStore;
}

export interface ShowMessage extends ClientCommand {
    browser_message_kind: BrowserMessageKind;
    command: CommandType;
    message: string[];
}

export interface StoreCommand extends ClientCommand {
    command: CommandType;
}

export interface StoreElement {
    id: string;
}

export interface StoreValue {
}

export interface TerminateSession extends ClientCommand {
    command: CommandType;
    message: string | null;
}

export interface TimeValue extends StoreValue {
    city: string;
    date_time: DateTimeValue;
}

export interface UserElement extends StoreElement {
    company_id: string;
    company_longname: string;
    company_shortname: string;
    current_auction_id: string | null;
    email: string;
    has_connection_problem: boolean;
    id: string;
    isAuctioneer: boolean;
    isObserver: boolean;
    isOnline: boolean;
    isTester: boolean;
    password: string;
    phone: string;
    role: AuUserRole;
    socket_state: ClientSocketState | null;
    socket_state_last_closed: Date | null;
    termination_reason: SessionTerminationReason | null;
    user_id: string;
    username: string;
}

// REDUNDANT INFO IS OK HERE BECAUSE THESE ARE GENERATED CLASSES

// - eg: properties are always given default values for reactivity to work

export class AuStore{
	time: TimeValue | null = null
	seconds_since_last_message_received: number = 0
}


export class LiveClientStore extends AuStore {
	auction_rows: AuctionRowElement[] = []
	companies: CompanyElement[] = []
	counterparty_credits: CounterpartyCreditElement[] = []
	de_auction: DeAuctionValue | null = null
	session_user: SessionUserValue | null = null
	time: TimeValue | null = null
	users: UserElement[] = []
}

export class StaleClientStore{
	stale_de_matrix_rounds: DeMatrixRoundElement[] = []
}

================
File: libs/client-connector/src/lib/ClientCommandHandler.ts
================
import {ClientCommand} from '@au21-frontend/client-connector';


export abstract class ClientCommandHandler {

  abstract handle(c: ClientCommand)

  // display(c: ClientCommand) {
  //   //console.log(m);
  //   console.log(pretty(c));
  // }
}

================
File: libs/client-connector/src/lib/SocketConnector.ts
================
import {AuClient, AuStore, ClientConnectorConfig, EngineCommandEnvelope} from '@au21-frontend/client-connector';
import {Container, OnlyInstantiableByContainer, Singleton} from 'typescript-ioc';
import {ClientCommandHandler} from './ClientCommandHandler';
import {pretty} from '@au21-frontend/utils';
import * as isBrowser from 'is-browser';
import {spawn, Thread, Worker} from 'threads';

// import { create } from 'jsondiffpatch';

@Singleton
@OnlyInstantiableByContainer
export class SocketConnector {

  config = Container.get(ClientConnectorConfig);
  handler = Container.get(ClientCommandHandler);
  client = Container.get(AuClient); // was previously in constructor?
  store = Container.get(AuStore);
  private ws: WebSocket = null;

  private cmd_worker: any; // : Worker | null = null;

  connected = false;

  // outgoing queue:
  private queue: EngineCommandEnvelope[] = [];

  private started = false;

  constructor() {

    /**
     * seems like we can't reliably use constructor injection in production:
     * - so instead we need to start this some time later, to allow config to be set
     */

  }

  async connect() {
    if (this.started) {
      return;
    }
    this.started = true;
    console.log('this.config:', pretty(this.config));

    if (isBrowser) {
      window.addEventListener('beforeunload', (e) => {
        // TODO: remove reliance on generated ?
        // this.publish(common_session_termination_request({
        //   reason: TerminationReason.BROWSER_UNLOADED
        // }))
      });
    }

    // spawn a thread worker:

    if (this.cmd_worker) {
      await Thread.terminate(this.cmd_worker);
    }

    this.cmd_worker = await spawn(new Worker('./command-worker'));


    // from:
    // - https://stackoverflow.com/questions/22431751/websocket-how-to-automatically-reconnect-after-it-dies

    if (this.config.show_connector_log) {
      console.log('SocketConnector.connect()');
    }

    if (this.ws?.CONNECTING || this.ws?.OPEN) {
      //  alert("NOT EXPECTING ws="+this.ws.OPEN ? "OPEN" : "CONNECTING")
      this.ws.close();
    }

    this.ws = new WebSocket(this.config.websocket_url_actual); // `ws://${server}/monitor/${session_id}`);
    this.ws.binaryType = 'arraybuffer';
    this.ws.onopen = () => {
      this.connected = true;
      // if any pending messages then publish
      // - ie: messages added to the queue because we were offline
      this.queue.forEach(msg => this.publish(msg));
      // this.client.onEvent("NetworkUp", {
      //     type: "NetworkUp",
      //     down_seconds: down_time
      //   } as NetworkUp
      // );
    };

    let count = 0;

    this.ws.onmessage = async (e: MessageEvent) => {
      try {
        // TODO: compare performance of pako vs fflate
        // const start = performance.now()

        const arraybuff = await e.data;

        // using thread worker:
        const cmd = await this.cmd_worker(arraybuff);
        if (cmd) { // if error will return null
          // TODO: send back the error??
          if (this.config.show_connector_log) {
            if (!['SetLiveStore', 'AddElements'].includes(cmd.command))
              console.log('received from socket: ' + pretty(cmd)); // no need to see the whole store, we can just look in the Vue devtools
          }
          this.handler.handle(cmd);
        }
      } catch (e) {
        console.error({ e });
      }
    };


    this.ws.onclose = (e: CloseEvent) => {
      this.connected = false;
      console.log('SocketConnector is closed. Reconnect will be attempted in 1 second.', e.reason);
      setTimeout(() => {
        // increment, handler will reset to zero with each message
        this.store.seconds_since_last_message_received++;
        this.connect();
      }, 1000);
    };

    this.ws.onerror = (err: Event) => {
      console.error('SocketConnector encountered error: ', { err }, 'Closing socket');
      this.ws.close();
    };

  }

  publish(envelope: EngineCommandEnvelope) {
    try {
      envelope.session_id = this.config.session_id;
      if (this.connected) {
        const json = JSON.stringify(envelope);
        this.ws.send(json);
        if (this.config.show_connector_log) {
          console.log('>>> sending engine command:');
          console.log(pretty({ command: envelope }));
          console.log('<<<');
        }

      } else {
        this.queue.push(envelope);
        if (this.config.show_connector_log) {
          console.log('not connected, putting message on the queue:',
            JSON.stringify(envelope, null, 2));
        }
      }
    } catch (e) {
      console.error({ e });
    }
  }
}

/*
//console.log(pretty(cmd.store));
// console.log({ store: cmd.store });
Object.keys(cmd.store).forEach(k => {
  this.store[k] = cmd.store[k];
});

// // 1) generate patch operations:
// const start_compare = performance.now();
// const jsondffpatcher = create(); // TODO: add options
// const patches = jsondffpatcher.diff(this.store, cmd.store);
// console.log('diff took: ' + duration_ms_str(start_compare));
// console.log(JSON.stringify({ patches }, null, 2));
//
// // 2) apply patch operations:
// const start_patch = performance.now();
// jsondffpatcher.patch(this.store, patches)
// console.log('patch took: ' + duration_ms_str(start_patch));
} else if (cmd.type === 'CommandSucceeded') {
this.client.onEvent('CommandSucceeded', cmd);
} else if (cmd.type === 'ShowMessage') {
this.client.onEvent('ShowMessage', cmd);
} else if (cmd.type === 'TerminateSession') {
this.client.onEvent('TerminateSession', cmd);
}
 */



================================================================
End of Codebase
================================================================
