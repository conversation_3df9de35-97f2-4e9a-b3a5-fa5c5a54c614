{"version": 2, "cli": {"defaultCollection": "@nrwl/nest"}, "defaultProject": "charlize", "projects": {"charlize": {"projectType": "application", "root": "apps/charlize", "sourceRoot": "apps/charlize/src", "targets": {"build": {"executor": "@nx-plus/vue:browser", "options": {"dest": "dist/apps/charlize", "index": "apps/charlize/public/index.html", "main": "apps/charlize/src/main.ts", "tsConfig": "apps/charlize/tsconfig.app.json"}, "configurations": {"production": {"mode": "production", "filenameHashing": true, "productionSourceMap": true, "css": {"extract": true, "sourceMap": false}}}}, "serve": {"executor": "@nx-plus/vue:dev-server", "options": {"browserTarget": "charlize:build"}, "configurations": {"production": {"browserTarget": "charlize:build:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["apps/charlize/**/*.{ts,tsx,vue}"]}}, "test": {"executor": "@nrwl/jest:jest", "options": {"jestConfig": "apps/charlize/jest.config.js", "passWithNoTests": true}}}}, "client-connector": {"root": "libs/client-connector", "sourceRoot": "libs/client-connector/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["libs/client-connector/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/client-connector"], "options": {"jestConfig": "libs/client-connector/jest.config.js", "passWithNoTests": true}}}}, "monitor-backend": {"root": "apps/monitor/monitor-backend", "sourceRoot": "apps/monitor/monitor-backend/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/monitor/monitor-backend", "main": "apps/monitor/monitor-backend/src/main.ts", "tsConfig": "apps/monitor/monitor-backend/tsconfig.app.json", "assets": ["apps/monitor/monitor-backend/src/assets"]}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/monitor/monitor-backend/src/environments/environment.ts", "with": "apps/monitor/monitor-backend/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "monitor-backend:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["apps/monitor/monitor-backend/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/monitor/monitor-backend"], "options": {"jestConfig": "apps/monitor/monitor-backend/jest.config.js", "passWithNoTests": true}}}}, "session-differ": {"projectType": "application", "root": "apps/monitor/session-differ", "sourceRoot": "apps/monitor/session-differ/src", "targets": {"build": {"executor": "@nx-plus/vue:browser", "options": {"dest": "dist/apps/monitor/session-differ", "index": "apps/monitor/session-differ/public/index.html", "main": "apps/monitor/session-differ/src/main.ts", "tsConfig": "apps/monitor/session-differ/tsconfig.app.json"}, "configurations": {"production": {"mode": "production", "filenameHashing": true, "productionSourceMap": true, "css": {"extract": true, "sourceMap": false}}}}, "serve": {"executor": "@nx-plus/vue:dev-server", "options": {"browserTarget": "session-differ:build"}, "configurations": {"production": {"browserTarget": "session-differ:build:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["apps/monitor/session-differ/**/*.{ts,tsx,vue}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/monitor/session-differ"], "options": {"jestConfig": "apps/monitor/session-differ/jest.config.js", "passWithNoTests": true}}}}, "socket-monitor": {"projectType": "application", "root": "apps/monitor/socket-monitor", "sourceRoot": "apps/monitor/socket-monitor/src", "targets": {"build": {"executor": "@nx-plus/vue:browser", "options": {"dest": "dist/apps/monitor/socket-monitor", "index": "apps/monitor/socket-monitor/public/index.html", "main": "apps/monitor/socket-monitor/src/main.ts", "tsConfig": "apps/monitor/socket-monitor/tsconfig.app.json"}, "configurations": {"production": {"mode": "production", "filenameHashing": true, "productionSourceMap": true, "css": {"extract": true, "sourceMap": false}}}}, "serve": {"executor": "@nx-plus/vue:dev-server", "options": {"browserTarget": "socket-monitor:build"}, "configurations": {"production": {"browserTarget": "socket-monitor:build:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["apps/monitor/socket-monitor/**/*.{ts,tsx,vue}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/monitor/socket-monitor"], "options": {"jestConfig": "apps/monitor/socket-monitor/jest.config.js", "passWithNoTests": true}}}}, "utils": {"root": "libs/utils", "sourceRoot": "libs/utils/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "options": {"lintFilePatterns": ["libs/utils/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/utils"], "options": {"jestConfig": "libs/utils/jest.config.js", "passWithNoTests": true}}}}}}