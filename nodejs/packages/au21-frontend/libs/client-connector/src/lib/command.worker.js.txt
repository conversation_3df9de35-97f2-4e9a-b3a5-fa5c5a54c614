//  /* eslint-disable @typescript-eslint/no-explicit-any, no-restricted-globals, no-extend-native */

// console.log('in worker.ts');

// const ctx: Worker = self as any // for ts

import { inflate } from "pako";

const ctx = self;

ctx.addEventListener("message", async ({ data }) => {
  try {
    //const start = performance.now();
    const unzipped_arr = inflate(data);
    const unzipped_string = new TextDecoder().decode(unzipped_arr);
    const cmd = JSON.parse(unzipped_string);
    //console.log({ unzipped_string });
    //console.log({ cmd });
    //const duration = (performance.now() - start).toFixed(3);
    // console.log(`parsing ${cmd.command} took ${duration} ms`, { cmd });
    ctx.postMessage(cmd);
  } catch (e) {
    console.log(e);
  }
});
