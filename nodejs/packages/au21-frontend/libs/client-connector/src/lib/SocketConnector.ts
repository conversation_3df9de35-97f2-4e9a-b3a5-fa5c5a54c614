import {AuClient, AuStore, ClientConnectorConfig, EngineCommandEnvelope} from '@au21-frontend/client-connector';
import {Container, OnlyInstantiableByContainer, Singleton} from 'typescript-ioc';
import {ClientCommandHandler} from './ClientCommandHandler';
import {pretty} from '@au21-frontend/utils';
import * as isBrowser from 'is-browser';
import {spawn, Thread, Worker} from 'threads';

// import { create } from 'jsondiffpatch';

@Singleton
@OnlyInstantiableByContainer
export class SocketConnector {

  config = Container.get(ClientConnectorConfig);
  handler = Container.get(ClientCommandHandler);
  client = Container.get(AuClient); // was previously in constructor?
  store = Container.get(AuStore);
  private ws: WebSocket = null;

  private cmd_worker: any; // : Worker | null = null;

  connected = false;

  // outgoing queue:
  private queue: EngineCommandEnvelope[] = [];

  private started = false;

  constructor() {

    /**
     * seems like we can't reliably use constructor injection in production:
     * - so instead we need to start this some time later, to allow config to be set
     */

  }

  async connect() {
    if (this.started) {
      return;
    }
    this.started = true;
    console.log('this.config:', pretty(this.config));

    if (isBrowser) {
      window.addEventListener('beforeunload', (e) => {
        // TODO: remove reliance on generated ?
        // this.publish(common_session_termination_request({
        //   reason: TerminationReason.BROWSER_UNLOADED
        // }))
      });
    }

    // spawn a thread worker:

    if (this.cmd_worker) {
      await Thread.terminate(this.cmd_worker);
    }

    this.cmd_worker = await spawn(new Worker('./command-worker'));


    // from:
    // - https://stackoverflow.com/questions/22431751/websocket-how-to-automatically-reconnect-after-it-dies

    if (this.config.show_connector_log) {
      console.log('SocketConnector.connect()');
    }

    if (this.ws?.CONNECTING || this.ws?.OPEN) {
      //  alert("NOT EXPECTING ws="+this.ws.OPEN ? "OPEN" : "CONNECTING")
      this.ws.close();
    }

    this.ws = new WebSocket(this.config.websocket_url_actual); // `ws://${server}/monitor/${session_id}`);
    this.ws.binaryType = 'arraybuffer';
    this.ws.onopen = () => {
      this.connected = true;
      // if any pending messages then publish
      // - ie: messages added to the queue because we were offline
      this.queue.forEach(msg => this.publish(msg));
      // this.client.onEvent("NetworkUp", {
      //     type: "NetworkUp",
      //     down_seconds: down_time
      //   } as NetworkUp
      // );
    };

    let count = 0;

    this.ws.onmessage = async (e: MessageEvent) => {
      try {
        // TODO: compare performance of pako vs fflate
        // const start = performance.now()

        const arraybuff = await e.data;

        // using thread worker:
        const cmd = await this.cmd_worker(arraybuff);
        if (cmd) { // if error will return null
          // TODO: send back the error??
          if (this.config.show_connector_log) {
            if (!['SetLiveStore', 'AddElements'].includes(cmd.command))
              console.log('received from socket: ' + pretty(cmd)); // no need to see the whole store, we can just look in the Vue devtools
          }
          this.handler.handle(cmd);
        }
      } catch (e) {
        console.error({ e });
      }
    };


    this.ws.onclose = (e: CloseEvent) => {
      this.connected = false;
      console.log('SocketConnector is closed. Reconnect will be attempted in 1 second.', e.reason);
      setTimeout(() => {
        // increment, handler will reset to zero with each message
        this.store.seconds_since_last_message_received++;
        this.connect();
      }, 1000);
    };

    this.ws.onerror = (err: Event) => {
      console.error('SocketConnector encountered error: ', { err }, 'Closing socket');
      this.ws.close();
    };

  }

  publish(envelope: EngineCommandEnvelope) {
    try {
      envelope.session_id = this.config.session_id;
      if (this.connected) {
        const json = JSON.stringify(envelope);
        this.ws.send(json);
        if (this.config.show_connector_log) {
          console.log('>>> sending engine command:');
          console.log(pretty({ command: envelope }));
          console.log('<<<');
        }

      } else {
        this.queue.push(envelope);
        if (this.config.show_connector_log) {
          console.log('not connected, putting message on the queue:',
            JSON.stringify(envelope, null, 2));
        }
      }
    } catch (e) {
      console.error({ e });
    }
  }
}

/*
//console.log(pretty(cmd.store));
// console.log({ store: cmd.store });
Object.keys(cmd.store).forEach(k => {
  this.store[k] = cmd.store[k];
});

// // 1) generate patch operations:
// const start_compare = performance.now();
// const jsondffpatcher = create(); // TODO: add options
// const patches = jsondffpatcher.diff(this.store, cmd.store);
// console.log('diff took: ' + duration_ms_str(start_compare));
// console.log(JSON.stringify({ patches }, null, 2));
//
// // 2) apply patch operations:
// const start_patch = performance.now();
// jsondffpatcher.patch(this.store, patches)
// console.log('patch took: ' + duration_ms_str(start_patch));
} else if (cmd.type === 'CommandSucceeded') {
this.client.onEvent('CommandSucceeded', cmd);
} else if (cmd.type === 'ShowMessage') {
this.client.onEvent('ShowMessage', cmd);
} else if (cmd.type === 'TerminateSession') {
this.client.onEvent('TerminateSession', cmd);
}
 */



