// Can't rely on process.env or other define plugin niceties,
// because this is a library, it has no webpack file!
// So: instead we have to pass in all config

import {uuid} from '@au21-frontend/utils';
import {Singleton} from 'typescript-ioc';

/**
 * if WEBSOCKET_URL defined then use it
 * else use the remote host
 */
export const to_websocket_url = (location: Location, session_id: string): string => {
  if (!process.env.WEBSOCKET_URL) {
    // Intended to work with both localhost and staging/production url.
    // http://localhost:8080 -> to ws://localhost:8080/socket/123456
    // https://dev1.auctionologies.com -> to https://dev1.auctionologies.com/socket/123456
    return `${location.protocol.replace('http', 'ws')}//${location.host}/socket/${session_id}`
  }
  return `${process.env.WEBSOCKET_URL}/${session_id}`
}

@Singleton
export class ClientConnectorConfig {
  readonly session_id: string = uuid();
  websocket_url_actual: string;

  /**
   * If we have WEBSOCKET_URL, and it starts with 'ws' then we use it
   * Else we use the remote host
   */
  setURL(location: Location, browser: {name:string, version:string, os:string} | null) {
    this.websocket_url_actual =
      `${to_websocket_url(location, this.session_id)}?browser_name=${browser.name}&browser_version=${browser.version}&browser_os=${browser.os}`
  }

  DISCONNECT_RELOAD_MSEC = 15_000;
  NODE_ENV = process.env.NODE_ENV;
  SESSION_PING_INTERVAL = 5_000;
  SHOW_CONNECTOR_LOG = process.env.SHOW_CONNECTOR_LOG;
  VUE_APP_LOGINS = process.env.VUE_APP_LOGINS;
  VUE_APP_LOG_MESSAGES = process.env.VUE_APP_LOG_MESSAGES;
  VUE_APP_SHOW_DEBUG = process.env.VUE_APP_SHOW_DEBUG;
  WEBSOCKET_URL = process.env.WEBSOCKET_URL;


  is_production = this.NODE_ENV === 'production';
  show_connector_log = this.SHOW_CONNECTOR_LOG === 'true';
  show_debug = this.VUE_APP_SHOW_DEBUG === 'true';


  // SUPPRESS_DISCONNECT: boolean;

  // msec to wait before reloading page, ie: browser-driven disconnect
  // - should be longer than the server driven
  // - TODO: if server pauses for this amount of time for any reason, then we're booted!,
  // - TODO: need to decide if we want clients to disconnect (if there is a server problem, they'll just get a blank screen!!
  // - TODO: server needs to have a better way of handling this, specifically
  //   - server needs to know if the delay was due to it's own processing !
  //   - server should allow user to force re-login
}
