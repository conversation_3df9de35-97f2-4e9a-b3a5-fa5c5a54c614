import Vue from "vue";

// TODO Check what it's doing and rework.
export const WindowInstanceMap = new Vue({
  data () {
    return {
      scrollbar_size: 0,
      height: Math.max(window.innerHeight, document.body.clientHeight),
      width: Math.max(window.innerWidth, document.body.clientHeight),
    }
  },
  created () {
    // 1) scrollbar size:
    this.scrollbar_size = (() => {
      const div1 = window.document.createElement('div')
      const div2 = window.document.createElement('div')

      div1.style.width = '100px'
      div1.style.overflowX = 'scroll'
      div2.style.width = '100px'

      window.document.body.appendChild(div1)
      window.document.body.appendChild(div2)

      const _size = div1.offsetHeight - div2.offsetHeight

      window.document.body.removeChild(div1)
      window.document.body.removeChild(div2)
      return _size
    })()

    window.addEventListener('resize', () => this.refresh())
    this.refresh()
  },
  methods: {
    refresh () {
      this.height = Math.max(window.innerHeight, document.body.clientHeight)
      this.width = Math.max(window.innerWidth, document.body.clientHeight)
    },
  },
})
