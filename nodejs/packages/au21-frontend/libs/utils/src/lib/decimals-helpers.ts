// Add given number of decimal numbers to given numeric value
export function forceDecimals(numericValue: string, decimalPlaces = 0): string {
  if (!numericValue) {
    numericValue = '0'
  }
  const [integerPart, decimalPart] = numericValue.split('.')
  if (!decimalPlaces) {
    return integerPart
  }
  let decimalPartArray = Array.from(decimalPart || '')
  const zerosToAdd = decimalPlaces - decimalPartArray.length
  if (zerosToAdd > 0) {
    decimalPartArray = [...decimalPartArray, ...Array(zerosToAdd).fill('0')]
  }
  if (zerosToAdd < 0) {
    decimalPartArray.length = decimalPlaces
  }
  return `${integerPart}.${decimalPartArray.join('')}`
}
