import {range} from 'lodash';

export function createMultipleByClosure<T>(closure: (index: number) => T, times: number, startFromOne = false): T[] {
  return Array.apply(null, Array(times)).map((value, index) => closure(Number(index + (startFromOne ? 1 : 0))))
}

export const toggle = <T>(array: T[], item: T): T[] => {
  if (array.indexOf(item) === -1) {
    return [...array, item]
  } else {
    return array.filter(existingItem => existingItem !== item)
  }
}

// Mostly intended to be used for ticks on axis.
// We have values, min and max. And we need to squeeze a set number of ticks.
export const rangeFromLimits = (min: number, max: number): number[] => {
  const step = Math.pow(10, (max - min).toFixed().length - 1) // with interval 120 - gives 100 as tick
  const minRounded = Math.ceil(min / step) * step
  const maxRounded = Math.ceil(max / step) * step
  return range(minRounded, maxRounded, step)
}
