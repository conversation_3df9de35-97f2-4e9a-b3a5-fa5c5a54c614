import {sample, takeRight} from 'lodash';
import {format} from 'date-fns';

// @deprecated - too complex
export const random_number_old = (o = { rand: 1, mult: 1 }): number => {
  return Math.round(Math.random() * o.rand) * o.mult
}

export const random_number = (max = 100): number => {
  return Math.ceil(Math.random() * (max + 1))
}

export const random_number_string = (max = 100): string => {
  return random_number(max) + ''
}

export const random_bool = (): boolean => {
  return sample([true, false])
}

// gives true with given probability
export const probability = (value: number): boolean => {
  return Math.random() <= value
}

export const random_digits = (length: number): string => {
  return takeRight('' + Math.floor(Math.random() * 100000), length).join('')
}

export const random_enum = <T>(enumObject: Record<string, T>): T => {
  let result
  let count = 0
  for (const prop in enumObject) {
    if (Math.random() < 1 / ++count) {
      result = prop
    }
  }

  return result
}

export const random_string = (length = 5): string => {
  let result = ''
  const characters = 'abcdefghijklmnopqrstuvwxyz'
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return result
}

export const random_from_array = <T>(array: T[]): T => sample(array)

export const random_timestamp = (): number => Date.now() + random_number(1000000)

export const random_time = (): string => format(random_timestamp(), 'hh:mm:ss')

export const random_date_time = (): string => format(random_timestamp(), 'MM/dd/yyyy hh:mm:ss')
