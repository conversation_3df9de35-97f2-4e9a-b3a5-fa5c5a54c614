import { DateTimeValue } from '@au21-frontend/client-connector';

SEE CHARLIZE ENTITY HELPERS

export default function now_plus_mins_DateTimeValue(mins: number): DateTimeValue {
  const now = new Date(new Date().getTime() + (mins * 60_000));
  console.log(now)
  return {
    day_of_month: now.getDate(),
    day_of_week: now.getDay(),
    hour: now.getHours(),
    minutes: now.getMinutes(),
    month: now.getMonth(),
    seconds: 0,
    year: now.getFullYear()
  };
}
