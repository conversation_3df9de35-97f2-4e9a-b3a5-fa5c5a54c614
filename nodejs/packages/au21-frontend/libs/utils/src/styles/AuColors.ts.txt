/**************************************************************
 * This file needs to be kept in sync with ./colors.less
 *
 * NOTE: variables.less overlaps with this;
 **************************************************************/

import { OnlyInstantiableByContainer, Singleton } from 'typescript-ioc';
import chroma from 'chroma-js';

// great color picker: http://hslpicker.com/

@Singleton
@OnlyInstantiableByContainer
export class AuColors {

  // TODO: more this and colors.less back into charlize

  logo_green = 'hsl(94, 100%, 50%)'
  logo_orange = 'hsl(44, 100%, 50%)';
  logo_purple = 'hsl(270, 50%, 40%)';
  logo_blue_0 = 'hsla(184, 82%, 39%, 1)'
  logo_blue_1 = 'hsl(190, 96%, 40%)';
  logo_blue = 'hsla(190, 100%, 66%, 1)';
  logo_yellow = 'hsla(59, 75%, 58%, 1)';

  prime_color = 'hsl(77, 10%, 70%)';
  prime_color_disabled = 'hsl(77, 5%, 45%)';

  state_round_closed_awardable = "hsl(44, 100%, 50%)";
  state_auction_closed = "hsl(44, 100%, 50%)";

  auRound = 'white';

  sell = this.logo_yellow; // 'hsla(94, 68%, 68%, 1)'; //'#D189E0'; //'#a5ca56';
  sellDimmed = chroma(this.sell).alpha(0.75);

  buy = this.logo_blue; // '#73c9e1';
  buyDimmed = chroma(this.buy).alpha(0.75);

  auPrice = '#CC9FF5';
  upColor = '#77ce77';
  downColor = '#fa8787';

  white = '#ddd';
  disabled = "#666"



  // TODO: sync these with .less files:

  auMatched = '#F56F46';
  auExcess = '#F5EC83';
  auPotential = 'ddd';
  auOrange = 'orange';
  auYellow = 'yellow';
  auWarning = '#ffff00';

//  auBtnDisabled = "darken(@primary-color, 2.75)"; //chroma(this.primary_color).darken(2.75);
//  auBtnEnabled = this.primary_color; // chroma('#3E755B').alpha(0.80)

  price_up_color = '#77ce77';
  price_down_color = '#fa8787';

  NORMAL = '#6de56d';
  WARNING = '#f1bf60';
  ERROR = '#ff7075';
  DISABLED = '#777';
}
