/**************************************************************
 * This file needs to be kept in sync with:
 *   ./AuColors.ts
 * Tools:
 *    https://hslpicker.com

 *
 **************************************************************/
@import (reference) "../../../../apps/charlize/src/plugins/ant-design-vue-plugin/ant-design-vue-plugin.less";

// primary is specified in ant-design-vue-plugin.less
//@label_font_color: hsl(44, 58%, 50%) !important;
//@label_font_size: 13px !important;

@sell_color:hsl(94, 100%, 50%); // #D189E0; // #a5ca56;
@buy_color: @logo_orange; // #73c9e1;
@price-color: #CC9FF5;

@price-up-color: #77ce77;
@price-down-color: #fa8787;

@white: #eee;

@logo_orange: hsl(44, 100%, 50%); //hsl(44, 100%, 50%);
@logo_purple: hsl(270, 50%, 40%);
@logo_blue: hsla(184, 82%, 39%, 1);

// change checkbox color
.ant-checkbox-inner {
  background-color: @primary-color;
}



// NOT USED ANYMORE:
//
//.sell_color_text {
//  color: @sell_color;
//}
//
//.buy_color_text {
//  color: @buy_color;
//}
//
//.price_color_text {
//  color: @sell_color;
//}
//.text-color-match {
//  color: yellow;
//}


// ONLY USED in 1 SVG component: MOVED to that component:
//
//.color-price {
//  background-color: @price-color;
//  fill: @price-color;
//}
//.color-match {
//  background-color: yellow;
//  fill: yellow;
//}
