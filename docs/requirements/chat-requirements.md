# Auction Chat Component Requirements

## Overview

The Auction Chat component provides real-time messaging functionality for auction participants, enabling communication between traders, auctioneers, and the system during live auction sessions.

## Component Architecture

### Core Component: `AuctionChat`
- **Location**: `apps/dashboard/src/components/chat/AuctionChat.tsx`
- **Demo**: `apps/dashboard/src/components/chat/ChatDemo.tsx`
- **Width**: 400px (compact design for sidebar/panel usage)

## Props Interface

```typescript
interface AuctionChatProps {
  is_auctioneer: boolean;        // User role determines UI behavior
  messages: MessageElement[];   // Array of chat messages
  outer_height: number;         // Container height in pixels
  width: number;               // Container width in pixels (400px recommended)
  onSubmitMessage?: (message: string) => void; // Message submission callback
}
```

## Message Types & Communication Rules

### Message Flow Rules
1. **Traders**: Can only send messages to the auctioneer
2. **Auctioneers**: Can only send messages to all participants (broadcast)
3. **System**: Can send messages to specific traders, all traders, or auctioneer only

### Message Types (AuMessageType enum)
- `TRADER_TO_AUCTIONEER`: Individual trader questions/comments
- `AUCTIONEER_BROADCAST`: Auctioneer announcements to all participants
- `SYSTEM_BROADCAST`: System announcements to all participants
- `SYSTEM_TO_TRADER`: System notifications to specific traders
- `SYSTEM_TO_AUCTIONEER`: System alerts to auctioneer only

## Message Format

### MessageElement Interface
```typescript
interface MessageElement {
  id: string;                    // Unique message identifier
  from: string;                 // Sender name in format "username (Company Name)"
  to: string;                   // Recipient ("Auctioneer", "All", or specific user)
  message: string;              // Message content
  message_type: AuMessageType;  // Message type enum
  message_type_label: string;   // Human-readable type label
  timestamp: number;            // Unix timestamp
  timestamp_label: string;      // Formatted time display
}
```

### Username Format
- **Traders**: `"username (Company Longname)"` (e.g., "jsmith (GreenPower Corp)")
- **Auctioneer**: `"Auctioneer"`
- **System**: `"System"`

## Visual Design

### Layout
- **Compact design**: 400px width, reduced padding and spacing
- **Message alignment**: Outgoing messages right-aligned, incoming left-aligned
- **Message bubbles**: Color-coded by message type with appropriate icons

### Typography
- **Message content**: `text-xs` (12px) for compact display
- **Message headers**: `text-xs` with medium font weight
- **Timestamps**: `text-xs` with reduced opacity, right-aligned

### Color Coding
- **Auctioneer Broadcast**: Blue (`text-blue-600 bg-blue-50 border-blue-200`)
- **Trader to Auctioneer**: Purple (`text-purple-600 bg-purple-50 border-purple-200`)
- **System Broadcast**: Orange (`text-orange-600 bg-orange-50 border-orange-200`)
- **System to Trader**: Red (`text-red-600 bg-red-50 border-red-200`)
- **System to Auctioneer**: Gray (`text-gray-600 bg-gray-50 border-gray-200`)

### Icons (Lucide React)
- **Auctioneer Broadcast**: `Radio` icon
- **Trader to Auctioneer**: `User` icon
- **System messages**: `Settings`, `AlertCircle`, `MessageSquare` icons

## Behavior Requirements

### Auto-Scrolling Requirements
- **Default behavior**: New messages automatically scroll to bottom
- **User scroll detection**: When user scrolls up from bottom, disable auto-scroll
- **Simple message addition**: When auto-scroll is disabled, just add new messages to the end of the list
- **No forced scrolling**: Wait for user to end scroll - don't force scroll during user interaction
- **Visual feedback**: When new messages are added without auto-scroll, user will see scrollbar thumb move
- **User control**: User can scroll manually or wait for auto-scroll to resume when they release mouse
- **No timeouts**: Don't resume auto-scroll based on time - only when user stops scrolling
- **No special indicators**: No "new messages" notifications needed

### Message Input
- **Textarea**: Multi-line input with auto-resize (1-12 rows)
- **Submission**: Enter key submits (Ctrl/Shift+Enter for new lines)
- **Send button**: Icon button with disabled state when input is empty
- **Clear**: Input clears after successful submission

### Role-Based UI
- **Auctioneer view**: No info message, full message height
- **Trader view**: Info message explaining communication rules
- **Message highlighting**: Trader messages highlighted when viewed by auctioneer

## Animation & Interactions

### Message Appearance
- **Animation library**: Use Framer Motion for smooth animations
- **New message animation**: Use motion 'show' animation with 200ms duration (fast)
- **No staggered delays**: All messages appear immediately without delays
- **Hover effects**: Subtle shadow increase on message hover
- **Performance**: Animations must not interfere with auto-scroll behavior

### Loading States
- **Empty state**: Centered message with icon when no messages
- **Smooth scrolling**: Direct scroll to bottom (no animation) for auto-scroll
- **Animation conflicts**: Avoid animations that conflict with scroll behavior

## Demo Requirements

### Sample Data
- **15 initial messages**: Realistic auction scenario with energy trading
- **Mixed message types**: All 6 message types represented
- **Realistic content**: Auction-specific terminology and scenarios
- **Timeline**: Messages spread over 5-minute period with proper timestamps

### Interactive Demo
- **Dual views**: Tabs for auctioneer and trader perspectives
- **Live controls**: Add/remove messages, clear all, reset demo
- **Auto-generation**: 70% chance of new message every 2 seconds
- **Real input**: Functional message submission that adds to list

### Demo Features
- **Company variety**: Multiple energy companies with realistic names
- **Message variety**: Questions, announcements, system notifications
- **Realistic flow**: Proper auction communication patterns

## Technical Implementation

### Dependencies
- **React**: Hooks (useState, useEffect, useRef)
- **Framer Motion**: Animation library for message appearance
- **shadcn/ui**: Card, Textarea, Button components
- **Lucide React**: Icon components
- **Tailwind CSS**: Styling and responsive design
- **TypeScript**: Full type safety

### State Management
- **Local state**: Input text, user scrolling flag
- **Refs**: Message container, scroll end marker, scroll timeout
- **Props**: Messages array managed by parent component

### Scroll Behavior Implementation
- **Simple approach**: Add new messages to DOM regardless of scroll position
- **Scroll detection**: Monitor when user is actively scrolling
- **Auto-scroll logic**: Only scroll to bottom when user is NOT actively scrolling
- **Resume conditions**: Auto-scroll resumes when user stops scrolling (mouse up, wheel end)
- **No complex logic**: Don't try to force visibility - let natural scrollbar behavior work
- **Implementation approach**: Keep it simple - just add messages and scroll when appropriate

### Performance Considerations
- **Scroll detection**: Simple event-based detection without complex timeouts
- **Animation performance**: Fast 200ms animations to avoid scroll conflicts
- **Memory cleanup**: Event listener cleanup in useEffect
- **Smooth operation**: Animations must not interfere with scroll behavior
- **Simple state**: Minimal state tracking for user scroll activity

## Integration Points

### Parent Component Responsibilities
- **Message management**: Maintain messages array state
- **Message submission**: Handle onSubmitMessage callback
- **Real-time updates**: Update messages array when new messages arrive
- **User context**: Provide is_auctioneer flag based on user role

### API Integration
- **Message sending**: Submit messages via WebSocket or API
- **Message receiving**: Subscribe to real-time message updates
- **User identification**: Include user/company information in messages
- **Timestamp handling**: Server-side timestamp generation recommended

## Accessibility

### Keyboard Navigation
- **Tab order**: Logical flow through interactive elements
- **Enter submission**: Standard form submission behavior
- **Escape handling**: Clear input or close component

### Screen Readers
- **ARIA labels**: Appropriate labels for interactive elements
- **Message structure**: Semantic HTML for message content
- **Live regions**: Announce new messages to screen readers

## Testing Requirements

### Unit Tests
- **Message rendering**: Verify correct display of different message types
- **Scroll behavior**: Test auto-scroll and user scroll detection
- **Input handling**: Test message submission and validation
- **Role-based display**: Test auctioneer vs trader view differences

### Integration Tests
- **Real-time updates**: Test with live message streams
- **Performance**: Test with large message volumes
- **Cross-browser**: Verify scroll behavior across browsers

### Demo Testing
- **Auto-generation**: Verify 2-second interval message creation
- **Interactive controls**: Test all demo buttons and features
- **Visual verification**: Confirm proper styling and animations
